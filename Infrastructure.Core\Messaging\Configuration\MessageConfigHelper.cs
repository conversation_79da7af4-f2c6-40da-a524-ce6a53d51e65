﻿using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Extensions;
using System.Text.RegularExpressions;

namespace LendQube.Infrastructure.Core.Messaging.Configuration;

internal static partial class MessageConfigHelper
{
    public const string FileService = "Messaging";
    public static List<string> GetTemplateKeys(string input)
    {
        if (string.IsNullOrEmpty(input))
            return [];

        var regex = MatchBracketRegex();
        var matches = regex.Matches(input);
        return [.. matches.Distinct().Select(i => i.Groups[1].Value.Trim().ToLower())];
    }


    public static bool CheckKeyEquality(List<string> textKeys, List<string> htmlKeys)
    {
        if (!textKeys.IsNullOrEmpty() && !htmlKeys.IsNullOrEmpty())
        {
            var systemKeys = EnumExtensions.GetEnumNames<MessageTemplateSystemTags>();
            var textAndHtmlHasSameKeys = textKeys.Where(x => !systemKeys.Contains(x)).Order().SequenceEqual(htmlKeys.Where(x => !systemKeys.Contains(x)).Order());
            if (!textAndHtmlHasSameKeys)
            {
                return false;
            }
        }

        return true;
    }

    public static bool CheckKeyPresent(List<string> dataKeys, List<string> keys)
    {
        if (keys.IsNullOrEmpty() || (dataKeys.IsNullOrEmpty() && keys.IsNullOrEmpty()))
            return true;
       
        return keys.All(dataKeys.Contains);
    }

    public static MessageStatus ToMessageStatus<T>(this Dictionary<T, MessageStatus> results)
        => !results.Values.IsNullOrEmpty() && results.Values.All(x => x == MessageStatus.Sent) ? MessageStatus.Sent : results.Values.Any(x => x == MessageStatus.Sent || x == MessageStatus.SentPartially) ? MessageStatus.SentPartially : MessageStatus.Failed;

    public static MessageStatus ToMessageStatus(this bool result) => result ? MessageStatus.Sent : MessageStatus.Failed;

    [GeneratedRegex("{(.*?)}")]
    private static partial Regex MatchBracketRegex();
}

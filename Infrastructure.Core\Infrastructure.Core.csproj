﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
		<RootNamespace>LendQube.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
		<PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
		<PackageReference Include="Azure.Storage.Blobs" Version="12.24.1" />
		<PackageReference Include="BlazorInputTags" Version="9.0.0" />
		<PackageReference Include="Community.Microsoft.Extensions.Caching.PostgreSql" Version="5.0.0" />
		<PackageReference Include="Coravel.Pro" Version="5.2.3" />
		<PackageReference Include="DistributedLock.Postgres" Version="1.3.0" />
		<PackageReference Include="EFCoreSecondLevelCacheInterceptor" Version="5.3.0" />
		<PackageReference Include="EntityFrameworkCore.Exceptions.PostgreSQL" Version="8.1.3" />
		<PackageReference Include="EPPlus" Version="8.0.6" />
		<PackageReference Include="FirebaseAdmin" Version="3.2.0" />
		<PackageReference Include="Google.Apis.Auth" Version="1.70.0" />
		<PackageReference Include="itext7" Version="9.2.0" />
		<PackageReference Include="MetadataExtractor" Version="2.8.1" />
		<PackageReference Include="Microsoft.ApplicationInsights" Version="2.23.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.23.0" />
		<PackageReference Include="Microsoft.ApplicationInsights.DependencyCollector" Version="2.23.0" />
		<PackageReference Include="Microsoft.AspNetCore.DataProtection.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.6" />
		<PackageReference Include="Microsoft.AspNetCore.RateLimiting" Version="7.0.0-rc.2.22476.2" />
		<PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.6" />
		<PackageReference Include="Microsoft.Extensions.Logging.AzureAppServices" Version="9.0.6" />
		<PackageReference Include="Mime-Detective" Version="25.4.25" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="NodaTime.Serialization.SystemTextJson" Version="1.3.0" />
		<PackageReference Include="Npgsql.DependencyInjection" Version="9.0.3" />
		<PackageReference Include="OpenIddict" Version="6.4.0" />
		<PackageReference Include="OpenIddict.AspNetCore" Version="6.4.0" />
		<PackageReference Include="OpenIddict.EntityFrameworkCore" Version="6.4.0" />
		<PackageReference Include="PDFtoImage" Version="5.1.1" />
		<PackageReference Include="Postmark" Version="5.2.0" />
		<PackageReference Include="Radzen.Blazor" Version="7.1.1" />
		<PackageReference Include="ScottBrady91.AspNetCore.Identity.Argon2PasswordHasher" Version="1.4.0" />
		<PackageReference Include="SendGrid" Version="9.29.3" />
		<PackageReference Include="Serilog" Version="4.3.0" />
		<PackageReference Include="Serilog.Enrichers.Environment" Version="3.0.1" />
		<PackageReference Include="Serilog.Extensions.Logging" Version="9.0.2" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="4.0.0" />
		<PackageReference Include="SendGrid.Extensions.DependencyInjection" Version="1.0.1" />
		<PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
		<PackageReference Include="System.Formats.Asn1" Version="9.0.6" />
		<PackageReference Include="System.Linq.Async" Version="6.0.3" />
		<PackageReference Include="System.Net.Http" Version="4.3.4" />
		<PackageReference Include="System.Text.RegularExpressions" Version="4.3.1" />
		<PackageReference Include="Twilio" Version="7.11.3" />
		<PackageReference Include="WTelegramClient" Version="4.3.5" />
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Entities.Core\Entities.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Web.Admin" />
	</ItemGroup>

	<ItemGroup>
	  <Content Update="AppSettings\appsettings.Development.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="AppSettings\appsettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="AppSettings\appsettings.Production.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="AppSettings\firebase-credentials.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>

</Project>

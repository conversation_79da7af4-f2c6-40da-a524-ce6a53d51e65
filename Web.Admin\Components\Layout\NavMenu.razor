﻿@using LendQube.Infrastructure.Core.Authentication
@using LendQube.Infrastructure.Core.Components
@using LendQube.Infrastructure.Core.Database.DbContexts
@using LendQube.Infrastructure.Core.AdminUserManagement
@using System.Security.Claims
@using LendQube.Infrastructure.Core.DateAndTime

@inherits AuthComponentBase

@inject IJSRuntime jSRuntime
@inject UserTimeProvider timeProvider
@inject NavigationManager NavigationManager
@inject AppDbContext context;
<div class="d-menu">
    @if (doneLoading)
    {
        if (navigation != null)
        {
            foreach (var item in navigation)
            {
                <a class="d-menu__link @(item.Value.NavLink == currentSelected ? "active" : "")" href="@item.Value.NavLink">
                    @item.Key
                </a>
            }
        }
    }
    else
    {
        <span style="color: white;">
            <i class="fa fa-spinner fa-spin"></i>
        </span>
    }

</div>


<div class="nav-dropdown hidden--xs ml-auto">
    <div class="nav-item dropdown --menu">
        <span class="nav-link dropdown-toggle text_medium" type="button" id="headerDropdown"
        data-bs-toggle="dropdown" aria-expanded="false">
            @userFullName

        </span>
        <ul class="nav-link-drop dropdown-menu" aria-labelledby="headerDropdown">
            <li>
                <a class="task-link" href="/account/manage/profile">Profile</a>
            </li>
            <li><a class="task-link" href="javascript:document.getElementById('logoutForm').submit()">Sign out</a></li>
        </ul>
    </div>
    <form action="account/logout" method="post" id="logoutForm" style="padding: 0;">
        <AntiforgeryToken />
        <input type="hidden" name="ReturnUrl" value="@currentUrl" />
    </form>
</div>
@code {
    private Dictionary<string, NavigatorVM> navigation;
    private string userFullName;
    private string currentUrl;
    private IEnumerable<Claim> claims;
    private string currentSelected;
    private bool doneLoading = false;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        if (claims.IsNullOrEmpty())
        {
            claims = GetPrincipalUser().Claims;
            userFullName = context.GetUserFullName(UserName);
        }

        if (!claims.IsNullOrEmpty())
        {
            navigation = Navigator.GeneralNavigator.Where(x => !x.Value.DoNotShowInMainMenu && (string.IsNullOrEmpty(x.Value.Permission) || claims.Any(z => z.Value == x.Value.Permission)))
                .ToDictionary(x => x.Key, x => x.Value);
        }

        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        currentSelected = NavigatorQuery(currentUrl);
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            NavigationManager.LocationChanged += OnLocationChanged;
            doneLoading = true;
            StateHasChanged();
            var timeZone = await jSRuntime.GetBrowserTimezone(Cancel);
            timeProvider.SetUserTimeZone(timeZone);
        }

        await jSRuntime.InvokeVoidAsync("blazorExtensions.OnNavigate", Cancel);
    }

    private string NavigatorQuery(string currentUrl) => Navigator.GeneralNavigator.Where(x => x.Value.NavLink == currentUrl
        || (x.Value.SubNavigation?.Any(y => y.NavLink == currentUrl || currentUrl.StartsWith(y.NavLink, StringComparison.OrdinalIgnoreCase)) ?? false)).FirstOrDefault().Value?.NavLink;

    private void OnLocationChanged(object sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        currentSelected = NavigatorQuery(currentUrl);
        StateHasChanged();
    }

    public override void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
        base.Dispose();
        context.Dispose();
    }
}

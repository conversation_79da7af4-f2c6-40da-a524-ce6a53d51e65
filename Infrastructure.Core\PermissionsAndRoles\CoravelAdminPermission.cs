﻿using System.Security.Claims;
using Coravel.Pro.Features.Auth.Interfaces;
using LendQube.Infrastructure.Core.Navigation;
using Microsoft.AspNetCore.Http;

namespace LendQube.Infrastructure.Core.PermissionsAndRoles;

public class CoravelAdminPermission : IHasPermission
{
	public bool HasPermission(HttpRequest request, ClaimsPrincipal claimsPrincipal) => claimsPrincipal.Identity.IsAuthenticated && claimsPrincipal.HasClaim(x => x.Value == SetupNavigation.BackgroundTasksPermission);
}
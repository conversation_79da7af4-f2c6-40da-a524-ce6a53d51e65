﻿namespace LendQube.Entities.Core.BackgroundTasks;

public enum BackgroundEventSource
{
    System,
    Collection,
}


public enum BackgroundTask
{
    SendMessages,
    ScheduledMessages,
    UploadCollection,
    MessagingGroupUpload,
    ScheduledReports
}

public enum BackgroundEventStatus
{
    Queued,
    Running,
    Success,
    Failed
}

public enum BackgroundControlState
{
    Running,
    Idle,
    Stopping,
    Stopped
}
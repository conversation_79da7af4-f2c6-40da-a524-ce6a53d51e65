﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using NodaTime;

namespace LendQube.Entities.Collection.Workflows.Debt;

public class AgentWorkflowAvailability : BaseEntityWithIdentityId<AgentWorkflowAvailability>, IEntityHasNotifyTrigger
{
    public Guid UserId { get; set; }
    public virtual ApplicationUser User { get; set; }
    public Instant Start { get; set; }
    public Instant? End { get; set; }
    public AgentAvailabilityStatus Status { get; set; }
    public int TotalAssigned { get; set; }
    public int CurrentlyAssignedCount { get; set; }

    public string Schema => CollectionEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => string.Empty;
}

public class AgentWorkflowAvailabilityStatusChangeLog : BaseActivityTimeline<AgentWorkflowAvailabilityStatusChangeLog>
{
    public long AvailabilityId { get; set; }
    public virtual AgentWorkflowAvailability Availability { get; set; }
    public AgentAvailabilityStatus Status { get; set; }
}


public enum AgentAvailabilityStatus
{
    Available,
    AboutToGoOnBreak,
    Away,
    Busy,
    SessionEnded
}
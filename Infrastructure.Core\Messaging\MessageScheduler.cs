﻿using Coravel.Invocable;
using Coravel.Pro;
using Coravel.Queuing.Interfaces;
using Coravel.Scheduling.Schedule.Interfaces;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;
using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class MessageSchedulerTriggerHandler(BackgroundTaskControlService service, IUnitofWork uow, IScheduler scheduler) : IHandleTriggerNotification<MessageSchedule>
{
    private readonly BackgroundEventSource Source = BackgroundEventSource.System;
    private readonly BackgroundTask Key = BackgroundTask.ScheduledMessages;

    public Task OnStartup(CancellationToken ct) => MessageScheduler.StartupScheduledMessages(uow, ct);

    public async ValueTask OnChanged(string? id, MessageSchedule oldData, MessageSchedule newData, TriggerChange change, CancellationToken ct)
    {
        var stopped = await service.CheckStoppedOrStopping(Source, Key, ct);
        if (stopped)
            return;

        await service.SetStatusToRunning(Source, Key, ct);

        scheduler.CreateBackgroundMessage(newData);

        await service.SetStatusToIdle(Source, Key, ct);
    }
}

internal static class MessageScheduler
{
    public static Task<int> StartupScheduledMessages(IUnitofWork uow, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<MessageSchedule>(x => x.ActiveOn.HasValue, x => x.SetProperty(y => y.Starting, true), ct);

    public static void CreateBackgroundMessage(this IScheduler scheduler, MessageSchedule config)
    {
        scheduler = scheduler.OnWorker(nameof(MessageScheduler));

        var schedule = scheduler.ScheduleWithParams<MessageSchedulerBackground>(config.Id);

        IScheduledEventConfiguration eventConfig = config.Frequency switch
        {
            MessageScheduleFrequency.CronExpression => schedule.Cron(config.CronExpression),
            MessageScheduleFrequency.Weekly => schedule.Weekly(),
            MessageScheduleFrequency.DailyAtHour => schedule.DailyAtHour(config.FrequencyNumber),
            _ => default
        };

        if (config.Frequency != MessageScheduleFrequency.CronExpression)
            config.CronExpression = null;

        if (config is { Days: not ScheduleDay.None })
        {
            foreach (var day in config.Days.FlagsToList<ScheduleDay>())
            {
                eventConfig = day switch
                {
                    ScheduleDay.Monday => eventConfig.Monday(),
                    ScheduleDay.Tuesday => eventConfig.Tuesday(),
                    ScheduleDay.Wednesday => eventConfig.Wednesday(),
                    ScheduleDay.Thursday => eventConfig.Thursday(),
                    ScheduleDay.Friday => eventConfig.Friday(),
                    ScheduleDay.Saturday => eventConfig.Saturday(),
                    ScheduleDay.Sunday => eventConfig.Sunday(),
                    _ => eventConfig
                };
            }
        }
        var timeZoneSet = false;

        if (config is { TimeZone: not null or "" })
        {
            var tzTimeZone = DateTimeZoneProviders.Tzdb.GetZoneOrNull(config.TimeZone);
            if (tzTimeZone != null)
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(tzTimeZone.Id);
                if (timeZone != null)
                {
                    eventConfig = eventConfig.Zoned(timeZone);
                    timeZoneSet = true;
                }
            }
        }

        if (!timeZoneSet)
        {
            eventConfig = eventConfig.Zoned(TimeZoneInfo.Utc);
        }

        eventConfig.PreventOverlapping(config.Id.ToString());
    }
}


public sealed class MessageSchedulerBackground(IUnitofWork uow, IQueue queue, long configId) : IInvocable, IDoNotAutoRegister
{
    public async Task Invoke()
    {
        var config = await uow.Db.OneAsync(Query<MessageSchedule>.Where(x => x.Id == configId && x.ActiveOn.HasValue).Include(x => x.Include(y => y.Config)), default);
        if (config is null)
            return;

        MessageBuilder.New("Scheduled Message", config.CreatedByUserId)
            .Message(config.Config.Name, config.ConfigId)
            .WithRecipients(config.Groups, config.TemplateValues)
            .Queue(queue);


        config.RunCount += 1;
        config.Starting = false;
        uow.Db.Update(config);

        await uow.SaveAsync(default);
    }
}


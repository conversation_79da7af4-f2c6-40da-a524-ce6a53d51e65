﻿using LendQube.Infrastructure.ExternalApi.ViewModels;

namespace LendQube.Infrastructure.ExternalApi.Acquired;

public interface IAcquiredExternalApiService
{
    Task<ExternalApiResult<AcquiredCreateCustomerResponseVM>> CreateCustomer(AcquiredCustomerRequestVM requestBody, CancellationToken ct);
    Task<ExternalApiResult<AcquiredGeneratePaymentLinkResponseVM>> GeneratePaymentLinkId(AcquiredGenerateLinkRequestVM requestBody, CancellationToken ct);
    Task<ExternalApiResult<AcquiredTransactionResponseVM>> GetTransaction(string orderId, CancellationToken ct);
    Task<ExternalApiResult<AcquiredChargeCardResponseVM>> ChargeCard(AcquiredChargeCardRequestVM requestBody, CancellationToken ct);
}
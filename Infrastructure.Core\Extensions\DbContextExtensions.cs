﻿using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Query;
using System.Diagnostics;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class DbContextExtensions
{
    public static Expression<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>> AppendSetProperty<TEntity>(
    this Expression<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>> left, Expression<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>> right)
    {
        var replace = new ReplacingExpressionVisitor(right.Parameters, [left.Body]);
        var combined = replace.Visit(right.Body);
        return Expression.Lambda<Func<SetPropertyCalls<TEntity>, SetPropertyCalls<TEntity>>>(combined, left.Parameters);
    }
}


[DebuggerStepThrough]
public static class RepositoryHelper
{
    public static readonly IReadOnlyList<string> CreatedStamp = [nameof(IBaseEntityWithSystemStamp.CreatedDate),
        nameof(IBaseEntityWithSystemStamp.CreatedByIp), nameof(IBaseEntityWithSystemStamp.CreatedByUser),
        nameof(IBaseEntityWithSystemStamp.CreatedByUserId)]; //do not overwrite created stamp
}
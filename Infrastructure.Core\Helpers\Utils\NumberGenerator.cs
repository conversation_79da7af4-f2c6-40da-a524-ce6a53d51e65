﻿namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class NumberGenerator
{
    private readonly static Random random = new();
    private const string numbers = "0123456789";
    private const string chars = "ABCDEFGHJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnpqrstuvwxyz";
    public static string GenerateAuthCode(int length = 6)
    {
        var code = new string(Enumerable.Repeat(numbers, length).AsParallel()
          .Select(s => s[random.Next(s.Length)]).ToArray());
        return code;
    }

    public static string GenerateTokenCode(int maxlength = 6)
    {
        var lengthArray = new List<int>();
        int minLength = 6;
        for (int i = minLength; i <= maxlength; i++)
        {
            lengthArray.Add(i);
        }

        int length = lengthArray[random.Next(lengthArray.Count)];

        var code = new string(Enumerable.Repeat(chars, length).AsParallel()
          .Select(s => s[random.Next(s.Length)]).ToArray());

        return code;
    }
}
﻿@page "/reporting/logs"

@using LendQube.Entities.Core.Reporting
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.ViewModels.Reporting
@inject GenericVMSpecificationService<SystemReport, SystemReportLogVM> Service

@inherits BaseTableComponentBase

@attribute [Authorize(Policy = ReportingNavigation.ReportLogsIndexPermission)]

<PageTitle>@Title</PageTitle>
<div class="pg-row grid grid-col-1 grid-tab-1">
	<div class="card">

        <div class="title-wrapper flex __justify-between __align-center">
            <span class="text_xl_medium">@SubTitle</span>
        </div>
        <StatusMessage @ref="TableMessage" />

		<DataTable T="SystemReportLogVM" TableDefinition="TableDefinition" LoadData="Load"  DeleteRow="SubmitDelete" @ref="table" />

    </div>
</div>


<ModalComponent Policy="@DeletePermission" ModalId="@ClearLogsModalName" ModalCss="width-xlg" Title=@($"Clear {FormBaseTitle}") >
	<BodyContent>
		Are you sure you want to clear all logs?
	</BodyContent>
	<FooterContent>
		<button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
		<LoadButton Label="Yes" OnClick="ClearLogs" />
	</FooterContent>
</ModalComponent>


@code
{
	private DataTable<SystemReportLogVM> table;
	private string ClearLogsModalName => "ClearLogsModal";

	protected override void OnInitialized()
	{
		Title = "System Report Logs";
		SubTitle = "System Report Logs";
		FormBaseTitle = "All Reports";
		DeletePermission = ReportingNavigation.ReportLogsClearLogsPermission;
	}

	protected override async Task OnInitializedAsync()
	{
		if (TableDefinition == null)
		{
			await base.OnInitializedAsync();

			AddTopButton(DeletePermission, new TopActionButton("Clear Logs", "btn--danger", Icon: "trash-2", ModalName: ClearLogsModalName, Action: () => CloseMessage(ModalMessage)));
			AddRowButton(new RowActionButton("Download Report", Icon: "download", IconClass: "__edit", Action: async (object row) =>
			{
				CloseMessage();
				table.Loading = true;
				var report = row as SystemReportLogVM;
				await JSRuntime.DownloadFile(Path.GetFileName(report.FileUrl), report.FileUrl, Cancel);
				table.Loading = false;

				StateHasChanged();
			}, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as SystemReportLogVM).FileUrl)));
		}
	}

	protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition();

	protected ValueTask<bool> SubmitDelete(SystemReportLogVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(async () =>
	{
		var result = await Service.CrudService.Delete(x => x.Id == data.Id, ct);
		if (!result)
		{
			CustomMessage = $"Unable to delete log with Id {data.Id}";
		}
		return result;
	}, refresh);

	private ValueTask<TypedBasePageList<SystemReportLogVM>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
	{
		CloseMessage();
		return Service.CrudService.GetTypeBasedPagedData(Service, filterAndPage, SystemReportLogVM.Mapping, ct);
	}

	private async Task ClearLogs()
	{
		var success = await Service.CrudService.Db.TruncateTable<SystemReport>(Cancel);
		await JSRuntime.CloseModal(ClearLogsModalName, Cancel);
		await table.Refresh();

		TableMessage.Set(success, "All logs cleared", $"Unable clear all logs");
	}
}

﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Messaging;
using LendQube.Entities.Core.Uploads;

namespace LendQube.Entities.Collection.Collections;

public class CollectionFileUpload : BaseFileUpload<CollectionFileUpload>, IEntityHasNotifyTrigger
{
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public CollectionFileUploadType Type { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long? MessageConfigId { get; set; }
    public virtual MessageConfiguration MessageConfig { get; set; }

    public string Schema => CollectionEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => $@"NEW.""{nameof(Status)}"" = {(int)UploadStatus.Queued}";
}

public enum CollectionFileUploadType
{
    Placement,
    Transaction,
    Arrangement,
    Closure,
    PlacementAmend
}
﻿@page "/collections/placements/{PlacementId:long}"
@using LendQube.Entities.Collection.Placements
@using LendQube.Infrastructure.Collection.Helpers
@using LendQube.Infrastructure.Collection.ViewModels.PlacementData
@using LendQube.Infrastructure.Core.Components.Timeline
@using LendQube.Infrastructure.Core.Database.GenericCrud
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.Database.Specification


@inject IJSRuntime jSRuntime
@inject GeneralGenericCrudVMService crudService
@attribute [Authorize(Policy = CollectionNavigation.PlacementViewPermission)]

@inherits AuthComponentBase

<PageTitle>@Title</PageTitle>
<StatusMessage Message="@message" />

<div class="pg-actions">
    <ul class="breadcrumbs">
        <li class="__item">
            <a href="/collections/placements">
                <i data-feather="arrow-left"></i>
            </a>
        </li>
        <li class="__item">
            <a>@Title: @Data?.Company</a>
        </li>
    </ul>
    <div class="actions-item ml-auto">
    </div>
</div>

<div class="pg-row grid loan-grid grid-tab-1">
    <div class="info-col">
        <div class="card info-item">
            <div class="__header flex __justify-between">
                <div class="title">
                    <span class="__title">@Data.Company</span>
                    <span class="__amount">@Data.Profile?.CurrencySymbol @Data.BalanceTotal.ToString("n2")</span>
                    <div class="status">
                        @switch (Data.Status)
                        {
                            case PlacementStatus.New:
                                <span class="lz __yellow">@Data.Status.GetDisplayName()</span>
                                break;
                            case PlacementStatus.Active:
                                <span class="lz __green">@Data.Status.GetDisplayName()</span>
                                break;
                            case PlacementStatus.Broken:
                                <span class="lz __red">@Data.Status.GetDisplayName()</span>
                                break;
                            case PlacementStatus.Settled:
                                <span class="lz __purple">@Data.Status.GetDisplayName()</span>
                                break;
                            default:
                                <span class="lz __default">@Data.Status.GetDisplayName()</span>
                                break;
                        }
                    </div>
                </div>
            </div>
            <div class="detail-wrapper grid-2">
                <div class="detail-item">
                    <span class="label">Source Account Number</span>
                    <span class="value">@Data.SourceAccountNumber</span>
                </div>
                <div class="detail-item">
                    <span class="label">Created On</span>
                    <span class="value"><LocalTime Value="Data.CreatedDate" /></span>
                </div>
                <div class="detail-item">
                    <span class="label">Agreement Date</span>
                    <span class="value"><LocalTime Value="Data.AgreementDate" Format="@InstantExtensions.LONG_DATE_FORMAT_FULL_DAY" /></span>
                </div>
                <div class="detail-item">
                    <span class="label">Default Date</span>
                    <span class="value"><LocalTime Value="Data.DefaultDate" Format="@InstantExtensions.LONG_DATE_FORMAT_FULL_DAY" /></span>
                </div>
                <div class="detail-item">
                    <span class="label">Last Payment Date</span>
                    <span class="value"><LocalTime Value="Data.LastPaymentDate" Format="@InstantExtensions.LONG_DATE_FORMAT_FULL_DAY" /></span>
                </div>
                <div class="detail-item">
                    <span class="label">Balance Total</span>
                    <span class="value">@($"{Data.Profile?.CurrencySymbol}{Data.BalanceTotal:n2}")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Balance Paid</span>
                    <span class="value">@($"{Data.Profile?.CurrencySymbol}{Data.BalancePaid:n2}")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Balance Outstanding</span>
                    <span class="value">@($"{Data.Profile?.CurrencySymbol}{Data.BalanceRemaining:n2}")</span>
                </div>
            </div>
        </div>
        <div class="card activity-item">
            <div class="accordion" id="activityAccordion">
                <div class="accordion-item">
                    <span class="accordion-header" id="activityHeading">
                        <button class="accordion-button" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapseActivity" aria-expanded="true"
                                aria-controls="collapseActivity">
                            Activity
                        </button>
                    </span>
                    <div id="collapseActivity"
                         class="accordion-collapse collapse show"
                         aria-labelledby="activityHeading"
                         data-bs-parent="#activityAccordion">
                        <div class="accordion-body">
                            @if (Data.Id != 0)
                            {
                                <ActivityTimeline T="PlacementActivity" LoadData="LoadActivity" @ref=activityTimeline />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="details-col">
        <div class="card">
            <div class="accordion loan-details" id="loanAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="detailsHeader">
                        <button class="accordion-button" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#detailsCollapse" aria-expanded="true"
                                aria-controls="detailsCollapse">
                            <i data-feather="file-text"></i>
                            Customer Details
                        </button>
                    </h2>
                    <div id="detailsCollapse"
                         class="accordion-collapse collapse show"
                         aria-labelledby="detaillsHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <div class="app_details">
                                <ul class="nav nav-tabs" id="appTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active"
                                                id="personal-tab" data-bs-toggle="tab"
                                                data-bs-target="#personal" type="button"
                                                role="tab" aria-controls="personal"
                                                aria-selected="true">
                                            Personal
                                            Information
                                        </button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="myTabContent">
                                    <div class="tab-pane fade show active"
                                         id="personal" role="tabpanel"
                                         aria-labelledby="personal-tab">
                                        <div class="detail-wrapper grid-4 grid-tab-3">
                                            <div class="detail-item">
                                                <span class="label">
                                                    First
                                                    name
                                                </span>
                                                <span class="value">@Data.Profile?.FirstName</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="label">Last name</span>
                                                <span class="value">@Data.Profile?.LastName</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="label">
                                                    Other
                                                    name(s)
                                                </span>
                                                <span class="value">@Data.Profile?.MiddleName</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="label">Gender</span>
                                                <span class="value">@Data.Profile?.Gender.GetDisplayName()</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="label">
                                                    Date of
                                                    Birth
                                                </span>
                                                <span class="value"><LocalTime Value="Data.Profile?.DateOfBirth" /></span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="label">
                                                    Email
                                                    address
                                                </span>
                                                <span class="value">@Data.Profile?.Email</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="label">
                                                    Mobile number
                                                </span>
                                                <span class="value">@Data.Profile?.MobileNumber?.ToString()</span>
                                            </div>
                                            <div class="detail-item">
                                                <span class="label">
                                                    Phone number
                                                </span>
                                                <span class="value">@Data.Profile?.PhoneNumber?.ToString()</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="viewPlacementHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#viewPlacementCollapse" aria-expanded="false"
                                aria-controls="viewPlacementCollapse">
                            <i data-feather="align-center"></i>
                            Other Placements
                        </button>
                    </h2>
                    <div id="viewPlacementCollapse" class="accordion-collapse collapse"
                         aria-labelledby="viewPlacementHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="PlacementInProfileVM" DeferLoading="true" TableDefinition="crudService.GetTableDefinition<Placement, PlacementInProfileVM>()" LoadData="LoadPlacements" @ref="placementTable" />
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="viewTxnHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#viewTxnCollapse" aria-expanded="false"
                                aria-controls="viewTxnCollapse">
                            <i data-feather="align-center"></i>
                            Transactions
                        </button>
                    </h2>
                    <div id="viewTxnCollapse" class="accordion-collapse collapse"
                         aria-labelledby="viewTxnHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="PlacementTransaction" DeferLoading="true" TableDefinition="crudService.GetTableDefinition<PlacementTransaction>(new())" LoadData="LoadTxns" @ref="txnTable" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@code {
    [Parameter]
    public long PlacementId { get; set; }


    private string Title { get; set; } = "View Placement";
    private StatusMessageBuilder message = null;

    private IUnitofWork uow;

    private Placement Data { get; set; } = new();
    private DataTable<PlacementInProfileVM> placementTable;
    private DataTable<PlacementTransaction> txnTable;
    private ActivityTimeline<PlacementActivity> activityTimeline;

    protected override void OnInitialized()
    {
        uow = crudService.Uow;
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            Data = await uow.Db.OneAsync(Query<Placement>.Where(x => x.Id == PlacementId).Include(x => x.Include(y => y.Profile).Include(y => y.Notes)), Cancel);
            await placementTable.LoadElement();
            await txnTable.LoadElement();
            await jSRuntime.RunFeather(Cancel);
            StateHasChanged();
        }
    }

    private ValueTask<TypedBasePageList<PlacementInProfileVM>> LoadPlacements(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<Placement>
        {
            PrimaryCriteria = x => x.Id != Data.Id && x.ProfileId == Data.ProfileId
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Company, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.AccountId, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.FirstName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.LastName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.Email, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.PhoneNumber.Number, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.MobileNumber.Number, filterAndPage.TextFilter));
        }

        return crudService.GetTypeBasedPagedData<Placement, PlacementInProfileVM>(spec, filterAndPage, PlacementInProfileVM.Mapping, ct: ct);
    }

    private ValueTask<TypedBasePageList<PlacementTransaction>> LoadTxns(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<PlacementTransaction>
            {
                PrimaryCriteria = x => x.PlacementId == Data.Id
            };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.PaymentMethod, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.PaymentType, filterAndPage.TextFilter));
        }

        return crudService.GetTypeBasedPagedData<PlacementTransaction>(spec, filterAndPage, ct);
    }

    private ValueTask<TypedBasePageList<PlacementActivity>> LoadActivity(DataFilterAndPage filterAndPage, GenericSpecificationService<PlacementActivity> service, CancellationToken ct)
    {
        service.PrimaryCriteria = x => x.PlacementId == Data.Id;
        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            service.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Title, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Activity, filterAndPage.TextFilter) || EF.Functions.ILike(x.CreatedByUser, filterAndPage.TextFilter));
        }

        return service.CrudService.GetTypeBasedPagedData(service, filterAndPage, ct: ct);
    }
}

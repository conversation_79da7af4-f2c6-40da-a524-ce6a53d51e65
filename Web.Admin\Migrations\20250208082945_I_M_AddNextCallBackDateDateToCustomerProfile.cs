﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class I_M_AddNextCallBackDateDateToCustomerProfile : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.AddColumn<DateOnly>(
                name: "NextCallbackDate",
                schema: "collection",
                table: "CustomerProfile",
                type: "date",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NextCallbackDate",
                schema: "collection",
                table: "CustomerProfile");
        }
    }
}

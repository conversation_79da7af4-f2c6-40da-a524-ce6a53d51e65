﻿using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;

namespace LendQube.Infrastructure.Collection.Workflow.Debt;

internal sealed class AgentAvailabilityHandler(WorkflowAssignmentService assignmentService) : IHandleTriggerNotification<AgentWorkflowAvailability>
{
    public Task OnStartup(CancellationToken ct) => Task.CompletedTask;

    public async ValueTask OnChanged(string id, AgentWorkflowAvailability oldData, AgentWorkflowAvailability newData, TriggerChange change, CancellationToken ct)
    {
        await assignmentService.AutoAssignAccountToSingleAgent(newData, ct);
    }
}

﻿using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.Repository;

partial class RelationalDbRepository
{
    public void StampChangeTracker(CancellationToken ct)
    {
        var allEntries = context.ChangeTracker.Entries().AsParallel();

        var createdWithoutStamp = allEntries.Where(x => x.State == EntityState.Added && x.Entity is IBaseEntityWithSystemStamp).Select(x => x.Entity as IBaseEntityWithSystemStamp);
        AddBulkCreationStamp(createdWithoutStamp, ct);

        var modifiedWithoutStamp = allEntries.Where(x => x.State == EntityState.Modified && x.Entity is IBaseEntityWithSystemStamp).Select(x => x.Entity as IBaseEntityWithSystemStamp);
        AddBulkUpdateStamp(modifiedWithoutStamp, ct);
    }

    private void LoadUserForInsert<T>(T entity) where T : class
    {
        if (entity is IBaseEntityWithSystemStamp baseEntity)
        {
            if (!string.IsNullOrEmpty(baseEntity.CreatedByUserId) && string.IsNullOrEmpty(baseEntity.CreatedByUser))
                userInfo.LoadUser(baseEntity.CreatedByUserId);
        }
    }

    private void SetInsertValues<T>(T entity, bool isBulk) where T : class
    {
        if (entity is IBaseEntityWithSystemStamp baseEntity)
        {
            if (!isBulk)
                LoadUserForInsert(entity);

            baseEntity.CreatedDate ??= userInfo.Now;
            baseEntity.CreatedByIp ??= userInfo.IpAddress;
            baseEntity.CreatedByUserId ??= userInfo.UserId;
            baseEntity.CreatedByUser ??= userInfo.Name;
        }

        SetId(entity);
    }

    private void AddBulkCreationStamp<T>(IEnumerable<T> entities, CancellationToken ct) where T : class
    {
        if (entities.IsNullOrEmpty())
            return;

        LoadUserForInsert(entities.FirstOrDefault());
        Parallel.ForEach(entities, new ParallelOptions { MaxDegreeOfParallelism = entities.Count(), CancellationToken = ct }, (entity, state) =>
        {
            SetInsertValues(entity, true);
        });
    }

    private void LoadUserForUpdate<T>(T entity) where T : class
    {
        if (entity is IBaseEntityWithSystemStamp baseEntity)
        {
            if (!string.IsNullOrEmpty(baseEntity.ModifiedByUserId) && string.IsNullOrEmpty(baseEntity.ModifiedByUser))
                userInfo.LoadUser(baseEntity.ModifiedByUserId);
        }
    }

    private void SetUpdateValues<T>(T entity, bool isBulk) where T : class
    {
        if (entity is IBaseEntityWithSystemStamp baseEntity)
        {
            if (!isBulk)
                LoadUserForUpdate(baseEntity);

            baseEntity.LastModifiedDate = userInfo.Now;
            baseEntity.ModifiedByIp = userInfo.IpAddress;
            baseEntity.ModifiedByUserId = userInfo.UserId;
            baseEntity.ModifiedByUser = userInfo.Name;
        }
    }

    private void AddBulkUpdateStamp<T>(IEnumerable<T> entities, CancellationToken ct) where T : class
    {
        if (entities.IsNullOrEmpty())
            return;

        LoadUserForUpdate(entities.FirstOrDefault());
        Parallel.ForEach(entities, new ParallelOptions { MaxDegreeOfParallelism = entities.Count(), CancellationToken = ct }, (entity, state) =>
        {
            SetUpdateValues(entity, true);
        });
    }
}


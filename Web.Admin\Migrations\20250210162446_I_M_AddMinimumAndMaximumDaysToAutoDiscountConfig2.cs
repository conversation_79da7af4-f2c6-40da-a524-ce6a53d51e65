﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class I_M_AddMinimumAndMaximumDaysToAutoDiscountConfig2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.AddColumn<int>(
                name: "MaxDays",
                schema: "collection",
                table: "AutoDiscountConfig",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MinDays",
                schema: "collection",
                table: "AutoDiscountConfig",
                type: "integer",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MaxDays",
                schema: "collection",
                table: "AutoDiscountConfig");

            migrationBuilder.DropColumn(
                name: "MinDays",
                schema: "collection",
                table: "AutoDiscountConfig");


        }
    }
}

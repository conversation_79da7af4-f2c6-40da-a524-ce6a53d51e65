﻿using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Workflows.Debt;

public class AgentWorkflowAnalytics : BaseEntityWithIdentityId<AgentWorkflowAnalytics>
{
    public Guid UserId { get; set; }
    public virtual ApplicationUser User { get; set; }
    public int TotalAssigned { get; set; }
    public int TotalOpened { get; set; }
    public int TotalClosed { get; set; }
    public int TotalEscalated { get; set; }
    public decimal TotalAmountCollected { get; set; }
    public int TotalPTP { get; set; }
    public decimal TotalPTPAmount { get; set; }
    public int TotalSchedulesSetup { get; set; }
    public int TotalContacted { get; set; }
    public int TotalResolved { get; set; }
    public int TotalCallBack { get; set; }


    public override void Configure(EntityTypeBuilder<AgentWorkflowAnalytics> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.UserId).IsUnique();

        builder.Property(x => x.TotalAssigned)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalOpened)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalClosed)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalEscalated)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalAmountCollected)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalPTP)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalPTPAmount)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalSchedulesSetup)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalContacted)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalResolved)
            .HasDefaultValue(0);
        builder.Property(x => x.TotalCallBack)
            .HasDefaultValue(0);

    }
}

﻿using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Customers;

public class CustomerDiscount : BaseEntityWithIdentityId<CustomerDiscount>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public long PlacementId { get; set; }
    public virtual Placement Placement { get; set; }
    public decimal BalanceBeforeDiscount { get; set; }
    public decimal BalanceAfterDiscount { get; set; }
    public decimal Discount { get; set; }
    public decimal MaxPercentage { get; set; }
    public string Reason { get; set; }
}

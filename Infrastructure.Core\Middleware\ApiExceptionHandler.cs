﻿using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace LendQube.Infrastructure.Core.Middleware;

internal sealed class ApiExceptionHandler(ILogManager<ApiExceptionHandler> logger) : IExceptionHandler
{
    public async ValueTask<bool> TryHandleAsync(HttpContext context, Exception ex, CancellationToken ct)
    {
        logger.LogError(EventSource.CustomerWeb, EventAction.Api, ex, "Error occured while processing request", data: [context.Request.Method, context.Request.Path]);

        var response = new ProblemDetails
        {
            Status = StatusCodes.Status500InternalServerError,
            Title = "Server error"
        };

        context.Response.StatusCode = response.Status.Value;

        await context.Response.WriteAsJsonAsync(response, ct);

        return true;
    }
}

using LendQube.Infrastructure.Collection.DependencyInjection;
using LendQube.Infrastructure.Core.DependencyInjection;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Web.Admin.Components;
using Microsoft.AspNetCore.Mvc.Controllers;

namespace LendQube.Web.Admin;

public class Program
{
    public static async Task Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        // Add services to the container.
        builder.Services.AddControllers()
            .ConfigureApplicationPartManager(manager =>
            {
                manager.FeatureProviders.Remove(manager.FeatureProviders.OfType<ControllerFeatureProvider>().FirstOrDefault());
                manager.FeatureProviders.Add(new AdminControllerFeatureProvider());
            });

        builder.Services
            .AddRazorComponents(options =>
                options.DetailedErrors = builder.Environment.IsDevelopment())
            .AddInteractiveServerComponents()
            .AddHubOptions(options => options.MaximumReceiveMessageSize = 10.ToMB());

        builder.AddBaseAdminService()
            .AddDbContext();

        builder.AddCollectionAdminServices();

        builder.Services
        .AddBaseDIServices()
        .AddAdminServices()
        .AddAdminUserManagement()
        .AddRadzenServices()
        .AddBackgroundServices()
        .AddAdminTriggers();

        var app = builder.Build();

        await using var scope = app.Services.CreateAsyncScope();
        await app.CreateMigration(scope);
        await app.CreateCollectionPermissions(scope);

        await app.FinishPermissionSetup(scope);

        app.AddAdminApp(builder);

        app.MapRazorComponents<App>()
            .AddAdditionalAssemblies([.. AdminDI.RoutableAssemblies])
            .AddInteractiveServerRenderMode();

        app.MapAdditionalIdentityEndpoints();

        app.MapBlazorHub(options =>
        {
            options.CloseOnAuthenticationExpiration = true;
        }).WithOrder(-1);


        app.MapControllers();

        app.UseBackgroundServices();

        await app.RunAsync();
    }
}


﻿@page "/customers/holdsetup"
@using LendQube.Entities.Collection.Setup
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using Ra<PERSON><PERSON>
@using Radzen.Blazor

@inherits GenericCrudVMTable<HoldConfig,HoldConfigVM>

@attribute [Authorize(Policy = ManageCustomersNavigation.HoldSetupIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<HoldConfig> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Reason">Reason</label>
            <InputText @bind-Value="context.Reason" class="form-input" aria-required="true" placeholder="Reason" />
            <ValidationMessage For="() => context.Reason" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Description">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Duration">Default Duration</label>
            <RadzenDropDown @bind-Value=@context.DefaultDurationId Data=@durationOptions
                            TextProperty="@nameof(HoldDurationConfig.Label)" ValueProperty="@nameof(HoldDurationConfig.Id)"
                            Name="Duration" AllowClear=true Placeholder="Select default duration" class="form-input" />
            <ValidationMessage For="() => context.DefaultDurationId" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Action">Action</label>
            <RadzenDropDown @bind-Value=@context.ActionList Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<HoldAction>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Action" Multiple=true AllowClear=true Placeholder="Select action" Chips=true class="form-input"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" FilterOperator="StringFilterOperator.StartsWith" AllowFiltering="true" />
            <ValidationMessage For="() => context.ActionList" class="text-danger" />
        </div>

        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Allow users override default settings
                    <InputCheckbox class="check-input" @bind-Value="context.AllowDefaultOverride" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </div>
    ;

    private List<HoldDurationConfig> durationOptions = [];
    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "Setup Hold Config";
        FormBaseTitle = "Hold Config";
        CreatePermission = ManageCustomersNavigation.HoldSetupCreatePermission;
        EditPermission = ManageCustomersNavigation.HoldSetupEditPermission;
        DeletePermission = ManageCustomersNavigation.HoldSetupDeletePermission;
        QuerySelector = HoldConfigVM.Mapping;
        durationOptions = Service.CrudService.Db.Many(Query<HoldDurationConfig>.All());
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Reason, filterAndPage.TextFilter);
    }

    protected override ValueTask SubmitAdd()
    {
        AddModel.Action = AddModel.ActionList.CombineFlags();
        return base.SubmitAdd();
    }

    protected override ValueTask StartEdit(HoldConfigVM data, CancellationToken ct) => BaseEdit(() =>
    {
        EditModel = data.Get();
        EditModel.ActionList = EditModel.Action.FlagsToList<HoldAction>();
        return Task.CompletedTask;
    }, ct);

    protected override ValueTask SubmitEdit()
    {
        EditModel.Action = EditModel.ActionList.CombineFlags();
        return base.SubmitEdit();
    }


    protected override ValueTask<bool> SubmitDelete(HoldConfigVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}
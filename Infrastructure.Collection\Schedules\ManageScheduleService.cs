﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.PlacementData;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Medallion.Threading;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Schedules;

public sealed class ManageScheduleService(IUnitofWork uow, PaymentFactory paymentFactory, IClock clock, IDistributedLockProvider distributedLockProvider)
{
    #region ScheduleManagement

    internal async Task<Result<ScheduleResponseVM>> NewSchedule(string userId, RequestScheduleVM vm, CancellationToken ct)
    {
        if (vm.Amount <= 0)
            return Result<ScheduleResponseVM>.Failed("Amount is invalid");

        if (DateTime.UtcNow.AddMonths(1) < vm.StartDate)
            return Result<ScheduleResponseVM>.Failed("Start date must be within a month from today");

        var profile = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerScheduleVM>.Where(x => x.Id == userId).Select(CustomerScheduleVM.Mapping), ct);

        if (profile is null)
            return Result<ScheduleResponseVM>.Failed("Account not found");

        if (vm.Amount > profile.BalanceRemaining)
            return Result<ScheduleResponseVM>.Failed($"Amount must not be more than {profile.CurrencySymbol}{profile.BalanceRemaining:n2}");

        if (!profile.Schedules.IsNullOrEmpty())
        {
            return Result<ScheduleResponseVM>.Failed("Please contact support to reschedule your account");
        }

        if (vm.DateOfBirth.HasValue && !profile.DateOfBirth.HasValue)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == userId, x => x.SetProperty(y => y.DateOfBirth, vm.DateOfBirth), ct);
            profile.DateOfBirth = vm.DateOfBirth;
        }

        var immediatePayment = vm.StartDate.Value.Date == DateTime.UtcNow.Date;

        var noOfPayments = (int)Math.Ceiling(profile.BalanceRemaining / vm.Amount);
        var lastPaymentDate = vm.Frequency.GetDate(vm.StartDate.Value, noOfPayments);
        var txn = new Transaction
        {
            Purpose = immediatePayment ? "First payment" : "Save payment method",
            Provider = PaymentProvider.Acquired,
            Type = immediatePayment ? PaymentType.Card : PaymentType.SetupCard,
            UnitAmount = vm.Amount,
            Quantity = 1,
            Fee = 0,
            Discount = 0,
            Status = TransactionStatus.Initiated,
            UserData =
            [
                new(nameof(RequestScheduleVM.StartDate), vm.StartDate.Value.ToShortDateFormat()),
                new(nameof(RequestScheduleVM.Amount), vm.Amount.ToString()),
                new(nameof(RequestScheduleVM.Frequency), vm.Frequency.ToString()),
            ],
            Fields =
            [
                new("Account Balance", profile.CurrencySymbol + (immediatePayment ? (profile.BalanceRemaining - vm.Amount).ToString("n2") : profile.BalanceRemaining.ToString("n2"))),
                new("Type", $"Recurring {vm.Frequency}"),
                new("First Payment Date", vm.StartDate.Value.ToLongDateFormat()),
                new("Final Payment Date", lastPaymentDate.ToLongDateFormat())
            ],
        };


        var initiateTxn = await paymentFactory.InitiateTransaction(profile, txn, ct);

        if (initiateTxn.IsSuccessful)
        {
            uow.Db.Insert(new CustomerTransaction
            {
                ProfileId = profile.Id,
                AmountTried = txn.Amount,
                TransactionId = txn.Id,
                PaymentProvider = txn.Provider,
            });

            await uow.SaveAsync(ct);
            return Result<ScheduleResponseVM>.Successful("Schedule created successfully", data: new ScheduleResponseVM(vm.Amount, profile.BalanceRemaining - vm.Amount, txn.CurrencySymbol,
                vm.StartDate.Value, initiateTxn.Data, $"Recurring {vm.Frequency}", lastPaymentDate));
        }

        return initiateTxn.Message;
    }

    internal async Task<Result<ScheduleResponseVM>> Reschedule(string userId, RequestScheduleVM vm, bool forceReschedule, CancellationToken ct)
    {
        if (vm.Amount <= 0)
            return Result<ScheduleResponseVM>.Failed("Amount is invalid");

        if (DateTime.UtcNow.AddMonths(1) < vm.StartDate)
            return Result<ScheduleResponseVM>.Failed("Start date must be within a month from today");

        var profile = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerScheduleVM>.Where(x => x.Id == userId).Select(CustomerScheduleVM.Mapping), ct);
        if (profile is null)
            return Result<ScheduleResponseVM>.Failed("Account not found");

        if (profile.Schedules is null)
            return Result<ScheduleResponseVM>.Failed("Please create a schedule to continue");

        if (vm.Amount > profile.BalanceRemaining)
            return Result<ScheduleResponseVM>.Failed($"Amount must not be more than {profile.CurrencySymbol}{profile.BalanceRemaining:n2}");

        if (!profile.CanReschedule && !forceReschedule)
            return Result<ScheduleResponseVM>.Failed("Please contact support to reschedule");


        if (vm.DateOfBirth.HasValue && !profile.DateOfBirth.HasValue)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == userId, x => x.SetProperty(y => y.DateOfBirth, vm.DateOfBirth), ct);
            profile.DateOfBirth = vm.DateOfBirth;
        }

        DateOnly? lastPaymentDate = null;
        var deletedCount = await uow.Db.CountAsync<CustomerSchedule>(x => x.ProfileId == profile.Id && x.PaymentStatus != SchedulePaymentStatus.Paid, ct);
        //delete unpaid schedules and redistribute amount - allow to happen only once
        var noOfPayments = (int)Math.Ceiling(profile.BalanceRemaining / vm.Amount);
        var balanceLeft = profile.BalanceRemaining;
        var schedulesToCreate = new List<CustomerSchedule>();
        var periodToStartAt = profile.Schedules.Count() - deletedCount;
        for (int i = 0; i < noOfPayments; i++)
        {
            var amount = vm.Amount < balanceLeft ? vm.Amount : balanceLeft;
            var date = vm.Frequency.GetDate(vm.StartDate.Value, i);
            var today = DateOnly.FromDateTime(DateTime.UtcNow);

            var schedule = new CustomerSchedule
            {
                ProfileId = profile.Id,
                Period = periodToStartAt + 1 + i,
                Amount = amount,
                DueDate = date,
                CPADate = date,
                PeriodStatus = date == today ? SchedulePeriodStatus.Due : SchedulePeriodStatus.NotDue,
                PaymentStatus = SchedulePaymentStatus.NotPaid
            };
            schedulesToCreate.Add(schedule);
            balanceLeft -= amount;
            lastPaymentDate = schedule.DueDate;
        }

        var immediatePayment = vm.StartDate.Value.Date == DateTime.UtcNow.Date;
        var noCard = !await uow.Db.ExistsAsync<CustomerPaymentMethod>(x => x.ProfileId == userId && x.Status == PaymentMethodStatus.Active, ct);


        if ((noCard || immediatePayment) && !forceReschedule)
        {
            var amount = noCard ? 0 : vm.Amount;
            var txn = new Transaction
            {
                Purpose = "Schedule payment",
                Provider = PaymentProvider.Acquired,
                Type = noCard ? PaymentType.SetupCard : PaymentType.Card,
                UnitAmount = noCard ? 1 : vm.Amount,
                Quantity = 1,
                Fee = 0,
                Discount = 0,
                Status = TransactionStatus.Initiated,
                UserData =
                [
                    new(nameof(RequestScheduleVM.Amount), vm.Amount.ToString()),
                    new($"{TransactionHelper.TransactionType}", $"{RepaymentTransactionType.AmendSchedule}"),
                    new(nameof(RequestScheduleVM.Frequency), $"{vm.Frequency}"),
                    new(nameof(RequestScheduleVM.StartDate),vm.StartDate.Value.ToShortDateFormat()),
                ],
                Fields =
                [
                    new("Account Balance", profile.CurrencySymbol + (profile.BalanceRemaining - amount).ToString("n2")),
                    new("Type", $"Recurring {vm.Frequency}")
                ],
                History = [],
            };

            if (lastPaymentDate != null)
                txn.Fields.Add("Final Payment Date", lastPaymentDate.Value.ToLongDateFormat());

            Result<TransactionResultVM> initiateTxn = await paymentFactory.InitiateTransaction(profile, txn, ct);

            if (initiateTxn.IsSuccessful)
            {
                uow.Db.Insert(new CustomerTransaction
                {
                    ProfileId = profile.Id,
                    AmountTried = txn.Amount,
                    TransactionId = txn.Id,
                    PaymentProvider = txn.Provider,
                });

                await uow.SaveAsync(ct);
                return Result<ScheduleResponseVM>.Successful("Schedule update pending payment", data: new ScheduleResponseVM(vm.Amount, profile.BalanceRemaining - vm.Amount,
                       profile.CurrencySymbol, vm.StartDate.Value, initiateTxn.Data, $"Recurring {vm.Frequency}"));
            }

            return initiateTxn.Message;
        }

        _ = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerSchedule>(x => x.ProfileId == profile.Id && x.PaymentStatus != SchedulePaymentStatus.Paid, ct);

        uow.Db.InsertBulk(schedulesToCreate, ct);
        uow.Db.Insert(new CustomerActivity { ProfileId = profile.Id, Title = "Re-Scheduling", Activity = $"Account schedule updated. {vm.Frequency} payment set" });
        await uow.SaveAsync(ct);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == profile.Id, x => x
        .SetProperty(y => y.TotalRescheduleCount, y => y.TotalRescheduleCount + 1)
        .SetProperty(y => y.LastRescheduleDate, clock.GetCurrentInstant())
        .SetProperty(y => y.PaymentFrequency, vm.Frequency),
        ct);

        _ = await MessageBuilder.New("Amend Schedule", profile.Id)
            .Message(MessageConfigNames.AmendSchedule.GetDisplayName())
            .WithRecipient(profile.Id,
            [
                new($"{MessageTemplateKeys.Frequency}", $"{vm.Frequency}"),
                new($"{MessageTemplateKeys.Amount}", $"{profile.CurrencySymbol}{vm.Amount:n2}"),
                new($"{MessageTemplateKeys.Duration}", $"{noOfPayments} {vm.Frequency.ToString().Replace("ly", "s")}"),
            ])
            .Send(uow, ct);


        return Result<ScheduleResponseVM>.Successful("Schedule updated successfully", data: new ScheduleResponseVM(vm.Amount, profile.BalanceRemaining - vm.Amount,
               profile.CurrencySymbol, vm.StartDate.Value, null, $"Recurring {vm.Frequency}"));
    }
    #endregion

    #region PaymentManagement

    internal async Task<Result<ScheduleResponseVM>> InitiatePayment(string userId, InitiateTransactionRequest vm, CancellationToken ct)
    {
        if (vm.Amount <= 0)
            return "Amount is invalid";

        var profile = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerScheduleVM>.Where(x => x.Id == userId && x.BalanceRemaining > 0).Select(CustomerScheduleVM.Mapping), ct);
        if (profile is null)
            return "Account not found";

        if (vm.Amount > profile.BalanceRemaining)
            return $"Amount must not be more than {profile.CurrencySymbol}{profile.BalanceRemaining:n2}";

        if (vm.Type == RepaymentTransactionType.ReduceBalance && (profile.Schedules.IsNullOrEmpty() || !profile.Schedules.Any(x => x.PaymentStatus != SchedulePaymentStatus.Paid)))
            return "No outstanding schedule found";


        var txn = new Transaction
        {
            Purpose = "Schedule payment",
            Provider = PaymentProvider.Acquired,
            Type = PaymentType.Card,
            UnitAmount = vm.Amount,
            Quantity = 1,
            Fee = 0,
            Discount = 0,
            Status = TransactionStatus.Initiated,
            UserData =
            [
                new(TransactionHelper.TransactionType, vm.Type.ToString())
            ],
            Fields =
            [
                new("Account Balance", profile.CurrencySymbol + (profile.BalanceRemaining - vm.Amount).ToString("n2")),
                new("Type", "One-Time Payment"),
            ],
            History = [],
        };

        var initiateTxn = await paymentFactory.InitiateTransaction(profile, txn, ct, true);

        if (initiateTxn.IsSuccessful)
        {
            uow.Db.Insert(new CustomerTransaction
            {
                ProfileId = profile.Id,
                AmountTried = txn.Amount,
                TransactionId = txn.Id,
                PaymentProvider = txn.Provider,
            });

            await uow.SaveAsync(ct);

            return Result<ScheduleResponseVM>.Successful("Payment initiated successfully", data: new ScheduleResponseVM(vm.Amount, profile.BalanceRemaining - vm.Amount,
                txn.CurrencySymbol, DateTime.UtcNow, initiateTxn.Data, "One-Time Payment"));
        }
        return initiateTxn.Message ?? "Payment initiation failed";
    }

    internal Task<Result<TransactionResultVM>> ConfirmPayment(string userId, ProcessTransactionRequestVM vm, CancellationToken ct) => ApplyPaymentHandler.ConfirmPayment(uow, clock, distributedLockProvider, paymentFactory, userId, vm, ct);
    #endregion

    #region I&E
    internal async Task<Result<decimal>> EvalulateIncomeAndExpenditure(string userId, CustomerIncomeAndExpenditureVM vm, CancellationToken ct)
    {
        var incomeAndExpenditure = await uow.Db.OneAsync(Query<CustomerIncomeAndExpenditure>.Where(x => x.ProfileId == userId), ct);
        if (incomeAndExpenditure == null && vm != null)
        {
            incomeAndExpenditure = vm.Get(userId);
            uow.Db.Insert(incomeAndExpenditure);
            await uow.SaveAsync(ct);
        }

        if (incomeAndExpenditure == null)
            return vm.DurationInMonths > 3 ? Result<decimal>.Failed("No Income and Expenditure for user") : Result<decimal>.Successful("Ok");

        //evaluate

        if (vm.AmountToPay > incomeAndExpenditure.NetDisposableIncome) //amount is more, confirm
        {
            return Result<decimal>.Pending("Schedule amount is more than your disposable income, do you wish to proceed?");
        }

        var expectedRepaymentAmount = incomeAndExpenditure.NetDisposableIncome * 0.4m;

        if (vm.AmountToPay < expectedRepaymentAmount) //amount is less, recommend
        {
            var suggestedAmount = expectedRepaymentAmount < vm.Balance ? expectedRepaymentAmount : vm.Balance;
            return Result<decimal>.Pending($"Based on your disposable income, we recommend you make a payment of {suggestedAmount:n2}. Do you wish to use this amount?", data: suggestedAmount);
        }

        return Result<decimal>.Successful("Ok");
    }
    #endregion
}
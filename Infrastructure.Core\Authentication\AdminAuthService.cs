﻿using System.Globalization;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Logs;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;

namespace LendQube.Infrastructure.Core.Authentication;

public sealed class AdminAuthService(SignInManager<ApplicationUser> signInManager, UserAccessService accessService,
  UrlEncoder urlEncoder, ILogManager<AdminAuthService> logger, DefaultAppConfig config)
{
	private const string AuthenticatorUriFormat = "otpauth://totp/{0}:{1}?secret={2}&issuer={0}&digits=6";

	public async Task<UserLoginState> LoginNew(AdminLoginVM request, string ip)
	{
		var user = await signInManager.UserManager.FindByEmailAsync(request.Username);
		if (user is null)
			return UserLoginState.Failed;

		var grantType = GrantType.Password;

		if (user.Role != SystemRoleConfig.AdminRole)
		{
			accessService.RecordAccessIP(user.UserName, ip, grantType);
			return UserLoginState.Failed;
		}

		var signinResult = await signInManager.PasswordSignInAsync(user, request.Password, request.RememberMe, true);

		if (signinResult.IsLockedOut || signinResult.IsNotAllowed)
			return UserLoginState.LockedOut;

		if (signinResult.Succeeded || signinResult.RequiresTwoFactor)
		{
			accessService.RecordAccessIP(user.UserName, ip, grantType);

			if (signinResult.RequiresTwoFactor && !(config.DeployState.IsDemo && request.Skip2fa))
			{
				return UserLoginState.TwoFactorSignInRequired;
			}


			var twoFactorRemembered = (config.DeployState.IsDemo && request.Skip2fa) || await signInManager.IsTwoFactorClientRememberedAsync(user);
			if (twoFactorRemembered)
			{
				await signInManager.SignInWithClaimsAsync(user, request.RememberMe, [new Claim("amr", "mfa")]);
				return user.MustChangePasswordOn.HasValue ? UserLoginState.ChangePasswordRequired : UserLoginState.Succeeded;
			}

			return UserLoginState.TwoFactorSetupRequired;
		}

		accessService.RecordFailedAccessIP(user.UserName, ip, grantType);
		return UserLoginState.Failed;
	}

	public async Task<AuthenticatorKeyModel> AuthenticatorSetup(ClaimsPrincipal claimsPrincipal)
	{
		var user = await signInManager.UserManager.GetUserAsync(claimsPrincipal);

		if (user == null)
		{
			return null;
		}

		var unformattedKey = await signInManager.UserManager.GetAuthenticatorKeyAsync(user);

		if (string.IsNullOrEmpty(unformattedKey))
		{
			await signInManager.UserManager.ResetAuthenticatorKeyAsync(user);
			unformattedKey = await signInManager.UserManager.GetAuthenticatorKeyAsync(user);
		}

		if (string.IsNullOrEmpty(unformattedKey))
			return new AuthenticatorKeyModel("", "", "Invalid Authenticator setup attempt");

		var sharedKey = FormatKey(unformattedKey);

		var authenticatorUri = GenerateQrCodeUri(user.Email, unformattedKey);

		return new AuthenticatorKeyModel(sharedKey, authenticatorUri, "");
	}

	public async Task<UserLoginState> ValidateAuthenticatorSetup(ValidateAuthenticatorSetup request)
	{
		var user = await signInManager.UserManager.GetUserAsync(request.User);

		var grantType = GrantType.Complete2FASetup;

		if (user == null)
		{
			return UserLoginState.Failed;
		}

		var isAuthenticatorCodeValid = await signInManager.UserManager.VerifyTwoFactorTokenAsync(user, signInManager.UserManager.Options.Tokens.AuthenticatorTokenProvider, request.AuthenticatorCode.CleanUpAuthCode());

		if (!isAuthenticatorCodeValid)
		{
			accessService.RecordFailedAccessIP(user.UserName, request.Ip, grantType);
			return UserLoginState.Failed;
		}

		var setupResult = await signInManager.UserManager.SetTwoFactorEnabledAsync(user, true);

		if (setupResult.Succeeded)
		{
			await signInManager.SignInWithClaimsAsync(user, false, [new Claim("amr", "mfa")]);
			accessService.RecordAccessIP(user.UserName, request.Ip, grantType);
			return UserLoginState.Succeeded;
		}

		accessService.RecordFailedAccessIP(user.UserName, request.Ip, grantType);
		return UserLoginState.Failed;
	}

	public async Task<UserLoginState> LoginWith2fa(LoginWith2faVM request, string ip)
	{
		var user = await signInManager.GetTwoFactorAuthenticationUserAsync();

		var grantType = GrantType.Login2FA;

		if (user == null)
		{
			return UserLoginState.RedirectToLogin;
		}

		var result = await signInManager.TwoFactorAuthenticatorSignInAsync(request.AuthenticatorCode.CleanUpAuthCode(), request.RememberMe, request.RememberMachine);

		if (result.Succeeded)
		{
			accessService.RecordAccessIP(user.UserName, ip, grantType);
			return user.MustChangePasswordOn.HasValue ? UserLoginState.ChangePasswordRequired : UserLoginState.Succeeded;
		}
		else if (result.IsLockedOut)
		{
			return UserLoginState.LockedOut;
		}
		else
		{
			logger.LogInformation(EventSource.AdminWeb, EventAction.Authentication, $"Invalid authenticator code entered for user with ID {user.Email}");
			accessService.RecordFailedAccessIP(user.UserName, ip, grantType);
			return UserLoginState.Failed;
		}
	}

	public async Task<UserLoginState> LoginWithRecoveryCode(LoginWithRecoveryCodeVM vm, string ip)
	{
		var user = await signInManager.GetTwoFactorAuthenticationUserAsync();

		var grantType = GrantType.LoginRecoveryCode;

		if (user == null)
		{
			return UserLoginState.RedirectToLogin;
		}

		var recoveryCode = vm.RecoveryCode.Replace(" ", string.Empty);

		var result = await signInManager.TwoFactorRecoveryCodeSignInAsync(recoveryCode);

		if (result.Succeeded)
		{
			accessService.RecordAccessIP(user.UserName, ip, grantType);
			return user.MustChangePasswordOn.HasValue ? UserLoginState.ChangePasswordRequired : UserLoginState.Succeeded;
		}
		else if (result.IsLockedOut)
		{
			return UserLoginState.LockedOut;
		}
		else
		{
			logger.LogWarning(EventSource.AdminWeb, EventAction.Authentication, $"Invalid authenticator code entered for user with ID {user.Id}");
			accessService.RecordFailedAccessIP(user.UserName, ip, grantType);
			return UserLoginState.Failed;
		}
	}

	public async Task<(UserLoginState, IEnumerable<string>, string)> GetRecoveryCodes(ClaimsPrincipal claimsPrincipal)
	{
		var user = await signInManager.UserManager.GetUserAsync(claimsPrincipal);
		if (user == null)
			return (UserLoginState.RedirectToLogin, [], string.Empty);

		if (user == null || !user.TwoFactorEnabled)
		{
			return (UserLoginState.Failed, [], "Cannot generate recovery codes for user because they do not have 2FA enabled.");
		}

		return (UserLoginState.Succeeded, await signInManager.UserManager.GenerateNewTwoFactorRecoveryCodesAsync(user, 10), "You have generated new recovery codes.");
	}

	public async Task Logout()
	{
		try
		{
			await signInManager.SignOutAsync(); //sign out may fail if user is already signed out
		}
		catch (Exception)
		{

		}
	}

	public bool IsSignedIn(HttpContext context) => signInManager.IsSignedIn(context.User);

	private static string FormatKey(string unformattedKey)
	{
		var result = new StringBuilder();
		int currentPosition = 0;

		while (currentPosition + 4 < unformattedKey.Length)
		{
			result.Append(unformattedKey.AsSpan(currentPosition, 4)).Append(' ');
			currentPosition += 4;
		}

		if (currentPosition < unformattedKey.Length)
		{
			result.Append(unformattedKey.AsSpan(currentPosition));
		}

		return result.ToString().ToLowerInvariant();
	}

	private string GenerateQrCodeUri(string email, string unformattedKey) =>
		string.Format(
		CultureInfo.InvariantCulture,
		AuthenticatorUriFormat,
			urlEncoder.Encode(config.DeployState.IsDemo ? $"[Dev] {StringConstants.AdminAppName}" : StringConstants.AdminAppName),
			urlEncoder.Encode(email),
			unformattedKey);

}
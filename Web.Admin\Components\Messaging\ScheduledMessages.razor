﻿@page "/messaging/scheduler"
@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using Radzen.Blazor

@inherits GenericCrudVMTable<MessageSchedule, MessageScheduleVM>

@attribute [Authorize(Policy = MessagingNavigation.MessageSchedulerIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddLocalModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditLocalModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    [SupplyParameterFromForm]
    protected MessageScheduleVM AddLocalModel { get; set; }

    [SupplyParameterFromForm]
    protected MessageScheduleVM EditLocalModel { get; set; }

    private RenderFragment<MessageScheduleVM> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="Frequency">Frequency</label>
                <RadzenDropDown @bind-Value=@context.Frequency Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<MessageScheduleFrequency>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="Frequency" Placeholder="Select frequency" class="form-input" />
                <ValidationMessage For="() => context.Frequency" class="text-danger" />
            </div>
            
            @if(context.Frequency == MessageScheduleFrequency.CronExpression) 
            {
                <div class="form-row">
                    <label class="form-label" for="CronExpression">Cron Expression</label>
                    <InputText @bind-Value="context.CronExpression" class="form-input" aria-required="true" placeholder="* * * * *" />
                    <ValidationMessage For="() => context.CronExpression" class="text-danger" />
                    <small class="text_sm_medium text_small">Expressions can be built using <a href="https://crontab.guru" target="_blank">Crontab</a>. Use numbers only</small>
                </div>
            }
            else
            {
                <div class="form-row">
                    <label class="form-label" for="Days">Days</label>
                <RadzenDropDown @bind-Value=@context.DaysList Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<ScheduleDay>([ScheduleDay.None]))
                                    TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                    Name="Days" Multiple=true AllowClear=true Placeholder="Select day" Chips=true class="form-input"
                                    FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true" />
                    <ValidationMessage For="() => context.DaysList" class="text-danger" />
                </div>
            }
        </div>
        
        @if(context.Frequency == MessageScheduleFrequency.DailyAtHour) 
        {
            <div class="form-row">
                <label class="form-label" for="FrequencyNumber">At What Hour?</label>
                <InputNumber @bind-Value="context.FrequencyNumber" class="form-input" aria-required="true" placeholder="What hour?" />
                <ValidationMessage For="() => context.FrequencyNumber" class="text-danger" />
            </div>
        }

        <div class="form-row">
            <label class="form-label" for="TimeZone">Time Zone</label>
            <RadzenDropDown @bind-Value=@timeZone Data=@timezones
                            Name="TimeZone" Placeholder="Select timezone" class="form-input"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true">
                <ValueTemplate>
                    <RadzenTextBox @bind-Value=@timeZone Style="width:120%; height:120%; margin:-10px" />
                </ValueTemplate>
            </RadzenDropDown>
            <ValidationMessage For="() => context.TimeZone" class="text-danger" />
            <small class="text_sm_medium text_small">Default is UTC. Acceptable values can be found at <a href="https://en.wikipedia.org/wiki/List_of_tz_database_time_zones" target="_blank">Wikipedia</a></small>
        </div>

        <div class="form-row">
            <label class="form-label" for="ConfigId">Message Config</label>
            <RadzenDropDown @bind-Value=@context.ConfigId Data=@configs
                            TextProperty="@nameof(MessageConfigForSchedulerVM.Name)" ValueProperty="@nameof(MessageConfigForSchedulerVM.Id)"
                            Name="Frequency" Placeholder="Select message" class="form-input"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
            <ValidationMessage For="() => context.ConfigId" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Groups">Recipient Group</label>
            <RadzenDropDown @bind-Value=@context.Groups Data=@groups
                            TextProperty="@nameof(MessageConfigForSchedulerVM.Name)" ValueProperty="@nameof(MessageConfigForSchedulerVM.Id)"
                            Name="Days" Multiple=true AllowClear=true Placeholder="Select target group" Chips=true class="form-input"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
            <ValidationMessage For="() => context.Groups" class="text-danger" />
        </div>

        @if(context.ConfigId > 0)
        {
            var keys = configs.FirstOrDefault(x => x.Id == context.ConfigId)?.Keys ?? [];
            @foreach (var item in keys)
            {
                context.KeysWithValues[item] = null;
                <div class="form-row">
                    <label class="form-label" for="@item">Template Key: @item</label>
                    <input type="text" class="form-input" aria-required="true" placeholder="@item" value="@context.KeysWithValues.GetValueOrDefault(item)" @onchange="(e) => SetTemplateValue(context, e, item)" />
                    @if (systemKeys.Contains(item))
                    {
                        <small class="text_sm_medium text_small">This is a system key, leave empty for automatic substitution</small>
                    }
                </div>
            }
        }
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Is Active
                    <InputCheckbox class="check-input" @bind-Value="context.Active" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </div>
    ;

    private string timeZone = string.Empty;
    private readonly List<string> timezones = ["Europe/London", "Africa/Lagos"];
    private List<MessageConfigForSchedulerVM> configs = [];
    private List<MessageConfigForSchedulerVM> groups = [];
    private readonly IEnumerable<string> systemKeys = Entities.Core.Extensions.EnumExtensions.GetEnumNames<MessageTemplateSystemTags>();
    protected override void OnInitialized()
    {
        Title = "Messaging";
        FormBaseTitle = "Scheduled Messages";
        SubTitle = "Scheduled Message";
        CreatePermission = MessagingNavigation.MessageSchedulerCreatePermission;
        EditPermission = MessagingNavigation.MessageSchedulerEditPermission;
        DeletePermission = MessagingNavigation.MessageSchedulerDeletePermission;
        QuerySelector = MessageScheduleVM.Mapping;
        configs = Service.CrudService.Db.ManySelect(Query<MessageConfiguration, MessageConfigForSchedulerVM>.All().Select(x => new MessageConfigForSchedulerVM(x.Id, x.Name, x.Keys)));
        groups = Service.CrudService.Db.ManySelect(Query<MessagingGroup, MessageConfigForSchedulerVM>.Where(x => x.Directory.Any() || x.QueryDirectory != null).Select(x => new MessageConfigForSchedulerVM(x.Id, x.Name, null)));
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            AddLocalModel = new();
            EditLocalModel = new();

            await base.OnInitializedAsync();
        }
    }


    private void SetTemplateValue(MessageScheduleVM context, ChangeEventArgs eventArgs, string key)
    {
        context.KeysWithValues[key] = eventArgs.Value?.ToString();
    }

    protected override void StartAdd() => AddLocalModel = new();

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Config.Name, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Config.Description, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Name, filterAndPage.TextFilter);
    }


    protected override ValueTask SubmitAdd() => BaseSaveAdd(() =>
    {
        ( var valid, CustomMessage) = AddLocalModel.IsValid();
        if (!valid)
            return Task.FromResult(false);

        AddModel = AddLocalModel.Get();
        AddModel.TimeZone = timeZone;
        return Service.CrudService.New(AddModel, Cancel);
    },
    () =>
    {
        timeZone = string.Empty;
        AddLocalModel = new();
        return table.Refresh();
    });

    protected override ValueTask StartEdit(MessageScheduleVM editData, CancellationToken ct) => BaseEdit(() =>
    {
        timeZone = editData.TimeZone;
        EditLocalModel = editData;
        EditLocalModel.DaysList = editData.Days.FlagsToList<ScheduleDay>();
        EditLocalModel.KeysWithValues = editData.TemplateValues.ToDictionary(x => x.Key, x => x.Value);
        return Task.CompletedTask;
    }, ct);

    protected override ValueTask SubmitEdit() => BaseSaveEdit(() =>
    {
        (var valid, CustomMessage) = EditLocalModel.IsValid();
        if (!valid)
            return Task.FromResult(false);

        var data = EditLocalModel.Get();
        data.TimeZone = timeZone;
        return Service.CrudService.Update(data, Cancel);

    },
    () =>
    {
        timeZone = string.Empty;
        EditLocalModel = new();
        return table.Refresh();

    });


    protected override ValueTask<bool> SubmitDelete(MessageScheduleVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}

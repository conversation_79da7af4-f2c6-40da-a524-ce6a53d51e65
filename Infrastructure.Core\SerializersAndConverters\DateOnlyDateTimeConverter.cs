﻿using LendQube.Infrastructure.Core.Extensions;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.Core.SerializersAndConverters;

public sealed class DateOnlyDateTimeConverter : JsonConverter<DateTime?>
{
    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        if (string.IsNullOrEmpty(value)) return null;
        return value.ParseForOs("dd MMM, yyyy");
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.HasValue ? value.Value.ToString("dd MMM, yyyy") : "");
    }
}
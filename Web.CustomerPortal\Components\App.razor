@namespace Web.CustomerPortal.Components

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />
    <link href="@Assets["css/main.css"]" rel="stylesheet" />
    <link href="@Assets["app.css"]" rel="stylesheet" />
    <link href="@Assets["Web.CustomerPortal.styles.css"]" rel="stylesheet" />
    <link rel="icon" type="image/png" href="@Assets["favicon.png"]" />
    <HeadOutlet />
</head>

<body>
    <Routes />

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>

    <script src="_framework/blazor.web.js"></script>
</body>

</html>

<style>
    #blazor-error-ui {
        background: lightyellow;
        bottom: 0;
        box-shadow: 0 -1px 2px rgba(0, 0, 0, 0.2);
        display: none;
        left: 0;
        padding: 0.6rem 1.25rem 0.7rem 1.25rem;
        position: fixed;
        width: 100%;
        z-index: 1000;
    }

    #blazor-error-ui .dismiss {
        cursor: pointer;
        position: absolute;
        right: 0.75rem;
        top: 0.5rem;
    }
</style>

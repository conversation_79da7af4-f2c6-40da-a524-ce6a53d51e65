﻿using System.Linq.Expressions;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Core.Database.GenericCrud;

public class GeneralGenericCrudVMService(IUnitofWork uow) : GeneralGenericCrudService(uow)
{
    private readonly IUnitofWork uow = uow;

    public ValueTask<TypedBasePageList<TVM>> GetTypeBasedPagedData<T,TVM>(ISpecification<T> spec, DataFilterAndPage filterAndPage, Expression<Func<T, TVM>> select, CancellationToken ct) where T : class, IBaseEntityForRelationalDb
    {
        spec.DoAllFilter(filterAndPage);
        var dbSet = uow.Db.NotTrackedWithSpec(spec);

        var paginator = new SelectPagedList<T, TVM>(dbSet, filterAndPage, spec, select, ct);

        return paginator.ToType(ct);
    }

    public ValueTask<TypedBasePageList<TVM>> GetTypeBasedPagedData<T, TVM>(IQueryable<T> dbSet, ISpecification<TVM> spec, DataFilterAndPage filterAndPage, Expression<Func<T, TVM>> select, CancellationToken ct)
    where T : class
    {
        spec.DoAllFilter(filterAndPage);

        var paginator = new SelectPagedList<T, TVM>(dbSet, filterAndPage, spec, select, ct);

        return paginator.ToType(ct);
    }

    public ColumnList GetTableDefinition<T, TVM>(TableSettings<TVM> settings = null) where T : class =>
        GenericColumnAndFilterService<T, TVM>.GetTableDefinition(settings ?? new());
}

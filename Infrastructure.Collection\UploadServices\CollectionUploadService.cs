﻿using System.Collections.Concurrent;
using Coravel.Queuing.Interfaces;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.FileManagement;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using Microsoft.AspNetCore.Components.Forms;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.UploadServices;

internal sealed class CollectionUploadService(IFileManagementService fileService, IQueue queue, Func<CollectionFileUploadType, IUploadTypeService> accessor, BackgroundTaskControlService backgroundService,
    HttpClient httpClient, ILogManager<CollectionFileUpload> logger) :
    AbstractFileUploadService<CollectionFileUpload>(fileService, queue, backgroundService, httpClient)
{
    protected override ILogManager<CollectionFileUpload> Logger => logger;

    protected override string UPLOADKEY => "Collection";
    protected override BackgroundEventSource Source => BackgroundEventSource.Collection;
    protected override BackgroundTask Key => BackgroundTask.UploadCollection;


    public static event EventHandler<SystemBackgroundTaskEventArgs>? StatusNotificationEvent;
    public async Task<Result<bool>> Save(CollectionFileUpload upload, IBrowserFile file, CancellationToken ct)
    {
        if (accessor(upload.Type) == null)
            return $"{upload.Type.GetDisplayName()} currently not supported";

        return await Upload(upload.Type.GetDisplayName(), upload, file, ct);
    }

    protected override void RaiseEvent(SystemBackgroundTaskEventArgs data)
    {
        StatusNotificationEvent?.Invoke(this, data);
    }

    protected override Task<Result<MessageBrick>> UploadData(CollectionFileUpload upload, ExcelWorksheet worksheet, MessageBrick message, ConcurrentBag<UploadResult> resultsList, CancellationToken ct)
        => accessor(upload.Type).SaveData(upload, worksheet, message, resultsList, ct);
}

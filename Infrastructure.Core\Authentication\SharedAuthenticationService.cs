﻿using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.AspNetCore.Identity;

namespace LendQube.Infrastructure.Core.Authentication;

public static class SharedAuthenticationService
{
    public static async Task<IdentityResult> AddApplicationUser(this UserManager<ApplicationUser> userManager, ApplicationUser user, string password = null, IEnumerable<string> roles = default, string? transactionPin = null)
    {
        var result = password == null ? await userManager.CreateAsync(user) : await userManager.CreateAsync(user, password);

        if (result.Succeeded)
        {
            if (!roles.IsNullOrEmpty())
                result = await userManager.AddToRolesAsync(user, roles);

            if (result.Succeeded && !string.IsNullOrEmpty(transactionPin))
            {
                user.WalletPinHash = userManager.PasswordHasher.HashPassword(user, transactionPin);
                result = await userManager.UpdateAsync(user);
            }
        }

        return result;
    }
}
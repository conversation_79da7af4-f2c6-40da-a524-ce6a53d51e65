﻿using System.Diagnostics;
using System.Globalization;
using NodaTime;
using NodaTime.Text;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class InstantExtensions
{
    public static readonly DateTimeZone LONDON_TIMEZONE = DateTimeZoneProviders.Tzdb.GetZoneOrNull("Europe/London");
    public const string TIME_ONLY_FORMAT = "hh:mm tt";
    public const string SHORT_DATE_FORMAT = "dd-MM-yyyy";
    public const string MID_DATE_FORMAT = "dd MMM, yyyy";
    public const string LONG_DATE_FORMAT_FULL_DAY = "dddd, dd MMM, yyyy";
    public const string LONG_DATE_TIME_FORMAT = "dd MMM, yyyy hh:mm:ss tt";
    public const string LONG_DATE_TIME_FORMAT_FULL_DAY = "dddd, dd MMM, yyyy hh:mm:ss tt";
    public const string LONG_DATE_TIME_FORMAT_WITH_TIMEZONE = "dddd, dd MMM, yyyy hh:mm:ss tt\" GMT\"zzz";

    public static Instant GetInstantFromString(this string date, string pattern = SHORT_DATE_FORMAT) => InstantPattern.CreateWithInvariantCulture(pattern).Parse(date).Value;
    public static Instant GetInstantFromStringGivenTimeZone(this string date, string timeZoneId, string pattern = SHORT_DATE_FORMAT)
    {
        var timeZone = DateTimeZoneProviders.Tzdb.GetZoneOrNull(timeZoneId);
        var localDateTime = LocalDateTimePattern.CreateWithInvariantCulture(pattern).Parse(date).Value;
        var zonedDateTime = localDateTime.InZoneLeniently(timeZone); //local datetimeZone
        return zonedDateTime.ToInstant();
    }

    public static string GetStringFromInstant(this Instant instant, string format = SHORT_DATE_FORMAT) => instant.ToString(format, CultureInfo.InvariantCulture);
    public static string GetStringFromInstant(this ZonedDateTime instant, string format = SHORT_DATE_FORMAT) => instant.ToString(format, CultureInfo.InvariantCulture);
    public static Instant StartOfNextDay(this Instant instant) => instant.Plus(Duration.FromDays(1));

    public static Interval GetDayInterval(this Instant instant)
    {
        var utc = instant.InUtc();
        DateTimeZone timeZone = utc.Zone;
        ZonedDateTime dayStart = timeZone.AtStartOfDay(utc.Date);
        ZonedDateTime dayEnd = timeZone.AtStartOfDay(utc.Date.PlusDays(1));
        return new Interval(dayStart.ToInstant(), dayEnd.ToInstant());
    }
}

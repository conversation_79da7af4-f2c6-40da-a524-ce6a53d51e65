﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Entities.Core.Messaging;

[DbTableFillFactor(70)]
public class MessagingTemplate : BaseEntityWithIdentityId<MessagingTemplate>
{
	[Required]
    public string Name { get; set; }
    [Required]
    public string Description { get; set; }
    [Required]
    public MessageConfigTemplateType Types { get; set; }
    public List<string> Keys { get; set; }
    public string TextTemplate { get; set; }
    public string HtmlTemplate { get; set; }
    public bool IsContainer { get; set; }
    public Instant? DisabledOn { get; set; }

    public override void Configure(EntityTypeBuilder<MessagingTemplate> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Name)
            .IsUnique()
            .IncludeProperties(e => e.Types);
    }
}

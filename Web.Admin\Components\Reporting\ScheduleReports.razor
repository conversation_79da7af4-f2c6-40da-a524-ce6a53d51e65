﻿@page "/reporting/scheduler"
@using LendQube.Entities.Core.Messaging
@using LendQube.Entities.Core.Reporting
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.Reporting
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using LendQube.Infrastructure.Core.ViewModels.Reporting
@using Radzen.Blazor

@inherits GenericCrudVMTable<ReportSchedule, ReportScheduleVM>

@attribute [Authorize(Policy = ReportingNavigation.ScheduleReportingIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddLocalModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditLocalModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    [SupplyParameterFromForm]
    protected ReportScheduleVM AddLocalModel { get; set; }

    [SupplyParameterFromForm]
    protected ReportScheduleVM EditLocalModel { get; set; }

    private RenderFragment<ReportScheduleVM> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Description">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="ReportTypeName">Report Type</label>
                <RadzenDropDown @bind-Value=@context.ReportTypeName Data=@reportTypes
                                TextProperty="@nameof(ReportTypes.TypeName)" ValueProperty="@nameof(ReportTypes.TypeName)"
                                Name="ReportTypeName" Placeholder="Select report type" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"/>
                <ValidationMessage For="() => context.ReportTypeName" class="text-danger" />
            </div>

            @if(!string.IsNullOrEmpty(context.ReportTypeName))
            {
                <div class="form-row">
                    <label class="form-label" for="ReportNames">Report Names</label>
                    <RadzenDropDown @bind-Value=@context.ReportNames Data=@reportTypes.FirstOrDefault(x => x.TypeName == context.ReportTypeName).Names Multiple=true AllowClear=true Chips=true
                                    Name="ReportNames" Placeholder="Select reports" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true" />
                    <ValidationMessage For="() => context.ReportNames" class="text-danger" />
                </div>
            }
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="FileType">File Type</label>
                <RadzenDropDown @bind-Value=@context.FileType Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<ReportType>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="FileType" Placeholder="Select report file type" class="form-input" />
                <ValidationMessage For="() => context.FileType" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="Action">Schedule Action</label>
                <RadzenDropDown @bind-Value=@context.Action Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<ReportScheduleAction>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="Action" Placeholder="Select schedule action" class="form-input" />
                <ValidationMessage For="() => context.Action" class="text-danger" />
            </div>
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="StartDate">Report Start Date</label>
                <RazdenInstantDatePicker @bind-DateValue="context.StartDate" Placeholder="Report Start Date" Name="StartDate" />
                <ValidationMessage For="() => context.StartDate" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="EndDate">Report Start End</label>
                <RazdenInstantDatePicker @bind-DateValue="context.EndDate" Placeholder="Report End Date" Name="EndDate" />
                <ValidationMessage For="() => context.EndDate" class="text-danger" />
            </div>
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="Frequency">Frequency</label>
                <RadzenDropDown @bind-Value=@context.Frequency Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<ReportFrequency>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="Frequency" Placeholder="Select frequency" class="form-input" />
                <ValidationMessage For="() => context.Frequency" class="text-danger" />
            </div>

            @if(context.Frequency == ReportFrequency.Weekly)
            {
                <div class="form-row">
                    <label class="form-label" for="Days">Days</label>
                    <RadzenDropDown @bind-Value=@context.DaysList Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<ScheduleDay>([ScheduleDay.None]))
                                    TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                    Name="Days" Multiple=true AllowClear=true Placeholder="Select day" Chips=true class="form-input"
                                    FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true" />
                    <ValidationMessage For="() => context.DaysList" class="text-danger" />
                </div>
            } 
            else if(context.Frequency == ReportFrequency.Daily)
            {
                <div class="form-row">
                    <label class="form-label" for="FrequencyNumber">At What Hour?</label>
                    <InputNumber @bind-Value="context.FrequencyNumber" class="form-input" aria-required="true" placeholder="What hour?" />
                    <ValidationMessage For="() => context.FrequencyNumber" class="text-danger" />
                </div>
            }
        </div>

        <div class="form-row">
                <label class="form-label" for="TimeZone">Time Zone</label>
                <RadzenDropDown @bind-Value=@timeZone Data=@timezones
                                Name="TimeZone" Placeholder="Select timezone" class="form-input" 
                                FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true">
                    <ValueTemplate>
                    <RadzenTextBox @bind-Value=@timeZone Style="width:120%; height:120%; margin:-10px" />
                    </ValueTemplate>
                </RadzenDropDown>
                <ValidationMessage For="() => context.TimeZone" class="text-danger" />
            <small class="text_sm_medium text_small">Default is UTC. Acceptable values can be found at <a href="https://en.wikipedia.org/wiki/List_of_tz_database_time_zones" target="_blank">Wikipedia</a></small>
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="ConfigId">Message Config</label>
                <RadzenDropDown @bind-Value=@context.ConfigId Data=@configs
                                TextProperty="@nameof(MessageConfigForSchedulerVM.Name)" ValueProperty="@nameof(MessageConfigForSchedulerVM.Id)"
                                Name="Frequency" Placeholder="Select message" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"/>
                <ValidationMessage For="() => context.ConfigId" class="text-danger" />
            </div>

            <div class="form-row">
                <label class="form-label" for="MessagingGroupId">Recipient Group</label>
                <RadzenDropDown @bind-Value=@context.MessagingGroupId Data=@groups
                                TextProperty="@nameof(MessageConfigForSchedulerVM.Name)" ValueProperty="@nameof(MessageConfigForSchedulerVM.Id)"
                                Name="Days" AllowClear=true Placeholder="Select target group" class="form-input"
                                FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true" />
                <ValidationMessage For="() => context.MessagingGroupId" class="text-danger" />
            </div>
        </div>
        
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Is Disabled
                    <InputCheckbox class="check-input" @bind-Value="context.Disabled" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </div>
    ;

    private string timeZone = string.Empty;
    private string localTimeZone = string.Empty;
    private readonly List<string> timezones = ["Europe/London", "Africa/Lagos"];
    private List<MessageConfigForSchedulerVM> configs = [];
    private List<MessageConfigForSchedulerVM> groups = [];
    private IReadOnlyList<ReportTypes> reportTypes = ReportingHelper.ReportNames;
    protected override void OnInitialized()
    {
        Title = "Reporting";
        SubTitle = "Scheduled Reports";
        FormBaseTitle = "Scheduled Report";
        CreatePermission = ReportingNavigation.ScheduleReportingCreatePermission;
        EditPermission = ReportingNavigation.ScheduleReportingEditPermission;
        DeletePermission = ReportingNavigation.ScheduleReportingDeletePermission;
        QuerySelector = ReportScheduleVM.Mapping;
        configs = Service.CrudService.Db.ManySelect(Query<MessageConfiguration, MessageConfigForSchedulerVM>.All().Select(x => new MessageConfigForSchedulerVM(x.Id, x.Name, x.Keys)));
        groups = Service.CrudService.Db.ManySelect(Query<MessagingGroup, MessageConfigForSchedulerVM>.Where(x => x.Directory.Any() || x.QueryDirectory != null).Select(x => new MessageConfigForSchedulerVM(x.Id, x.Name, null)));
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            AddLocalModel = new();
            EditLocalModel = new();

            await base.OnInitializedAsync();
        }
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        await base.OnAfterRenderAsync(firstRender);
        localTimeZone = await JSRuntime.GetBrowserTimezone(Cancel);
    }


    protected override void StartAdd() => AddLocalModel = new();

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Config.Name, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Config.Description, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.ReportTypeName, filterAndPage.TextFilter);
    }


    protected override ValueTask SubmitAdd() => BaseSaveAdd(() =>
    {
        AddModel = AddLocalModel.Get();
        AddModel.TimeZone = timeZone;
        return Service.CrudService.New(AddModel, Cancel);
    },
    () =>
    {
        timeZone = string.Empty;
        AddLocalModel = new();
        return table.Refresh();
    });

    protected override ValueTask StartEdit(ReportScheduleVM editData, CancellationToken ct) => BaseEdit(() =>
    {
        timeZone = editData.TimeZone;
        EditLocalModel = editData;
        EditLocalModel.DaysList = editData.Days.FlagsToList<ScheduleDay>();
        return Task.CompletedTask;
    }, ct);

    protected override ValueTask SubmitEdit() => BaseSaveEdit(() =>
    {
        var data = EditLocalModel.Get();
        data.TimeZone = timeZone;
        return Service.CrudService.Update(data, Cancel);

    },
    () =>
    {
        timeZone = string.Empty;
        EditLocalModel = new();
        return table.Refresh();

    });


    protected override ValueTask<bool> SubmitDelete(ReportScheduleVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}

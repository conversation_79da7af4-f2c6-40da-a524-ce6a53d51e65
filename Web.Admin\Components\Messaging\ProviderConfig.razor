﻿@page "/messaging/providers"

@using LendQube.Entities.Core.Location
@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.Messaging.Providers
@using LendQube.Infrastructure.Core.ViewModels.SystemSetup
@using Radzen.Blazor
@inherits GenericCrudTable<MessageProviderConfig>
@inject NavigationManager navigationManager

@attribute [Authorize(Policy = MessagingNavigation.MessageProviderConfigIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputSelect @bind-Value="context.Name">
                <option label="Select provider" selected></option>
                @foreach (var item in MessageProviderHelpers.ProviderNames)
                {
                    <option value="@item">@item</option>
                }
            </InputSelect>
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        @EditFormContent(context)
    </BodyContent>
</ModalEditComponent>


<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @EditFormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private List<CountryVM> countries = [];
    private RenderFragment<MessageProviderConfig> EditFormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="FundingAmount">Funding Amount</label>
            <InputNumber @bind-Value="context.FundingAmount" class="form-input" aria-required="true" placeholder="Funding Amount" />
            <ValidationMessage For="() => context.FundingAmount" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="UnitCost">Unit Cost</label>
            <InputNumber @bind-Value="context.UnitCost" class="form-input" aria-required="true" placeholder="Unit Cost" />
            <ValidationMessage For="() => context.UnitCost" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="SupportedCountries">Supported Countries</label>
            <RadzenDropDown @bind-Value=@context.SupportedCountries Data=@countries Name="SupportedCountries" TextProperty="@nameof(CountryVM.Name)" ValueProperty="@nameof(CountryVM.Code)"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"
                            Multiple=true AllowClear=true Placeholder="Select supported countries" Chips=true class="form-input" />
            <ValidationMessage For="() => context.SupportedCountries" class="text-danger" />
        </div>

        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Is Disabled
                    <InputCheckbox class="check-input" @bind-Value="context.Disabled" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>

        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Log To Message Activity
                    <InputCheckbox class="check-input" @bind-Value="context.LogActivity" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>

    </div>
    ;

    protected override void OnInitialized()
    {
        Title = "Message Providers";
        FormBaseTitle = "Message Provider";
        SubTitle = "Setup Message Providers";
        CreatePermission = MessagingNavigation.MessageProviderConfigCreatePermission;
        EditPermission = MessagingNavigation.MessageProviderConfigEditPermission;
        DeletePermission = MessagingNavigation.MessageProviderConfigDeletePermission;
        countries = Service.CrudService.Db.ManySelect(Query<Country, CountryVM>.Select(x => new CountryVM(x.Id, x.Name, x.Code)));
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new()
        {
            ColumnFilterOptions =
            [
                new()
                {
                    ColumnName = nameof(MessageProviderConfig.SupportedCountries),
                    Options = countries.Select(x => x.Code)
                }
            ]
        });

    protected override async Task OnInitializedAsync()
    {
        if(TableDefinition ==  null)
        {
            await base.OnInitializedAsync();

            AddRowButton(EditPermission, new("Login", Icon: "log-in", Action: (object row) =>
            {
                CloseMessage();
                if((row as MessageProviderConfig).Name.ToLower().Contains("telegram"))
                {
                    navigationManager.NavigateTo("/messaging/providers/telegram-login");
                }
                return Task.CompletedTask;
            }, ShowCondition: (object row) =>
            {
                return (row as MessageProviderConfig).Name.ToLower().Contains("telegram");
            }));

        }
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter);
    }

    protected override ValueTask SubmitAdd()
    {
        AddModel.TotalFunding = AddModel.FundingAmount;
        AddModel.DisabledOn = AddModel.Disabled ? clock.GetCurrentInstant() : null;
        AddModel.LogActivityOn = AddModel.LogActivity ? clock.GetCurrentInstant() : null;
        return base.SubmitAdd();
    }

    protected override ValueTask StartEdit(MessageProviderConfig data, CancellationToken ct)
    {
        data.LogActivity = data.LogActivityOn.HasValue;
        data.Disabled = data.DisabledOn.HasValue;
        return base.StartEdit(data, ct);
    }

    protected override ValueTask SubmitEdit() => SaveEdit((data, ct) => Service.CrudService.UpdateWithFilter(x => x.Id == data.Id, x =>
        x.SetProperty(y => y.DisabledOn, data.Disabled ? clock.GetCurrentInstant() : null)
            .SetProperty(y => y.LogActivityOn, data.LogActivity ? clock.GetCurrentInstant() : null)
            .SetProperty(y => y.SupportedCountries, data.SupportedCountries)
            .SetProperty(y => y.TotalFunding, y => y.TotalFunding + data.FundingAmount)
            , ct), table.Refresh);
}

﻿using System.Text;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components.FormsAndModals;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using Microsoft.AspNetCore.Components;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageConfig;

public partial class ViewMessageConfig
{
    private List<MessagingTemplate> ConfiguredTemplates { get; set; } = [];
    private string htmlTemplate;
    private bool hasPendingChanges, hasHtmlCustomChanges, hasTextCustomChanges = false;
    private PagedTextTemplate pagedTextTemplate { get; set; } = new();
    private string templateDirectory = "messagetemplates";

    private async Task LoadBodyTemplate()
    {
        Data.BodyTemplate = ConfiguredTemplates.FirstOrDefault(x => x.Id == Data.BodyTemplateId);
        if (Data.BodyTemplate == null)
            return;
        if (Data.TextRequired)
        {
            if (string.IsNullOrEmpty(pagedTextTemplate.Text))
            {
                pagedTextTemplate.Text = Data.BodyTemplate.TextTemplate;
            }
            else if (pagedTextTemplate.Text.Contains("{body}"))
            {
                pagedTextTemplate.Text = pagedTextTemplate.Text.Replace("{body}", Data.BodyTemplate.TextTemplate);
            }

            Data.TextTemplate = null;
        }

        if (Data.EmailRequired)
        {
            Data.BodyTemplate.HtmlTemplate = await httpClient.ReadPhysicalFileAsString(Data.BodyTemplate.HtmlTemplate, ct: Cancel);
            if ((string.IsNullOrEmpty(htmlTemplate) || htmlTemplate.Contains("{body}")))
            {
                if (string.IsNullOrEmpty(htmlTemplate))
                {
                    htmlTemplate = Data.BodyTemplate.HtmlTemplate;
                }
                else
                {
                    htmlTemplate = htmlTemplate.Replace("{body}", Data.BodyTemplate.HtmlTemplate);
                }
            }
        }
    }

    private async Task LoadContainerTemplate()
    {
        Data.ContainerTemplate = ConfiguredTemplates.FirstOrDefault(x => x.Id == Data.ContainerTemplateId);
        if (Data.ContainerTemplate == null)
            return;
        if (Data.TextRequired)
        {
            if (string.IsNullOrEmpty(pagedTextTemplate.Text))
            {
                pagedTextTemplate.Text = Data.ContainerTemplate.TextTemplate;
            }
            else if (pagedTextTemplate.Text.Contains("{body}"))
            {
                pagedTextTemplate.Text = Data.ContainerTemplate.TextTemplate.Replace("{body}", pagedTextTemplate.Text);
            }
            Data.TextTemplate = null;
        }

        if (Data.EmailRequired)
        {
            Data.ContainerTemplate.HtmlTemplate = await httpClient.ReadPhysicalFileAsString(Data.ContainerTemplate.HtmlTemplate, ct: Cancel);

            if ((string.IsNullOrEmpty(htmlTemplate) || htmlTemplate.Contains("{body}")))
            {
                if (string.IsNullOrEmpty(htmlTemplate))
                {
                    htmlTemplate = Data.ContainerTemplate.HtmlTemplate;
                }
                else
                {
                    htmlTemplate = Data.ContainerTemplate.HtmlTemplate.Replace("{body}", htmlTemplate);
                }
            }
        }
    }

    private async Task OnSelectContainerTemplate(long? value)
    {
        Data.ContainerTemplateId = value;
        hasPendingChanges = true;
        if (value.HasValue)
        {
            if (hasHtmlCustomChanges)
                htmlTemplate = null;
            if (hasTextCustomChanges)
                pagedTextTemplate.Text = null;

            await LoadContainerTemplate();
        }
        else
        {
            Data.ContainerTemplate = null;
        }
    }

    private async Task OnSelectBodyTemplate(long? value)
    {
        Data.BodyTemplateId = value;
        hasPendingChanges = true;
        if (value.HasValue)
        {
            if (hasHtmlCustomChanges)
                htmlTemplate = null;
            if (hasTextCustomChanges)
                pagedTextTemplate.Text = null;
            await LoadBodyTemplate();
        }
        else
        {
            Data.BodyTemplate = null;
        }
    }

    private void SetTemplateValue(ChangeEventArgs eventArgs, string key)
    {
        SendMessageModel.KeysWithValues[key] = eventArgs.Value?.ToString();
    }

    private async Task SaveData()
    {
        Data.Keys = null;
        if (Data.TextRequired)
        {
            Data.Keys = MessageConfigHelper.GetTemplateKeys(pagedTextTemplate.Text);
            if (hasTextCustomChanges)
                Data.TextTemplate = pagedTextTemplate.Text;
        }

        if (Data.EmailRequired)
        {
            var htmlKeys = MessageConfigHelper.GetTemplateKeys(htmlTemplate);

            if (!MessageConfigHelper.CheckKeyEquality(Data.Keys, htmlKeys))
            {
                message.Error($"Your text and html template do not have the same template keys. Text Keys: {string.Join(", ", Data.Keys)}. Html Keys: {string.Join(", ", htmlKeys)}");
                return;
            }

            Data.Keys = htmlKeys;

            if (hasHtmlCustomChanges)
            {
                Data.HtmlTemplate = await fileService.SaveBytes(MessageConfigHelper.FileService, $"{templateDirectory}/{SecurityDriven.FastGuid.NewGuid()}.html", Encoding.UTF8.GetBytes(htmlTemplate), Cancel);
            }
        }

        var subjectKeys = MessageConfigHelper.GetTemplateKeys(Data.Subject);

        if (!MessageConfigHelper.CheckKeyPresent(Data.Keys, subjectKeys))
        {
            Data.Keys.AddRange(subjectKeys);
        }

        try
        {
            uow.Db.Update(Data.DeepCopy());
            uow.Db.Insert(new MessagingConfigurationActivity { MessageConfigurationId = Data.Id, Activity = "Template updated" });
            await uow.SaveAsync(Cancel);
            message.Success("Message template saved");

            if ((!Data.TextRequired || Data.TextConfigured) && (!Data.EmailRequired || Data.EmailConfigured))
            {
                _ = await uow.Db.UpdateAndSaveWithFilterAsync<MessageLogEntry>(x => x.MessageConfigurationId == Data.Id && x.Log.Status == MessageStatus.WaitingForConfiguration,
                    x => x.SetProperty(y => y.TextRequired, Data.TextRequired)
                    .SetProperty(y => y.EmailRequired, Data.EmailRequired)
                    .SetProperty(x => x.Name, Data.Name)
                    .SetProperty(x => x.Subject, Data.Subject)
                    .SetProperty(x => x.Channels, Data.Channels)
                    .SetProperty(x => x.Keys, Data.Keys)
                    .SetProperty(x => x.TextTemplate, Data.TextTemplate)
                    .SetProperty(x => x.HtmlTemplate, Data.HtmlTemplate)
                    .SetProperty(x => x.ContainerTextTemplate, Data.ContainerTemplate != null ? Data.ContainerTemplate.TextTemplate : null)
                    .SetProperty(x => x.ContainerHtmlTemplate, Data.ContainerTemplate != null ? Data.ContainerTemplate.HtmlTemplate : null)
                    .SetProperty(x => x.BodyTextTemplate, Data.BodyTemplate != null ? Data.BodyTemplate.TextTemplate : null)
                    .SetProperty(x => x.BodyHtmlTemplate, Data.BodyTemplate != null ? Data.BodyTemplate.HtmlTemplate : null)
                , Cancel);

                _ = await uow.Db.UpdateAndSaveWithFilterAsync<MessageLog>(x => x.Status == MessageStatus.WaitingForConfiguration && x.MessageLogEntries.Any(y => y.MessageConfigurationId == Data.Id),
                x => x.SetProperty(y => y.Status, MessageStatus.Queued), Cancel);
            }

            hasPendingChanges = false;
        }
        catch (Exception)
        {
            message.Warning("Saving message template failed. See message activity log");
        }
        await activityTimeline.Refresh();
    }
}

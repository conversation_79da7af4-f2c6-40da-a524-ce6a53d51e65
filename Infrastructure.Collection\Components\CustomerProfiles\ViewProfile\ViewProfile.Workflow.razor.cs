﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Collection.Analytics;
using LendQube.Infrastructure.Collection.Components.DebtWorkflow;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.AspNetCore.Components;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{

    [Parameter]
    public long? WorkTaskId { get; set; }

    [Parameter]
    public bool ViewMode { get; set; }

    private bool isAssignedUser = false;

    private AgentWorkflowTask task = null;
    private AgentWorkTaskVM workTaskVM = null;

    private List<RequiredNoteVM> workflowNotes = [];
    private List<AgentWorkflowPaymentVM> workflowPayments = [];
    private List<AgentWorkflowPtpVM> workflowPtps = [];
    private List<AgentUserVM> workflowEscalationUsers = [];


    private string RecordWorkflowActionModalName => "RecordWorkflowActionModal";
    private RecordAgentWorkflowAction RecordWorkflowActionModel { get; set; } = new();


    private string WorkflowEscalateModalName => "WorkflowEscalate";
    private WorkflowEscalationVM WorkflowEscalateModel { get; set; } = new();


    private string FinishWorkflowModalName => "FinishWorkflow";
    private readonly string finishWorkflowMessageTemplate = "{0} Are you sure you want to conclude work on this account?";
    private string finishWorkflowMessage = string.Empty;

    private readonly List<WorkflowTaskAction> actionsThatRequireNotes = [WorkflowTaskAction.ContactMade, WorkflowTaskAction.Resolved, WorkflowTaskAction.PTPSetup];

    private async Task LoadTask()
    {
        if (!WorkTaskId.HasValue)
            return;

        task = await uow.Db.OneAsync(Query<AgentWorkflowTask>.Where(x => x.Id == WorkTaskId.Value), Cancel);
        workTaskVM = await uow.Db.OneSelectAsync(Query<AgentWorkflowTask>.Where(x => x.Id == WorkTaskId.Value).Select(AgentWorkTaskVM.Mapping), Cancel);
        isAssignedUser = task.UserId == UserId;
    }

    private async Task LoadTaskNotes()
    {
        if (!isAssignedUser || ViewMode)
            return;

        workflowPtps = await uow.Db.ManySelectAsync(Query<CustomerPromiseToPay>.Where(x => x.ProfileId == ProfileId && x.CreatedDate >= task.Opened && x.CreatedByUserId == UserName).Select(x => new AgentWorkflowPtpVM(x.Id, x.Amount)), Cancel);
        workflowNotes = await uow.Db.ManySelectAsync(Query<CustomerNote>.Where(x => x.ProfileId == ProfileId && x.CreatedDate >= task.Opened && x.CreatedByUserId == UserName).Select(x => new RequiredNoteVM(x.Id, $"{x.Type}: {x.Note}")), Cancel);
        workflowPayments = await uow.Db.ManySelectAsync(Query<Transaction>.Where(x => x.ProfileId == ProfileId && x.CreatedDate >= task.Opened && x.Status == TransactionStatus.Completed && x.Purpose != "CPA").Select(x => new AgentWorkflowPaymentVM(x.Id, x.TotalAmountPaid)), Cancel);
    }

    private async Task LoadEscalationUsers()
    {
        if (!isAssignedUser || ViewMode)
            return;

        var query = Query<ApplicationUser>.Where(x => x.Id != UserId && x.Role == SystemRoleConfig.AdminRole && x.UserRoles.Any(y => y.Role.RoleClaims.Any(z => z.ClaimValue == DebtWorkflowNavigation.DebtWorkflowDeskCanBeEscalatedToPermission)));

        if (!Config.DeployState.IsDemo)
            query = query.AndWhere(x => x.UserRoles.Any(y => y.Role.Name != SystemRoleConfig.SuperAdminRole));

        workflowEscalationUsers = await uow.Db.ManySelectAsync(query.Select(x => new AgentUserVM(x.Id, x.FullName)), Cancel);
    }

    protected async ValueTask RecordWorkflowAction()
    {
        CloseMessage();

        if (task.Tasks.Any(x => x.Action == RecordWorkflowActionModel.Action) && !task.IsEscalated)
        {
            ModalMessage.Error("Action already recorded");
            return;
        }

        if (RecordWorkflowActionModel.Action == WorkflowTaskAction.PaymentCollected && string.IsNullOrEmpty(RecordWorkflowActionModel.PaymentId))
        {
            ModalMessage.Error("Applied payment is required");
            return;
        }

        if (RecordWorkflowActionModel.Action == WorkflowTaskAction.PTPSetup && !RecordWorkflowActionModel.PtpId.HasValue)
        {
            ModalMessage.Error("PTP is required");
            return;
        }

        if (RecordWorkflowActionModel.Action == WorkflowTaskAction.CallBack && !RecordWorkflowActionModel.NextCallbackDate.HasValue)
        {
            ModalMessage.Error("Call back date is required");
            return;
        }

        if (actionsThatRequireNotes.Contains(RecordWorkflowActionModel.Action.Value) && !RecordWorkflowActionModel.NoteId.HasValue)
        {
            ModalMessage.Error("Please select applicable note");
            return;
        }

        if (RecordWorkflowActionModel.Action == WorkflowTaskAction.PaymentCollected && task.Tasks.Any(x => x.Action == WorkflowTaskAction.PaymentCollected && x.PaymentId == RecordWorkflowActionModel.PaymentId))
        {
            ModalMessage.Error("Payment has already been recorded");
            return;
        }

        if (RecordWorkflowActionModel.Action == WorkflowTaskAction.PTPSetup && task.Tasks.Any(x => x.Action == WorkflowTaskAction.PTPSetup && x.PtpId == RecordWorkflowActionModel.PtpId))
        {
            ModalMessage.Error("PTP has already been recorded");
            return;
        }

        if (RecordWorkflowActionModel.NoteId.HasValue && task.Tasks.Any(x => x.NoteId == RecordWorkflowActionModel.NoteId))
        {
            ModalMessage.Error("Note has already been used");
            return;
        }

        var amount = RecordWorkflowActionModel.Action == WorkflowTaskAction.PaymentCollected ? workflowPayments?.FirstOrDefault(x => x.Id == RecordWorkflowActionModel.PaymentId)?.Amount
            : workflowPtps?.FirstOrDefault(x => x.Id == RecordWorkflowActionModel.PtpId)?.Amount;

        if (RecordWorkflowActionModel.Action == WorkflowTaskAction.CallBack && RecordWorkflowActionModel.NextCallbackDate.HasValue)
        {
            task.NextCallbackDate = RecordWorkflowActionModel.NextCallbackDate.Value;
        }

        task.Tasks.Add(RecordWorkflowActionModel.Get(SystemClock.Instance.GetCurrentInstant(), amount));

        uow.Db.Update(task);
        await uow.SaveAsync(Cancel);
        if (task.NextCallbackDate.HasValue)
        {
            await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == task.CustomerProfileId, x => x.SetProperty(y => y.NextCallbackDate, RecordWorkflowActionModel.NextCallbackDate.Value), Cancel);
        }
        await JSRuntime.CloseModal(RecordWorkflowActionModalName, Cancel);

        switch (RecordWorkflowActionModel.Action)
        {
            case WorkflowTaskAction.ContactMade:
                await ManageAnalyticsService.UpdateContactedWorkflow(uow, task.UserId, Cancel);
                break;
            case WorkflowTaskAction.Resolved:
                await ManageAnalyticsService.UpdateResolvedWorkflow(uow, task.UserId, Cancel);
                break;
            case WorkflowTaskAction.PaymentCollected:
                await ManageAnalyticsService.UpdateAmountCollectedWorkflow(uow, task.UserId, amount.Value, Cancel);
                break;
            case WorkflowTaskAction.PTPSetup:
                await ManageAnalyticsService.UpdatePtpWorkflow(uow, task.UserId, amount.Value, Cancel);
                break;
            case WorkflowTaskAction.ScheduleSetup:
                await ManageAnalyticsService.UpdateScheduleWorkflow(uow, task.UserId, Cancel);
                break;
            case WorkflowTaskAction.Escalated:
                await ManageAnalyticsService.UpdateEscalatedWorkflow(uow, task.UserId, Cancel);
                break;
            case WorkflowTaskAction.CallBack:
                await ManageAnalyticsService.UpdateCallBackWorkflow(uow, task.UserId, Cancel);
                break;
            default:
                break;
        }

        StateHasChanged();
    }

    protected async ValueTask EscalateWorkflow()
    {
        CloseMessage();
        var now = SystemClock.Instance.GetCurrentInstant();
        task.Tasks.Add(new WorkflowTask
        {
            Action = WorkflowTaskAction.Escalated,
            When = now,
        });

        uow.Db.Insert(new AgentWorkflowTask
        {
            UserId = WorkflowEscalateModel.EscalateTo,
            EscalationReason = WorkflowEscalateModel.Reason,
            Assigned = now,
            Tasks = task.Tasks,
            EscalatedByUserId = task.UserId,
            CustomerProfileId = task.CustomerProfileId,
            DebtSegmentId = task.DebtSegmentId,
            EscalatedTaskId = task.Id,
            IsEscalated = true,
        });

        task.IsEscalated = true;
        task.EscalatedToUserId = WorkflowEscalateModel.EscalateTo;
        task.EscalationReason = WorkflowEscalateModel.Reason;
        task.Removed = now;

        uow.Db.Update(task);

        await uow.SaveAsync(Cancel);

        NavigationManager.NavigateTo("/debtworkflow/agentworkdesk");
    }

    protected void ConfirmFinishWorkflow()
    {
        CloseMessage();
        if (task.Tasks.Count == 0)
            finishWorkflowMessage = string.Format(finishWorkflowMessageTemplate, "You have not recorded any actions yet. ");
        else
            finishWorkflowMessage = string.Format(finishWorkflowMessageTemplate, string.Empty);
    }

    protected async Task FinishWorkflow()
    {
        task.Removed = SystemClock.Instance.GetCurrentInstant();
        uow.Db.Update(task);
        await uow.SaveAsync(Cancel);
        _ = await uow.Db.UpdateAndSaveWithFilterAsync<AgentWorkflowAvailability>(x => x.Id == task.AvailabilityId,
            x => x.SetProperty(y => y.Status, y => y.Status == AgentAvailabilityStatus.AboutToGoOnBreak ? AgentAvailabilityStatus.AboutToGoOnBreak : AgentAvailabilityStatus.Available)
        .SetProperty(y => y.CurrentlyAssignedCount, y => y.CurrentlyAssignedCount - 1), Cancel);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == task.CustomerProfileId, x => x.SetProperty(y => y.CurrentlyAssigned, false), Cancel);

        await JSRuntime.CloseModal(FinishWorkflowModalName, Cancel);

        NavigationManager.NavigateTo("/debtworkflow/agentworkdesk");
    }
}

﻿@page "/debtworkflow/agentsegmentmapping"
@using LendQube.Entities.Collection.Workflows.Debt
@using LendQube.Entities.Core.BaseUser
@using LendQube.Infrastructure.Core.AppSettings
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using Radzen
@using Radzen.Blazor

@inherits GenericCrudVMTable<AgentToDebtSegmentMapping, AgentDebtSegmentMappingVM>
@inject DefaultAppConfig config

@attribute [Authorize(Policy = DebtWorkflowNavigation.DebtWorkflowAgentMappingConfigIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<AgentToDebtSegmentMapping> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="UserId">Agent</label>
            <RadzenDropDown @bind-Value=@context.UserId Data=@users
                            TextProperty="@nameof(AgentUserVM.Name)" ValueProperty="@nameof(AgentUserVM.Id)"
                            Name="UserId" AllowClear=true Placeholder="Select agent" class="form-input" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            FilterOperator="StringFilterOperator.Contains" AllowFiltering="true" />
            <ValidationMessage For="() => context.UserId" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="DebtSegmentId">Segment</label>
            <RadzenDropDown @bind-Value=@context.DebtSegmentId Data=@segments
                            TextProperty="@nameof(DebtSegmentSelectorVM.Name)" ValueProperty="@nameof(DebtSegmentSelectorVM.Id)"
                            Name="DebtSegmentId" Placeholder="Select debt segment" class="form-input" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                            FilterOperator="StringFilterOperator.Contains" AllowFiltering="true" />
            <ValidationMessage For="() => context.DebtSegmentId" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="RecordsPerTime">No of Accounts Presented Per Time</label>
            <InputNumber @bind-Value="context.RecordsPerTime" />
            <ValidationMessage For="() => context.RecordsPerTime" class="text-danger" />
        </div>
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Is Enabled
                    <InputCheckbox class="check-input" @bind-Value="context.Enabled" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </div>
    ;

    private List<AgentUserVM> users = [];
    private List<DebtSegmentSelectorVM> segments = [];
    protected override void OnInitialized()
    {
        Title = "Debt Workflow";
        SubTitle = "Assign Agents to Debt Segments";
        FormBaseTitle = "Agent - Debt Segment Mapping";
        CreatePermission = DebtWorkflowNavigation.DebtWorkflowAgentMappingConfigCreatePermission;
        EditPermission = DebtWorkflowNavigation.DebtWorkflowAgentMappingConfigEditPermission;
        DeletePermission = DebtWorkflowNavigation.DebtWorkflowAgentMappingConfigDeletePermission;
        QuerySelector = AgentDebtSegmentMappingVM.Mapping;
        var query = Query<ApplicationUser>.Where(x => x.Role == SystemRoleConfig.AdminRole && x.UserRoles.Any(y => y.Role.RoleClaims.Any(z => z.ClaimValue == DebtWorkflowNavigation.DebtWorkflowDeskIndexPermission)));

        if (!config.DeployState.IsDemo)
            query = query.AndWhere(x => x.UserRoles.Any(y => y.Role.Name != SystemRoleConfig.SuperAdminRole));

        users = Service.CrudService.Db.ManySelect(query.Select(x => new AgentUserVM(x.Id, x.FullName)));
        segments = Service.CrudService.Db.ManySelect(Query<DebtSegment>.Where(x => x.RuleIds.Any()).Select(x => new DebtSegmentSelectorVM(x.Id, $"{x.Name}: {x.Priority} Priority - Start: {x.Start}, End: {x.End}")));
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.User.FullName, filterAndPage.TextFilter) || EF.Functions.ILike(x.DebtSegment.Name, filterAndPage.TextFilter);
    }

    protected override ValueTask StartEdit(AgentDebtSegmentMappingVM data, CancellationToken ct) => BaseEdit(() =>
    {
        EditModel = data.Get();
        return Task.CompletedTask;
    }, ct);

    protected override ValueTask<bool> SubmitDelete(AgentDebtSegmentMappingVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}


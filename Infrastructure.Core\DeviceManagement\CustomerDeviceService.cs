﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Logs;
using LendQube.Entities.Core.VersionManagement;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.ViewModels.DeviceManagement;

namespace LendQube.Infrastructure.Core.DeviceManagement;

public sealed class CustomerDeviceService(IUnitofWork uow)
{
    public async Task Save(string userId, CustomerDeviceVM vm, CancellationToken ct)
    {
        if (vm == null)
            return;
        await uow.Db.DeleteAndSaveWithFilterAsync<CustomerDevice>(x => x.UserId == userId && x.DeviceId != vm.DeviceId && x.Type == vm.Type, ct);

        var result = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerDevice>(x => x.UserId == userId && x.DeviceId == vm.DeviceId,
            x => x.SetProperty(y => y.AppVersion, vm.AppVersion)
            .SetProperty(y => y.AppBuild, vm.AppBuild)
            .SetProperty(y => y.PushNotificationId, vm.PushNotificationId)
            .SetProperty(y => y.MobileOsVersion, vm.MobileOsVersion), ct);

        if (result <= 0)
        {
            var data = vm.Get(userId);
            uow.Db.Insert(data);
            await uow.SaveAsync(ct);
        }
    }

    public Task<List<UserPushNotification>> GetAllPushNotificationIds(IEnumerable<string> userIds, CancellationToken ct)
        => uow.Db.ManySelectAsync(Query<CustomerDevice, UserPushNotification>.Where(x => userIds.Contains(x.UserId) && !string.IsNullOrWhiteSpace(x.PushNotificationId))
            .Select(x => new UserPushNotification(x.UserId, x.PushNotificationId)), ct);

    public Task<bool> DoesNotRequireUpdate(MobileVersionVM vm, CancellationToken ct) => uow.Db.ExistsAsync<CustomerDeviceVersion>(x => x.Type == vm.Device && x.CurrentVersion == vm.Version, ct);
}

public record MobileVersionVM([Required] CustomerDeviceType Device, [Required] string Version);

public record UserPushNotification(string UserId, string Token);
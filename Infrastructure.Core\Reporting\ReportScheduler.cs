﻿using Coravel.Invocable;
using Coravel.Pro;
using Coravel.Scheduling.Schedule.Interfaces;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;
using LendQube.Infrastructure.Core.Database.Repository;
using NodaTime;

namespace LendQube.Infrastructure.Core.Reporting;
internal sealed class ReportSchedulerTriggerHandler(BackgroundTaskControlService service, IScheduler scheduler) : IHandleTriggerNotification<ReportSchedule>
{
    private readonly BackgroundEventSource Source = BackgroundEventSource.System;
    private readonly BackgroundTask Key = BackgroundTask.ScheduledReports;

    public Task OnStartup(CancellationToken ct) => ReportScheduler.StartupScheduledReports(service.Uow, ct);

    public async ValueTask OnChanged(string? id, ReportSchedule oldData, ReportSchedule newData, TriggerChange change, CancellationToken ct)
    {
        var stopped = await service.CheckStoppedOrStopping(Source, Key, ct);
        if (stopped)
            return;

        await service.SetStatusToRunning(Source, Key, ct);

        scheduler.CreateBackgroundReport(newData);

        newData.Starting = false;
        service.Uow.Db.Update(newData);
        await service.Uow.SaveAsync(ct);

        await service.SetStatusToIdle(Source, Key, ct);
    }
}

internal static class ReportScheduler
{
    public static Task<int> StartupScheduledReports(IUnitofWork uow, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<ReportSchedule>(x => !x.Disabled, x => x.SetProperty(y => y.Starting, true), ct);

    public static void CreateBackgroundReport(this IScheduler scheduler, ReportSchedule config)
    {
        scheduler = scheduler.OnWorker(nameof(ReportScheduler));

        var schedule = scheduler.ScheduleWithParams<ReportSchedulerSchedulerBackground>(config.Id);

        IScheduledEventConfiguration eventConfig = config.Frequency switch
        {
            ReportFrequency.Daily => schedule.DailyAtHour(config.FrequencyNumber),
            ReportFrequency.Weekly => schedule.Weekly(),
            ReportFrequency.Monthly => schedule.Cron("0 0 1 * *"),
            _ => default
        };

        if (config is { Days: not ScheduleDay.None })
        {
            foreach (var day in config.Days.FlagsToList<ScheduleDay>())
            {
                eventConfig = day switch
                {
                    ScheduleDay.Monday => eventConfig.Monday(),
                    ScheduleDay.Tuesday => eventConfig.Tuesday(),
                    ScheduleDay.Wednesday => eventConfig.Wednesday(),
                    ScheduleDay.Thursday => eventConfig.Thursday(),
                    ScheduleDay.Friday => eventConfig.Friday(),
                    ScheduleDay.Saturday => eventConfig.Saturday(),
                    ScheduleDay.Sunday => eventConfig.Sunday(),
                    _ => eventConfig
                };
            }
        }

        var timeZoneSet = false;

        if (config is { TimeZone: not null or "" })
        {
            var tzTimeZone = DateTimeZoneProviders.Tzdb.GetZoneOrNull(config.TimeZone);
            if (tzTimeZone != null)
            {
                var timeZone = TimeZoneInfo.FindSystemTimeZoneById(tzTimeZone.Id);
                if (timeZone != null)
                {
                    eventConfig = eventConfig.Zoned(timeZone);
                    timeZoneSet = true;
                }
            }
        }

        if (!timeZoneSet)
        {
            eventConfig = eventConfig.Zoned(TimeZoneInfo.Utc);
        }

        eventConfig.PreventOverlapping(config.Id.ToString());
    }
}


public sealed class ReportSchedulerSchedulerBackground(IUnitofWork uow, IClock clock, long configId) : IInvocable, IDoNotAutoRegister
{
    public async Task Invoke()
    {
        var config = await uow.Db.OneAsync(Query<ReportSchedule>.Where(x => x.Id == configId && !x.Disabled), default);
        if (config is null)
            return;

        var report = new SystemReport
        {
            TypeName = config.ReportTypeName,
            Names = config.ReportNames,
            Description = config.Description,
            Status = ReportStatus.Queued,
            StartDate = config.StartDate,
            EndDate = config.EndDate,
            AutoRun = true,
            FileType = config.FileType,
            ReportScheduleId = configId,
        };

        uow.Db.Insert(report);
        var now = clock.GetCurrentInstant();

        Instant? newStartDate = config.StartDate.HasValue ? config.EndDate.HasValue ? config.EndDate : now : null;

        Instant? newEndDate = config.EndDate.HasValue ? newStartDate.HasValue ? newStartDate + (config.EndDate - config.StartDate) : now + (now - config.EndDate) : null;

        config.StartDate = newStartDate;
        config.EndDate = newEndDate;
        config.RunCount++;
        config.LastRunDate = now;

        config.Starting = false;
        uow.Db.Update(config);
        await uow.SaveAsync(default);
    }
}
@page "/"
@page "/login"
@using LendQube.Infrastructure.Core.Database.DbContexts
@using LendQube.Entities.Collection.Customers
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@inject AppDbContext DbContext
@inject IHttpContextAccessor HttpContextAccessor
@inject ILogger<Login> Logger
@inject NavigationManager NavigationManager
@rendermode InteractiveServer

<PageTitle>Customer Login - LendQube</PageTitle>

<div class="login-container">
    <div class="login-card">
        <div class="login-header">
            <img src="@Assets["images/logo/logo.svg"]" alt="Silicon" class="logo" />
            <h1>Customer Portal</h1>
            <p>Enter your Customer ID to access your account</p>
        </div>

        <EditForm Model="@loginModel" OnValidSubmit="@HandleLogin" FormName="CustomerLoginForm">
            <DataAnnotationsValidator />
            
            <div class="form-group">
                <label for="customerId">Customer ID</label>
                <InputText @bind-value="loginModel.CustomerId"
                          class="form-control" 
                          id="customerId" 
                          placeholder="Enter your Customer ID" 
                          disabled="@isLoading" />
                <ValidationMessage For="@(() => loginModel.CustomerId)" />
            </div>

            @if (!string.IsNullOrEmpty(errorMessage))
            {
                <div class="alert alert-danger">
                    @errorMessage
                </div>
            }

            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                @if (isLoading)
                {
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    <span>Authenticating...</span>
                }
                else
                {
                    <span>Login</span>
                }
            </button>
        </EditForm>
    </div>
</div>

@code {
    [SupplyParameterFromForm]
    public LoginModel loginModel { get; set; } = new();

    private bool isLoading = false;
    private string errorMessage = string.Empty;
    private bool loginSuccessful = false;
    private string redirectUrl = string.Empty;

    public class LoginModel
    {
        [Required(ErrorMessage = "Customer ID is required")]
        public string CustomerId { get; set; } = string.Empty;
    }

    private async Task HandleLogin()
    {
        try
        {
            isLoading = true;
            errorMessage = string.Empty;
            StateHasChanged();

            Logger.LogInformation("Starting authentication for Customer ID: {CustomerId}", loginModel.CustomerId?.Trim());

            // Query database for customer - This resolves Blocker #3: Customer Profile Query Complexity
            var customer = await DbContext.Set<CustomerProfile>()
                .Where(c => c.AccountId == loginModel.CustomerId!.Trim())
                .FirstOrDefaultAsync();

            Logger.LogInformation("Database query completed. Customer found: {CustomerFound}", customer != null);

            if (customer != null)
            {
                Logger.LogInformation("Customer found: {CustomerId}, redirecting to dashboard", customer.AccountId);

                // Set success state and navigate directly
                loginSuccessful = true;
                redirectUrl = $"/dashboard?customerId={customer.AccountId}";

                Logger.LogInformation("Navigating to dashboard for customer: {CustomerId}", customer.AccountId);

                // Navigate without force reload to prevent double refresh
                NavigationManager.NavigateTo(redirectUrl, replace: true);
                return;
            }
            else
            {
                errorMessage = "Invalid Customer ID. Please check your credentials and try again.";
                Logger.LogWarning("Authentication failed for Customer ID: {CustomerId}", loginModel.CustomerId?.Trim());
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Login error: {ex.Message}";
            Logger.LogError(ex, "Error during login process for Customer ID: {CustomerId}", loginModel.CustomerId?.Trim());
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }
}

﻿using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.AdminUserManagement;

public sealed class AdminRoleClaimsManagerService(GenericEntityCrudVMService<ApplicationClaims, RolePermissionsVM> crudService) : BaseSpecification<ApplicationClaims>
{
    public async Task<GenericResponseVM> SaveClaims(string currentUser,  ApplicationRole role, IEnumerable<RolePermissionsVM> permissionsToAdd, IEnumerable<RolePermissionsVM> permissionsToRemove, CancellationToken ct)
    {
        if (role.Name == SystemRoleConfig.CustomerRole)
        {
            return new GenericResponseVM { Message = "Customer role cannot have any permissions assigned" };
        }

        if (role.Name == SystemRoleConfig.SuperAdminRole)
        {
            return new GenericResponseVM { Message = "Super admin role permissions cannot be modified" };
        }

        if (await crudService.Db.ExistsAsync<ApplicationUserRole>(x => x.User.UserName == currentUser && x.RoleId == role.Id, ct))
        {
            return new GenericResponseVM { Message = "You cannot assign a claim to a role you belong to. Please contact admin" };
        }

        if (!permissionsToAdd.IsNullOrEmpty())
        {
            crudService.Db.InsertBulk(permissionsToAdd.Select(x => new ApplicationRoleClaim { Source = x.Source, RoleId = role.Id, ClaimType = "Permission", ClaimValue = x.Permission }), ct);
            await crudService.Uow.SaveAsync(ct);
        }

        if(!permissionsToRemove.IsNullOrEmpty())
        {
            var permissionsToRemoveFormatted = permissionsToRemove.Select(x => $"{x.Source}{x.Permission}").Distinct().ToList();
            await crudService.Db.DeleteAndSaveWithFilterAsync<ApplicationRoleClaim>(x => x.RoleId == role.Id && permissionsToRemoveFormatted.Contains(x.Source + x.ClaimValue), default);
        }

        return new GenericResponseVM {  Successful = true };
    }

    public ColumnList GetTableDefinition() => crudService.GetTableDefinition(new() { HasDateColumns = false });

    public ValueTask<TypedBasePageList<RolePermissionsVM>> GetTypeBasedPagedData(ApplicationRole role, DataFilterAndPage pagedParam, CancellationToken ct)
    {
        if (!string.IsNullOrEmpty(pagedParam.TextFilter))
        {
            pagedParam.TextFilter = $"%{pagedParam.TextFilter}%";
            PrimaryCriteria = x => EF.Functions.ILike(x.Value, pagedParam.TextFilter)
            || EF.Functions.ILike(x.Description, pagedParam.TextFilter) || EF.Functions.ILike(x.Source, pagedParam.TextFilter);
        }

        TagOrigination();

        var assignedClaims = crudService.Db.Queryable<ApplicationRoleClaim>().Where(x => x.RoleId == role.Id).Select(x => new { x.Source, x.ClaimValue });

        pagedParam.OrderByColumnVM ??= nameof(RolePermissionsVM.Assigned);
        return crudService.GetTypeBasedPagedData(this, pagedParam, x => new RolePermissionsVM { Assigned = assignedClaims.Any(y => y.ClaimValue == x.Value && y.Source == x.Source), Source = x.Source, Description = x.Description, Permission = x.Value }, ct: ct);
    }
}

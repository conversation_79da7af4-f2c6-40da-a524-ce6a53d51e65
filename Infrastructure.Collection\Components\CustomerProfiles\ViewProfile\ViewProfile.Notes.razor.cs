﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Setup;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<CustomerNoteVM> notesTable;
    private ColumnList customerNotesTableDefinition;
    private string AddNoteModal => "AddNoteModal";
    private string EditNoteModal => "EditNoteModal";

    private CustomerNote AddNoteModel { get; set; } = new();
    private CustomerNote EditNoteModel { get; set; } = new();

    private List<CustomerDropDownPlacementVM> placementsForNote = [];

    private void SetupNotesConfig()
    {
        customerNotesTableDefinition = CrudService.GetTableDefinition<CustomerNote, CustomerNoteVM>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteNotePermission),
            HasEdit = HasClaim(ManageCustomersNavigation.CustomerProfileViewEditNotePermission)
        });

        customerNotesTableDefinition.TopActionButtons.Add(new TopActionButton("Add Note", ModalName: AddNoteModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddNotePermission)));

        customerNotesTableDefinition.RowActionButtons.Add(new RowActionButton("Files", Icon: "eye", Action: async (object row) =>
        {
            notesTable.Loading = true;
            AddNoteModel = (row as CustomerNoteVM).Get(Data.Id);
            await JSRuntime.OpenModal(AddNoteModal, Cancel);
            notesTable.Loading = false;
        }, ShowCondition: row => !(row as CustomerNoteVM).Files.IsNullOrEmpty()));

        notesTable.SetTableDefinition(customerNotesTableDefinition);

        placementsForNote = placementsDropDown;
        placementsForNote.Insert(0, new() { Id = null, Info = "All" });
    }

    private ValueTask<TypedBasePageList<CustomerNoteVM>> LoadNotes(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerNote>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Note, filterAndPage.TextFilter));
        }

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerNoteVM.Mapping, ct: ct);
    }

    private async Task LoadNoteTemplates(CustomerNote data)
    {
        var templates = await uow.Db.ManyAsync(Query<CustomerNoteTemplate>.Where(x => x.Type == data.Type && x.ContactType == data.ContactType), Cancel);
        if (templates.IsNullOrEmpty())
            return;

        data.Templates = templates;
    }

    private ValueTask SubmitNewNote() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddNotePermission, AddNoteModal, async () =>
    {
        AddNoteModel.ProfileId = Data.Id;
        uow.Db.Insert(AddNoteModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, () =>
    {
        AddNoteModel = new();
        StateHasChanged();
        return notesTable.Refresh();
    });

    private ValueTask StartEditNote(CustomerNoteVM data, CancellationToken ct) => BaseEdit(ManageCustomersNavigation.CustomerProfileViewEditNotePermission, EditNoteModal, () =>
    {
        EditNoteModel = data.Get(Data.Id);
        return Task.CompletedTask;
    }, ct);

    private ValueTask SubmitEditNote() => BaseSaveEdit(null, EditNoteModal, async () =>
    {
        uow.Db.Update(EditNoteModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, notesTable.Refresh);

    private ValueTask<bool> DeleteNote(CustomerNoteVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteNotePermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerNote>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

}

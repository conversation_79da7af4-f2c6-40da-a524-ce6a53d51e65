﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using NodaTime;

namespace LendQube.Entities.Collection.Customers;

public class CustomerOneTimeCode : BaseEntityWithIdentityId<CustomerOneTimeCode>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public long MessageLogId { get; set; }
    public string CodeType { get; set; }
    public string Code { get; set; }
    public int SendCount { get; set; }
    public int SendLimit { get; set; }
    public int TriesCount { get; set; }
    public bool IsValid { get; set; }
    public Instant ExpireAt { get; set; }
}


﻿using LendQube.Infrastructure.Core.DateAndTime;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Rendering;
using NodaTime;

namespace LendQube.Infrastructure.Core.Components.Helpers;

public sealed class LocalTime : ComponentBase, IDisposable
{
    [Inject]
    public UserTimeProvider TimeProvider { get; set; }

    [Parameter, EditorRequired]
    public object Value { get; set; }

    [Parameter]
    public string Name { get; set; }

    [Parameter]
    public string Format { get; set; } = InstantExtensions.LONG_DATE_TIME_FORMAT;

    private Type type;

    protected override void OnParametersSet()
    {
        type = Value?.GetType();
        if (Value != null && !type.IsSupportedDateType())
            throw new InvalidOperationException($"{type.Name} is not supported by this component");
    }

    protected override void OnInitialized()
    {
        TimeProvider.LocalTimeZoneChanged += LocalTimeZoneChanged;
    }

    protected override void BuildRenderTree(RenderTreeBuilder builder)
    {
        if (Value is null)
            return;

        Instant? convertedDate = type.IsInstant() ? (Instant)Value : null;

        if (type.IsInstant())
        {
            if (!string.IsNullOrEmpty(Name) && Name.EndsWith("Time"))
                Format = InstantExtensions.TIME_ONLY_FORMAT;
        }
        else if (type.IsDateTime())
        {
            var transDate = (DateTime)Value;
            convertedDate = Instant.FromDateTimeUtc(new DateTime(DateOnly.FromDateTime(transDate), TimeOnly.FromDateTime(transDate), DateTimeKind.Utc));
        }
        else if (type.IsDateTimeOffset())
        {
            convertedDate = Instant.FromDateTimeOffset((DateTimeOffset)Value);
        }
        else if (type.IsDateOnly())
        {
            var dateOnly = (DateOnly)Value;
            convertedDate = Instant.FromUtc(dateOnly.Year, dateOnly.Month, dateOnly.Day, 0, 0);
            Format = InstantExtensions.MID_DATE_FORMAT;
        }
        else if (type.IsLocalTimeOnly())
        {
            var timeOnly = (NodaTime.LocalTime)Value;
            var now = DateTime.UtcNow;
            LocalDateTime localDateTime = new(now.Year, now.Month, now.Day, timeOnly.Hour, timeOnly.Minute, timeOnly.Second);

            convertedDate = (TimeProvider.LocalTimeZone ?? DateTimeZone.Utc).AtLeniently(localDateTime).ToInstant();
            Format = InstantExtensions.TIME_ONLY_FORMAT;
        }
        else if (type.IsTimeOnly())
        {
            var timeOnly = TimeOnly.Parse(Value.ToString());
            var now = DateTime.UtcNow;
            var time = Instant.FromUtc(now.Year, now.Month, now.Day, timeOnly.Hour, timeOnly.Minute);
            Format = InstantExtensions.TIME_ONLY_FORMAT;
            builder.AddContent(0, time.GetStringFromInstant(Format));
        }
        else if (type.IsLocalDate())
        {
            var localDate = (LocalDate)Value;
            convertedDate = localDate.AtStartOfDayInZone(TimeProvider.LocalTimeZone ?? DateTimeZone.Utc).ToInstant();
            Format = InstantExtensions.MID_DATE_FORMAT;
        }
        if (convertedDate.HasValue)
        {
            var dateTime = convertedDate.Value.InZone(TimeProvider.LocalTimeZone ?? DateTimeZone.Utc);
            builder.AddContent(0, dateTime.GetStringFromInstant(Format));
        }
    }

    public void Dispose()
    {
        TimeProvider.LocalTimeZoneChanged -= LocalTimeZoneChanged;
    }

    private void LocalTimeZoneChanged(object sender, EventArgs e)
    {
        _ = InvokeAsync(StateHasChanged);
    }
}



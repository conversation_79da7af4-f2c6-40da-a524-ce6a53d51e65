﻿@page "/account/login"

@using System.ComponentModel.DataAnnotations
@using LendQube.Infrastructure.Core.AppSettings
@using LendQube.Infrastructure.Core.PermissionsAndRoles

@inject IdentityRedirectManager redirectManager
@inject AdminAuthService authService
@inject DefaultAppConfig config
@inherits AsyncComponentBase

<PageTitle>Log In</PageTitle>

<div id="wrapper">
    <div id="footer-control" class="index-wrapper">
        <div class="login-wrapper mx-auto">
            <a href="#" class="brand-img mx-auto">
                <img src="@Assets["images/logo/logo.svg"]" alt="Silicon">
            </a>
            <div class="form-wrapper">
                <StatusMessage @ref="@errorMessage" />
                <EditForm class="form form__login" method="post" Model="Input" OnValidSubmit="LoginUser" FormName="login">
                    <DataAnnotationsValidator />
                    <div class="form-row">
                        <label class="form-label" for="Username">Email</label>
                        <InputText id="username" @bind-Value="Input.Username" class="form-input" autocomplete="username" aria-required="true" placeholder="Username" />
                        <ValidationMessage For="() => Input.Username" class="text-danger" />
                    </div>
                    <div class="form-row">
                        <label for="password" class="form-label">Password</label>
                        <InputText id="password" type="password" @bind-Value="Input.Password" class="form-input" autocomplete="current-password" aria-required="true" placeholder="Password" />
                        <ValidationMessage For="() => Input.Password" class="text-danger" />
                    </div>
                    <div class="form-row">
                        <div class="check-group">
                            <label class="check-label">
                                Remember Me
                                <InputCheckbox class="check-input" @bind-Value="Input.RememberMe" />
                                <span class="checkmark"></span>
                            </label>
                        </div>
                    </div>

                    @if (config.DeployState.IsDemo)
                    {
                        <div class="form-row">
                            <div class="check-group">
                                <label class="check-label">
                                    Skip 2FA
                                    <InputCheckbox class="check-input" @bind-Value="Input.Skip2fa" />
                                    <span class="checkmark"></span>
                                </label>
                            </div>
                        </div>
                    }
                    <div class="button-row">
                        <button id="login" type="submit" class="btn btn--primary __full">Log In</button>
                    </div>
                    <div class="form-footer flex __justify-between">
                        <p class="text_medium">Having trouble signing in?</p>
                        <a href="#">Contact Admin</a>
                    </div>
                    <p class="text-muted text-center"><small>Ethica Resolve &copy; @DateTime.Now.Year</small></p>
                </EditForm>
            </div>
        </div>
    </div>
</div>


@code {
    private StatusMessage errorMessage;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    [SupplyParameterFromForm]
    private AdminLoginVM Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (HttpMethods.IsGet(HttpContext?.Request.Method))
        {
            if (authService.IsSignedIn(HttpContext))
            {
                redirectManager.RedirectTo(ReturnUrl);
            }
            else
            {
                await authService.Logout();
                Input.Skip2fa = config.DeployState.IsDemo;
            }
        }
    }

    public async Task LoginUser()
    {
        var state = await authService.LoginNew(Input, HttpContext.GetIpAddress());

        if (state == UserLoginState.Succeeded)
        {
            redirectManager.RedirectTo(ReturnUrl);
        }
        else if (state == UserLoginState.ChangePasswordRequired)
        {
            redirectManager.RedirectTo("account/manage/changepassword");
        }

        else if (state == UserLoginState.TwoFactorSignInRequired)
        {
            redirectManager.RedirectToAuthWithMFA(HttpContext, "account/login/2fa",
                new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }

        else if (state == UserLoginState.TwoFactorSetupRequired)
        {
            redirectManager.RedirectToAuthWithMFA(HttpContext, "account/login/enable2fa",
                new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }

        else if (state == UserLoginState.LockedOut)
        {
            errorMessage.Error("This account has been locked out. Please contact admin");
        }

        else
        {
            errorMessage.Error("Invalid login attempt.");
        }
    }
}

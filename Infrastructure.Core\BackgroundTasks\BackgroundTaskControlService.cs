﻿using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.Repository;
using NodaTime;

namespace LendQube.Infrastructure.Core.BackgroundTasks;

public sealed class BackgroundTaskControlService(IUnitofWork uow, IClock clock)
{
    public IUnitofWork Uow => uow;

    public async Task ProcessOldRecords(BackgroundEventSource source, BackgroundTask key, CancellationToken ct)
    {
        await uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventLog>(x => x.Source == source && x.Event == key && x.Status == BackgroundEventStatus.Queued, x => x.SetProperty(y => y.Status, BackgroundEventStatus.Queued), ct);
    }

    public async Task ProcessOldRecords(BackgroundTask key, CancellationToken ct)
    {
        await uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventLog>(x => x.Event == key && x.Status == BackgroundEventStatus.Queued, x => x.SetProperty(y => y.Status, BackgroundEventStatus.Queued), ct);
    }

    public void SetGeneralRunnerFinishState(bool done, BackgroundTaskEventLog runner, int triesLimit = 3)
    {
        if (done)
            runner.Status = BackgroundEventStatus.Success;
        else if (runner.Status != BackgroundEventStatus.Failed)
            runner.Status = runner.Tries >= triesLimit ? BackgroundEventStatus.Failed : BackgroundEventStatus.Queued;

        runner.TimeCompleted = (done || runner.Tries >= triesLimit) ? clock.GetCurrentInstant() : null;
    }

    public async Task CreateOrStart(BackgroundEventSource source, BackgroundTask key, CancellationToken ct)
    {
        var exists = await uow.Db.ExistsAsync<BackgroundTaskEventControl>(x => x.Source == source && x.Event == key, ct);
        if (!exists)
        {
            var task = new BackgroundTaskEventControl
            {
                Source = source,
                Event = key,
                Status = BackgroundControlState.Idle
            };

            uow.Db.Insert(task);
            await uow.SaveAsync(ct);
        }
        else
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventControl>(x => x.Source == source && x.Event == key &&
            (x.Status == BackgroundControlState.Stopped || x.Status == BackgroundControlState.Stopping), x => x.SetProperty(y => y.Status, BackgroundControlState.Idle), ct);
        }
    }

    public async Task<(BackgroundTaskEventControl, bool)> GetControlAndStopStatus(BackgroundEventSource source, BackgroundTask key, CancellationToken ct)
    {
        var control = await uow.Db.OneAsync(Query<BackgroundTaskEventControl>.Where(x => x.Source == source && x.Event == key), ct);
        return (control, control.Status == BackgroundControlState.Stopped || control.Status == BackgroundControlState.Stopping);
    }

    public async Task<bool> CheckStoppedOrStopping(BackgroundEventSource source, BackgroundTask key, CancellationToken ct)
    {
        var running = await uow.Db.ExistsAsync<BackgroundTaskEventControl>(x => x.Source == source && x.Event == key && x.Status != BackgroundControlState.Stopped && x.Status != BackgroundControlState.Stopping, ct);
        if (running)
            return false;

        _ = await SetStatusToStopped(source, key, ct);
        return true;
    }

    public Task<int> SetStatusToStopped(BackgroundEventSource source, BackgroundTask key, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventControl>(x => x.Source == source && x.Event == key, x => x.SetProperty(y => y.Status, BackgroundControlState.Stopped), ct);

    public Task SetStatusToStopped(BackgroundTaskEventControl data, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventControl>(x => x.Id == data.Id, x => x.SetProperty(y => y.Status, BackgroundControlState.Stopped), ct);

    public Task<int> SetStatusToRunning(BackgroundEventSource source, BackgroundTask key, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventControl>(x => x.Source == source && x.Event == key && x.Status == BackgroundControlState.Idle, x => x.SetProperty(y => y.Status, BackgroundControlState.Running), ct);

    public Task<int> SetStatusToIdle(BackgroundEventSource source, BackgroundTask key, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventControl>(x => x.Source == source && x.Event == key && x.Status == BackgroundControlState.Running, x => x.SetProperty(y => y.Status, BackgroundControlState.Idle), ct);

    public Task MarkForStopping(BackgroundTaskEventControl data, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<BackgroundTaskEventControl>(x => x.Id == data.Id && x.Status != BackgroundControlState.Stopped, x => x.SetProperty(y => y.Status, BackgroundControlState.Stopping), ct);

}
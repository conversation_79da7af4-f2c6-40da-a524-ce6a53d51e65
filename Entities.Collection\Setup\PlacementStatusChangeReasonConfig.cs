﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Setup;

public class PlacementStatusChangeReasonConfig : BaseEntityWithIdentityId<PlacementStatusChangeReasonConfig>
{
    [Required]
    public PlacementStatus Status { get; set; }
    [Required, MaxLength(4)]
    public string Code { get; set; }
    public string Description { get; set; }

    public override void Configure(EntityTypeBuilder<PlacementStatusChangeReasonConfig> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Code).IsUnique();
    }
}

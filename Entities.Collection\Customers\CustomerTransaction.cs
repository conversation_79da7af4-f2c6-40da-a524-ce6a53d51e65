﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Customers;

public class CustomerTransaction : BaseEntityWithIdentityId<CustomerTransaction>
{
    [DbGuid, Required, RemoveColumn]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    [StringLength(EntityConstants.DEFAULT_ID_FIELD_LENGTH)]
    public string TransactionId { get; set; }
    public string PaymentMethod { get; set; }
    public PaymentProvider PaymentProvider { get; set; }
    public decimal AmountTried { get; set; }
    public decimal AmountPaid { get; set; }
    public bool Successful { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public List<string> SchedulePeriodAffected { get; set; }
    public bool PaymentApplied { get; set; }
    public string PaymentType { get; set; }
    public string PeriodsAppliedTo => SchedulePeriodAffected != null && SchedulePeriodAffected.Count > 0 ? string.Join(',', SchedulePeriodAffected) : "";

    public override void Configure(EntityTypeBuilder<CustomerTransaction> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.TransactionId);
    }
}


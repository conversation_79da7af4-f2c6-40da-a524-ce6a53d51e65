﻿using LendQube.Infrastructure.Core.DeviceManagement;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using LendQube.Infrastructure.Core.ViewModels.DeviceManagement;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;

namespace LendQube.Web.Api.Controllers.AccessMangement;

[Route(ApiConstants.URL), EnableRateLimiting(ApiConstants.ConcurrentPolicy)]
public class DeviceManagementController(CustomerDeviceService deviceService) : ApiAuthControllerBase
{

    [HttpPost(nameof(RegisterDevice))]
    public async Task<IResult> RegisterDevice([FromBody] CustomerDeviceVM vm, CancellationToken ct)
    {
        await deviceService.Save(User.Identity.Name, vm, ct);
        return Results.Ok();
    }


    [HttpPost(nameof(CheckUpdateRequired)), AllowAnonymous]
    public async Task<bool> CheckUpdateRequired([FromBody] MobileVersionVM vm, CancellationToken ct) => !(await deviceService.DoesNotRequireUpdate(vm, ct));
}

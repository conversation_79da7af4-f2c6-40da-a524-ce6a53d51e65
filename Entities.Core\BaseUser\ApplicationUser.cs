﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;

namespace LendQube.Entities.Core.BaseUser;

public class ApplicationUser : IdentityUser<Guid>, IEntityTypeConfiguration<ApplicationUser>, IBaseEntityForRelationalDb, IEntityDoesNotUseHiLoIdentity
{
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string OtherNames { get; set; }
    [DbComputed($@"""{nameof(FirstName)}""  || ' ' || ""{nameof(LastName)}""")]
    public string FullName { get; set; }
    public Instant? LastLoginDate { get; set; }
    public Instant? RegistrationDate { get; set; }
    public Instant? MustChangePasswordOn { get; set; }
    [MaxLength(EntityConstants.DEFAULT_WALLET_PREFIX_LENGTH)]
    public string Role { get; set; }
    [JsonIgnore]
    public string WalletPinHash { get; set; }
    [JsonIgnore]
    public bool HasWalletPin => !string.IsNullOrEmpty(WalletPinHash);
    public string PhoneCode { get; set; }
    public virtual ICollection<ApplicationUserClaim> Claims { get; set; }
    public virtual ICollection<ApplicationUserLogin> Logins { get; set; }
    public virtual ICollection<ApplicationUserToken> Tokens { get; set; }
    public virtual ICollection<ApplicationUserRole> UserRoles { get; set; }

    public void Configure(EntityTypeBuilder<ApplicationUser> builder)
    {
        builder.Property(x => x.UserName)
            .HasConversion<Guid>();

        builder.HasIndex(e => e.UserName)
            .IsUnique()
            .IncludeProperties(e => new { e.SecurityStamp, e.TwoFactorEnabled, e.PasswordHash, e.WalletPinHash, e.MustChangePasswordOn, e.Role });

        builder.HasMany(e => e.Claims)
                .WithOne(e => e.User)
                .HasForeignKey(uc => uc.UserId)
                .IsRequired();

        // Each User can have many UserLogins
        builder.HasMany(e => e.Logins)
            .WithOne(e => e.User)
            .HasForeignKey(ul => ul.UserId)
            .IsRequired();

        // Each User can have many UserTokens
        builder.HasMany(e => e.Tokens)
            .WithOne(e => e.User)
            .HasForeignKey(ut => ut.UserId)
            .IsRequired();

        // Each User can have many entries in the UserRole join table
        builder.HasMany(e => e.UserRoles)
            .WithOne(e => e.User)
            .HasForeignKey(ur => ur.UserId)
            .IsRequired();
    }
}

public class ApplicationRole : IdentityRole<Guid>, IEntityTypeConfiguration<ApplicationRole>, IBaseEntityForRelationalDb, IEntityDoesNotUseHiLoIdentity
{
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Description { get; set; }
    public virtual ICollection<ApplicationUserRole> UserRoles { get; set; }
    public virtual ICollection<ApplicationRoleClaim> RoleClaims { get; set; }

    public void Configure(EntityTypeBuilder<ApplicationRole> builder)
    {
        // Each Role can have many entries in the UserRole join table
        builder.HasMany(e => e.UserRoles)
            .WithOne(e => e.Role)
            .HasForeignKey(ur => ur.RoleId)
            .IsRequired();

        // Each Role can have many associated RoleClaims
        builder.HasMany(e => e.RoleClaims)
            .WithOne(e => e.Role)
            .HasForeignKey(rc => rc.RoleId)
            .IsRequired();
    }
}

public class ApplicationUserRole : IdentityUserRole<Guid>, IBaseEntityForRelationalDb, IEntityDoesNotUseHiLoIdentity
{
    public virtual ApplicationUser User { get; set; }
    public virtual ApplicationRole Role { get; set; }
}

public class ApplicationUserClaim : IdentityUserClaim<Guid>, IEntityTypeConfiguration<ApplicationUserClaim>
{
    public virtual ApplicationUser User { get; set; }
    public void Configure(EntityTypeBuilder<ApplicationUserClaim> builder)
    {
        builder.Property(x => x.Id).UseIdentityAlwaysColumn();
    }
}

public class ApplicationUserLogin : IdentityUserLogin<Guid>
{
    public virtual ApplicationUser User { get; set; }
}

public class ApplicationRoleClaim : IdentityRoleClaim<Guid>, IBaseEntityForRelationalDb, IEntityDoesNotUseHiLoIdentity, IEntityTypeConfiguration<ApplicationRoleClaim>
{
    public string Source { get; set; }
    public virtual ApplicationRole Role { get; set; }
    public void Configure(EntityTypeBuilder<ApplicationRoleClaim> builder)
    {
        builder.Property(x => x.Id).UseIdentityAlwaysColumn();
    }
}

public class ApplicationUserToken : IdentityUserToken<Guid>
{
    public virtual ApplicationUser User { get; set; }
}

[Table("AspNetClaims")]
public class ApplicationClaims : BaseEntityWithIdentityId<ApplicationClaims>
{
    [Required, MaxLength(60)]
    public string Source { get; set; }
    [Required, MaxLength(10), ValidString(ValidStringRule.OnlyText)]
    public string Type { get; set; } = "Permission";
    [Required, ValidString(ValidStringRule.OnlyTextOrNumberOrSpecialCharacters)]
    public string Value { get; set; }
    [Required, ValidString(ValidStringRule.NoScriptTag)]
    public string Description { get; set; }
}
﻿using System.Net;
using System.Security.Claims;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Logs;
using LendQube.Infrastructure.Collection.Analytics;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using NodaTime;
using OpenIddict.Abstractions;
using OpenIddict.Server.AspNetCore;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace LendQube.Infrastructure.Collection.Authentication;

public sealed class CustomerAuthService(SignInManager<ApplicationUser> signInManager, UserAccessService accessService, IUnitofWork uow, DefaultAppConfig config,
    IOpenIddictScopeManager scopeManager, CustomerOneTimeCodeManager codeManager, IClock clock, ILogManager<CustomerAuthService> logger)
{
    internal async Task<UserLoginResponseVM> Login(UserLoginVM vm, string ip, CancellationToken ct)
    {
        var response = new UserLoginResponseVM
        {
            IsSuccessful = false
        };

        var user = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerProfileResponse>.Where(x => x.Email == vm.Username).Select(CustomerProfileResponse.Mapping), ct);
        var canSignIn = CheckUserCanSignIn(user, response);
        if (canSignIn != null)
            return canSignIn;

        var result = await signInManager.PasswordSignInAsync(user.Id, vm.Password, false, true);

        if (result.Succeeded)
        {
            response.IsSuccessful = true;
            response.AppUser = user;

            _ = await uow.Db.UpdateAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == user.Id, x => x.SetProperty(y => y.LastLoginDate, clock.GetCurrentInstant()), ct);
            accessService.RecordAccessIP(user.Id, ip, GrantType.Password);
        }
        else
        {

            if (result.IsLockedOut)
            {
                response.Response = new OpenIddictResponse
                {
                    Error = Errors.AccessDenied,
                    ErrorDescription = "You have been suspended because you tried a wrong credential too many times. Please wait for 5 minutes and try again."
                };


            }
            else if (result.IsNotAllowed)
            {
                response.Response = new OpenIddictResponse
                {
                    Error = Errors.AccessDenied,
                    ErrorDescription = "The specified user is not allowed to sign in"
                };
            }
            else
            {
                response.Response = new OpenIddictResponse
                {
                    Error = Errors.AccessDenied,
                    ErrorDescription = "Please check that your email and password is correct"
                };
            }
        }

        return response;
    }

    internal async Task<UserLoginResponseVM> Authenticate(HttpContext context, OpenIddictRequest authData, CancellationToken ct)
    {
        UserLoginResponseVM result = new()
        {
            IsSuccessful = false,
        };

        try
        {
            var ip = context.GetIpAddress();
            if (authData.IsPasswordGrantType())
            {
                result = await Login(result, authData, ip, ct);
            }
            else if (authData.IsRefreshTokenGrantType())
            {
                result = await RefreshToken(result, context, ip, ct);
            }
            else
            {
                authData.Username = authData.Username?.Trim();
                var email = authData.Username;

                _ = await uow.Db.UpdateAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == authData.Username && x.Role == SystemRoleConfig.CustomerRole,
                    x => x.SetProperty(y => y.LockoutEnd, new DateTimeOffset(DateTime.UtcNow, TimeSpan.FromDays(2))),
                    ct);

                result = new UserLoginResponseVM
                {
                    IsSuccessful = false,
                    Response = new OpenIddictResponse
                    {
                        Error = Errors.UnsupportedGrantType,
                        ErrorDescription = "The specified login is not supported. Your account has been locked."
                    }
                };
            }
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.CustomerWeb, EventAction.ExceptionOrError, ex, "Token authentication failed");
        }

        return result;
    }

    internal async Task<ClaimsPrincipal> CreateTicketAsync(OpenIddictRequest request, string userId, CancellationToken ct)
    {
        ClaimsIdentity identity = new(TokenValidationParameters.DefaultAuthenticationType, Claims.Name, Claims.Role);

        identity.SetClaim(Claims.Subject, request.Username);
        identity.SetClaim(Claims.Name, userId);

        identity.SetScopes(request.GetScopes());
        identity.SetResources(await scopeManager.ListResourcesAsync(identity.GetScopes(), ct).ToListAsync(ct));

        identity.SetDestinations(static claim => claim.Type switch
        {
            // Never add the "secret_value" claim to access or identity tokens.
            // In this case, it will only be added to authorization codes,
            // refresh tokens and user/device codes, that are always encrypted.
            "secret_value" or "AspNet.Identity.SecurityStamp" => [],

            // Otherwise, add the claim to the access tokens only.
            _ => [Destinations.AccessToken]
        });

        return new(identity);
    }

    private static UserLoginResponseVM CheckUserCanSignIn(CustomerProfileResponse user, UserLoginResponseVM response)
    {
        if (user == null)
        {
            response.Response = new OpenIddictResponse
            {
                Error = Errors.AccessDenied,
                ErrorDescription = "Login code is incorrect"
            };
            return response;
        }

        if (user.Blacklisted)
        {
            response.Response = new OpenIddictResponse
            {
                Error = Errors.AccessDenied,
                ErrorDescription = "Your account is disabled, please contact <NAME_EMAIL>"
            };
            return response;
        }


        return null;
    }

    private async Task<UserLoginResponseVM> Login(UserLoginResponseVM response, OpenIddictRequest request, string ip, CancellationToken ct)
    {
        var email = request.Username?.Trim();

        var user = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerProfileResponse>.Where(x => x.Email == email).Select(CustomerProfileResponse.Mapping), ct);

        var canSignIn = CheckUserCanSignIn(user, response);
        if (canSignIn != null)
            return canSignIn;

        var result = await signInManager.PasswordSignInAsync(user.Id, request.Password, false, true);

        if (result.Succeeded)
        {
            response.IsSuccessful = true;
            response.AppUser = user;
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == user.Id, x => x.SetProperty(y => y.LastLoginDate, clock.GetCurrentInstant()), ct);
            accessService.RecordAccessIP(user.Id, ip, GrantType.Password);
        }
        else
        {
            accessService.RecordFailedAccessIP(user.Id, ip, GrantType.Password);
            if (result.IsLockedOut)
            {
                response.Response = new OpenIddictResponse
                {
                    Error = Errors.AccessDenied,
                    ErrorDescription = "You have been suspended because you tried a wrong credential too many times. Please wait for 5 minutes and try again."
                };


            }
            else if (result.IsNotAllowed)
            {
                response.Response = new OpenIddictResponse
                {
                    Error = Errors.AccessDenied,
                    ErrorDescription = "The specified user is not allowed to sign in"
                };
            }
            else
            {
                response.Response = new OpenIddictResponse
                {
                    Error = Errors.AccessDenied,
                    ErrorDescription = "Please login again"
                };
            }
        }

        return response;
    }

    private async Task<UserLoginResponseVM> RefreshToken(UserLoginResponseVM response, HttpContext context, string ip, CancellationToken ct)
    {
        var info = await context.AuthenticateAsync(OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
        if(info == null || !info.Succeeded)
            return response;

        var user = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerProfileResponse>.Where(x => x.Email == info.Principal.Identity.Name).Select(CustomerProfileResponse.Mapping), ct);
        var canSignIn = CheckUserCanSignIn(user, response);
        if (canSignIn != null)
            return canSignIn;

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == user.Id, x => x.SetProperty(y => y.LastLoginDate, clock.GetCurrentInstant()), ct);
        
        response.AppUser = user;
        response.IsSuccessful = true;

        accessService.RecordAccessIP(user.Id, ip, GrantType.RefreshToken);
        return response;
    }


    #region TokenLogin

    internal async Task<Result<bool>> RequestToken(UserTokenRequestVM request, string ip, CancellationToken ct)
    {
        var phoneNumer = request.Type == LoginTokenType.Phone ? new PhoneNumber(request.PhoneCode, request.Username.CleanPhoneNumber(request.PhoneCode)) : null;

        var user = await uow.Db.OneAsync(Query<CustomerProfile>.Where(x => x.Email == request.Username || 
        (phoneNumer != null && ((x.PhoneNumber != null && x.PhoneNumber.Code == phoneNumer.Code && x.PhoneNumber.Number == phoneNumer.Number)  
        || (x.MobileNumber != null && x.MobileNumber.Code == phoneNumer.Code && x.MobileNumber.Number == phoneNumer.Number)))), ct);

        if (user is null)
        {
            var (action, label) = request.Type == LoginTokenType.Phone ? ("sms", "phone number") : ("email", "email address");
            return Result<bool>.Successful(string.Format(StringConstants.LoginTokenMessage, action, label));
        }

        var result = await codeManager.SendCode(new SendCodeRequestVM 
        {
            UserId = user.Id,
            ExpireAt = Instant.Add(clock.GetCurrentInstant(), Duration.FromHours(2)),
            MessageConfigName = request.Type == LoginTokenType.Phone ? MessageConfigNames.LoginTokenSms.GetDisplayName() : MessageConfigNames.LoginTokenEmail.GetDisplayName(),
            OriginatedFrom = MessageConfigNames.CustomerAuthentication.GetDisplayName(),
            SendLimit = 3
        }, ct);

        if (result.IsSuccessful)
        {
            accessService.RecordAccessIP(user.Id, ip, GrantType.Login2FA);
            if (!string.IsNullOrEmpty(result.Data?.Code))
            {
				var appUser = await signInManager.UserManager.FindByNameAsync(user.Id);
				if (appUser != null)
				{
                    if (await uow.Db.ExistsAsync<ApplicationUser>(x => x.UserName == user.Id && string.IsNullOrEmpty(x.PasswordHash), ct))
                    {
                        await ManageAnalyticsService.UpdateTotalSignedInCustomers(uow, ct);
                    }

                    await signInManager.UserManager.UpdateSecurityStampAsync(appUser);
                    var passwordHash = signInManager.UserManager.PasswordHasher.HashPassword(appUser, result.Data.Code);
					_ = await uow.Db.UpdateAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == user.Id,
						x => x.SetProperty(y => y.PasswordHash, passwordHash)
						.SetProperty(y => y.AccessFailedCount, 0)
						.SetProperty(y => y.LockoutEnd, (DateTimeOffset?)null),
						ct);

                }
			}

			return Result<bool>.Successful(user.Email, data: true);
        }

        accessService.RecordFailedAccessIP(user.Id, ip, GrantType.Login2FA);

        return Result<bool>.Failed(result?.Message ?? "Code could not be sent");
    }
    #endregion

    #region Password
    internal async Task<Result<bool>> InitiatePasswordReset(ResetPasswordVM vm, CancellationToken ct)
    {
        var disapprovedEmails = config.DisallowedEmails.DisallowedDomains.Split(",");
        if (!disapprovedEmails.IsNullOrEmpty() && disapprovedEmails.Any(x => vm.Email.EndsWith(x, StringComparison.OrdinalIgnoreCase)))
            return Result<bool>.Successful(StringConstants.PasswordResetMessage);


        var user = await uow.Db.OneAsync(Query<CustomerProfile>.Where(x => x.Email == vm.Email), ct);
        if (user is null)
            return Result<bool>.Successful(StringConstants.PasswordResetMessage);

        await codeManager.SendCode(new SendCodeRequestVM
        {
            UserId = user.Id,
            OriginatedFrom = MessageConfigNames.CustomerAuthentication.GetDisplayName(),
            MessageConfigName = MessageConfigNames.ResetPassword.GetDisplayName(),
            ExpireAt = Instant.Add(clock.GetCurrentInstant(), Duration.FromHours(1)),
            SendLimit = 0
        }, ct);

        return Result<bool>.Successful("Password reset code sent", data: true);
    }

    internal async Task<Result<bool>> CompletePasswordReset(SetPasswordVM vm, CancellationToken ct)
    {
        var disapprovedEmails = config.DisallowedEmails.DisallowedDomains.Split(",");
        foreach (var item in disapprovedEmails)
        {
            if (vm.Email.EndsWith(item, StringComparison.OrdinalIgnoreCase))
            {
                return Result<bool>.Failed(StringConstants.ValidationError);
            }
        }

        var user = await signInManager.UserManager.FindByEmailAsync(vm.Email);
        if (user is null || user.Role != SystemRoleConfig.CustomerRole)
            return Result<bool>.Failed(StringConstants.ValidationError);

        var result = await codeManager.ValidateCode(new ValidateCodeRequest(user.UserName, vm.Token, MessageConfigNames.ResetPassword.GetDisplayName()), ct);
        if (!result.IsSuccessful)
            return Result<bool>.Failed(result.Message);


        if (vm.NewPassword.Contains(user.Email, StringComparison.OrdinalIgnoreCase) || vm.NewPassword.Contains(user.PhoneNumber, StringComparison.OrdinalIgnoreCase)
            || vm.NewPassword.Contains(user.FirstName, StringComparison.OrdinalIgnoreCase)
            || vm.NewPassword.Contains(user.LastName, StringComparison.OrdinalIgnoreCase))
        {
            return Result<bool>.Failed("Your new password cannot contain your name, email or phone number");
        }

        var passwordHash = signInManager.UserManager.PasswordHasher.HashPassword(user, vm.NewPassword);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == user.UserName,
            x => x.SetProperty(y => y.PasswordHash, passwordHash)
            .SetProperty(y => y.AccessFailedCount, 0)
            .SetProperty(y => y.LockoutEnd, (DateTimeOffset?)null),
            ct);

        return Result<bool>.Successful("Password reset successfully.");
    }
    #endregion
}

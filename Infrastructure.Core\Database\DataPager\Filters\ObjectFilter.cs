﻿using LendQube.Entities.Core.Extensions;
using System.Linq.Expressions;
using System.Reflection;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

public abstract class ObjectFilter(ColumnFilterRule rule)
{
    public int Id => (int)rule;
    public string Name => rule.GetDisplayName();

    public virtual bool RequiresValue { get; set; } = true;
    public bool EmbeddedQuery { get; set; } = false;

    public abstract Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value);
    public abstract bool IsTypeSupported(ObjectFilterRule rule);
}

internal static class ObjectFilterBuilder
{
    private static readonly Dictionary<int, ObjectFilter> Filters = [];

    static ObjectFilterBuilder()
    {
        var type = typeof(ObjectFilter);
        Assembly.GetAssembly(type)
            .GetTypes()
            .AsParallel()
            .Where(x => type.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
            .Select(x => Activator.CreateInstance(x) as ObjectFilter)
            .ToList()
            .ForEach(filter => Filters[filter.Id] = filter);
    }

    public static IReadOnlyList<ObjectFilter> GetAll(ObjectFilterRule rule) => Filters.Values.Where(x => x.IsTypeSupported(rule)).ToList();

    public static ObjectFilter Get(int ruleId) => Filters[ruleId];
}

public sealed record ObjectFilterRule(Type Type, bool IsId, ColumnFilterDataType FilterType);
﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Core.Database.DataPager.Filters;
using LendQube.Infrastructure.Core.Extensions;
using NodaTime;

namespace LendQube.Infrastructure.Core.Database.DataPager;

internal static class TableHelper
{
    private static readonly List<string> allowedInheritedTypes = [typeof(BaseFileUpload<>).Name];
    public static ColumnList GetColumnDefinition<T, TVM>(TableSettings<TVM> settings)
    {
        var columnNames = new List<ColumnHeader>();
        var columnFilters = new List<ColumnFilter>();

        var defType = typeof(TVM);
        var info = TypeDescriptor.GetProperties(defType).Cast<PropertyDescriptor>()
            .Where(x => x.ComponentType.Name == defType.Name || allowedInheritedTypes.Contains(x.ComponentType.Name))
            .Select(p =>
            {
                var property = defType.GetProperty(p.Name);
                return new ColumnPropertyInfo(p.Name, p.DisplayName, p.PropertyType, property.CanWrite, property.GetMethod.IsVirtual);
            }).ToList();


        foreach (var item in info)
        {
            var property = typeof(TVM)?.GetProperty(item.Name);

            var removeColumn = property != null &&
                ((RemoveColumnAttribute)Attribute.GetCustomAttribute(property, typeof(RemoveColumnAttribute)) != null ||
                (NotMappedAttribute)Attribute.GetCustomAttribute(property, typeof(NotMappedAttribute)) != null);

            if (removeColumn)
                continue;

            var decorators = property != null ? (TableDecoratorAttribute)Attribute.GetCustomAttribute(property, typeof(TableDecoratorAttribute)) : null;
            if (!item.SkipColumn)
            {
                var hideColumn = (!settings.HasId && item.Name == nameof(IBaseEntityWithNumberId.Id)) || (decorators != null && decorators.Type.HasDecoratorType(TableDecoratorType.HideColumn));
                var originalProperty = typeof(T)?.GetProperty(item.Name);

                var canSort = item.CanWrite && !settings.TurnOffSorting && !(decorators?.Type.HasDecoratorType(TableDecoratorType.NoSort) ?? false) && !item.PropertyType.IsGenericList()
                    && !item.PropertyType.IsPhoneNumber();
                columnNames.Add(new()
                {
                    Name = item.Name,
                    Title = item.DisplayName.SplitOnUpper(),
                    DecoratorTypes = decorators?.Type,
                    IsSortable = canSort && property == originalProperty,
                    IsSortableVM = canSort && property != originalProperty,
                    IsHidden = hideColumn
                });
            }

            if (!settings.TurnOffFilter)
                columnFilters.AddFilter(item, decorators?.Type, dbFilterOptions: settings.ColumnFilterOptions);
        }

        return new() { ColumnHeaders = columnNames, ColumnFilters = columnFilters };
    }

    public static ColumnList AddDateColumn(this ColumnList list, List<ColumnHeader> columNames, string name)
    {
        columNames.Add(new() { Name = name, Title = name.SplitOnUpper(), DecoratorTypes = [TableDecoratorType.ShowInInfo, TableDecoratorType.ShowInDelete] });
        list.ColumnFilters.Add(new() { Name = name, DisplayName = name.SplitOnUpper(), DataType = ColumnFilterDataType.Date, Rules = ObjectFilterBuilder.GetAll(new(typeof(Instant?), false, ColumnFilterDataType.Date)) });
        return list;
    }

    public static ColumnList AddUserInfoColumns(this ColumnList list, List<ColumnHeader> columNames, string name)
    {
        columNames.Add(new() { Name = name, Title = name.SplitOnUpper(), DecoratorTypes = [TableDecoratorType.ShowInInfo, TableDecoratorType.ShowInDelete] });
        list.ColumnFilters.Add(new() { Name = name, DisplayName = name.SplitOnUpper(), DataType = ColumnFilterDataType.Text, Rules = ObjectFilterBuilder.GetAll(new(typeof(string), false, ColumnFilterDataType.Text)) });
        return list;
    }

    public static void AddFilter(this List<ColumnFilter> columnFilters, ColumnPropertyInfo item, TableDecoratorType[] decorators, int index = -1, IReadOnlyList<ColumnFilterOptionFromDb> dbFilterOptions = null)
    {
        if ((!decorators.IsNullOrEmpty() && decorators.HasDecoratorType(TableDecoratorType.SkipFilter)) || !item.CanWrite || item.PropertyType == typeof(DateTimeOffset?))
            return;

        var filter = new ColumnFilter
        {
            Name = item.Name,
            DisplayName = item.DisplayName.SplitOnUpper(),
            DataType = item switch
            {
                { PropertyType: var type } when type.IsEnum() => ColumnFilterDataType.EnumList,
                { PropertyType: var type } when type.IsDate() || type.IsInstant() => ColumnFilterDataType.Date,
                { PropertyType: var type } when type.IsNumericType() => ColumnFilterDataType.Number,
                { PropertyType: var type } when type.IsBooleanType() => ColumnFilterDataType.BoolList,
                { PropertyType: var type } when type.IsPhoneNumber() => ColumnFilterDataType.PhoneNumber,
                { PropertyType: var type } when !dbFilterOptions.IsNullOrEmpty() && dbFilterOptions.Any(f => f.ColumnName == item.Name) => ColumnFilterDataType.DBList,
                { PropertyType: var type } when type == typeof(string) => ColumnFilterDataType.Text,
                _ => ColumnFilterDataType.Object
            }
        };

        filter.DataTypeValueList = filter.DataType switch
        {
            ColumnFilterDataType.EnumList => item.PropertyType.IsEnum ? [.. Enum.GetNames(item.PropertyType)] : [.. Enum.GetNames(Nullable.GetUnderlyingType(item.PropertyType))],
            ColumnFilterDataType.BoolList => [.. Enum.GetNames(typeof(ColumnFilterBoolOptions))],
            ColumnFilterDataType.DBList => dbFilterOptions.FirstOrDefault(f => f.ColumnName == item.Name).Options.ToList(),
            _ => null
        };

        filter.Rules = ObjectFilterBuilder.GetAll(new(item.PropertyType, item.Name == nameof(IBaseEntityWithNumberId.Id) || (decorators != null && decorators.HasDecoratorType(TableDecoratorType.Id)), filter.DataType));

        if (index == -1 || columnFilters.Count < index)
            columnFilters.Add(filter);
        else
            columnFilters.Insert(index, filter);
    }

}

internal sealed record ColumnPropertyInfo(string Name, string DisplayName, Type PropertyType, bool CanWrite, bool SkipColumn = false);
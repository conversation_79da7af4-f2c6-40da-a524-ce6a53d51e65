﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_AddNewSegmentRules : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "TypeNumber",
                schema: "collection",
                table: "DebtSegmentRule",
                newName: "MinTypeNumber");

            migrationBuilder.AddColumn<long>(
                name: "FlagId",
                schema: "collection",
                table: "DebtSegmentRule",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "MaxTypeNumber",
                schema: "collection",
                table: "DebtSegmentRule",
                type: "integer",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRule_FlagId",
                schema: "collection",
                table: "DebtSegmentRule",
                column: "FlagId");

            migrationBuilder.AddForeignKey(
                name: "FK_DebtSegmentRule_CustomerFlagTemplate_FlagId",
                schema: "collection",
                table: "DebtSegmentRule",
                column: "FlagId",
                principalSchema: "collection",
                principalTable: "CustomerFlagTemplate",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DebtSegmentRule_CustomerFlagTemplate_FlagId",
                schema: "collection",
                table: "DebtSegmentRule");

            migrationBuilder.DropIndex(
                name: "IX_DebtSegmentRule_FlagId",
                schema: "collection",
                table: "DebtSegmentRule");

            migrationBuilder.DropColumn(
                name: "FlagId",
                schema: "collection",
                table: "DebtSegmentRule");

            migrationBuilder.DropColumn(
                name: "MaxTypeNumber",
                schema: "collection",
                table: "DebtSegmentRule");

            migrationBuilder.RenameColumn(
                name: "MinTypeNumber",
                schema: "collection",
                table: "DebtSegmentRule",
                newName: "TypeNumber");
        }
    }
}

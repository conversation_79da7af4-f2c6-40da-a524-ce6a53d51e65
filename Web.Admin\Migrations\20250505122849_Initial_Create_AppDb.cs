﻿using System;
using System.Collections.Generic;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Logs;
using LendQube.Entities.Core.Messaging;
using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class Initial_Create_AppDb : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "collection");

            migrationBuilder.EnsureSchema(
                name: "public");

            migrationBuilder.EnsureSchema(
                name: "core");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Disabled,FailedLastCharge,Suspended")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Acquired,Discount,Stripe,Upload")
                .Annotation("Npgsql:Enum:collection.PaymentType", "Bank,Card,SetupCard")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Monthly,Weekly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Completed,Failed,Initiated,PendingRefund,Processing,Queued,Refunded,Successful,Validated")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Failed,Granted")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Idle,Running,Stopped,Stopping")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Failed,Queued,Running,Success")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Android,Web,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "AdminReset2FA,ChangePassword,ChangePin,Complete2FASetup,Login2FA,LoginClientId,LoginRecoveryCode,NewLogin,Password,RefreshToken,RequestDeviceLoginToken,RequestRegistrationToken,ResetPassword,ResetPasswordConfirm,ValidateDeviceLoginToken,ValidateRegistrationToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "Delivered,Failed,Opened,Processing,Queued,Sent,SentPartially,WaitingForConfiguration");

            migrationBuilder.CreateSequence(
                name: "EntityFrameworkHiLoSequence",
                incrementBy: 100);

            migrationBuilder.CreateSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "collection",
                incrementBy: 100);

            migrationBuilder.CreateSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "core",
                incrementBy: 100);

            migrationBuilder.CreateTable(
                name: "AspNetClaims",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Source = table.Column<string>(type: "character varying(60)", maxLength: 60, nullable: false),
                    Type = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Value = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetClaims", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoles",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUsers",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    LastName = table.Column<string>(type: "text", nullable: true),
                    OtherNames = table.Column<string>(type: "text", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true, computedColumnSql: "\"FirstName\"  || ' ' || \"LastName\"", stored: true),
                    LastLoginDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    RegistrationDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    MustChangePasswordOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Role = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: true),
                    WalletPinHash = table.Column<string>(type: "text", nullable: true),
                    PhoneCode = table.Column<string>(type: "text", nullable: true),
                    UserName = table.Column<Guid>(type: "uuid", maxLength: 256, nullable: false),
                    NormalizedUserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedEmail = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordHash = table.Column<string>(type: "text", nullable: true),
                    SecurityStamp = table.Column<string>(type: "text", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AccessFailedCount = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUsers", x => x.Id);
                    table.UniqueConstraint("AK_AspNetUsers_UserName", x => x.UserName);
                });

            migrationBuilder.CreateTable(
                name: "BackgroundTaskEventControl",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Source = table.Column<int>(type: "integer", nullable: false),
                    Event = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<BackgroundControlState>(type: "core.\"BackgroundControlState\"", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BackgroundTaskEventControl", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BackgroundTaskEventLog",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    Source = table.Column<int>(type: "integer", nullable: false),
                    Event = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<BackgroundEventStatus>(type: "core.\"BackgroundEventSource\"", nullable: false),
                    Data = table.Column<string>(type: "text", nullable: true),
                    RequeryDelayTime = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TimeStarted = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TimeCompleted = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Tries = table.Column<int>(type: "integer", nullable: false),
                    Uri = table.Column<string>(type: "text", nullable: true),
                    ResponseMessage = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BackgroundTaskEventLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CollectionTemplate",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CollectionTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Country",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Code = table.Column<string>(type: "character(2)", fixedLength: true, maxLength: 2, nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    ImageUrl = table.Column<string>(type: "text", nullable: false),
                    PhoneCode = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Country", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerDevice",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeviceId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Type = table.Column<CustomerDeviceType>(type: "core.\"CustomerDeviceType\"", nullable: false),
                    PushNotificationId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AppVersion = table.Column<string>(type: "text", nullable: true),
                    AppBuild = table.Column<string>(type: "text", nullable: true),
                    MobileOsVersion = table.Column<string>(type: "text", nullable: true),
                    MobileMake = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerDevice", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerDeviceVersion",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Type = table.Column<CustomerDeviceType>(type: "core.\"CustomerDeviceType\"", nullable: false),
                    OldVersion = table.Column<string>(type: "text", nullable: false),
                    CurrentVersion = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerDeviceVersion", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerFlagTemplate",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Flag = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerFlagTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerInbox",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Template = table.Column<string>(type: "text", nullable: true),
                    ReadOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerInbox", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerNoteTemplate",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    ContactType = table.Column<int>(type: "integer", nullable: true),
                    Template = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerNoteTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerPaymentMethodConfiguration",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    Provider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    ProviderId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerPaymentMethodConfiguration", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DashboardAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TotalCustomers = table.Column<int>(type: "integer", nullable: false),
                    TotalSignedInCustomers = table.Column<int>(type: "integer", nullable: false),
                    TotalCustomersWithSchedules = table.Column<int>(type: "integer", nullable: false),
                    TotalPlacements = table.Column<int>(type: "integer", nullable: false),
                    TotalPlacementValue = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalPlacementValuePaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalClosedPlacements = table.Column<int>(type: "integer", nullable: false),
                    TotalClosedPlacementValue = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalSettledPlacements = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DashboardTimeAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Date = table.Column<DateOnly>(type: "date", nullable: false),
                    TotalCustomersThatCreatedSchedule = table.Column<int>(type: "integer", nullable: false),
                    TotalAmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardTimeAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DataProtectionKeys",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    FriendlyName = table.Column<string>(type: "text", nullable: true),
                    Xml = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataProtectionKeys", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DebtSegment",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    Start = table.Column<TimeOnly>(type: "time without time zone", nullable: true),
                    End = table.Column<TimeOnly>(type: "time without time zone", nullable: true),
                    RuleIds = table.Column<List<long>>(type: "bigint[]", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtSegment", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DebtWorkflowAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalPTPAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalCallBack = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtWorkflowAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DebtWorkflowTimeAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Date = table.Column<LocalDate>(type: "date", nullable: false),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalPTPAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalCallBack = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtWorkflowTimeAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "HoldDurationConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Length = table.Column<int>(type: "integer", nullable: false),
                    Duration = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HoldDurationConfig", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessageLog",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    OriginatedFrom = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<MessageStatus>(type: "core.\"MessageStatus\"", nullable: false),
                    AttemptCount = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessageProviderConfig",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: true),
                    SupportedCountries = table.Column<List<string>>(type: "text[]", nullable: true),
                    DisabledOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    LogActivityOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    UnitCost = table.Column<decimal>(type: "numeric(18,8)", nullable: false),
                    TotalCount = table.Column<long>(type: "bigint", nullable: false),
                    FailureCount = table.Column<long>(type: "bigint", nullable: false),
                    TotalFunding = table.Column<decimal>(type: "numeric(18,8)", nullable: false),
                    TotalDebit = table.Column<decimal>(type: "numeric(18,8)", nullable: false),
                    ExpectedTotalDebit = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Balance = table.Column<decimal>(type: "numeric(18,8)", nullable: false, computedColumnSql: "\"TotalFunding\" - \"TotalDebit\"", stored: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageProviderConfig", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessagingGroup",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingGroup", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessagingTemplate",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Types = table.Column<int>(type: "integer", nullable: false),
                    Keys = table.Column<List<string>>(type: "text[]", nullable: true),
                    TextTemplate = table.Column<string>(type: "text", nullable: true),
                    HtmlTemplate = table.Column<string>(type: "text", nullable: true),
                    IsContainer = table.Column<bool>(type: "boolean", nullable: false),
                    DisabledOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictApplications",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApplicationType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ClientId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ClientSecret = table.Column<string>(type: "text", nullable: true),
                    ClientType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ConsentType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    DisplayName = table.Column<string>(type: "text", nullable: true),
                    DisplayNames = table.Column<string>(type: "text", nullable: true),
                    JsonWebKeySet = table.Column<string>(type: "text", nullable: true),
                    Permissions = table.Column<string>(type: "text", nullable: true),
                    PostLogoutRedirectUris = table.Column<string>(type: "text", nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    RedirectUris = table.Column<string>(type: "text", nullable: true),
                    Requirements = table.Column<string>(type: "text", nullable: true),
                    Settings = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictApplications", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictScopes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Descriptions = table.Column<string>(type: "text", nullable: true),
                    DisplayName = table.Column<string>(type: "text", nullable: true),
                    DisplayNames = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    Resources = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictScopes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PlacementStatusChangeReasonConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "character varying(4)", maxLength: 4, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementStatusChangeReasonConfig", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SystemFileUpload",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    AnalysisFileUrl = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemFileUpload", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TelegramSession",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false),
                    Data = table.Column<byte[]>(type: "bytea", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TelegramSession", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserAccessLog",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Status = table.Column<AccessStatus>(type: "core.\"AccessStatus\"", nullable: false),
                    GrantType = table.Column<GrantType>(type: "core.\"GrantType\"", nullable: false),
                    Application = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    IpLocation = table.Column<string>(type: "text", nullable: true),
                    LocationLookupData = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserAccessLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Source = table.Column<string>(type: "text", nullable: true),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "public",
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DiscountConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    PercentageLimit = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CanOverride = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DiscountConfig", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DiscountConfig_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "public",
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalPTPAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalCallBack = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowAnalytics", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowAnalytics_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowAvailability",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    Start = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    End = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false),
                    CurrentlyAssignedCount = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowAvailability", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowAvailability_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowTimeAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Date = table.Column<LocalDate>(type: "date", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalPTPAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalCallBack = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowTimeAnalytics", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTimeAnalytics_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserClaims",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserLogins",
                columns: table => new
                {
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    ProviderKey = table.Column<string>(type: "text", nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserLogins", x => new { x.LoginProvider, x.ProviderKey });
                    table.ForeignKey(
                        name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserRoles",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "public",
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserTokens",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Currency",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    CountryId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "character(3)", fixedLength: true, maxLength: 3, nullable: false),
                    Symbol = table.Column<string>(type: "character(1)", fixedLength: true, maxLength: 1, nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Currency", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Currency_Country_CountryId",
                        column: x => x.CountryId,
                        principalSchema: "core",
                        principalTable: "Country",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "State",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    CountryId = table.Column<long>(type: "bigint", nullable: false),
                    StateCode = table.Column<string>(type: "character varying(32)", maxLength: 32, nullable: false),
                    StateName = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_State", x => x.Id);
                    table.ForeignKey(
                        name: "FK_State_Country_CountryId",
                        column: x => x.CountryId,
                        principalSchema: "core",
                        principalTable: "Country",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DebtSegmentRule",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Instruction = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    MinTypeNumber = table.Column<int>(type: "integer", nullable: true),
                    MaxTypeNumber = table.Column<int>(type: "integer", nullable: true),
                    StatusType = table.Column<int>(type: "integer", nullable: true),
                    FlagId = table.Column<long>(type: "bigint", nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtSegmentRule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DebtSegmentRule_CustomerFlagTemplate_FlagId",
                        column: x => x.FlagId,
                        principalSchema: "collection",
                        principalTable: "CustomerFlagTemplate",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AgentToDebtSegmentMapping",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    DebtSegmentId = table.Column<long>(type: "bigint", nullable: false),
                    RecordsPerTime = table.Column<int>(type: "integer", nullable: false),
                    Enabled = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentToDebtSegmentMapping", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentToDebtSegmentMapping_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AgentToDebtSegmentMapping_DebtSegment_DebtSegmentId",
                        column: x => x.DebtSegmentId,
                        principalSchema: "collection",
                        principalTable: "DebtSegment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "HoldConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Reason = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    DefaultDurationId = table.Column<long>(type: "bigint", nullable: true),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    AllowDefaultOverride = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HoldConfig", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HoldConfig_HoldDurationConfig_DefaultDurationId",
                        column: x => x.DefaultDurationId,
                        principalSchema: "collection",
                        principalTable: "HoldDurationConfig",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "MessageLogActivity",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageLogActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageLogActivity_MessageLog_MessageLogId",
                        column: x => x.MessageLogId,
                        principalSchema: "core",
                        principalTable: "MessageLog",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessagingGroupEntry",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessagingGroupId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Emails = table.Column<List<string>>(type: "text[]", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    PhoneNumbers = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingGroupEntry", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessagingGroupEntry_MessagingGroup_MessagingGroupId",
                        column: x => x.MessagingGroupId,
                        principalSchema: "core",
                        principalTable: "MessagingGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessagingGroupQuery",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessagingGroupId = table.Column<long>(type: "bigint", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Query = table.Column<string>(type: "text", nullable: true),
                    SenderQuery = table.Column<string>(type: "text", nullable: true),
                    BuildingBlock = table.Column<string>(type: "jsonb", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingGroupQuery", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessagingGroupQuery_MessagingGroup_MessagingGroupId",
                        column: x => x.MessagingGroupId,
                        principalSchema: "core",
                        principalTable: "MessagingGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageConfiguration",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: true),
                    SenderEmail = table.Column<string>(type: "text", nullable: true),
                    SenderName = table.Column<string>(type: "text", nullable: true),
                    Channels = table.Column<int>(type: "integer", nullable: false),
                    Keys = table.Column<List<string>>(type: "text[]", nullable: true),
                    DoNotSendIfExists = table.Column<bool>(type: "boolean", nullable: false),
                    ExistsCheckWindow = table.Column<int>(type: "integer", nullable: false),
                    BodyTemplateId = table.Column<long>(type: "bigint", nullable: true),
                    ContainerTemplateId = table.Column<long>(type: "bigint", nullable: true),
                    TextTemplate = table.Column<string>(type: "text", nullable: true),
                    HtmlTemplate = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageConfiguration", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageConfiguration_MessagingTemplate_BodyTemplateId",
                        column: x => x.BodyTemplateId,
                        principalSchema: "core",
                        principalTable: "MessagingTemplate",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MessageConfiguration_MessagingTemplate_ContainerTemplateId",
                        column: x => x.ContainerTemplateId,
                        principalSchema: "core",
                        principalTable: "MessagingTemplate",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictAuthorizations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApplicationId = table.Column<Guid>(type: "uuid", nullable: true),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    Scopes = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Subject = table.Column<string>(type: "character varying(400)", maxLength: 400, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictAuthorizations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OpenIddictAuthorizations_OpenIddictApplications_Application~",
                        column: x => x.ApplicationId,
                        principalTable: "OpenIddictApplications",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowAvailabilityStatusChangeLog",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    AvailabilityId = table.Column<long>(type: "bigint", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowAvailabilityStatusChangeLog", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowAvailabilityStatusChangeLog_AgentWorkflowAvail~",
                        column: x => x.AvailabilityId,
                        principalSchema: "collection",
                        principalTable: "AgentWorkflowAvailability",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DebtSegmentRules",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    DebtSegmentId = table.Column<long>(type: "bigint", nullable: false),
                    RuleId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtSegmentRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DebtSegmentRules_DebtSegmentRule_RuleId",
                        column: x => x.RuleId,
                        principalSchema: "collection",
                        principalTable: "DebtSegmentRule",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DebtSegmentRules_DebtSegment_DebtSegmentId",
                        column: x => x.DebtSegmentId,
                        principalSchema: "collection",
                        principalTable: "DebtSegment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CollectionFileUpload",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    MessageConfigId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    AnalysisFileUrl = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CollectionFileUpload", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CollectionFileUpload_MessageConfiguration_MessageConfigId",
                        column: x => x.MessageConfigId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "MessageConfigurationMessagingGroup",
                schema: "core",
                columns: table => new
                {
                    MessageConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    MessagingGroupId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageConfigurationMessagingGroup", x => new { x.MessageConfigurationId, x.MessagingGroupId });
                    table.ForeignKey(
                        name: "FK_MessageConfigurationMessagingGroup_MessageConfiguration_Mes~",
                        column: x => x.MessageConfigurationId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MessageConfigurationMessagingGroup_MessagingGroup_Messaging~",
                        column: x => x.MessagingGroupId,
                        principalSchema: "core",
                        principalTable: "MessagingGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageLogEntry",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    MessageConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Subject = table.Column<string>(type: "text", nullable: true),
                    Channels = table.Column<int>(type: "integer", nullable: false),
                    Keys = table.Column<List<string>>(type: "text[]", nullable: true),
                    TextRequired = table.Column<bool>(type: "boolean", nullable: false),
                    EmailRequired = table.Column<bool>(type: "boolean", nullable: false),
                    TextTemplate = table.Column<string>(type: "text", nullable: true),
                    HtmlTemplate = table.Column<string>(type: "text", nullable: true),
                    BodyTextTemplate = table.Column<string>(type: "text", nullable: true),
                    BodyHtmlTemplate = table.Column<string>(type: "text", nullable: true),
                    ContainerTextTemplate = table.Column<string>(type: "text", nullable: true),
                    ContainerHtmlTemplate = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    CopiedIn = table.Column<string>(type: "jsonb", nullable: true),
                    Recipients = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageLogEntry", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageLogEntry_MessageConfiguration_MessageConfigurationId",
                        column: x => x.MessageConfigurationId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MessageLogEntry_MessageLog_MessageLogId",
                        column: x => x.MessageLogId,
                        principalSchema: "core",
                        principalTable: "MessageLog",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageSchedule",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CronExpression = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    Frequency = table.Column<int>(type: "integer", nullable: false),
                    FrequencyNumber = table.Column<int>(type: "integer", nullable: false),
                    Days = table.Column<int>(type: "integer", nullable: false),
                    ActiveOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Starting = table.Column<bool>(type: "boolean", nullable: false),
                    TimeZone = table.Column<string>(type: "text", nullable: true),
                    RunCount = table.Column<int>(type: "integer", nullable: false),
                    ConfigId = table.Column<long>(type: "bigint", nullable: false),
                    Groups = table.Column<List<long>>(type: "bigint[]", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TemplateValues = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageSchedule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageSchedule_MessageConfiguration_ConfigId",
                        column: x => x.ConfigId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessagingConfigurationActivity",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessageConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingConfigurationActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessagingConfigurationActivity_MessageConfiguration_Message~",
                        column: x => x.MessageConfigurationId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ReportSchedule",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    ReportTypeName = table.Column<string>(type: "text", nullable: false),
                    ReportNames = table.Column<List<string>>(type: "text[]", nullable: false),
                    FileType = table.Column<int>(type: "integer", nullable: false),
                    Frequency = table.Column<int>(type: "integer", nullable: false),
                    FrequencyNumber = table.Column<int>(type: "integer", nullable: false),
                    Days = table.Column<int>(type: "integer", nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    LastRunDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    StartDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    RunCount = table.Column<int>(type: "integer", nullable: false),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Starting = table.Column<bool>(type: "boolean", nullable: false),
                    TimeZone = table.Column<string>(type: "text", nullable: true),
                    MessagingGroupId = table.Column<long>(type: "bigint", nullable: false),
                    ConfigId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportSchedule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportSchedule_MessageConfiguration_ConfigId",
                        column: x => x.ConfigId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReportSchedule_MessagingGroup_MessagingGroupId",
                        column: x => x.MessagingGroupId,
                        principalSchema: "core",
                        principalTable: "MessagingGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictTokens",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApplicationId = table.Column<Guid>(type: "uuid", nullable: true),
                    AuthorizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Payload = table.Column<string>(type: "text", nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    RedemptionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReferenceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Subject = table.Column<string>(type: "character varying(400)", maxLength: 400, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictTokens", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OpenIddictTokens_OpenIddictApplications_ApplicationId",
                        column: x => x.ApplicationId,
                        principalTable: "OpenIddictApplications",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OpenIddictTokens_OpenIddictAuthorizations_AuthorizationId",
                        column: x => x.AuthorizationId,
                        principalTable: "OpenIddictAuthorizations",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AutoDiscountConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric", nullable: true),
                    MinDays = table.Column<int>(type: "integer", nullable: true),
                    MaxDays = table.Column<int>(type: "integer", nullable: true),
                    Percentage = table.Column<decimal>(type: "numeric", nullable: true),
                    UpperLimit = table.Column<decimal>(type: "numeric", nullable: true),
                    Rule = table.Column<int>(type: "integer", nullable: false),
                    DoNotApplyIfDiscountSettlesAccount = table.Column<bool>(type: "boolean", nullable: false),
                    ApplyToNewAccountsOnly = table.Column<bool>(type: "boolean", nullable: false),
                    ApplyToOnlyAccountsWithNoDiscount = table.Column<bool>(type: "boolean", nullable: false),
                    ApplyDiscountNow = table.Column<bool>(type: "boolean", nullable: false),
                    AutoApplyDiscount = table.Column<bool>(type: "boolean", nullable: false),
                    DiscountApplied = table.Column<bool>(type: "boolean", nullable: false),
                    TotalPlacementsAffected = table.Column<int>(type: "integer", nullable: false),
                    TotalAmountApplied = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    FileUploadId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoDiscountConfig", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AutoDiscountConfig_CollectionFileUpload_FileUploadId",
                        column: x => x.FileUploadId,
                        principalSchema: "collection",
                        principalTable: "CollectionFileUpload",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerProfile",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AccountId = table.Column<string>(type: "text", nullable: false),
                    FullName = table.Column<string>(type: "text", nullable: true, computedColumnSql: "\"FirstName\"  || ' ' || \"LastName\"", stored: true),
                    FirstName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MiddleName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    LastName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    DateOfBirth = table.Column<DateOnly>(type: "date", nullable: true),
                    Gender = table.Column<int>(type: "integer", nullable: false),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    CurrencySymbol = table.Column<string>(type: "text", nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Blacklisted = table.Column<bool>(type: "boolean", nullable: false),
                    LastRescheduleDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TotalRescheduleCount = table.Column<int>(type: "integer", nullable: false),
                    BalanceTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalancePaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Discount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceRemaining = table.Column<decimal>(type: "numeric(18,2)", nullable: false, computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"", stored: true),
                    PaymentFrequency = table.Column<SchedulePaymentFrequency>(type: "collection.\"SchedulePaymentFrequency\"", nullable: true),
                    LastAssignedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    CurrentlyAssigned = table.Column<bool>(type: "boolean", nullable: false),
                    NextCallbackDate = table.Column<DateOnly>(type: "date", nullable: true),
                    FileUploadId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    MobileNumber = table.Column<string>(type: "jsonb", nullable: true),
                    PhoneNumber = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerProfile", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerProfile_AspNetUsers_Id",
                        column: x => x.Id,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "UserName",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerProfile_CollectionFileUpload_FileUploadId",
                        column: x => x.FileUploadId,
                        principalSchema: "collection",
                        principalTable: "CollectionFileUpload",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "SystemReport",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TypeName = table.Column<string>(type: "text", nullable: false),
                    Names = table.Column<List<string>>(type: "text[]", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    FileType = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true),
                    StartDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    RunCount = table.Column<int>(type: "integer", nullable: false),
                    AutoRun = table.Column<bool>(type: "boolean", nullable: false),
                    ReportScheduleId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemReport", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SystemReport_ReportSchedule_ReportScheduleId",
                        column: x => x.ReportScheduleId,
                        principalSchema: "core",
                        principalTable: "ReportSchedule",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowTask",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    AvailabilityId = table.Column<long>(type: "bigint", nullable: true),
                    DebtSegmentId = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    EscalatedTaskId = table.Column<long>(type: "bigint", nullable: true),
                    EscalatedToUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    EscalatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    EscalationReason = table.Column<string>(type: "text", nullable: true),
                    Assigned = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    Opened = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Removed = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    IsEscalated = table.Column<bool>(type: "boolean", nullable: false),
                    NextCallbackDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Tasks = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowTask", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AgentWorkflowAvailability_AvailabilityId",
                        column: x => x.AvailabilityId,
                        principalSchema: "collection",
                        principalTable: "AgentWorkflowAvailability",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AspNetUsers_EscalatedByUserId",
                        column: x => x.EscalatedByUserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AspNetUsers_EscalatedToUserId",
                        column: x => x.EscalatedToUserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_CustomerProfile_CustomerProfileId",
                        column: x => x.CustomerProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_DebtSegment_DebtSegmentId",
                        column: x => x.DebtSegmentId,
                        principalSchema: "collection",
                        principalTable: "DebtSegment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerActivity",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    CustomerProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerActivity_CustomerProfile_CustomerProfileId",
                        column: x => x.CustomerProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerAddress",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    CustomerProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    AddressLine1 = table.Column<string>(type: "text", nullable: true),
                    AddressLine2 = table.Column<string>(type: "text", nullable: true),
                    Locality = table.Column<string>(type: "text", nullable: true),
                    PostCode = table.Column<string>(type: "text", nullable: true),
                    City = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CountryCode = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerAddress", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerAddress_CustomerProfile_CustomerProfileId",
                        column: x => x.CustomerProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerContactDetail",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber_Code = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber_Number = table.Column<string>(type: "text", nullable: true),
                    Preferred = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerContactDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerContactDetail_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerFlag",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    FlagId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerFlag", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerFlag_CustomerFlagTemplate_FlagId",
                        column: x => x.FlagId,
                        principalSchema: "collection",
                        principalTable: "CustomerFlagTemplate",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerFlag_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerIncomeAndExpenditure",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    Address = table.Column<string>(type: "text", nullable: true),
                    NetSalary = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BenefitsIncome = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    OtherIncome = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Rent = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Utilities = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CouncilTax = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Food = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Transport = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Insurance = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Loan = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    OtherExpenditure = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    PhoneNumber = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerIncomeAndExpenditure", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerIncomeAndExpenditure_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerOneTimeCode",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    CodeType = table.Column<string>(type: "text", nullable: true),
                    Code = table.Column<string>(type: "text", nullable: true),
                    SendCount = table.Column<int>(type: "integer", nullable: false),
                    SendLimit = table.Column<int>(type: "integer", nullable: false),
                    TriesCount = table.Column<int>(type: "integer", nullable: false),
                    IsValid = table.Column<bool>(type: "boolean", nullable: false),
                    ExpireAt = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerOneTimeCode", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerOneTimeCode_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerPaymentMethod",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerPaymentMethodConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    ProviderId = table.Column<string>(type: "text", nullable: true),
                    CanBeReused = table.Column<bool>(type: "boolean", nullable: false),
                    Status = table.Column<PaymentMethodStatus>(type: "collection.\"PaymentMethodStatus\"", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Data = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerPaymentMethod", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerPaymentMethod_CustomerPaymentMethodConfiguration_Cu~",
                        column: x => x.CustomerPaymentMethodConfigurationId,
                        principalSchema: "collection",
                        principalTable: "CustomerPaymentMethodConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerPaymentMethod_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerSchedule",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    Period = table.Column<int>(type: "integer", nullable: false),
                    DueDate = table.Column<DateOnly>(type: "date", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CPADate = table.Column<DateOnly>(type: "date", nullable: false),
                    PeriodStatus = table.Column<int>(type: "integer", nullable: false),
                    PaymentStatus = table.Column<int>(type: "integer", nullable: false),
                    Balance = table.Column<decimal>(type: "numeric(18,2)", nullable: false, computedColumnSql: "\"Amount\" - \"AmountPaid\"", stored: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerSchedule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerSchedule_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerTransaction",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    TransactionId = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    PaymentMethod = table.Column<string>(type: "text", nullable: true),
                    PaymentProvider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    AmountTried = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Successful = table.Column<bool>(type: "boolean", nullable: false),
                    SchedulePeriodAffected = table.Column<List<string>>(type: "text[]", nullable: true),
                    PaymentApplied = table.Column<bool>(type: "boolean", nullable: false),
                    PaymentType = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerTransaction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerTransaction_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Placement",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    SourceAccountNumber = table.Column<string>(type: "text", nullable: true),
                    Company = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    AgreementDate = table.Column<DateOnly>(type: "date", nullable: true),
                    DefaultDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LastPaymentDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LastInvoiceDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PlacementCount = table.Column<int>(type: "integer", nullable: false),
                    MonthlyRecurringBills = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalancePrincipal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceInterest = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceFees = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalancePaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Discount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceRemaining = table.Column<decimal>(type: "numeric(18,2)", nullable: false, computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"", stored: true),
                    FileUploadId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Placement", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Placement_CollectionFileUpload_FileUploadId",
                        column: x => x.FileUploadId,
                        principalSchema: "collection",
                        principalTable: "CollectionFileUpload",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_Placement_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Transaction",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: false),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProviderReference = table.Column<string>(type: "text", nullable: true),
                    Purpose = table.Column<string>(type: "text", nullable: true),
                    CurrencyCode = table.Column<string>(type: "text", nullable: true),
                    CurrencySymbol = table.Column<string>(type: "text", nullable: true),
                    UnitAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Fee = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalAmountPayable = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalAmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Discount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Type = table.Column<PaymentType>(type: "collection.\"PaymentType\"", nullable: false),
                    Provider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    Status = table.Column<TransactionStatus>(type: "collection.\"TransactionStatus\"", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Fields = table.Column<string>(type: "jsonb", nullable: true),
                    UserData = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Transaction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Transaction_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerDiscount",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    BalanceBeforeDiscount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceAfterDiscount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Discount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    MaxPercentage = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerDiscount", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerDiscount_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CustomerDiscount_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerHold",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    PlacementId = table.Column<long>(type: "bigint", nullable: true),
                    HoldConfigId = table.Column<long>(type: "bigint", nullable: false),
                    Comment = table.Column<string>(type: "text", nullable: true),
                    ExpiresOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    HoldDefaultsOverriden = table.Column<bool>(type: "boolean", nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerHold", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerHold_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CustomerHold_HoldConfig_HoldConfigId",
                        column: x => x.HoldConfigId,
                        principalSchema: "collection",
                        principalTable: "HoldConfig",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerHold_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerNote",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    PlacementId = table.Column<long>(type: "bigint", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    ContactType = table.Column<int>(type: "integer", nullable: true),
                    Note = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Files = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerNote", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerNote_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CustomerNote_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PlacementActivity",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementActivity_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementNotes",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    Note = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementNotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementNotes_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementStatusChange",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    FromStatus = table.Column<int>(type: "integer", nullable: false),
                    ToStatus = table.Column<int>(type: "integer", nullable: false),
                    ReasonId = table.Column<long>(type: "bigint", nullable: true),
                    Comment = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementStatusChange", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementStatusChange_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PlacementStatusChange_PlacementStatusChangeReasonConfig_Rea~",
                        column: x => x.ReasonId,
                        principalSchema: "collection",
                        principalTable: "PlacementStatusChangeReasonConfig",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PlacementStatusChange_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementStatusChangeLog",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    OldStatus = table.Column<int>(type: "integer", nullable: false),
                    NewStatus = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementStatusChangeLog", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementStatusChangeLog_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementTag",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    Tag = table.Column<string>(type: "text", nullable: true),
                    Comment = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementTag", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementTag_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementTransaction",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    TransactionId = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    PaymentMethod = table.Column<string>(type: "text", nullable: true),
                    PaymentProvider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    PaymentType = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementTransaction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementTransaction_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TransactionHistory",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TransactionId = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransactionHistory_Transaction_TransactionId",
                        column: x => x.TransactionId,
                        principalSchema: "collection",
                        principalTable: "Transaction",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerPromiseToPay",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    DueDate = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    Redeemed = table.Column<bool>(type: "boolean", nullable: false),
                    NoteId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerPromiseToPay", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerPromiseToPay_CustomerNote_NoteId",
                        column: x => x.NoteId,
                        principalSchema: "collection",
                        principalTable: "CustomerNote",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerPromiseToPay_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AgentToDebtSegmentMapping_DebtSegmentId",
                schema: "collection",
                table: "AgentToDebtSegmentMapping",
                column: "DebtSegmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentToDebtSegmentMapping_UserId_DebtSegmentId",
                schema: "collection",
                table: "AgentToDebtSegmentMapping",
                columns: new[] { "UserId", "DebtSegmentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowAnalytics_UserId",
                schema: "collection",
                table: "AgentWorkflowAnalytics",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowAvailability_UserId",
                schema: "collection",
                table: "AgentWorkflowAvailability",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowAvailabilityStatusChangeLog_AvailabilityId",
                schema: "collection",
                table: "AgentWorkflowAvailabilityStatusChangeLog",
                column: "AvailabilityId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_AvailabilityId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "AvailabilityId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_CustomerProfileId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "CustomerProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_DebtSegmentId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "DebtSegmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_EscalatedByUserId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "EscalatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_EscalatedToUserId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "EscalatedToUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_UserId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTimeAnalytics_UserId_Date",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics",
                columns: new[] { "UserId", "Date" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                schema: "public",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                schema: "public",
                table: "AspNetRoles",
                column: "NormalizedName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserClaims_UserId",
                schema: "public",
                table: "AspNetUserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "AspNetUserLogins",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserRoles_RoleId",
                table: "AspNetUserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                schema: "public",
                table: "AspNetUsers",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers",
                column: "UserName",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "SecurityStamp", "TwoFactorEnabled", "PasswordHash", "WalletPinHash", "MustChangePasswordOn", "Role" });

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                schema: "public",
                table: "AspNetUsers",
                column: "NormalizedUserName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AutoDiscountConfig_FileUploadId",
                schema: "collection",
                table: "AutoDiscountConfig",
                column: "FileUploadId");

            migrationBuilder.CreateIndex(
                name: "IX_BackgroundTaskEventControl_Source_Event",
                schema: "core",
                table: "BackgroundTaskEventControl",
                columns: new[] { "Source", "Event" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BackgroundTaskEventLog_UserId",
                schema: "core",
                table: "BackgroundTaskEventLog",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CollectionFileUpload_MessageConfigId",
                schema: "collection",
                table: "CollectionFileUpload",
                column: "MessageConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_Country_Code",
                schema: "core",
                table: "Country",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Country_Name",
                schema: "core",
                table: "Country",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Currency_Code",
                schema: "core",
                table: "Currency",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Currency_CountryId",
                schema: "core",
                table: "Currency",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_Currency_Symbol",
                schema: "core",
                table: "Currency",
                column: "Symbol",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerActivity_CustomerProfileId",
                schema: "collection",
                table: "CustomerActivity",
                column: "CustomerProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerActivity_ProfileId",
                schema: "collection",
                table: "CustomerActivity",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerAddress_CustomerProfileId",
                schema: "collection",
                table: "CustomerAddress",
                column: "CustomerProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerContactDetail_ProfileId",
                schema: "collection",
                table: "CustomerContactDetail",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerDevice_UserId",
                schema: "core",
                table: "CustomerDevice",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerDiscount_PlacementId",
                schema: "collection",
                table: "CustomerDiscount",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerDiscount_ProfileId",
                schema: "collection",
                table: "CustomerDiscount",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFlag_FlagId",
                schema: "collection",
                table: "CustomerFlag",
                column: "FlagId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFlag_ProfileId",
                schema: "collection",
                table: "CustomerFlag",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFlagTemplate_Flag",
                schema: "collection",
                table: "CustomerFlagTemplate",
                column: "Flag",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_ExpiresOn",
                schema: "collection",
                table: "CustomerHold",
                column: "ExpiresOn")
                .Annotation("Npgsql:IndexInclude", new[] { "HoldDefaultsOverriden" });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_HoldConfigId",
                schema: "collection",
                table: "CustomerHold",
                column: "HoldConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_PlacementId",
                schema: "collection",
                table: "CustomerHold",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_ProfileId",
                schema: "collection",
                table: "CustomerHold",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerInbox_UserId",
                schema: "core",
                table: "CustomerInbox",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerIncomeAndExpenditure_ProfileId",
                schema: "collection",
                table: "CustomerIncomeAndExpenditure",
                column: "ProfileId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNote_PlacementId",
                schema: "collection",
                table: "CustomerNote",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNote_ProfileId",
                schema: "collection",
                table: "CustomerNote",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNoteTemplate_Name",
                schema: "collection",
                table: "CustomerNoteTemplate",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "Type", "ContactType" });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerOneTimeCode_ProfileId",
                schema: "collection",
                table: "CustomerOneTimeCode",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPaymentMethod_CustomerPaymentMethodConfigurationId",
                schema: "collection",
                table: "CustomerPaymentMethod",
                column: "CustomerPaymentMethodConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPaymentMethod_ProfileId",
                schema: "collection",
                table: "CustomerPaymentMethod",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerProfile_AccountId",
                schema: "collection",
                table: "CustomerProfile",
                column: "AccountId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerProfile_FileUploadId",
                schema: "collection",
                table: "CustomerProfile",
                column: "FileUploadId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPromiseToPay_NoteId",
                schema: "collection",
                table: "CustomerPromiseToPay",
                column: "NoteId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPromiseToPay_ProfileId",
                schema: "collection",
                table: "CustomerPromiseToPay",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerSchedule_ProfileId",
                schema: "collection",
                table: "CustomerSchedule",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerTransaction_ProfileId",
                schema: "collection",
                table: "CustomerTransaction",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerTransaction_TransactionId",
                schema: "collection",
                table: "CustomerTransaction",
                column: "TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_DashboardTimeAnalytics_Date",
                schema: "collection",
                table: "DashboardTimeAnalytics",
                column: "Date",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "TotalCustomersThatCreatedSchedule", "TotalAmountPaid" });

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegment_Name",
                schema: "collection",
                table: "DebtSegment",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRule_FlagId",
                schema: "collection",
                table: "DebtSegmentRule",
                column: "FlagId");

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRule_Name",
                schema: "collection",
                table: "DebtSegmentRule",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRules_DebtSegmentId",
                schema: "collection",
                table: "DebtSegmentRules",
                column: "DebtSegmentId");

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRules_RuleId",
                schema: "collection",
                table: "DebtSegmentRules",
                column: "RuleId");

            migrationBuilder.CreateIndex(
                name: "IX_DebtWorkflowTimeAnalytics_Date",
                schema: "collection",
                table: "DebtWorkflowTimeAnalytics",
                column: "Date",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DiscountConfig_RoleId",
                schema: "collection",
                table: "DiscountConfig",
                column: "RoleId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HoldConfig_DefaultDurationId",
                schema: "collection",
                table: "HoldConfig",
                column: "DefaultDurationId");

            migrationBuilder.CreateIndex(
                name: "IX_HoldConfig_Reason",
                schema: "collection",
                table: "HoldConfig",
                column: "Reason",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HoldDurationConfig_Length_Duration",
                schema: "collection",
                table: "HoldDurationConfig",
                columns: new[] { "Length", "Duration" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfiguration_BodyTemplateId",
                schema: "core",
                table: "MessageConfiguration",
                column: "BodyTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfiguration_ContainerTemplateId",
                schema: "core",
                table: "MessageConfiguration",
                column: "ContainerTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfiguration_Name",
                schema: "core",
                table: "MessageConfiguration",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "Channels" });

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfigurationMessagingGroup_MessagingGroupId",
                schema: "core",
                table: "MessageConfigurationMessagingGroup",
                column: "MessagingGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLog_Status",
                schema: "core",
                table: "MessageLog",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLogActivity_MessageLogId",
                schema: "core",
                table: "MessageLogActivity",
                column: "MessageLogId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLogEntry_MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry",
                column: "MessageConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLogEntry_MessageLogId",
                schema: "core",
                table: "MessageLogEntry",
                column: "MessageLogId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageProviderConfig_Name",
                schema: "core",
                table: "MessageProviderConfig",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MessageSchedule_ConfigId",
                schema: "core",
                table: "MessageSchedule",
                column: "ConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageSchedule_Name",
                schema: "core",
                table: "MessageSchedule",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "CronExpression", "Frequency", "FrequencyNumber", "Days", "ActiveOn", "Starting", "RunCount" });

            migrationBuilder.CreateIndex(
                name: "IX_MessagingConfigurationActivity_MessageConfigurationId",
                schema: "core",
                table: "MessagingConfigurationActivity",
                column: "MessageConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_MessagingGroup_Name",
                schema: "core",
                table: "MessagingGroup",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MessagingGroupEntry_MessagingGroupId",
                schema: "core",
                table: "MessagingGroupEntry",
                column: "MessagingGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_MessagingGroupQuery_MessagingGroupId",
                schema: "core",
                table: "MessagingGroupQuery",
                column: "MessagingGroupId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MessagingTemplate_Name",
                schema: "core",
                table: "MessagingTemplate",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "Types" });

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictApplications_ClientId",
                table: "OpenIddictApplications",
                column: "ClientId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictAuthorizations_ApplicationId_Status_Subject_Type",
                table: "OpenIddictAuthorizations",
                columns: new[] { "ApplicationId", "Status", "Subject", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictScopes_Name",
                table: "OpenIddictScopes",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictTokens_ApplicationId_Status_Subject_Type",
                table: "OpenIddictTokens",
                columns: new[] { "ApplicationId", "Status", "Subject", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictTokens_AuthorizationId",
                table: "OpenIddictTokens",
                column: "AuthorizationId");

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictTokens_ReferenceId",
                table: "OpenIddictTokens",
                column: "ReferenceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Placement_FileUploadId",
                schema: "collection",
                table: "Placement",
                column: "FileUploadId");

            migrationBuilder.CreateIndex(
                name: "IX_Placement_ProfileId",
                schema: "collection",
                table: "Placement",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementActivity_PlacementId",
                schema: "collection",
                table: "PlacementActivity",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementNotes_PlacementId",
                schema: "collection",
                table: "PlacementNotes",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChange_PlacementId",
                schema: "collection",
                table: "PlacementStatusChange",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChange_ProfileId",
                schema: "collection",
                table: "PlacementStatusChange",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChange_ReasonId",
                schema: "collection",
                table: "PlacementStatusChange",
                column: "ReasonId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChangeLog_PlacementId",
                schema: "collection",
                table: "PlacementStatusChangeLog",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChangeReasonConfig_Code",
                schema: "collection",
                table: "PlacementStatusChangeReasonConfig",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlacementTag_PlacementId",
                schema: "collection",
                table: "PlacementTag",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementTransaction_PlacementId",
                schema: "collection",
                table: "PlacementTransaction",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementTransaction_TransactionId",
                schema: "collection",
                table: "PlacementTransaction",
                column: "TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSchedule_ConfigId",
                schema: "core",
                table: "ReportSchedule",
                column: "ConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSchedule_MessagingGroupId",
                schema: "core",
                table: "ReportSchedule",
                column: "MessagingGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_State_CountryId_StateCode",
                schema: "core",
                table: "State",
                columns: new[] { "CountryId", "StateCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_SystemReport_ReportScheduleId",
                schema: "core",
                table: "SystemReport",
                column: "ReportScheduleId");

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_ProfileId",
                schema: "collection",
                table: "Transaction",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHistory_TransactionId",
                schema: "collection",
                table: "TransactionHistory",
                column: "TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_UserAccessLog_CreatedByUserId",
                schema: "core",
                table: "UserAccessLog",
                column: "CreatedByUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgentToDebtSegmentMapping",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowAvailabilityStatusChangeLog",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowTask",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowTimeAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AspNetClaims",
                schema: "public");

            migrationBuilder.DropTable(
                name: "AspNetRoleClaims",
                schema: "public");

            migrationBuilder.DropTable(
                name: "AspNetUserClaims",
                schema: "public");

            migrationBuilder.DropTable(
                name: "AspNetUserLogins");

            migrationBuilder.DropTable(
                name: "AspNetUserRoles");

            migrationBuilder.DropTable(
                name: "AspNetUserTokens");

            migrationBuilder.DropTable(
                name: "AutoDiscountConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "BackgroundTaskEventControl",
                schema: "core");

            migrationBuilder.DropTable(
                name: "BackgroundTaskEventLog",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CollectionTemplate",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "Currency",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerActivity",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerAddress",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerContactDetail",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerDevice",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerDeviceVersion",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerDiscount",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerFlag",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerHold",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerInbox",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerIncomeAndExpenditure",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerNoteTemplate",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerOneTimeCode",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerPaymentMethod",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerPromiseToPay",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerSchedule",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerTransaction",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DashboardAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DashboardTimeAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DataProtectionKeys");

            migrationBuilder.DropTable(
                name: "DebtSegmentRules",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtWorkflowAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtWorkflowTimeAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DiscountConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "MessageConfigurationMessagingGroup",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageLogActivity",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageLogEntry",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageProviderConfig",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageSchedule",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessagingConfigurationActivity",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessagingGroupEntry",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessagingGroupQuery",
                schema: "core");

            migrationBuilder.DropTable(
                name: "OpenIddictScopes");

            migrationBuilder.DropTable(
                name: "OpenIddictTokens");

            migrationBuilder.DropTable(
                name: "PlacementActivity",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementNotes",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementStatusChange",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementStatusChangeLog",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementTag",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementTransaction",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "State",
                schema: "core");

            migrationBuilder.DropTable(
                name: "SystemFileUpload",
                schema: "core");

            migrationBuilder.DropTable(
                name: "SystemReport",
                schema: "core");

            migrationBuilder.DropTable(
                name: "TelegramSession",
                schema: "public");

            migrationBuilder.DropTable(
                name: "TransactionHistory",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "UserAccessLog",
                schema: "core");

            migrationBuilder.DropTable(
                name: "AgentWorkflowAvailability",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "HoldConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerPaymentMethodConfiguration",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerNote",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtSegmentRule",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtSegment",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AspNetRoles",
                schema: "public");

            migrationBuilder.DropTable(
                name: "MessageLog",
                schema: "core");

            migrationBuilder.DropTable(
                name: "OpenIddictAuthorizations");

            migrationBuilder.DropTable(
                name: "PlacementStatusChangeReasonConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "Country",
                schema: "core");

            migrationBuilder.DropTable(
                name: "ReportSchedule",
                schema: "core");

            migrationBuilder.DropTable(
                name: "Transaction",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "HoldDurationConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "Placement",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerFlagTemplate",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "OpenIddictApplications");

            migrationBuilder.DropTable(
                name: "MessagingGroup",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerProfile",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AspNetUsers",
                schema: "public");

            migrationBuilder.DropTable(
                name: "CollectionFileUpload",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "MessageConfiguration",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessagingTemplate",
                schema: "core");

            migrationBuilder.DropSequence(
                name: "EntityFrameworkHiLoSequence");

            migrationBuilder.DropSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "collection");

            migrationBuilder.DropSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "core");
        }
    }
}

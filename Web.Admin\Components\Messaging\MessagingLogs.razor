﻿@page "/messaging/logs"
@using LendQube.Entities.Core.BaseUser
@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using System.ComponentModel.DataAnnotations
@using System.Linq.Expressions

@inherits GenericCrudVMTable<MessageLog, MessageLogVM>
@inject NavigationManager navigationManager

@attribute [Authorize(Policy = MessagingNavigation.MessageLogsIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalComponent Policy="@DeletePermission" ModalId="@ClearLogsModalName" ModalCss="width-md" Title=@($"Clear {FormBaseTitle}") >
    <BodyContent>
        Are you sure you want to clear all logs?
    </BodyContent>
    <FooterContent>
        <button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
        <LoadButton Label="Yes" OnClick="ClearLogs" />
    </FooterContent>
</ModalComponent>

@code
{
    private string ClearLogsModalName => "ClearLogsModal";
    protected override void OnInitialized()
    {
        Title = "Messaging";
        SubTitle = "Message Logs";
        FormBaseTitle = "Message Log";
        DeletePermission = MessagingNavigation.MessageLogsDeletePermission;
        QuerySelector = MessageLogVM.SelectorMapping;
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            await base.OnInitializedAsync();

            AddTopButton(DeletePermission, new TopActionButton("Clear Logs", "btn--danger", Icon: "trash-2", ModalName: ClearLogsModalName));

            AddRowButton(MessagingNavigation.MessageLogsIndexPermission, new RowActionButton("View", Icon: "eye", Action: (object row) =>
            {
                var config = row as MessageLogVM;
                navigationManager.NavigateTo($"messaging/logs/{config.Id}");
                return Task.CompletedTask;
            }));
        }
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new()
    {
        ColumnsToAdd = [new ColumnsToAdd<MessageLogVM> { Index = 5, Name = x => x.CreatedByUser, ShowInDelete = true, ShowInFilter = true }],
        ColumnFilterOptions =
        [
            new()
            {
                ColumnName = nameof(MessageLogVM.Configs),
                Options = Service.CrudService.Db.ManySelect(Query<MessageConfiguration, string>.Select(x => x.Name))
            }
        ]
    });


    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        var filter = $"%{filterAndPage.TextFilter.Trim()}%";
        var isNumber = filterAndPage.TextFilter.Trim().IsNumber();
        if (isNumber)
        {
            var users = Service.CrudService.Db.GetQuery(Query<ApplicationUser, string>.Where(x => EF.Functions.ILike(x.PhoneNumber, filter)).Select(x => x.UserName));
            var groupIds = Service.CrudService.Db.GetQuery(Query<MessagingGroupEntry, long>.Where(x => x.PhoneNumbers.Any(y => EF.Functions.ILike(y.Number, filter))).Select(x => x.MessagingGroupId));
            Service.PrimaryCriteria = x => x.MessageLogEntries.Any(y => y.Recipients.Any(z => users.Contains(z.UserId) || EF.Functions.ILike(z.AdHoc.PhoneNumber.Number, filter))) ||
            x.MessageLogEntries.Any(y => y.Recipients.Any(z => groupIds.Contains(z.MessagingGroupId.Value)));
        }
        else
        {
            var users = Service.CrudService.Db.GetQuery(Query<ApplicationUser, string>.Where(x => EF.Functions.ILike(x.Email, filter) || EF.Functions.ILike(x.FullName, filter)).Select(x => x.UserName));
            var groupIds = Service.CrudService.Db.GetQuery(Query<MessagingGroupEntry, long>.Where(x => x.Emails.Any(y => EF.Functions.ILike(y, filter)) || EF.Functions.ILike(x.Name, filter)).Select(x => x.MessagingGroupId));
            Service.PrimaryCriteria = x => x.MessageLogEntries.Any(y => y.Recipients.Any(z => users.Contains(z.UserId) || EF.Functions.ILike(z.AdHoc.Email, filter) || EF.Functions.ILike(z.AdHoc.Name, filter))) ||
            x.MessageLogEntries.Any(y => y.Recipients.Any(z => groupIds.Contains(z.MessagingGroupId.Value)));
        }

        Expression<Func<MessageLog, bool>> where = x => EF.Functions.ILike(x.OriginatedFrom, filter)
            || x.MessageLogEntries.Any(y => EF.Functions.ILike(y.Name, filter)
            || EF.Functions.ILike(y.Subject, filter)
            || EF.Functions.ILike(y.Config.Description, filter)
            );

        Service.PrimaryCriteria = Service.PrimaryCriteria.CombineWithOrElse(where);
    }

    protected override ValueTask<bool> SubmitDelete(MessageLogVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);


    private async Task ClearLogs()
    {
        var result = await Service.CrudService.Db.TruncateTable<MessageLog>(table.Cancel);
        await JSRuntime.CloseModal(ClearLogsModalName, Cancel);
        await table.Refresh();

        TableMessage.Set(result, "All logs cleared", "Clearing logs failed");
    }
}

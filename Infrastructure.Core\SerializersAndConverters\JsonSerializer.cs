﻿using System.Text.Json;
using System.Text.Json.Nodes;
using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.Core.SerializersAndConverters
{
    public static class JsonOptions
    {
        public static JsonSerializerOptions CamelCaseAndEnumAsStringOptions => new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new JsonStringEnumConverter() },
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        public static JsonSerializerOptions NoCharacterConversionOptions => new()
        {
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
        };

        public static JsonSerializerOptions SnakeCaseLowerAndEnumAsStringOptions => new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            Converters = { new JsonStringEnumConverter() },
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };

        public static JsonSerializerOptions CaseInsensitive => new()
        {
            PropertyNameCaseInsensitive = true,
            Converters = { new JsonStringEnumConverter() },
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
    }
}

namespace LendQube.Infrastructure.Core.SerializersAndConverters.NodaTimeSerializers
{
    public static class PostgreJsonSerializer
    {
        public static JsonSerializerOptions Options => new() { PropertyNameCaseInsensitive = true, Converters = { new JsonStringEnumConverter(), new NullableInstantConverter(), new InstantConverter(), new NullableTimeOnlyConverter(), new NullableDateTimeConverter() } };
        public static object? Deserialize(JsonObject data, Type type) => JsonSerializer.Deserialize(data.ToJsonString(), type, Options);
        public static T Deserialize<T>(string data) => JsonSerializer.Deserialize<T>(data, Options);
    }
}

namespace LendQube.Infrastructure.Core.SerializersAndConverters.CaseInsensitiveSerializer
{
    public static class JsonSerializer
    {
        public static JsonSerializerOptions Options => new() { PropertyNameCaseInsensitive = true, Converters = { new JsonStringEnumConverter() }, DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull };
        public static string ToNoFormatJsonString(object data) => System.Text.Json.JsonSerializer.Serialize(data);
        public static string ToJsonString(object data, JsonSerializerOptions options = null) => System.Text.Json.JsonSerializer.Serialize(data, options ?? Options);
        public static T Deserialize<T>(string data, JsonSerializerOptions options = null) => System.Text.Json.JsonSerializer.Deserialize<T>(data, options ?? Options);
    }
}

namespace LendQube.Infrastructure.Core.SerializersAndConverters.CamelCaseSerializer
{
    public static class JsonSerializer
    {
        private static JsonSerializerOptions Options => new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new JsonStringEnumConverter(), new NullableDateTimeConverter(), new DecimalConverter() },
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
        public static string ToJsonString(object data) => System.Text.Json.JsonSerializer.Serialize(data, Options);
        public static T Deserialize<T>(string data) => System.Text.Json.JsonSerializer.Deserialize<T>(data, Options);
    }


    public static class JsonSerializerDateOnly
    {
        private static JsonSerializerOptions Options => new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            Converters = { new JsonStringEnumConverter(), new DateOnlyDateTimeConverter(), new DecimalConverter() },
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull
        };
        public static string ToJsonString(object data) => System.Text.Json.JsonSerializer.Serialize(data, Options);
        public static T Deserialize<T>(string data) => System.Text.Json.JsonSerializer.Deserialize<T>(data, Options);
    }
}

namespace LendQube.Infrastructure.Core.SerializersAndConverters.AllowNullSerializer
{
    public static class JsonSerializer
    {
        private static JsonSerializerOptions Options => new() { PropertyNameCaseInsensitive = true, Converters = { new JsonStringEnumConverter(), new NullableDateTimeConverter() } };
        public static string ToNoFormatJsonString(object data) => System.Text.Json.JsonSerializer.Serialize(data);
        public static string ToJsonString(object data) => System.Text.Json.JsonSerializer.Serialize(data, Options);
        public static T Deserialize<T>(string data) => System.Text.Json.JsonSerializer.Deserialize<T>(data, Options);
    }
}

namespace LendQube.Infrastructure.Core.SerializersAndConverters.SnakeCaseLowerSerializer
{
    public static class JsonSerializer
    {
        private static JsonSerializerOptions Options => new()
        {
            PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower,
            DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
            Converters = { new JsonStringEnumConverter() }
        };
        public static string ToJsonString(object data) => System.Text.Json.JsonSerializer.Serialize(data, Options);
        public static T Deserialize<T>(string data) => System.Text.Json.JsonSerializer.Deserialize<T>(data, Options);
    }
}
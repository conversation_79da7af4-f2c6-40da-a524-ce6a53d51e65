﻿using LendQube.Entities.Core.Reporting;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Core.Reporting;

public interface IReportingService
{
    public string UPLOADKEY { get; }
    public Dictionary<string, IQueryable<object>> Build(SystemReport data);
}

public interface IReportDescribesExcelFormat : IReportingService
{
    Task SaveReportAsCustomExcelOrCsv(ExcelPackage package, SystemReport data, Dictionary<string, IQueryable<object>> reportData, CancellationToken ct);
}
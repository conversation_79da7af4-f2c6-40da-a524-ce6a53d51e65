﻿using System.Diagnostics;
using Microsoft.AspNetCore.Http;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class HttpContextAccessorExtension
{
    public const string USERNAME_KEY = "LoggedInUsername";
    public static string GetIpAddress(this IHttpContextAccessor context)
    {
        var ip = context?.HttpContext?.Request?.GetHeader("CF-Connecting-IP");
        if (string.IsNullOrEmpty(ip))
            ip = context?.HttpContext?.Connection?.RemoteIpAddress?.ToString() ?? context?.HttpContext?.Connection?.LocalIpAddress?.ToString();

        return ip;
    }

    public static string GetAuthenticatedUsername(this IHttpContextAccessor context)
    {
        return context?.HttpContext?.User?.Identity?.IsAuthenticated ?? false ? context?.HttpContext?.User.Identity.Name : "anonymous";
    }
}

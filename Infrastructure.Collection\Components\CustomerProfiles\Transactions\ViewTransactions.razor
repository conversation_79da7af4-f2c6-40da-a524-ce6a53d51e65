﻿@page "/customers/transactions/view/{TxnId}"

@using LendQube.Entities.Collection.Customers
@using LendQube.Infrastructure.Collection.Schedules
@using LendQube.Infrastructure.Core.Components.Timeline
@using LendQube.Infrastructure.Core.Database.Repository


@attribute [Authorize(Policy = ManageCustomersNavigation.TransactionsViewPermission)]

@inherits AuthComponentBase
@inject IUnitofWork uow
@inject IJSRuntime jSRuntime
@inject ManageScheduleService manageScheduleService

<PageTitle>@Title</PageTitle>
<StatusMessage @ref="@TableMessage" />

<div class="pg-actions">
    <ul class="breadcrumbs">
        <li class="__item">
            <a href="/customers/transactions">
                <i data-feather="arrow-left"></i>
            </a>
        </li>
        <li class="__item">
            <a>@Title</a>
        </li>
    </ul>
    <div class="actions-item ml-auto">
        @if (Data.Status != TransactionStatus.Completed)
        {
            <LoadButton Label="Re-confirm Status" OnClick="ReconfirmPayment" Css="btn--default" />
        }
    </div>
</div>
<div class="pg-row grid loan-grid grid-tab-1">
    <div class="info-col">
        <div class="card info-item">
            <div class="__header flex __justify-between">
                <div class="title">
                    <span class="__title">@Data.Id</span>
                    <span class="__amount">@Data.CurrencySymbol @Data.Amount.ToString("n2")</span>
                    <div class="status">
                        @switch (Data.Status)
                        {
                            case TransactionStatus.Initiated or TransactionStatus.Validated:
                                <span class="lz __yellow">@Data.Status.GetDisplayName()</span>
                                break;
                            case TransactionStatus.Completed:
                                <span class="lz __green">@Data.Status.GetDisplayName()</span>
                                break;
                            case TransactionStatus.Failed:
                                <span class="lz __red">@Data.Status.GetDisplayName()</span>
                                break;
                            case TransactionStatus.Queued or TransactionStatus.Processing or TransactionStatus.Successful:
                                <span class="lz __purple">@Data.Status.GetDisplayName()</span>
                                break;
                            default:
                                <span class="lz __default">@Data.Status.GetDisplayName()</span>
                                break;
                        }
                    </div>
                </div>
            </div>
            <div class="detail-wrapper grid-2">

                <div class="detail-item">
                    <span class="label">Amount Paid</span>
                    <span class="value">@Data.CurrencySymbol @Data.TotalAmountPaid.ToString("n2")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Total Amount Payable</span>
                    <span class="value">@Data.CurrencySymbol @Data.TotalAmountPayable.ToString("n2")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Fee</span>
                    <span class="value">@Data.CurrencySymbol @Data.Fee.ToString("n2")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Discount</span>
                    <span class="value">@Data.CurrencySymbol @Data.Discount.ToString("n2")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Quantity</span>
                    <span class="value">@Data.Quantity</span>
                </div>
                <div class="detail-item">
                    <span class="label">Payment Provider</span>
                    <span class="value">@Data.Provider</span>
                </div>
                <div class="detail-item">
                    <span class="label">Customer</span>
                    <span class="value">@Data.Profile?.FirstName @Data.Profile?.LastName</span>
                </div>
                <div class="detail-item">
                    <span class="label">Provider Reference</span>
                    <span class="value">@Data.ProviderReference</span>
                </div>
                <div class="detail-item">
                    <span class="label">Purpose</span>
                    <span class="value">@Data.Purpose</span>
                </div>
                <div class="detail-item">
                    <span class="label">Created on</span>
                    <span class="value"><LocalTime Value="Data.CreatedDate"/></span>
                </div>
            </div>
        </div>

        <div class="card activity-item">
            <div class="accordion" id="activityAccordion">
                <div class="accordion-item">
                    <span class="accordion-header" id="activityHeading">
                        <button class="accordion-button" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#collapseActivity" aria-expanded="true"
                        aria-controls="collapseActivity">
                            Activity
                        </button>
                    </span>
                    <div id="collapseActivity"
                    class="accordion-collapse collapse show"
                    aria-labelledby="activityHeading"
                    data-bs-parent="#activityAccordion">
                        <div class="accordion-body">
                            @if (!string.IsNullOrEmpty(Data.Id))
                            {
                                <ActivityTimeline T="TransactionHistory" LoadData="LoadActivity" @ref=activityTimeline />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="details-col">
        <div class="card">
            <div class="accordion loan-details" id="loanAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="detailsHeader">
                        <button class="accordion-button" type="button"
                        data-bs-toggle="collapse"
                        data-bs-target="#detailsCollapse" aria-expanded="true"
                        aria-controls="detailsCollapse">
                            <i data-feather="file-text"></i>
                            Payment Data
                        </button>
                    </h2>
                    <div id="detailsCollapse"
                    class="accordion-collapse collapse show"
                    aria-labelledby="detaillsHeader"
                    data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <div class="app_details">
                                <ul class="nav nav-tabs" id="appTab" role="tablist">
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link active"
                                        id="personal-tab" data-bs-toggle="tab"
                                        data-bs-target="#personal" type="button"
                                        role="tab" aria-controls="personal"
                                        aria-selected="true">
                                            User Data
                                        </button>
                                    </li>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link" id="employment-tab"
                                        data-bs-toggle="tab"
                                        data-bs-target="#employment"
                                        type="button" role="tab"
                                        aria-controls="employment"
                                        aria-selected="false">
                                            Fields
                                        </button>
                                    </li>
                                </ul>
                                <div class="tab-content" id="myTabContent">
                                    <div class="tab-pane fade show active"
                                    id="personal" role="tabpanel"
                                    aria-labelledby="personal-tab">
                                        <div class="detail-wrapper grid-4 grid-tab-3">
                                            @foreach (var item in Data.UserData)
                                            {
                                                <div class="detail-item">
                                                    <span class="label">
                                                        @item.Key
                                                    </span>
                                                    <span class="value">@item.Value</span>
                                                </div>

                                            }
                                        </div>
                                    </div>
                                    <div class="tab-pane fade" id="employment"
                                    role="tabpanel"
                                    aria-labelledby="employment-tab">
                                        <div class="detail-wrapper grid-4 grid-tab-3">
                                            @foreach (var item in Data.Fields)
                                            {
                                                <div class="detail-item">
                                                    <span class="label">
                                                        @item.Key
                                                    </span>
                                                    <span class="value">@item.Value</span>
                                                </div>

                                            }
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


@code {
    [Parameter]
    public string TxnId { get; set; }

    private string Title { get; set; } = "View Transaction";

    private Transaction Data { get; set; } = new();

    protected StatusMessage TableMessage { get; set; }

    private ActivityTimeline<TransactionHistory> activityTimeline;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            Data = await uow.Db.OneAsync(Query<Transaction>.Where(x => x.Id == TxnId).Include(x => x.Include(y => y.Profile)), Cancel);
            await jSRuntime.RunFeather(Cancel);
            StateHasChanged();
        }
    }

    private ValueTask<TypedBasePageList<TransactionHistory>> LoadActivity(DataFilterAndPage filterAndPage, GenericSpecificationService<TransactionHistory> service, CancellationToken ct)
    {
        service.PrimaryCriteria = x => x.TransactionId == Data.Id;
        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            service.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Title, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Activity, filterAndPage.TextFilter) || EF.Functions.ILike(x.CreatedByUser, filterAndPage.TextFilter));
        }

        return service.CrudService.GetTypeBasedPagedData(service, filterAndPage, ct: ct);
    }


    protected async Task ReconfirmPayment()
    {
        Data.Status = TransactionStatus.Validated;
        uow.Db.Update(Data);
        await uow.SaveAsync(Cancel);

        var result = await manageScheduleService.ConfirmPayment(Data.ProfileId, new Payments.ProcessTransactionRequestVM(Data.Id), Cancel);

        if (result.IsSuccessful)
        {
            TableMessage.Success(result.Message);
            await activityTimeline.Refresh();
        }
        else
            TableMessage.Error(result.Message);

        Data = await uow.Db.OneAsync(Query<Transaction>.Where(x => x.Id == TxnId).Include(x => x.Include(y => y.Profile)), Cancel);
        StateHasChanged();
    }
}
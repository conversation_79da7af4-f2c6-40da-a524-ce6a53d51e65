﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.SerializersAndConverters.CaseInsensitiveSerializer;
using LendQube.Infrastructure.Core.Telemetry;
using System.Net.Http.Headers;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class MailgunProvider : AbstractMessageProvider, IEmailProvider
{
    public const string Name = "Mailgun";
    protected override MessageChannel SupportedChannel => MessageChannel.Email;
    private readonly HttpClient httpClient;
    private readonly DefaultAppConfig config;
    private readonly ILogManager<MailgunProvider> logger;
    private readonly int bulkLimit = 1000;

    public MailgunProvider(IUnitofWork uow, HttpClient httpClient, DefaultAppConfig config, ILogManager<MailgunProvider> logger) : base(uow)
    {
        this.httpClient = httpClient;
        this.config = config;
        this.logger = logger;

        if (config.Mailgun != null)
        {
            Config = MessagingCompiledQueries.GetProviderConfig(uow, Name) ?? new ProviderConfigVM { Disabled = true };
        }
        else
        {
            Config = new ProviderConfigVM { Disabled = true };
        }
    }

    public override ProviderConfigVM Config { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        Dictionary<long, MessageStatus> result = [];
        var leadingMessage = messages[0];

        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        var name = config.Mailgun.SenderName;
        var address = config.Mailgun.From;

        await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count, CancellationToken = ct }, async (message, ct) =>
        {
            message.Data = message.Data.Where(x => x.HasEmail).ToList();
            if (message.Data.Count == 0)
                return;

            if (!string.IsNullOrEmpty(message.SenderName))
                name = message.SenderName;
            if (!string.IsNullOrEmpty(message.SenderEmail))
                address = message.SenderEmail;

            var from = $"{name} <{address}>";

            if (message.HasSeparateTemplateValues && (message.Data.Any(x => !x.Attachments.IsNullOrEmpty()) || message.Data.Any(x => !x.CopiedIn.IsNullOrEmpty())))
            {
                foreach (var (recipient, index) in message.Data.Select((x, i) => (x, i)))
                {
                    result[message.MessageId + index] = await ProcessSingleMessage(from, message, recipient, ct);
                }

                return;
            }

            ReplaceHtmlTemplateSyntax(message, message.TemplateValues.IsNullOrEmpty() ? message.Data[0].TemplateValues : message.TemplateValues);

            var data = message.Data.SelectMany(x => x.Emails.Select(y => new SinglePreparedMessageVM
            {
                UserId = x.UserId,
                Email = y,
                Name = x.Name,
                TemplateValues = x.TemplateValues,
                Subject = x.Subject,
                HtmlTemplate = x.HtmlTemplate,
            })).OrderBy(x => x.Email);

            var total = data.Count();
            var skip = 0;
            while (total > 0)
            {
                var toSkip = skip * bulkLimit;
                var pagedData = data.Skip(toSkip).Take(bulkLimit);
                if(message.HasSeparateTemplateValues)
                {
                    var recipientVariables = pagedData.ToDictionary(x => x.Email, x => x.TemplateValues.ToDictionary(t => t.Key.ToLowerInvariant(), t => t.Value));
                    result[message.MessageId + skip] = await ProcessBatchMessage(from, recipientVariables.Select(x => x.Key), message, recipientVariables, ct);
                }
                else
                {
                    result[message.MessageId + skip] = await ProcessBatchMessage(from, pagedData.Select(m => m.Email), message, null, ct);
                }

                skip++;
                total -= bulkLimit;
            }
        });

        var status = result.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, result, null, null, ct);
        return status;
    }

    public async Task<MessageStatus> ProcessSingleMessage(string from, PreparedMessageVM message, SinglePreparedMessageVM vm, CancellationToken ct)
    {
        bool successful = false;
        try
        {
            SubstituteSingleReceiverHtmlTemplateTypeKeys(message, vm);
            var request = new HttpRequestMessage(HttpMethod.Post, "messages");

            var formData = new MultipartFormDataContent
            {
                { new StringContent(from), "from" },
                { new StringContent(vm.Subject), "subject" },
                { new StringContent(vm.HtmlTemplate), "html" },
                { new StringContent(string.Join(',', vm.Emails)), "to" }
            };

            if (!vm.CopiedIn.IsNullOrEmpty())
            {
                var cc = vm.CopiedIn.Where(c => !c.BlindCopy).Select(x => x.Email);
                if (!cc.IsNullOrEmpty())
                {
                    formData.Add(new StringContent(string.Join(',', cc)), "cc");
                }

                var bcc = vm.CopiedIn.Where(c => c.BlindCopy).Select(x => x.Email);
                if (!bcc.IsNullOrEmpty())
                {
                    formData.Add(new StringContent(string.Join(',', bcc)), "bcc");
                }
            }

            if (!vm.Attachments.IsNullOrEmpty())
            {
                foreach (var item in vm.Attachments)
                {
                    var fileType = Path.GetExtension(item.Url);
                    var bytes = await httpClient.ReadPhysicalFileAsByteArray(item.Url, ct);
                    if (bytes != null && bytes.Length > 0)
                    {
                        var fileContent = new ByteArrayContent(bytes);
                        fileContent.Headers.ContentType = MediaTypeHeaderValue.Parse(ContentTypeHelper.GetContentType(fileType));
                        formData.Add(fileContent, "attachment", item.FileName + fileType);
                    }
                }
            }

            request.Content = formData;
            var response = await httpClient.SendAsync(request, ct);
            successful = response?.IsSuccessStatusCode ?? false;

        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Messaging, EventAction.SendMail, ex, $"Error occured while sending email {message.MessageId}");
        }

        return successful.ToMessageStatus();
    }

    public async Task<MessageStatus> ProcessBatchMessage(string from, IEnumerable<string> emails, PreparedMessageVM message,
        Dictionary<string, Dictionary<string, string>> recipientVariables, CancellationToken ct)
    {
        bool successful = false;
        try
        {
            var request = new HttpRequestMessage(HttpMethod.Post, "messages");
            var formData = new MultipartFormDataContent
            {
                { new StringContent(from), "from" }
            };

            foreach (var recipient in emails)
            {
                formData.Add(new StringContent(recipient), "to");
            }


            formData.Add(new StringContent(message.Subject), "subject");
            formData.Add(new StringContent(message.HtmlTemplate), "html");
            if (recipientVariables is not null)
            {
                formData.Add(new StringContent(JsonSerializer.ToJsonString(recipientVariables)), "recipient-variables");
            }


            if (!message.CopiedIn.IsNullOrEmpty())
            {
                var cc = message.CopiedIn.Where(c => !c.BlindCopy).Select(x => x.Email);
                if(!cc.IsNullOrEmpty())
                {
                    formData.Add(new StringContent(string.Join(',', cc)), "cc");
                }

                var bcc = message.CopiedIn.Where(c => c.BlindCopy).Select(x => x.Email);
                if (!bcc.IsNullOrEmpty())
                {
                    formData.Add(new StringContent(string.Join(',', bcc)), "bcc");
                }
            }

            request.Content = formData;
            var response = await httpClient.SendAsync(request, ct);
            successful = response?.IsSuccessStatusCode ?? false;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Messaging, EventAction.SendMail, ex, $"Error occured while sending sms {message.MessageId}");
        }
        return successful.ToMessageStatus();
    }

    private static void ReplaceHtmlTemplateSyntax(PreparedMessageVM message, List<TemplateKeyValue> templateValues)
    {
        if (!message.HasHtml || templateValues.IsNullOrEmpty())
        {
            return;
        }

        foreach (var item in templateValues)
        {
            var originalTemplateKey = $"{{{item.Key.ToLowerInvariant()}}}";
            var newtemplateKey = $"%recipient.{item.Key.ToLowerInvariant()}%";

            message.HtmlTemplate = message.HtmlTemplate.Replace(originalTemplateKey, newtemplateKey);

            message.Subject = message.Subject.Replace(originalTemplateKey, newtemplateKey);
        }
    }
}

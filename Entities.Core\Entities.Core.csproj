﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
		<RootNamespace>LendQube.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="FastGuid" Version="1.3.0" />
		<PackageReference Include="Microsoft.Extensions.Identity.Stores" Version="9.0.6" />
		<PackageReference Include="MongoDB.Bson" Version="3.4.0" />
		<PackageReference Include="NodaTime" Version="3.2.2" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
		<PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.NodaTime" Version="9.0.4" />
	</ItemGroup>

	<ItemGroup>
		<InternalsVisibleTo Include="Infrastructure.Core" />
	</ItemGroup>
</Project>

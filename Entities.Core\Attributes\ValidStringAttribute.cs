﻿using System.ComponentModel.DataAnnotations;
using System.Runtime.CompilerServices;
using System.Text.RegularExpressions;
using LendQube.Entities.Core.Extensions;

namespace LendQube.Entities.Core.Attributes;


[AttributeUsage(AttributeTargets.Property | AttributeTargets.Parameter, AllowMultiple = false, Inherited = true)]
public partial class ValidStringAttribute(ValidStringRule rule, [CallerMemberName] string propertyName = null) : ValidationAttribute
{
    public override bool IsValid(object? value)
    {
        if (value is not null && value is not string)
            throw new NotSupportedException("Type must be string");

        if (string.IsNullOrEmpty(value as string))
            return true;


        var regex = rule switch
        {
            ValidStringRule.OnlyTextWithSpacing => TextWithSpacing(),
            ValidStringRule.OnlyTextAndNumber => TextAndNumberOnly(),
            ValidStringRule.OnlyTextAndNumberWithSpacing => TextAndNumberWithSpacing(),
            ValidStringRule.OnlyNumber => NumberOnly(),
            ValidStringRule.OnlyNumberOrSpecialCharactersWithSpacing => NumberOrSpecialCharactersWithSpaceOnly(),
            ValidStringRule.OnlyTextWithSpecialCharacters => TextWithSpecialCharactersOnly(),
            ValidStringRule.OnlyTextWithSpecialCharactersWithSpacing => TextOrSpecialCharactersWithSpacingOnly(),
            ValidStringRule.OnlyNumberWithSpacing => NumberWithSpacing(),
            ValidStringRule.OnlyTextOrNumberOrSpecialCharacters => OnlyTextOrNumberOrSpecialCharacters(),
            ValidStringRule.OnlyTextOrNumberOrSpecialCharactersWithSpacing => TextOrNumberOrSpecialCharactersWithSpacingOnly(),
            ValidStringRule.NumberWithPlusOnly => NumberWithPlusOnly(),
            ValidStringRule.OnlyNumbersAndDot => NumbersWithDotOnly(),
            ValidStringRule.NoScriptTag => NoScriptTag(),
            ValidStringRule.TextNumbersDashesAndUnderscoreOnly => TextNumbersDashesAndUnderscoreOnly(),
            ValidStringRule.OnlyTextAndUnderscore => TextAndUnderscoreOnly(),
            ValidStringRule.OnlyTextWithHyphenAndApostrophe => TextWithHyphenAndApostropheOnly(),
            ValidStringRule.OnlyTextAndHyphenAndApostropheWithSpacing => TextAndHyphenAndApostropheWithSpacingOnly(),
            ValidStringRule.OnlyTextOrNumberOrCommaOrPeriodOrApostropheOrHyphenWithSpacing => TextOrNumberOrCommaOrPeriodOrApostropheOrHyphenWithSpacing(),
            _ => TextOnly(),
        };

        return rule == ValidStringRule.NoScriptTag ? !regex.IsMatch(value.ToString()) : regex.IsMatch(value.ToString());
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (IsValid(value))
        {
            return ValidationResult.Success;
        }

        return new ValidationResult($"{rule.GetDisplayName()} is allowed for {propertyName.SplitOnUpper()} field", [validationContext.MemberName]);
    }

    [GeneratedRegex("^[a-zA-Z]*$")]
    private static partial Regex TextOnly();

    [GeneratedRegex("^[a-zA-Z ]*$")]
    private static partial Regex TextWithSpacing();

    [GeneratedRegex("^[a-zA-Z0-9]*$")]
    private static partial Regex TextAndNumberOnly();

    [GeneratedRegex("^[a-zA-Z0-9 ]*$")]
    private static partial Regex TextAndNumberWithSpacing();

    [GeneratedRegex("^[0-9]*$")]
    private static partial Regex NumberOnly();

    [GeneratedRegex("^[0-9:/+() -]*$")]
    private static partial Regex NumberOrSpecialCharactersWithSpaceOnly();

    [GeneratedRegex("^[a-zA-Z:/_@{}.+()]*$")]
    private static partial Regex TextWithSpecialCharactersOnly();

    [GeneratedRegex("^[a-zA-Z:/,.@{}+ ]*$")]
    private static partial Regex TextOrSpecialCharactersWithSpacingOnly();

    [GeneratedRegex("^[0-9 ]*$")]
    private static partial Regex NumberWithSpacing();

    [GeneratedRegex("^[a-zA-Z0-9:/+.{} -]*$")]
    private static partial Regex TextOrNumberOrSpecialCharactersWithSpacingOnly();

    [GeneratedRegex("^[a-zA-Z0-9:/.{} ]*$")]
    private static partial Regex OnlyTextOrNumberOrSpecialCharacters();

    [GeneratedRegex(@"<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>")]
    private static partial Regex NoScriptTag();

    [GeneratedRegex("^[+0-9]*$")]
    private static partial Regex NumberWithPlusOnly();

    [GeneratedRegex("^[a-zA-Z0-9\\-_]*$")]
    private static partial Regex TextNumbersDashesAndUnderscoreOnly();

    [GeneratedRegex("^[0-9.]*$")]
    private static partial Regex NumbersWithDotOnly();

    [GeneratedRegex("^[a-zA-Z_]*$")]
    private static partial Regex TextAndUnderscoreOnly();

    [GeneratedRegex("^[a-zA-Z-']*$")]
    private static partial Regex TextWithHyphenAndApostropheOnly();

    [GeneratedRegex("^[a-zA-Z-' ]*$")]
    private static partial Regex TextAndHyphenAndApostropheWithSpacingOnly();


    [GeneratedRegex("^[a-zA-Z0-9.,'\\- ]*$")]
    private static partial Regex TextOrNumberOrCommaOrPeriodOrApostropheOrHyphenWithSpacing();
}

public enum ValidStringRule
{
    OnlyText,
    OnlyTextWithSpacing,
    OnlyTextAndNumber,
    OnlyTextAndNumberWithSpacing,
    OnlyNumber,
    OnlyNumberOrSpecialCharactersWithSpacing,
    OnlyTextWithSpecialCharacters,
    OnlyTextWithSpecialCharactersWithSpacing,
    OnlyNumberWithSpacing,
    OnlyTextOrNumberOrSpecialCharacters,
    OnlyTextOrNumberOrSpecialCharactersWithSpacing,
    NoScriptTag,
    NumberWithPlusOnly,
    TextNumbersDashesAndUnderscoreOnly,
    OnlyNumbersAndDot,
    OnlyTextAndUnderscore,
    OnlyTextWithHyphenAndApostrophe,
    OnlyTextAndHyphenAndApostropheWithSpacing,
    OnlyTextOrNumberOrCommaOrPeriodOrApostropheOrHyphenWithSpacing
}
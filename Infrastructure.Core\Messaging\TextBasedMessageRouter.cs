﻿using System.Collections.Concurrent;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Messaging.Providers;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class TextBasedMessageRouter(IUnitofWork uow, Func<MessageChannel, List<IMessageProvider>> provider) : IMessageProvider
{
    public ProviderConfigVM Config => new() { Disabled = false };

    public async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        Dictionary<MessageChannel, MessageStatus> results = [];
        Dictionary<MessageChannel, Task<MessageStatus>> dispatchResults = [];
        ConcurrentDictionary<MessageChannel, List<PreparedMessageVM>> groupedMessages = [];

        //convert these channels to their single counterparts
        IReadOnlyList<MessageChannel> combinedPushToSingle = [MessageChannel.PushNotification, MessageChannel.PushNotificationAndSms, MessageChannel.PushNotificationAndEmail, MessageChannel.PushNotificationOrEmail];
        IReadOnlyList<MessageChannel> combinedSmsToSingle = [MessageChannel.Sms, MessageChannel.SmsAndEmail, MessageChannel.SmsOrEmail, MessageChannel.EmailOrSms];


        //prepare messages grouped by channel
        Parallel.ForEach(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count, CancellationToken = ct }, (message) =>
        {
            if (message.HasSeparateTemplateValues)
            {
                message.Data.ForEach(receiver =>
                {
                    SubstituteSingleReceiverTextTemplateTypeKeys(message, receiver);
                });
            }
            else
            {
                SubstituteMultipleReceiverTextTemplateTypeKeys(message);
            }

            message.Data.RemoveAll(x => x.PhoneNumbers.IsNullOrEmpty());
            if (message.Data.IsNullOrEmpty())
                return;

            if (combinedPushToSingle.Any(x => message.Channel.HasFlag(x)))
                AddToChannel(MessageChannel.PushNotification, message, groupedMessages);

            if (combinedSmsToSingle.Any(x => message.Channel.HasFlag(x)))
                AddToChannel(MessageChannel.Sms, message, groupedMessages);

            if (message.Channel.HasFlag(MessageChannel.PushNotificationOrSms))
                AddToChannel(MessageChannel.PushNotificationOrSms, message, groupedMessages);

            if (message.Channel.HasFlag(MessageChannel.WhatsApp))
                AddToChannel(MessageChannel.WhatsApp, message, groupedMessages);

            if (message.Channel.HasFlag(MessageChannel.Telegram))
                AddToChannel(MessageChannel.Telegram, message, groupedMessages);

        });

        if (groupedMessages.IsEmpty)
            return MessageStatus.Failed;

        List<IMessageProvider> smsProviders = groupedMessages.Any(x => x.Key.HasFlag(MessageChannel.Sms)) || groupedMessages.Any(x => x.Key.HasFlag(MessageChannel.PushNotificationOrSms))
            ? provider(MessageChannel.Sms).Where(x => !x.Config.Disabled).ToList() : null,

        pushNotificationProviders = groupedMessages.Any(x => x.Key.HasFlag(MessageChannel.PushNotification)) || groupedMessages.Any(x => x.Key.HasFlag(MessageChannel.PushNotificationOrSms))
            ? provider(MessageChannel.PushNotification).Where(x => !x.Config.Disabled).ToList() : null,

        whatsAppProviders = groupedMessages.Any(x => x.Key.HasFlag(MessageChannel.WhatsApp)) ? provider(MessageChannel.WhatsApp).Where(x => !x.Config.Disabled).ToList() : null,

        telegramProviders = groupedMessages.Any(x => x.Key.HasFlag(MessageChannel.Telegram)) ? provider(MessageChannel.Telegram).Where(x => !x.Config.Disabled).ToList() : null;

        ConcurrentBag<MessageLogActivity> activity = [];
        var messageId = messages[0].MessageId;

        if (!pushNotificationProviders.IsNullOrEmpty() && groupedMessages.TryGetValue(MessageChannel.PushNotification, out var pushMessage))
            dispatchResults[MessageChannel.PushNotification] = Dispatch(activity, messageId, MessageChannel.PushNotification, pushNotificationProviders, pushMessage, ct);

        if (!smsProviders.IsNullOrEmpty() & groupedMessages.TryGetValue(MessageChannel.Sms, out var smsMessage))
            dispatchResults[MessageChannel.Sms] = Dispatch(activity, messageId, MessageChannel.Sms, smsProviders, smsMessage, ct);

        if (!whatsAppProviders.IsNullOrEmpty())
            dispatchResults[MessageChannel.WhatsApp] = Dispatch(activity, messageId, MessageChannel.WhatsApp, whatsAppProviders, groupedMessages[MessageChannel.WhatsApp], ct);

        if (!telegramProviders.IsNullOrEmpty())
            dispatchResults[MessageChannel.Telegram] = Dispatch(activity, messageId, MessageChannel.Telegram, telegramProviders, groupedMessages[MessageChannel.Telegram], ct);

        if (dispatchResults.Count > 0)
        {
            await Task.WhenAll(dispatchResults.Values);

            foreach (var item in dispatchResults)
            {
                results[item.Key] = await item.Value;
            }
        }

        //PushNotificationOrSms channel cannot dispatch concurrently due to OR dependency
        if (groupedMessages.TryGetValue(MessageChannel.PushNotificationOrSms, out var pushOrSmsMessage))
        {
            results[MessageChannel.PushNotificationOrSms] = pushNotificationProviders.IsNullOrEmpty() ? MessageStatus.Failed :
                await Dispatch(activity, messageId, MessageChannel.PushNotification, pushNotificationProviders, pushOrSmsMessage, ct);

            if (results[MessageChannel.PushNotificationOrSms] != MessageStatus.Sent && !smsProviders.IsNullOrEmpty())
                results[MessageChannel.PushNotificationOrSms] = await Dispatch(activity, messageId, MessageChannel.Sms, smsProviders, pushOrSmsMessage, ct);
        }

        if (!activity.IsEmpty)
        {
            uow.Db.InsertBulk(activity, ct);
            await uow.SaveAsync(ct);
        }

        return results.ToMessageStatus();
    }

    private static void AddToChannel(MessageChannel channel, PreparedMessageVM message, ConcurrentDictionary<MessageChannel, List<PreparedMessageVM>> groupedMessages)
    {
        if (groupedMessages.TryGetValue(channel, out var item))
        {
            item.Add(message);
        }
        else
        {
            groupedMessages[channel] = [message];
        }
    }

    private static async Task<MessageStatus> Dispatch(ConcurrentBag<MessageLogActivity> activity, long messageId, MessageChannel channel, List<IMessageProvider> providers, IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        if (messages.Count == 0)
            return MessageStatus.Sent;

        if (providers.Count == 0)
        {
            activity.Add(new MessageLogActivity { MessageLogId = messageId, Title = channel.GetDisplayName(), Activity = "No suitable channel for messages", CreatedByUser = "System" });
            return MessageStatus.Failed;
        }

        Dictionary<string, MessageStatus> results = [];
        if (channel == MessageChannel.Sms)
        {
            var messageGroups = (from message in messages
                                 from data in message.Data.Where(x => !x.PhoneNumbers.IsNullOrEmpty())
                                 from phoneNo in data.PhoneNumbers
                                 group message by phoneNo.Code into g
                                 select new { PhoneCode = g.Key, Messages = g.Distinct().ToList().AsReadOnly() })
                                .Where(x => !string.IsNullOrEmpty(x.PhoneCode));

            if (messageGroups.IsNullOrEmpty())
            {
                activity.Add(new MessageLogActivity { MessageLogId = messageId, Title = channel.GetDisplayName(), Activity = "No suitable messages for channel", CreatedByUser = "System" });
                return MessageStatus.Failed;
            }

            await Parallel.ForEachAsync(messageGroups, new ParallelOptions { MaxDegreeOfParallelism = messageGroups.Count(), CancellationToken = ct }, async (message, ct) =>
            {
                results[message.PhoneCode] = providers
                    .Cast<ITextMessageProvider>()
                    .FirstOrDefault(x => x.SupportedCountryCodes != null && x.SupportedCountryCodes.Any(y => y.PhoneCode == message.PhoneCode)) is AbstractMessageProvider provider ?
                    await provider.ProcessMessage(message.Messages, ct) : MessageStatus.Failed;
            });
        }
        else
            results[channel.ToString()] = await providers[0].ProcessMessage(messages, ct);

        return results.ToMessageStatus();
    }

    private static void SubstituteMultipleReceiverTextTemplateTypeKeys(PreparedMessageVM message)
    {
        if (!message.HasText || message.TemplateValues.IsNullOrEmpty())
            return;


        foreach (var item in message.TemplateValues)
        {
            var key = $"{{{item.Key}}}";
            var value = item.Value;

            message.TextTemplate = message.TextTemplate.Replace(key, value, StringComparison.OrdinalIgnoreCase);

            message.Subject = message.Subject.Replace(key, value, StringComparison.OrdinalIgnoreCase);
        }
    }

    private static void SubstituteSingleReceiverTextTemplateTypeKeys(PreparedMessageVM message, SinglePreparedMessageVM receiver)
    {
        if (!message.HasText || receiver.TemplateValues.IsNullOrEmpty())
            return;

        if (!receiver.HasText)
        {
            receiver.TextTemplate = message.TextTemplate;
            receiver.Subject = message.Subject;
        }

        foreach (var item in receiver.TemplateValues)
        {
            var key = $"{{{item.Key}}}";
            var value = item.Value;

            receiver.TextTemplate = receiver.TextTemplate.Replace(key, value, StringComparison.OrdinalIgnoreCase);

            receiver.Subject = receiver.Subject.Replace(key, value, StringComparison.OrdinalIgnoreCase);
        }
    }
}
﻿using LendQube.Infrastructure.Core.Navigation;

namespace LendQube.Infrastructure.Collection.Navigation;

public class DebtWorkflowNavigation : INavigationDescriptor, INavigatorHasPermissions
{
    public bool IsDisabled { get; set; } = false;
    public const string GroupName = "Debt Workflow";
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = "Debt Workflow",
            Icon = "user",
            Permission = DebtWorkflowDashboardIndexPermission,
            Controller = GroupName,
            Url = "debtworkflow/index",
            SubNavigation =
            [
                new NavigatorVM { Name = "Agent Dashboard", Icon = "user", Permission = DebtWorkflowDashboardIndexPermission, Url = "debtworkflow/index", Controller = GroupName },
                new NavigatorVM { Name = "All Dashboard", Icon = "user", Permission = DebtWorkflowDashboardAllPermission, Url = "debtworkflow/dashboard", Controller = GroupName },
                new NavigatorVM { Name = "Agent Work Analytics", Icon = "user", Permission = DebtWorkflowTimeAnalyticsIndexPermission, Url = "debtworkflow/agentworkflowtimeanalytics", Controller = GroupName},
                new NavigatorVM { Name = "Work Desk", Icon = "user", Permission = DebtWorkflowDeskIndexPermission, Url = "debtworkflow/agentworkdesk", Controller = GroupName },
                new NavigatorVM { Name = "Setup Segment Rules", Icon = "user", Permission = DebtWorkflowSegmentRuleIndexPermission, Url = "debtworkflow/segmentrule", Controller = GroupName },
                new NavigatorVM { Name = "Setup Segments", Icon = "user", Permission = DebtWorkflowSegmentIndexPermission, Url = "debtworkflow/segment", Controller = GroupName },
                new NavigatorVM { Name = "Setup Agent-Segment Mapping", Icon = "user", Permission = DebtWorkflowAgentMappingConfigIndexPermission, Url = "debtworkflow/agentsegmentmapping", Controller = GroupName }
            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.DebtWorkflowIndex, GroupName, navs);
    }


    public void PreparePermissionDescriptions()
    {
        Navigator.PermissionDescription[DebtWorkflowDashboardIndexPermission] = $"Can view logged-in agent workflow dashboard and grants access to {GroupName}";
        Navigator.PermissionDescription[DebtWorkflowDashboardAllPermission] = "Can view all agents workflow dashboard and grants access";

        Navigator.PermissionDescription[DebtWorkflowSegmentRuleIndexPermission] = "Can view workflow segment rules";
        Navigator.PermissionDescription[DebtWorkflowSegmentRuleCreatePermission] = "Can add workflow segment rules";
        Navigator.PermissionDescription[DebtWorkflowSegmentRuleEditPermission] = "Can modify workflow segment rules";
        Navigator.PermissionDescription[DebtWorkflowSegmentRuleDeletePermission] = "Can remove workflow segment rules";

        Navigator.PermissionDescription[DebtWorkflowSegmentIndexPermission] = "Can view workflow segment";
        Navigator.PermissionDescription[DebtWorkflowSegmentCreatePermission] = "Can add workflow segment";
        Navigator.PermissionDescription[DebtWorkflowSegmentEditPermission] = "Can modify workflow segment";
        Navigator.PermissionDescription[DebtWorkflowSegmentDeletePermission] = "Can remove workflow segment";

        Navigator.PermissionDescription[DebtWorkflowAgentMappingConfigIndexPermission] = "Can view agent workflow segment mapping";
        Navigator.PermissionDescription[DebtWorkflowAgentMappingConfigCreatePermission] = "Can add agent workflow segment mapping";
        Navigator.PermissionDescription[DebtWorkflowAgentMappingConfigEditPermission] = "Can modify agent workflow segment mapping";
        Navigator.PermissionDescription[DebtWorkflowAgentMappingConfigDeletePermission] = "Can remove agent workflow segment mapping";

        Navigator.PermissionDescription[DebtWorkflowDeskIndexPermission] = "Can access agent workflow desk";
        Navigator.PermissionDescription[DebtWorkflowDeskCanBeEscalatedToPermission] = "Agents can escalate accounts to users with this permission";

        Navigator.PermissionDescription[DebtWorkflowTimeAnalyticsIndexPermission] = "Can access agent workflow time analytics";
    }

    public const string DebtWorkflowDashboardIndexPermission = "Permission.DebtWorkflowDashboard.Index";
    public const string DebtWorkflowDashboardAllPermission = "Permission.DebtWorkflowDashboard.All";

    public const string DebtWorkflowSegmentRuleIndexPermission = "Permission.DebtWorkflowSegmentRule.Index";
    public const string DebtWorkflowSegmentRuleCreatePermission = "Permission.DebtWorkflowSegmentRule.Create";
    public const string DebtWorkflowSegmentRuleEditPermission = "Permission.DebtWorkflowSegmentRule.Edit";
    public const string DebtWorkflowSegmentRuleDeletePermission = "Permission.DebtWorkflowSegmentRule.Delete";

    public const string DebtWorkflowSegmentIndexPermission = "Permission.DebtWorkflowSegment.Index";
    public const string DebtWorkflowSegmentCreatePermission = "Permission.DebtWorkflowSegment.Create";
    public const string DebtWorkflowSegmentEditPermission = "Permission.DebtWorkflowSegment.Edit";
    public const string DebtWorkflowSegmentDeletePermission = "Permission.DebtWorkflowSegment.Delete";

    public const string DebtWorkflowAgentMappingConfigIndexPermission = "Permission.DebtWorkflowAgentMappingConfig.Index";
    public const string DebtWorkflowAgentMappingConfigCreatePermission = "Permission.DebtWorkflowAgentMappingConfig.Create";
    public const string DebtWorkflowAgentMappingConfigEditPermission = "Permission.DebtWorkflowAgentMappingConfig.Edit";
    public const string DebtWorkflowAgentMappingConfigDeletePermission = "Permission.DebtWorkflowAgentMappingConfig.Delete";


    public const string DebtWorkflowDeskIndexPermission = "Permission.DebtWorkflowDesk.Index";
    public const string DebtWorkflowDeskCanBeEscalatedToPermission = "Permission.DebtWorkflowDesk.CanBeEscalatedTo";

    public const string DebtWorkflowTimeAnalyticsIndexPermission = "Permission.DebtWorkflowTimeAnalytics.Index";
}

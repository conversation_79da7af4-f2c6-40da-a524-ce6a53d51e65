﻿using System.Reflection;
using EntityFramework.Exceptions.PostgreSQL;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.PermissionsAndRoles;
using LendQube.Infrastructure.Core.Telemetry;
using Medallion.Threading;
using Medallion.Threading.Postgres;
using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Diagnostics;
using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace LendQube.Infrastructure.Core.DependencyInjection;

public static class DbContextDI
{
    internal static readonly string AdminProjectName = "Web.Admin";
    private static NpgsqlDbContextOptionsBuilder SetupEnumEntites(this NpgsqlDbContextOptionsBuilder options, List<IEntityHasEnum> type, INpgsqlNameTranslator nameTranslator)
    {
        type.ForEach(item => item.RegisterEnumInDataSource(options, nameTranslator));
        return options;
    }


    public static WebApplicationBuilder AddDbContext(this WebApplicationBuilder builder)
    {
        var connectionString = builder.Configuration.GetConnectionString("ConnectionString");
        var nameTranslator = new Npgsql.NameTranslation.NpgsqlNullNameTranslator();
        var interfaceType = typeof(IEntityHasEnum);
        var hasEnumEntityTypes = AppDomain.CurrentDomain.GetAssemblies()
            .AsParallel()
            .SelectMany(x => x.GetReferencedAssemblies())
            .Where(x => x.Name != null && (x.Name.StartsWith(CoreEntityConfig.EntitiesPrefix)))
            .Select(Assembly.Load)
            .SelectMany(x => x.GetTypes())
            .DistinctBy(x => x.Name)
            .Where(x => interfaceType.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
            .Select(x => Activator.CreateInstance(x) as IEntityHasEnum)
            .ToList();


        var dataSourceBuilder = new NpgsqlDataSourceBuilder(connectionString);
        dataSourceBuilder.UseNodaTime();
        hasEnumEntityTypes.ForEach(item => item.RegisterEnumInDataSource(dataSourceBuilder, nameTranslator));
        NpgsqlDataSource dataSource = dataSourceBuilder.Build();

        builder.Services.AddSingleton(dataSource);

        builder.Services.AddDbContext<AppDbContext>((serviceProvider, opt) =>
        {
            opt.UseNpgsql(
                    dataSource,
                    options => options
                        .SetupEnumEntites(hasEnumEntityTypes, nameTranslator)
                        .MigrationsAssembly(AdminProjectName)
                        .EnableRetryOnFailure(
                            maxRetryCount: 2,
                            maxRetryDelay: TimeSpan.FromSeconds(60),
                            null)
                        .UseNodaTime())
                .UseExceptionProcessor()
                .ConfigureWarnings(w => w.Ignore(RelationalEventId.MultipleCollectionIncludeWarning).Ignore(RelationalEventId.PendingModelChangesWarning))
                .EnableDetailedErrors(builder.Environment.IsDevelopment());
            opt.UseOpenIddict<Guid>();
            opt.AddInterceptors(serviceProvider.GetRequiredService<TrackCommandTimeInterceptor>());
            opt.EnableSensitiveDataLogging(builder.Environment.IsDevelopment());
        }, ServiceLifetime.Transient);

        builder.Services.AddSingleton<ILogManager<IRelationalDbRepository>, LogManager<IRelationalDbRepository>>();

        builder.Services.AddSingleton(sp =>
        {
            var options = new DbContextOptionsBuilder<AppDbContext>().UseNpgsql(
                    dataSource,
                    options => options
                        .UseNodaTime()
                        .SetupEnumEntites(hasEnumEntityTypes, nameTranslator))
                .ConfigureWarnings(w => w.Ignore(RelationalEventId.MultipleCollectionIncludeWarning).Ignore(RelationalEventId.PendingModelChangesWarning))
                .UseExceptionProcessor()
                .EnableDetailedErrors(builder.Environment.IsDevelopment())
                .Options;

            options.Freeze();

            return new PooledDbContext(options, sp.GetRequiredService<IDistributedCache>(), sp.GetRequiredService<ILogManager<IRelationalDbRepository>>());
        });

        builder.AddCoravelDbContext();

        builder.Services.AddSingleton<IDistributedLockProvider>(_ => new PostgresDistributedSynchronizationProvider(dataSource));
        return builder;
    }

    private static WebApplicationBuilder AddCoravelDbContext(this WebApplicationBuilder builder)
    {
        builder.Services.AddDbContext<CoravelDbContext>((serviceProvider, opt) =>
        {
            opt.UseNpgsql(
                    builder.Configuration.GetConnectionString("BackgroundTaskDbConnectionString"),
                    options => options
                        .MigrationsAssembly(AdminProjectName)
                        .EnableRetryOnFailure(
                            maxRetryCount: 3,
                            maxRetryDelay: TimeSpan.FromSeconds(30),
                            null)
                        .UseNodaTime())
                .ConfigureWarnings(w => w.Ignore(RelationalEventId.MultipleCollectionIncludeWarning))
                .EnableDetailedErrors(builder.Environment.IsDevelopment());

            opt.AddInterceptors(serviceProvider.GetService<TrackCommandTimeInterceptor>());
            opt.EnableSensitiveDataLogging(builder.Environment.IsDevelopment());
        });

        return builder;
    }

    internal static async Task<WebApplication> CreateMigration(this WebApplication app, AsyncServiceScope scope)
    {
        using var coravelDbContext = scope.ServiceProvider.GetRequiredService<CoravelDbContext>(); //should be called only once at first deployment
        await coravelDbContext.Database.MigrateAsync();

        using var appContext = scope.ServiceProvider.GetRequiredService<AppDbContext>();
        await appContext.Database.MigrateAsync();

        if (appContext.Database.GetDbConnection() is NpgsqlConnection dbConnection)
        {
            try { await dbConnection.OpenAsync(default); } catch { }

            await dbConnection.ReloadTypesAsync();
            await dbConnection.CloseAsync();
        }

        app.AddPermissions(typeof(AppDbContext));
        return app;
    }


    public static async Task<WebApplication> FinishPermissionSetup(this WebApplication app, AsyncServiceScope scope)
    {
        var seeder = scope.ServiceProvider.GetRequiredService<SuperAdminAccountSeeder>();
        await seeder.FinishPermissionSetup();

        return app;
    }

}
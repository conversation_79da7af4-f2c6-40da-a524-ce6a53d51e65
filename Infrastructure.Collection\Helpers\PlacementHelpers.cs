﻿using LendQube.Entities.Collection.Placements;

namespace LendQube.Infrastructure.Collection.Helpers;

public static class PlacementHelper
{
    public static readonly PlacementStatus ActiveStatus = PlacementStatus.Active | PlacementStatus.Broken;
    public static readonly PlacementStatus CurrentStatus = PlacementStatus.New | PlacementStatus.Active | PlacementStatus.Broken;
    public static readonly PlacementStatus PastStatus = PlacementStatus.Settled | PlacementStatus.Sold | PlacementStatus.Closed;
    public static readonly PlacementStatus ReportActiveStatus = PlacementStatus.New | PlacementStatus.Active;
}


public static class ScheduleHelper
{
    public static readonly SchedulePaymentStatus UnsettledStatus = SchedulePaymentStatus.PartiallyPaid | SchedulePaymentStatus.NotPaid;
}
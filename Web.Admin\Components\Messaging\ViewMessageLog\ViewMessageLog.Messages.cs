﻿using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Entities.Core.Messaging;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageLog;

public partial class ViewMessageLog
{
    private string ViewRecipientsModal => "ViewRecipientsModal";
    private string PreviewMessageModal => "PreviewMessageModal";
    private PreviewMessageVM previewMessageModel = new();

    private async Task PreviewMessage(ViewMessageRecipientVM row)
    {
        message.Close();
        modalMessage.Close();
        if (SelectedEntry.NeedsConfiguration)
        {
            modalMessage.Error($"Template {SelectedEntry.Name} needs configuration");
            return;
        }

        var item = row.GetPreviewData(SelectedEntry, Config.Url.Logo);
        previewMessageModel = new()
        {
            Name = item.Name,
            Subject = SelectedEntry.Subject,
            TextRequired = SelectedEntry.TextRequired,
            EmailRequired = SelectedEntry.EmailRequired
        };

        await JSRuntime.OpenModal(PreviewMessageModal, Cancel);

        if (previewMessageModel.TextRequired)
        {
            if (!string.IsNullOrEmpty(SelectedEntry.TextTemplate))
                previewMessageModel.TextTemplate = SelectedEntry.TextTemplate;
            else
            {
                if (!string.IsNullOrEmpty(SelectedEntry.ContainerTextTemplate))
                {
                    previewMessageModel.TextTemplate = SelectedEntry.ContainerTextTemplate;
                }

                if (!string.IsNullOrEmpty(SelectedEntry.BodyTextTemplate))
                {
                    if (!string.IsNullOrEmpty(previewMessageModel.TextTemplate))
                        previewMessageModel.TextTemplate = previewMessageModel.TextTemplate.Replace("{body}", SelectedEntry.BodyTextTemplate);
                    else
                        previewMessageModel.TextTemplate = SelectedEntry.BodyTextTemplate;
                }
            }
        }

        if (previewMessageModel.EmailRequired)
        {
            if (!string.IsNullOrEmpty(SelectedEntry.HtmlTemplate))
                previewMessageModel.HtmlTemplate = await HttpClient.ReadPhysicalFileAsString(SelectedEntry.HtmlTemplate, ct: Cancel);
            else
            {
                if (!string.IsNullOrEmpty(SelectedEntry.ContainerHtmlTemplate))
                {
                    previewMessageModel.HtmlTemplate = await HttpClient.ReadPhysicalFileAsString(SelectedEntry.ContainerHtmlTemplate, ct: Cancel);
                }

                if (!string.IsNullOrEmpty(SelectedEntry.BodyHtmlTemplate))
                {
                    var bodyTemplate = await HttpClient.ReadPhysicalFileAsString(SelectedEntry.BodyHtmlTemplate, ct: Cancel);
                    if (!string.IsNullOrEmpty(previewMessageModel.HtmlTemplate))
                    {
                        previewMessageModel.HtmlTemplate = previewMessageModel.HtmlTemplate.Replace("{body}", bodyTemplate);
                    }
                    else
                        previewMessageModel.HtmlTemplate = bodyTemplate;
                }
            }
        }

        if (!SelectedEntry.Keys.IsNullOrEmpty())
        {
            foreach (var key in SelectedEntry.Keys)
            {
                var keyValue = item.TemplateValues?.FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase))?.Value ?? string.Empty;

                if (previewMessageModel.TextRequired)
                    previewMessageModel.TextTemplate = previewMessageModel.TextTemplate.Replace($"{{{key}}}", keyValue, StringComparison.OrdinalIgnoreCase);
                if (previewMessageModel.EmailRequired)
                    previewMessageModel.HtmlTemplate = previewMessageModel.HtmlTemplate.Replace($"{{{key}}}", keyValue, StringComparison.OrdinalIgnoreCase);

                previewMessageModel.Subject = previewMessageModel.Subject.Replace($"{{{key}}}", keyValue, StringComparison.OrdinalIgnoreCase);
            }
        }

        StateHasChanged();
    }

    private async Task ResendMessage()
    {
        message.Close();
        var result = await uow.Db.UpdateAndSaveWithFilterAsync<MessageLog>(x => x.Id == LogId, x => x.SetProperty(y => y.Status, MessageStatus.Queued), Cancel);
        if (result > 0)
        {
            uow.Db.Insert(new MessageLogActivity { MessageLogId = LogId, Activity = $"Status changed from {Data.Status} to {MessageStatus.Queued}" });
            await uow.SaveAsync(Cancel);
            message.Success("Message re-queued successfully");
        }
        else
        {
            message.Error("Message could not be re-queued. Please try again");
        }

        await LoadData();
        StateHasChanged();
    }

    private async Task SendMessage()
    {
        message.Close();
        await Factory.SendMessage(LogId, Cancel);
        message.Success("Message dispatched");
    }
}

﻿@page "/account/login/enable2fa"

@using System.ComponentModel.DataAnnotations
@using System.Globalization
@using System.Text
@using System.Text.Encodings.Web
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Infrastructure.Core.Authentication
@using Microsoft.AspNetCore.Identity

@inject IdentityRedirectManager redirectManager
@inject AdminAuthService authService

@inherits AuthComponentBase

<PageTitle>Configure 2FA</PageTitle>

<div id="wrapper">
    <div id="footer-control" class="index-wrapper">
        <div class="login-wrapper mx-auto">
            <a href="#" class="brand-img mx-auto" style="margin: 0 auto 40px">
                <img src="@Assets["images/logo/logo.svg"]" alt="Ethica Resolve">
            </a>
            <div class="card">

                <div class="title-wrapper flex __justify-between __align-center">
                    <span class="text_xl_medium">Configure Authenticator App</span>
                </div>

                <StatusMessage Message="@message" />

                <EditForm Model="Input" FormName="send-code" OnValidSubmit="OnValidSubmitAsync" method="post" style="padding: 5px">

                    <div class="form-row">
                        <span>You are required to setup Two Factor Authentication to secure your account.</span>
                        <span>Please configure your authenticator using the following steps:</span>
                    </div>

                    <div class="form-row">
                        <span>Download a two-factor authenticator app like Microsoft Authenticator or Google Authenticator from your phone app store</span>
                    </div>

                    <div class="form-row">
                        <span>Scan the QR Code below to setup your account</span>
                    </div>

                    <div class="form-row">
                        <div class="qrcode-container">
                            <div id="qrCode"></div>
                            <div id="qrCodeData" data-url="@authenticatorKeyModel?.AuthenticatorUri"></div>
                        </div>
                    </div>

                    <div class="form-row">
                        <span>Or enter this key (spaces and casing do not matter)</span>
                        <kbd>@authenticatorKeyModel?.SharedKey</kbd>
                    </div>



                    <div class="form-row">
                        <span>
                            Once you have scanned the QR code or input the key above, your two factor authentication app will provide you
                            with a unique code. Enter the code in the confirmation box below.
                        </span>
                    </div>

                    <DataAnnotationsValidator />
                    <div class="form-row">
                        <label class="form-label" for="Username">Verification Code</label>
                        <InputText @bind-Value="Input.Code" class="form-input" autocomplete="off" aria-required="true" placeholder="Enter code here" />
                        <ValidationMessage For="() => Input.Code" class="text-danger" />
                    </div>


                    <div class="button-row">
                        <button type="submit" class="btn btn--primary __full">Continue</button>
                    </div>

                </EditForm>

            </div>

        </div>
    </div>
</div>


@code {
    private StatusMessageBuilder message = new();
    private AuthenticatorKeyModel authenticatorKeyModel;

    [SupplyParameterFromForm]
    private EnableAuthenticatorVM Input { get; set; } = new();

    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    [SupplyParameterFromQuery]
    private string ReturnUrl { get; set; }

    [SupplyParameterFromQuery]
    private bool RememberMe { get; set; }

    [SupplyParameterFromQuery]
    private string Key { get; set; }


    protected override async Task OnInitializedAsync()
    {
        if (!redirectManager.IsValidMFARouteKey(Key, HttpContext))
        {
            await authService.Logout();
            redirectManager.RedirectTo("account/login");
            return;
        }

        authenticatorKeyModel = await authService.AuthenticatorSetup(HttpContext.User);

        if (authenticatorKeyModel == null)
            redirectManager.RedirectTo("account/login");

        if (!string.IsNullOrEmpty(authenticatorKeyModel.ErrorMessage))
        {
            message.Error(authenticatorKeyModel.ErrorMessage);
        }
    }

    private async Task OnValidSubmitAsync()
    {
        message.Close();

        if (!redirectManager.IsValidMFARouteKey(Key, HttpContext))
        {
            await authService.Logout();
            redirectManager.RedirectTo("account/login");
            return;
        }

        var enable2faResult = await authService.ValidateAuthenticatorSetup(
            new ValidateAuthenticatorSetup(Input.Code, HttpContext.User, HttpContext.GetIpAddress(), RememberMe));

        if (enable2faResult == UserLoginState.Succeeded)
        {
            redirectManager.RedirectToAuthWithMFA(HttpContext, "/account/login/recoverycodes", new() { ["returnUrl"] = ReturnUrl }, Key);
        }
        else
        {
            Input.Code = string.Empty;
            message.Error("Code is invalid");
        }
    }
}

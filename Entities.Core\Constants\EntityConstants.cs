﻿namespace LendQube.Entities.Core.Constants;

public class EntityConstants
{
    public const int DEFAULT_NAME_FIELD_LENGTH = 255;
    public const int DEFAULT_PHONE_FIELD_LENGTH = 20;
    public const int DEFAULT_EMAIL_FIELD_LENGTH = 256;
    public const int DEFAULT_ADDRESS_FIELD_LENGTH = 1024;
    public const int DEFAULT_ID_FIELD_LENGTH = 24;
    public const int DEFAULT_TYPE_FIELD_LENGTH = 32;
    public const int DEFAULT_CURRENCY_FIELD_LENGTH = 3;
    public const int DEFAULT_CURRENCY_SYMBOL_FIELD_LENGTH = 1;
    public const int DEFAULT_COUNTRY_CODE_FIELD_LENGTH = 2;
    public const int DEFAULT_IP_FIELD_LENGTH = 46;
    public const int DEFAULT_WALLET_PREFIX_LENGTH = 15;
    public const int DEFAULT_DESCRIPTION_FIELD_LENGTH = 512;
    public const int DEFAULT_FOUR_FIELD_LENGTH = 4;
    public const int DEFAULT_SIX_FIELD_LENGTH = 6;
}


public class ErrorConstants
{
    public const string TWO_DIGIT_STRING_LENGTH_ERROR = "Code must be exactly 2 characters long.";
}
﻿using LendQube.Infrastructure.Core.Database.DataPager;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.Database.Specification;

public interface IBaseSpecification<T>
{
    Expression<Func<T, bool>> PrimaryCriteria { get; set; }
    Func<IQueryable<T>, IOrderedQueryable<T>> OrderBy { get; }
    internal void DoAllFilter(DataFilterAndPage filterAndPage);
    internal Expression<Func<TVM, bool>> GetAllFilter<TVM>(DataFilterAndPage filterAndPage);
}

﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Location;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.ViewModels.SystemSetup;
public sealed class CurrencyVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<Currency, CurrencyVM>> Mapping = data => new CurrencyVM
    {
        Id = data.Id,
        CountryId = data.CountryId,
        CountryCode = data.Country.Code,
        CountryName = data.Country.Name,
        Name = data.Name,
        Code = data.Code,
        Symbol = data.Symbol,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public Currency Get() => new()
    {
        Id = Id,
        CountryId = CountryId,
        Name = Name,
        Code = Code,
        Symbol = Symbol,
    };

    [TableDecorator(TableDecoratorType.SkipFilter, TableDecoratorType.HideColumn)]
    public long CountryId { get; set; }
    [TableDecorator(TableDecoratorType.SkipFilter, TableDecoratorType.HideColumn)]
    public string CountryCode { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string CountryName { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Name { get; set; } 
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Code { get; set; } 
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Symbol { get; set; }
}
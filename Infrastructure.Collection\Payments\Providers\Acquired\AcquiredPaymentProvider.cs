﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.ExternalApi.Acquired;
using LendQube.Infrastructure.ExternalApi.AppSettings;
using Microsoft.AspNetCore.Http;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Payments.Providers.Acquired;

internal sealed class AcquiredPaymentProvider(IAcquiredExternalApiService apiService, IUnitofWork uow, ExternalApiConfig apiConfig, DefaultAppConfig config, IClock clock,
    IHttpContextAccessor httpContextAccessor, ILogManager<AcquiredPaymentProvider> logger) :
    IPaymentProvider, ICreatesCustomerPaymentProvider, IChargeCardPaymentProvider, ISavesPaymentMethodPaymentProvider, ICreatesPaymentMethodProvider, ICreateAndConfirmCardChargePaymentProvider
{
    public async Task<CustomerPaymentMethodConfiguration> CreateCustomer(CustomerScheduleVM profile, CancellationToken ct)
    {
        var address = await uow.Db.OneAsync(Query<CustomerAddress>.Where(x => x.CustomerProfileId == profile.Id), ct);
        var ip = httpContextAccessor.GetIpAddress();
        var requestData = new AcquiredCustomerRequestVM
        {
            Reference = profile.AccountId,
            FirstName = profile.FirstName,
            LastName = profile.LastName,
            DateOfBirth = profile.DateOfBirth,
            Email = profile.Email,
            Phone = profile.Phone == null ? null : new() { CountryCode = profile.Phone.Code, Number = profile.Phone.Number },
            Billing = address != null ? new()
            {
                Address = new()
                {
                    Line1 = address.AddressLine1,
                    Line2 = address.AddressLine2,
                    City = address.City,
                    CountryCode = address.CountryCode,
                    Postcode = address.PostCode,
                }
            } : null,
            Shipping = new() { AddressMatch = true },
            Ip = !string.IsNullOrEmpty(ip) && ip != "::1" && ip.Length <= 32 ? ip : null
        };

        var response = await apiService.CreateCustomer(requestData, ct);
        if (response is { Failed: true })
        {
            return null;
        }


        return new CustomerPaymentMethodConfiguration { ProfileId = profile.Id, Provider = PaymentProvider.Acquired, Currency = profile.CurrencyCode, ProviderId = response.Result.CustomerId };
    }


    public async Task<Result<bool>> CreateSetupCharge(CustomerPaymentMethodConfiguration payConfig, Transaction txn, CancellationToken ct)
    {
        try
        {
            var requestData = new AcquiredGenerateLinkRequestVM
            {
                IsRecurring = true,
                Transaction = new()
                {
                    OrderId = txn.Id,
                    Amount = txn.Amount,
                    Currency = txn.CurrencyCode,
                },
                Tds = new()
                {
                    IsActive = true,
                    ContactUrl = config.Url.Contact
                },
                Customer = new() { CustomerId = payConfig.ProviderId }
            };


            var response = await apiService.GeneratePaymentLinkId(requestData, ct);

            if (response is { Failed: true })
            {
                return false;
            }

            txn.UserData.Add(nameof(AcquiredConstants.PaymentLink), $"{apiConfig.Acquired.BaseAppUrl}{response.Result.LinkId}");

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.BackgroundTask, EventAction.Payment, ex, $"Acquired create-setup-charge failed for customer: {txn.ProfileId} tx: {txn.Id}");
            return false;
        }
    }

    public async Task<(TransactionStatus, CustomerPaymentMethod, bool)> ConfirmSetupCharge(IUnitofWork uow, Transaction txn, CancellationToken ct)
    {
        var response = await apiService.GetTransaction(txn.Id, ct);
        var setup = response.Result?.Data?.FirstOrDefault();

        var status = setup?.Status switch
        {
            "success" => TransactionStatus.Successful,
            "processing" => TransactionStatus.Processing,
            null => TransactionStatus.Validated,
            _ => TransactionStatus.Failed,
        };

        txn.ProviderReference = setup?.TransactionId;

        if (status == TransactionStatus.Successful)
        {
            txn.TotalAmountPaid = txn.Amount;

            var (savedPaymentMethod, paymentMethodExists) = await SavePaymentMethod(uow, txn, setup.CardId, setup.Card, ct);
            return (status, savedPaymentMethod, paymentMethodExists);
        }
        else if (status != TransactionStatus.Validated)
        {
            uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = $"charge confirmation failed: {setup?.Reason}", Title = $"{txn.Status}", CreatedByUserId = txn.CreatedByUserId });
        }

        return (status, null, false);

    }


    public async Task<Result<bool>> CreateCharge(CustomerPaymentMethodConfiguration payConfig, Transaction txn, CancellationToken ct)
    {
        try
        {
            var type = txn.Fields.Get("Type");
            var requestData = new AcquiredGenerateLinkRequestVM
            {
                IsRecurring = type != null && !type.Contains("One-Time"),
                Payment = new()
                {
                    Reference = clock.GetCurrentInstant().ToUnixTimeMilliseconds().ToString(),
                },
                Tds = new()
                {
                    IsActive = true,
                    ContactUrl = config.Url.Contact
                },
                Transaction = new()
                {
                    OrderId = txn.Id,
                    Amount = txn.TotalAmountPayable,
                    Currency = txn.CurrencyCode,
                    ChargeNow = true,
                },
                Customer = new() { CustomerId = payConfig.ProviderId }
            };


            var response = await apiService.GeneratePaymentLinkId(requestData, ct);

            if (response is { Failed: true })
            {
                return false;
            }

            txn.UserData.Add(nameof(AcquiredConstants.PaymentLink), $"{apiConfig.Acquired.BaseAppUrl}{response.Result.LinkId}");

            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.BackgroundTask, EventAction.Payment, ex, $"Acquired create-charge failed for customer: {txn.ProfileId} tx: {txn.Id}");
            return false;
        }
    }

    public async Task<(TransactionStatus, CustomerPaymentMethod)> ConfirmCharge(IUnitofWork uow, Transaction txn, CustomerPaymentMethod savedPaymentMethod, CancellationToken ct)
    {
        try
        {
            if (savedPaymentMethod != null && txn.Status < TransactionStatus.Processing)
            {
                var cardChargeResponse = await apiService.ChargeCard(new()
                {
                    Transaction = new()
                    {
                        OrderId = txn.Id,
                        Amount = txn.TotalAmountPayable,
                        ChargeNow = true,
                        Currency = txn.CurrencyCode,
                    },
                    Payment = new() { CardId = savedPaymentMethod.ProviderId }
                }, ct);

                if (cardChargeResponse.Failed || !cardChargeResponse.Result.IsSuccessful)
                    return (TransactionStatus.Failed, null);
            }

            var paymentResponse = await apiService.GetTransaction(txn.Id, ct);

            var payment = paymentResponse.Result?.Data?.FirstOrDefault();


            txn.ProviderReference = payment?.TransactionId;

            var status = payment?.Status switch
            {
                "success" => TransactionStatus.Successful,
                "processing" => TransactionStatus.Processing,
                null => TransactionStatus.Validated,
                _ => TransactionStatus.Failed,
            };

            if (status == TransactionStatus.Processing && paymentResponse.Successful && payment == null)
                status = TransactionStatus.Failed;

            if (status == TransactionStatus.Successful)
            {
                txn.TotalAmountPaid = payment.Transaction.Amount;


                if (savedPaymentMethod == null)
                {
                    (savedPaymentMethod, _) = await SavePaymentMethod(uow, txn, payment.CardId, payment.Card, ct);

                    if (savedPaymentMethod == null)
                    {
                        txn.UserData.Add(TransactionHelper.PaymentMethod, payment.PaymentMethod?.FirstCharToUpper());
                    }

                    return (status, savedPaymentMethod);
                }
                else
                {
                    txn.UserData.Add(TransactionHelper.PaymentMethod, $"{savedPaymentMethod.Data.Brand} - {savedPaymentMethod.Data.Last4}");
                    txn.Fields.Add(TransactionHelper.PaymentMethod, $"{savedPaymentMethod.Data.Brand} - {savedPaymentMethod.Data.Last4}");
                }
            }
            else if (status != TransactionStatus.Validated)
            {
                uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = $"charge confirmation failed: {payment?.Reason}", Title = $"{txn.Status}", CreatedByUserId = txn.CreatedByUserId });
            }

            return (status, null);
        }
        catch (Exception ex)
        {

            logger.LogError(EventSource.BackgroundTask, EventAction.Payment, ex, $"Acquired charge failed for customer: {txn.ProfileId} tx: {txn.Id}");
            return (TransactionStatus.Failed, null);
        }
    }


    public async Task<TransactionStatus?> CreateAndConfirmSingleCharge(IUnitofWork uow, CreateChargeAndConfirmPaymentMethodVM config, Transaction txn, CancellationToken ct)
    {
        try
        {
            var cardChargeResponse = await apiService.ChargeCard(new()
            {
                Transaction = new()
                {
                    OrderId = txn.Id,
                    Amount = txn.TotalAmountPayable,
                    ChargeNow = true,
                    Currency = txn.CurrencyCode,
                },
                Payment = new() { CardId = config.PaymentMethodId }
            }, ct);

            if (cardChargeResponse.Failed || !cardChargeResponse.Result.IsSuccessful)
                return TransactionStatus.Failed;

            var paymentResponse = await apiService.GetTransaction(txn.Id, ct);

            var payment = paymentResponse.Result?.Data?.FirstOrDefault();

            txn.ProviderReference = payment?.TransactionId;

            var status = payment?.Status switch
            {
                "success" => TransactionStatus.Successful,
                null or "processing" => TransactionStatus.Processing,
                _ => TransactionStatus.Failed,
            };

            if (status == TransactionStatus.Successful)
            {
                txn.TotalAmountPaid = payment.Transaction.Amount;

                txn.UserData.Add(TransactionHelper.PaymentMethod, $"{config.PaymentMethodData.Brand} - {config.PaymentMethodData.Last4}");
                txn.Fields.Add(TransactionHelper.PaymentMethod, $"{config.PaymentMethodData.Brand} - {config.PaymentMethodData.Last4}");
            }
            else
            {
                uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = $"Auto-charge failed: {payment?.Reason}", Title = $"{txn.Status}" });
            }

            return status;

        }
        catch (Exception ex)
        {

            logger.LogError(EventSource.BackgroundTask, EventAction.Payment, ex, $"Stripe auto-charge failed for customer: {txn.ProfileId} tx: {txn.Id}");
            return null;
        }
    }

    private async Task<(CustomerPaymentMethod, bool paymentMethodExists)> SavePaymentMethod(IUnitofWork uow, Transaction txn, string paymentMethodId, AcquiredCardTransactionResponseCard card, CancellationToken ct)
    {
        if (!string.IsNullOrEmpty(paymentMethodId) && card != null)
        {
            _ = Enum.TryParse(card.Scheme, true, out CardBrand brand);
            var data = new CardResponseVM(card.Number, brand);

            txn.UserData.Add(TransactionHelper.PaymentMethod, $"{brand} - {card.Number}");
            txn.Fields.Add(TransactionHelper.PaymentMethod, $"{brand} - {card.Number}");

            var profileIdForExistingCard = await uow.Db.OneSelectAsync(Query<CustomerPaymentMethod, string>
                .Where(x => x.Data.Last4 == data.Last4 && x.Data.Brand == data.Brand && (!config.DeployState.IsDemo || x.ProfileId == txn.ProfileId))
                .Select(x => x.ProfileId), ct);

            if (string.IsNullOrEmpty(profileIdForExistingCard)) //make sure no other user has this card unless it's demo
            {
                var lastDayOfMonth = DateTime.DaysInMonth(int.Parse("20" + card.ExpiryYear), int.Parse(card.ExpiryMonth));
                var newPaymentMethod = new CustomerPaymentMethod
                {
                    ProviderId = paymentMethodId,
                    Status = PaymentMethodStatus.Active,
                    CanBeReused = true,
                    Data = data,
                    ExpiryDate = DateOnly.FromDateTime(new DateTime(int.Parse("20" + card.ExpiryYear), int.Parse(card.ExpiryMonth), lastDayOfMonth))
                };


                return (newPaymentMethod, false);
            }

            return (null, profileIdForExistingCard == txn.ProfileId);
        }

        return (null, false);
    }
}

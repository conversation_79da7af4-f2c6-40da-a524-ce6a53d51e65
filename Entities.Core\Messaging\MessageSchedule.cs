﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;

namespace LendQube.Entities.Core.Messaging;

public class MessageSchedule : BaseEntityWithIdentityId<MessageSchedule>, IEntityHasNotifyTrigger
{
    [Required, MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH)]
    public string Name { get; set; }
    [MaxLength(EntityConstants.DEFAULT_ID_FIELD_LENGTH)]
    public string CronExpression { get; set; }
    public MessageScheduleFrequency Frequency { get; set; }
    public int FrequencyNumber { get; set; }
    public ScheduleDay Days { get; set; }
    public Instant? ActiveOn { get; set; }
    public bool Starting { get; set; }
    public string TimeZone { get; set; }
    public int RunCount { get; set; }
    public List<TemplateKeyValue> TemplateValues { get; set; }
    public long ConfigId { get; set; }
    public virtual MessageConfiguration Config { get; set; }

    public List<long> Groups { get; set; }

    #region Change Trigger Notification
    public string Schema => CoreEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => $@"NEW.""{nameof(ActiveOn)}"" IS NOT NULL AND NEW.""{nameof(Starting)}"" IS TRUE";

    #endregion

    public override void Configure(EntityTypeBuilder<MessageSchedule> builder)
    {
        base.Configure(builder);

        builder.HasIndex(x => x.Name).IsUnique()
            .IncludeProperties(x => new { x.CronExpression, x.Frequency, x.FrequencyNumber, x.Days, x.ActiveOn, x.Starting, x.RunCount });

        builder.OwnsMany(x => x.TemplateValues, x => x.ToJson());
    }
}


public enum MessageScheduleFrequency
{
    CronExpression,
    Weekly,
    DailyAtHour
}

[Flags]
public enum ScheduleDay
{
    None,
    Monday,
    Tuesday,
    Wednesday,
    Thursday,
    Friday,
    Saturday,
    Sunday
}
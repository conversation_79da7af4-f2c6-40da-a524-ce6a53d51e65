﻿using System.Text.Json;
using System.Text.Json.Serialization;
using NodaTime;
using NodaTime.Text;

namespace LendQube.Infrastructure.Core.SerializersAndConverters;

public sealed class NullableInstantConverter : JsonConverter<Instant?>
{
    public override Instant? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        if (string.IsNullOrEmpty(value)) return null;
        value = value.Replace("+00:00", "Z");
        return InstantPattern.ExtendedIso.Parse(value).Value;
    }

    public override void Write(Utf8JsonWriter writer, Instant? value, JsonSerializerOptions options)
    {
        throw new NotImplementedException();
    }
}

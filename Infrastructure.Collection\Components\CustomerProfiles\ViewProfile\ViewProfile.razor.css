/* Customer Profile Left Menu Enhancements */

/* Info Column Styling */
.info-col {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    border-radius: 12px;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease; 
}

.info-col:hover {
    box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

/* Card Styling */
.info-col .card {
    background: rgba(255, 255, 255, 0.95);
    border: none;
    border-radius: 8px;
    margin-bottom: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: transform 0.2s ease;
}

.info-col .card:hover {
    transform: translateY(-2px);
}

/* Header Styling */
.info-col .__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
    border-radius: 8px 8px 0 0;
}

.info-col .__title {
    font-size: 1.5rem; 
    font-weight: 600;
    display: block;
    margin-bottom: 0.5rem;
}
 
.info-col .__amount {
    font-size: 1rem;
    opacity: 0.9;
    display: block;
}

/* Status Badges */
.info-col .status {
    margin-top: 1rem;
}

.info-col .status .lz {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
    margin-right: 0.5rem;
    animation: pulse 2s infinite;
}

.info-col .status .lz.__yellow {
    background-color: #f39c12;
    color: white;
}

.info-col .status .lz.__red {
    background-color: #e74c3c;
    color: white;
}

/* Detail Items Grid */
.info-col .detail-wrapper {
    padding: 1rem;
}

.info-col .detail-item {
    padding: 0.75rem;
    border-radius: 6px;
    background: #f8f9fa;
    transition: all 0.2s ease;
    cursor: default;
    position: relative;
    overflow: hidden;
}

.info-col .detail-item:hover {
    background: #e9ecef;
    transform: scale(1.02);
}

.info-col .detail-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 3px;
    height: 100%;
    background: #667eea;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
}

.info-col .detail-item:hover::before {
    transform: translateX(0);
}

/* Labels and Values */
.info-col .detail-item .label {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: 500;
    display: block;
    margin-bottom: 0.25rem;
}

.info-col .detail-item .value {
    font-size: 1rem;
    color: #212529;
    font-weight: 600;
    display: block;
    transition: color 0.2s ease;
}

/* Financial Values Special Styling */
.info-col .detail-item[data-field-type="financial"] .value {
    color: #28a745;
    font-size: 1.1rem;
}

.info-col .detail-item[data-field-type="balance-outstanding"] .value {
    color: #dc3545;
    font-size: 1.2rem;
    font-weight: 700;
}

/* Contact Fields - Copyable */
.info-col .detail-item[data-field-type="contact"] .value {
    color: #007bff;
    cursor: pointer;
    text-decoration: underline;
    text-decoration-style: dotted;
}

.info-col .detail-item[data-field-type="contact"] .value:hover {
    text-decoration-style: solid;
}

/* Date Fields */
.info-col .detail-item[data-field-type="date"] .value {
    color: #6610f2;
}

/* Animation for value updates */
.info-col .value.updated {
    animation: highlight 1s ease;
}

@keyframes highlight {
    0% {
        background-color: #ffeaa7;
        transform: scale(1.05);
    }
    100% {
        background-color: transparent;
        transform: scale(1);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(255, 255, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 255, 255, 0);
    }
}

/* Activity Timeline Styling */
.info-col .activity-item {
    background: rgba(255, 255, 255, 0.95);
}

.info-col .accordion-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-weight: 600;
    border: none;
}

.info-col .accordion-button:not(.collapsed) {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
    color: white;
}

.info-col .accordion-button:focus {
    box-shadow: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .info-col {
        padding: 0.5rem;
    }

    .info-col .detail-wrapper {
        grid-template-columns: 1fr;
    }

    .info-col .__title {
        font-size: 1.25rem;
    }
}

/* Loading State */
.info-col.loading {
    position: relative;
    pointer-events: none;
}

.info-col.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Icons Enhancement */
.info-col .detail-item[data-field-type="financial"]::after,
.info-col .detail-item[data-field-type="contact"]::after,
.info-col .detail-item[data-field-type="date"]::after {
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    opacity: 0.2;
    font-size: 1.5rem;
}

.info-col .detail-item[data-field-type="financial"]::after {
    content: '\f155'; /* Dollar sign */
}

.info-col .detail-item[data-field-type="contact"]::after {
    content: '\f0e0'; /* Envelope */
}

.info-col .detail-item[data-field-type="date"]::after {
    content: '\f073'; /* Calendar */
}

/* Smooth scrolling for the info column */
.info-col {
    scroll-behavior: smooth;
    max-height: calc(100vh - 100px);
    overflow-y: auto;
}

/* Custom scrollbar */
.info-col::-webkit-scrollbar {
    width: 8px;
}

.info-col::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
} 

.info-col::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 4px;
}

.info-col::-webkit-scrollbar-thumb:hover {
    background: #764ba2; 
}
﻿using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_AutoDiscounts : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            //migrationBuilder.AlterColumn<Guid>(
            //    name: "UserName",
            //    schema: "public",
            //    table: "AspNetUsers",
            //    type: "uuid",
            //    maxLength: 256,
            //    nullable: false,
            //    defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
            //    oldClrType: typeof(string),
            //    oldType: "character varying(256)",
            //    oldMaxLength: 256,
            //    oldNullable: true);

            migrationBuilder.AddUniqueConstraint(
                name: "AK_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers",
                column: "UserName");

            migrationBuilder.CreateTable(
                name: "AutoDiscountConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric", nullable: true),
                    Percentage = table.Column<decimal>(type: "numeric", nullable: true),
                    UpperLimit = table.Column<decimal>(type: "numeric", nullable: true),
                    Rule = table.Column<int>(type: "integer", nullable: false),
                    DoNotApplyIfDiscountSettlesAccount = table.Column<bool>(type: "boolean", nullable: false),
                    ApplyToNewAccountsOnly = table.Column<bool>(type: "boolean", nullable: false),
                    ApplyToOnlyAccountsWithNoDiscount = table.Column<bool>(type: "boolean", nullable: false),
                    ApplyDiscountNow = table.Column<bool>(type: "boolean", nullable: false),
                    DiscountApplied = table.Column<bool>(type: "boolean", nullable: false),
                    TotalPlacementsAffected = table.Column<int>(type: "integer", nullable: false),
                    TotalAmountApplied = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AutoDiscountConfig", x => x.Id);
                });

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerProfile_AspNetUsers_Id",
                schema: "collection",
                table: "CustomerProfile",
                column: "Id",
                principalSchema: "public",
                principalTable: "AspNetUsers",
                principalColumn: "UserName",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomerProfile_AspNetUsers_Id",
                schema: "collection",
                table: "CustomerProfile");

            migrationBuilder.DropTable(
                name: "AutoDiscountConfig",
                schema: "collection");

            migrationBuilder.DropUniqueConstraint(
                name: "AK_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers");

            migrationBuilder.AlterColumn<string>(
                name: "UserName",
                schema: "public",
                table: "AspNetUsers",
                type: "character varying(256)",
                maxLength: 256,
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uuid",
                oldMaxLength: 256);
        }
    }
}

﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using System.Linq.Dynamic.Core;
using System.Reflection;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal interface IMessageProvider
{
    ProviderConfigVM Config { get; }
    Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct);
}

internal interface ITextMessageProvider : IMessageProvider
{
    IReadOnlyList<ProviderConfigVM> SupportedCountryCodes { get; }
}

internal interface IEmailProvider : IMessageProvider
{

}

internal abstract class AbstractMessageProvider : IMessageProvider
{
    private readonly MessageChannel AllowedChannels = MessageChannel.Sms | MessageChannel.Email | MessageChannel.WhatsApp | MessageChannel.PushNotification | MessageChannel.CustomerInbox | MessageChannel.Telegram;
    protected abstract MessageChannel SupportedChannel { get; }
    protected readonly IUnitofWork uow;

    protected AbstractMessageProvider(IUnitofWork uow)
    {
        if (!AllowedChannels.HasFlag(SupportedChannel))
            throw new InvalidOperationException("Channel set for inheriting class not supported");

        this.uow = uow;
    }

    public abstract ProviderConfigVM Config { get; }

    public abstract Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct);

    private readonly static Func<PreparedMessageVM, int> SumEmail = (message) => message.Data.Sum(d => d.Emails.Count);

    private readonly static Func<PreparedMessageVM, int> SumPhoneNumbers = (message) => message.Data.Sum(d => d.PhoneNumbers.Count);

    protected Task UpdateProviderWithResult(IReadOnlyList<PreparedMessageVM> messages, Dictionary<long, MessageStatus> result, decimal? balance, decimal? cost, CancellationToken ct)
    {
        int totalSuccessful = result.Values.Count(x => x == MessageStatus.Sent), totalFailed = result.Values.Count(x => x == MessageStatus.Failed);
        return UpdateProviderWithResult(messages, totalSuccessful, totalFailed, balance, cost, ct);
    }

    protected Task UpdateProviderWithResult(IReadOnlyList<PreparedMessageVM> messages, int totalSuccessful, int totalFailed, decimal? balance, decimal? cost, CancellationToken ct)
    {
        int totalSent = 0;
        if (SupportedChannel.HasFlag(MessageChannel.Email))
        {
            totalSent = messages.Sum(SumEmail);
        }
        else if (SupportedChannel.HasFlag(MessageChannel.CustomerInbox))
        {
            totalSent = messages.Sum(m => m.Data.Count);
        }
        else
        {
            totalSent = messages.Sum(SumPhoneNumbers);
        }

        return uow.Db.UpdateAndSaveWithFilterAsync<MessageProviderConfig>(x => x.Id == Config.ProviderId, x =>
        x.SetProperty(y => y.TotalCount, y => y.TotalCount + totalSent)
        .SetProperty(y => y.FailureCount, y => y.FailureCount + totalFailed)
        .SetProperty(y => y.ExpectedTotalDebit, y => totalSuccessful > 0 && y.UnitCost > 0 ? y.ExpectedTotalDebit + (y.UnitCost * totalSuccessful) : y.ExpectedTotalDebit)
        .SetProperty(y => y.TotalDebit, y => cost.HasValue ? y.TotalDebit + cost : balance.HasValue ? y.TotalDebit + y.Balance - balance : y.TotalDebit + (y.UnitCost * totalSent)),
            ct);
    }

    protected void LogActivity(PreparedMessageVM message, MessageStatus status, string providerName, string activity = default)
    {
        if (!Config.LogActivity)
            return;

        var logActivity = new MessageLogActivity
        {
            MessageLogId = message.MessageId,
            Title = providerName,
            Activity = $"Dispatch for channel {SupportedChannel} {(status == MessageStatus.Processing ? " started" : status == MessageStatus.Sent ? "successful" : status == MessageStatus.Failed ? "failed" : "partially successful" )}. {activity}",
            CreatedByUser = message.CreatedByUser,
            CreatedByUserId = message.CreatedByUserId
        };

        uow.Db.Insert(logActivity);
    }

    protected static void SubstituteAllTemplateTypeKeys(PreparedMessageVM message)
    {
        foreach (var item in message.TemplateValues)
        {
            var key = $"{{{item.Key}}}";
            var value = item.Value;

            if (message.HasText)
                message.TextTemplate = message.TextTemplate.Replace(key, value, StringComparison.OrdinalIgnoreCase);
            if (message.HasHtml)
                message.HtmlTemplate = message.HtmlTemplate.Replace(key, value, StringComparison.OrdinalIgnoreCase);

            message.Subject = message.Subject.Replace(key, value);
        }
    }

    protected static void SubstituteSingleReceiverHtmlTemplateTypeKeys(PreparedMessageVM message, SinglePreparedMessageVM receiver)
    {
        if (!message.HasHtml || receiver.TemplateValues.IsNullOrEmpty())
            return;

        if (!receiver.HasHtml)
        {
            receiver.HtmlTemplate = message.HtmlTemplate;
            receiver.Subject = message.Subject;
        }

        foreach (var item in receiver.TemplateValues)
        {
            var key = $"{{{item.Key}}}";
            var value = item.Value;

            receiver.HtmlTemplate = receiver.HtmlTemplate.Replace(key, value, StringComparison.OrdinalIgnoreCase);

            receiver.Subject = receiver.Subject.Replace(key, value, StringComparison.OrdinalIgnoreCase);
        }
    }
}

internal static class MessageProviderHelpers
{
    static MessageProviderHelpers()
    {
        var type = typeof(AbstractMessageProvider);
        Assembly asm = Assembly.GetAssembly(type);
        var names = asm.GetTypes()
            .AsParallel()
            .Where(type.IsAssignableFrom)
            .SelectMany(type => type.GetFields(BindingFlags.Public | BindingFlags.Static))
            .Where(m => m.IsLiteral && !m.IsInitOnly && m.Name == "Name")
            .Select(x => x.GetValue(x).ToString())
            .Distinct();

        ProviderNames.AddRange(names);
    }

    public static List<string> ProviderNames { get; set; } = [];
}
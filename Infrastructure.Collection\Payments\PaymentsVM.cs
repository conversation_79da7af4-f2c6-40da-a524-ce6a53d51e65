﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using System.Text.Json.Serialization;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Constants;
using LendQube.Infrastructure.ExternalApi.Acquired;

namespace LendQube.Infrastructure.Collection.Payments;

public class CreateChargeAndConfirmPaymentMethodVM
{
    public static readonly Expression<Func<CustomerPaymentMethod, CreateChargeAndConfirmPaymentMethodVM>> Mapping = data => new CreateChargeAndConfirmPaymentMethodVM
    {
        ProfileId = data.ProfileId,
        CustomerId = data.Config.ProviderId,
        PaymentMethodId = data.ProviderId,
        PaymentMethodData = data.Data,
    };
    public string ProfileId { get; set; }
    public string CustomerId { get; set; }
    public string PaymentMethodId { get; set; }
    public CardResponseVM PaymentMethodData { get; set; }
}

public class CustomerPaymentMethodVM
{
    public static readonly Expression<Func<CustomerPaymentMethod, CustomerPaymentMethodVM>> Mapping = data => new CustomerPaymentMethodVM
    {
        Id = data.Id,
        Data = data.Data,
    };

    public string Id { get; set; }
    public CardResponseVM Data { get; set; }
}


public class TransactionResultVM(Transaction txn, string key = null)
{
    [JsonIgnore]
    public Transaction Transaction => txn;
    public string TxnId => txn.Id;
    public string CurrencyCode => txn.CurrencyCode;
    public string CurrencySymbol => txn.CurrencySymbol;
    public decimal Amount => txn.Amount;
    public decimal Fee => txn.Fee;
    public decimal TotalAmountPayable => txn.TotalAmountPayable;
    public decimal TotalAmountPaid => txn.TotalAmountPaid;
    public decimal Quantity => txn.Quantity;
    public decimal Discount => txn.Discount;
    public TransactionStatus Status => txn.Status;
    public PaymentType Type => txn.Type;
    public string ProviderId => txn.Provider switch
    {
        _ or PaymentProvider.Acquired => txn.Status > TransactionStatus.Validated ? txn.ProviderReference : txn.UserData.Get(nameof(AcquiredConstants.PaymentLink)),
    };
    public List<KeyValueHelperVM> Fields => txn.Fields;
    [JsonIgnore]
    public List<KeyValueHelperVM> UserData => txn.UserData;
    public List<SavedPaymentMethods> PaymentMethods { get; set; }
    public string ProviderPublicKey => key;
}

public record ProcessTransactionRequestVM([Required, MaxLength(EntityConstants.DEFAULT_ID_FIELD_LENGTH), ValidString(ValidStringRule.OnlyTextAndNumber)] string TxnId, string PaymentMethodId = null);

public class ProcessTransactionRequestAdminVM
{
    public string PaymentMethodId { get; set; }

    public ProcessTransactionRequestVM Get(string txnId) => new(txnId, PaymentMethodId);
}

public record SavedPaymentMethods(string Id, CardBrand Brand, string Last4)
{
    [JsonIgnore]
    public string DisplayValue => string.IsNullOrEmpty(Id) ? Last4 : $"{Brand} - {Last4}";
}
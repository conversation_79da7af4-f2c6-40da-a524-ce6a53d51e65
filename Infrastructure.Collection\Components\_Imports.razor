﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Authorization
@using Microsoft.EntityFrameworkCore
@using LendQube.Entities.Core.Extensions
@using LendQube.Infrastructure.Core.Extensions
@using LendQube.Infrastructure.Core.Telemetry
@using LendQube.Infrastructure.Core.Database.GenericSpecification
@using LendQube.Infrastructure.Core.Components.Table
@using LendQube.Infrastructure.Core.Database.DataPager
@using LendQube.Infrastructure.Core.Components
@using LendQube.Infrastructure.Collection.Navigation
@using LendQube.Infrastructure.Core.Components.Helpers
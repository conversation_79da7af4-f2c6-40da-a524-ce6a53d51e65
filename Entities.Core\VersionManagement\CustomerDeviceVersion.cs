﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Logs;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Entities.Core.VersionManagement;

public class CustomerDeviceVersion : BaseEntityWithIdentityId<CustomerDeviceVersion>
{    
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public CustomerDeviceType Type { get; set; }

    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyNumbersAndDot)]
    public string OldVersion { get; set; }

    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyNumbersAndDot)]
    public string CurrentVersion { get; set; }
}
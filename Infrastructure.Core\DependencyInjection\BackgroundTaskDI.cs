﻿using Coravel;
using Coravel.Pro;
using Coravel.Queuing.Interfaces;
using Coravel.Scheduling.Schedule.Interfaces;
using LendQube.Entities.Core.Messaging;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.PermissionsAndRoles;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace LendQube.Infrastructure.Core.DependencyInjection;

public static class BackgroundTaskDI
{
    public static readonly List<Type> BackgroundTriggerTypes = [typeof(MessageLog), typeof(MessageSchedule), typeof(SystemReport), typeof(ReportSchedule)];
    public static IServiceCollection AddBaseBackgroundServices(this IServiceCollection services) => services.AddScheduler()
        .AddQueue();

    public static IServiceCollection AddBackgroundServices(this IServiceCollection services) => services
        .AddBaseBackgroundServices()
        .AddTransient<BackgroundMessageComposer>()
        .AddTransient<BackgroundUserAccessService>();

    public static IServiceCollection AddCoreBackgroundServices(this IServiceCollection services)
    {
        services.AddCoravelPro(typeof(CoravelDbContext))
            .AddPermission<CoravelAdminPermission>();

        services.AddOpenIddictCore();
        return services;
    }

    public static void UseCoreBackgroundService(this IApplicationBuilder app) => app.UseCoravelPro();
    public static void UseBackgroundServices(this IApplicationBuilder app)
    {
        var services = app.ApplicationServices;
        services.UseScheduler((scheduler) => { }).LogScheduledTaskProgress(services.GetService<ILogger<IScheduler>>());
        services.ConfigureQueue().LogQueuedTaskProgress(services.GetService<ILogger<IQueue>>());
    }

    public static IServiceCollection AddBackgroundTriggers(this IServiceCollection services) => services.AddTriggerNotification(BackgroundTriggerTypes)
        .AddTransient<BackgroundMessageComposer>()
        .AddTransient<BackgroundUserAccessService>();
}

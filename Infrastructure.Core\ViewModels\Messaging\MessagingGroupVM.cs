﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Extensions;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.ViewModels.Messaging;

public sealed record MessagingGroupUIVM(long Id, string Name);

public sealed class MessagingGroupVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<MessagingGroup, MessagingGroupVM>> Mapping = data => new MessagingGroupVM
    {
        Id = data.Id,
        Name = data.Name,
        Total = data.Directory.Count,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public MessagingGroup Get() => new()
    {
        Id = Id,
        Name = Name,
    };


    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Name { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.SkipFilter)]
    public int Total { get; set; }
}


public sealed class MessagingGroupEntryVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<MessagingGroupEntry, MessagingGroupEntryVM>> Mapping = data => new MessagingGroupEntryVM
    {
        Id = data.Id,
        Name = data.Name,
        Emails = data.Emails,
        PhoneNumbers = data.PhoneNumbers,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public MessagingGroupEntry Get(long messagingId) => new()
    {
        Id = Id,
        MessagingGroupId = messagingId,
        Name = Name,
        Emails = !Emails.IsNullOrEmpty() ? Emails : EmailsFormatted.IsNullOrEmpty() ? null : EmailsFormatted?.Split(", ", StringSplitOptions.TrimEntries).ToList(),
        PhoneNumbers = PhoneNumberInput.IsNullOrEmpty() ? null : PhoneNumberInput?.Select(x => {
            try
            {
                var phone = x.Split(' ', StringSplitOptions.RemoveEmptyEntries);
                return new PhoneNumber(phone[0], phone[1]);
            }
            catch (Exception)
            {
                return null;
            }
            }).ToList(),
    };

    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Name { get; set; }

    [DisplayName("Emails")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string EmailsFormatted => string.Join(", ", Emails);

    [DisplayName("PhoneNumbers")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string PhoneNumbersFormatted => PhoneNumbers.IsNullOrEmpty() ? string.Empty : string.Join(", ", PhoneNumbers?.Select(x => x.ToString()));


    [RemoveColumn]
    public List<PhoneNumber> PhoneNumbers { get; set; }

    [RemoveColumn]
    public List<string> Emails { get; set; } = [];

    [RemoveColumn]
    public List<string> PhoneNumberInput { get; set; } = [];

    [RemoveColumn]
    public bool IsValid => !EmailsFormatted.IsNullOrEmpty() || !PhoneNumberInput.IsNullOrEmpty();
}
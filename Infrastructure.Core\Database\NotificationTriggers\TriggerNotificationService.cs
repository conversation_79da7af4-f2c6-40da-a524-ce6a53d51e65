﻿using System.Diagnostics;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Core.SerializersAndConverters.NodaTimeSerializers;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.Extensions.DependencyInjection;
using Npgsql;

namespace LendQube.Infrastructure.Core.Database.NotificationTriggers;

internal sealed class TriggerNotificationService(TriggerConfiguration configuration, IServiceScopeFactory scopeFactory, NpgsqlDataSource dataSource, ILogManager<TriggerNotificationService> logger)
{
    private NpgsqlConnection connection;
    public async Task<bool> CreateAllTriggers(CancellationToken ct)
    {
        if (configuration.Triggers.Count == 0)
        {
            logger.LogWarning(EventSource.Infrastructure, EventAction.Triggers, "There are no triggers defined");
            return false;
        }

        connection = await dataSource.OpenConnectionAsync(ct);
        using var transaction = await connection.BeginTransactionAsync(System.Data.IsolationLevel.Serializable, ct);
        try
        {
            await using var createNotifyChangeCallbackCmd = new NpgsqlCommand(NotifyTriggerDbScripts.CreateNotify_WithOldData_ChangeCallback +
                NotifyTriggerDbScripts.CreateNotify_WithoutOldData_ChangeCallback +
                NotifyTriggerDbScripts.CreateNotify_With_OnlyChangedId_CallbackNameCallback
                , connection, transaction);
            await createNotifyChangeCallbackCmd.ExecuteNonQueryAsync(ct);

            var createScripts = NotifyTriggerDbScripts.CreateTriggers(configuration.Triggers);

            await using var createTriggerCmd = new NpgsqlCommand(createScripts, connection, transaction);
            await createTriggerCmd.ExecuteNonQueryAsync(ct);

            await transaction.CommitAsync(ct);

            logger.LogInformation(EventSource.Infrastructure, EventAction.Triggers, "Triggers created");

            return true;
        }
        catch (Exception e)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Triggers, e, "Unable to create triggers");
            await transaction.RollbackAsync(ct);
        }

        return false;
    }

    private async Task StartupTriggerHandlers(CancellationToken ct)
    {
        var triggerBaseType = typeof(IHandleTriggerNotification<>);
        await using var scope = scopeFactory.CreateAsyncScope();
        foreach (var trigger in configuration.Triggers)
        {
            var genericType = triggerBaseType.MakeGenericType(trigger.DataType);
            var subscribers = scope.ServiceProvider.GetServices(genericType);
            if (!subscribers.Any())
            {
                continue;
            }

            await Parallel.ForEachAsync(subscribers, new ParallelOptions { MaxDegreeOfParallelism = subscribers.Count(), CancellationToken = ct }, async (subscriber, ct) =>
            {
                await (Task)genericType.GetMethod(nameof(IHandleTriggerNotification<IEntityHasNotifyTrigger>.OnStartup)).Invoke(subscriber, parameters: [ct]);
            });
        }
    }

    public async Task Listen(CancellationToken ct)
    {
        try
        {
            await using var listenCmd = new NpgsqlCommand($"LISTEN {NotifyTriggerDbScripts.ChangeEventName};", connection);
            await listenCmd.ExecuteNonQueryAsync(ct);
            connection.Notification += async (_, args) => await HandleNotification(args, ct);
            await StartupTriggerHandlers(ct);
            while (!ct.IsCancellationRequested)
                await connection.WaitAsync(ct);
        }
        catch (Exception e)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Triggers, e, "Unable to listen data changes");
        }
    }

    private async Task HandleNotification(NpgsqlNotificationEventArgs args, CancellationToken ct)
    {
        try
        {
            var payload = PostgreJsonSerializer.Deserialize<NotifyTriggerPayload>(args.Payload);
            if (payload == null) return;

            var change = payload.Action.ToEnum<TriggerChange>();
            var trigger = configuration.Triggers.FirstOrDefault(x => x.Table == payload.Table && x.On.Contains(change));

            if (trigger == null)
            {
                Debug.WriteLine($"{EventSource.Infrastructure} {EventAction.Triggers} There is no trigger defined for table {payload.Table} and change {payload.Action}");
                return;
            }

            await Publish(trigger, payload, change, ct);
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Triggers, ex, $"Unable to handle notification {args.Payload}");
        }
    }

    private async Task Publish(NotifyTrigger trigger, NotifyTriggerPayload payload, TriggerChange change, CancellationToken ct)
    {
        var genericType = typeof(IHandleTriggerNotification<>).MakeGenericType(trigger.DataType);
        await using var scope = scopeFactory.CreateAsyncScope();
        var subscribers = scope.ServiceProvider.GetServices(genericType);
        if (!subscribers.Any())
        {
            logger.LogWarning(EventSource.Infrastructure, EventAction.Triggers, $"There is no notification handler defined for {genericType.FullName}");
            return;
        }

        var oldData = payload.OldData != null ? PostgreJsonSerializer.Deserialize(payload.OldData, trigger.DataType) : null;
        var newData = payload.NewData != null ? PostgreJsonSerializer.Deserialize(payload.NewData, trigger.DataType) : null;
        var id = payload.Id?.ToString();

        await Parallel.ForEachAsync(subscribers, new ParallelOptions { MaxDegreeOfParallelism = subscribers.Count(), CancellationToken = ct }, async (subscriber, ct) =>
        {
            await (ValueTask)genericType.GetMethod(nameof(IHandleTriggerNotification<IEntityHasNotifyTrigger>.OnChanged)).Invoke(subscriber, parameters: [id, oldData, newData, change, ct]);
        });
    }

    public async Task RemoveTriggers(CancellationToken ct)
    {
        try
        {
            await using var removeCmd = new NpgsqlCommand(NotifyTriggerDbScripts.RemoveTriggers(configuration.Triggers), connection);
            await removeCmd.ExecuteNonQueryAsync(ct);
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Triggers, ex, "Unable to remove triggers");
        }
    }
}


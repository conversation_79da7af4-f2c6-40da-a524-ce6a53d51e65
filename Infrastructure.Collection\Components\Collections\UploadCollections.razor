﻿@page "/collections/uploads"
@using LendQube.Entities.Collection.Collections
@using LendQube.Entities.Core.Messaging
@using LendQube.Entities.Core.Uploads
@using LendQube.Infrastructure.Collection.UploadServices
@using LendQube.Infrastructure.Collection.UploadServices.UploadTypes
@using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels
@using LendQube.Infrastructure.Collection.ViewModels.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using Radzen.Blazor

@inject CollectionUploadService uploadService 
@inherits GenericCrudTable<CollectionFileUpload>

@attribute [Authorize(Policy = CollectionNavigation.UploadIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Type">Type</label>
            <InputSelect @bind-Value="context.Type">
                <option label="Select upload type" selected></option>
                @foreach (var option in Enum.GetValues<CollectionFileUploadType>())
                {
                    <option value="@option">@option.GetDisplayName()</option>
                }
            </InputSelect>
            <ValidationMessage For="() => context.Status" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Action">Action</label>
            <InputSelect @bind-Value="context.Action">
                <option label="Select upload action" selected></option>
                @foreach (var option in Enum.GetValues<UploadAction>())
                {
                    <option value="@option">@option.GetDisplayName()</option>
                }
            </InputSelect>
            <ValidationMessage For="() => context.Action" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Name">Attach File (Excel/Csv)</label>
            <InputFile OnChange="LoadFile" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Description">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
        
        @if(context.Type == CollectionFileUploadType.Placement)
        {
            <div class="form-row">
                <label class="form-label" for="ConfigId">Message Config</label>
                <RadzenDropDown @bind-Value=@context.MessageConfigId Data=@configs
                                TextProperty="@nameof(MessageConfigVM.Name)" ValueProperty="@nameof(MessageConfigVM.Id)"
                                Name="MessageConfigId" Placeholder="Select message" class="form-input"
                                FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
                <ValidationMessage For="() => context.MessageConfigId" class="text-danger" />
                <small class="text_sm_medium text_small">Leave empty to use default</small>
            </div>
        }
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>

        <div class="form-row">
            <label class="form-label" for="Action">Action</label>
            <InputSelect @bind-Value="context.Action">
                <option label="Update upload action" selected></option>
                @foreach (var option in Enum.GetValues<UploadAction>())
                {
                    <option value="@option">@option.GetDisplayName()</option>
                }
            </InputSelect>
            <ValidationMessage For="() => context.Action" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Name">Attach File (Excel/Csv)</label>
            <InputFile OnChange="LoadFile" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
        </div>
    </BodyContent>
</ModalEditComponent>

@code
{
    private bool savedSuccessfully = false;
    private string message = null;
    private IBrowserFile file;
    private List<MessageConfigVM> configs = [];

    protected override void OnInitialized()
    {
        Title = "Collections";
        FormBaseTitle = "Upload";
        SubTitle = "Uploads For Collection";
        CreatePermission = CollectionNavigation.UploadCreatePermission;
        EditPermission = CollectionNavigation.UploadEditPermission;
        DeletePermission = CollectionNavigation.UploadDeletePermission;
        configs = Service.CrudService.Db.ManySelect(Query<MessageConfiguration, MessageConfigVM>.Where(x => x.Name.StartsWith(MessageConfigNames.NewPlacement.GetDisplayName())).Select(x => new MessageConfigVM(x.Id, x.Name)));

        AddRowButton(new RowActionButton("Download Analysis File", Icon: "download", IconClass: "__danger", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;
            var template = row as CollectionFileUpload;
            await JSRuntime.DownloadFile(Path.GetFileName(template.AnalysisFileUrl), template.AnalysisFileUrl, Cancel);
            table.Loading = false;

            StateHasChanged();
        }, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as CollectionFileUpload).AnalysisFileUrl)));

        AddRowButton(new RowActionButton("Download Upload File", Icon: "download", IconClass: "__edit", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;
            var template = row as CollectionFileUpload;
            await JSRuntime.DownloadFile(Path.GetFileName(template.FileUrl), template.FileUrl, Cancel);
            table.Loading = false;

            StateHasChanged();
        }, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as CollectionFileUpload).FileUrl)));
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        TableDefinition.HasEdit = false;


        AddRowButton(EditPermission, new RowActionButton("Modify Upload", Icon: "upload-cloud", Action: async (object row) =>
        {
            await StartEdit(row as CollectionFileUpload, Cancel);
        }, ShowCondition: (object row) => (row as CollectionFileUpload).Status != UploadStatus.Imported));

        AddRowButton(EditPermission, new RowActionButton("Import", ButtonClass: "btn--success", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;
            var template = row as CollectionFileUpload;
            var result = await uploadService.Queue(template.Id, UploadAction.Import, Cancel);

            table.Loading = false;
            SetStatusMessage(result.IsSuccessful, result.Message);

        }, ShowCondition: (object row) => (row as CollectionFileUpload).Status == UploadStatus.Analyzed));
    }

    private void SetStatusMessage(bool result, string message)
    {
        if (result)
        {
            CollectionUploadService.StatusNotificationEvent += UploadCompleted;
            TableMessage.Info(message, true);
            StateHasChanged();
        }
    }

    public void UploadCompleted(object sender, SystemBackgroundTaskEventArgs e)
    {
        if (e.Owner != UserName)
            return;

        _ = InvokeAsync(async () =>
        {
            await table.Refresh();
            TableMessage.Set(e.Successful, e.Message);
            StateHasChanged();
        });

        CollectionUploadService.StatusNotificationEvent -= UploadCompleted;
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new() { HasEdit = false, HasId = false });

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Description, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.FileUrl, filterAndPage.TextFilter);
    }

    private void LoadFile(InputFileChangeEventArgs e)
    {
        file = e.File;
    }

    protected override async ValueTask SubmitAdd()
    {
        await BaseSaveAdd(async () =>
        {
            var result = await uploadService.Save(AddModel, file, Cancel);
            savedSuccessfully = result.IsSuccessful;
            message = result.Message;
            CustomMessage = message;
            return savedSuccessfully;
        },
        async () =>
        {
            AddModel = new();
            file = null;
            await table.Refresh();
        });

        SetStatusMessage(savedSuccessfully, message);
        savedSuccessfully = false;
    }

    protected override async ValueTask SubmitEdit()
    {
        await BaseSaveEdit(async () =>
        {
            var result = await uploadService.Save(EditModel, file, Cancel);

            savedSuccessfully = result.IsSuccessful;
            message = result.Message;

            CustomMessage = message;
            return savedSuccessfully;
        },
        async () =>
        {
            EditModel = new();
            file = null;
            await table.Refresh();
        });

        SetStatusMessage(savedSuccessfully, message);
        savedSuccessfully = false;
    }

    protected override async ValueTask<bool> SubmitDelete(CollectionFileUpload data, Func<Task> refresh, CancellationToken ct) => await SaveDelete(() => uploadService.Delete(data, ct), refresh);

    public override void Dispose()
    {
        base.Dispose();
        CollectionUploadService.StatusNotificationEvent -= UploadCompleted;
    }

}

﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using NodaTime;

namespace LendQube.Entities.Collection.Customers;

public class CustomerPromiseToPay : BaseEntityWithIdentityId<CustomerPromiseToPay>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    [Required]
    public decimal Amount { get; set; }
    public decimal AmountPaid { get; set; }
    [Required]
    public Instant? DueDate { get; set; }
    public bool Redeemed { get; set; }
    [Required]
    public long NoteId { get; set; }
    public virtual CustomerNote Note { get; set; }
}

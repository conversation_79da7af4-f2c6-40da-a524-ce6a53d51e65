﻿using System.Data;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Infrastructure.Collection.Workflow.Debt.ViewModels;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Workflow.Debt;

public sealed class WorkflowAssignmentService(IUnitofWork uow, IClock clock, DefaultAppConfig config, ILogManager<WorkflowAssignmentService> logger)
{
    public async Task AutoAssignAccountToAllAvailableAgents(CancellationToken ct)
    {
        var now = clock.GetCurrentInstant();
        var availableAgents = await uow.Db.ManyAsync(Query<AgentWorkflowAvailability>.Where(x => x.Start <= now && !x.End.HasValue && (x.Status == AgentAvailabilityStatus.Available || x.Status == AgentAvailabilityStatus.Busy)), ct);

        foreach (var agent in availableAgents)
        {
            await AutoAssignAccountToSingleAgent(agent, ct);
        }
    }

    public async Task AutoAssignAccountToSingleAgent(AgentWorkflowAvailability data, CancellationToken ct)
    {
        if (data is not { Status: AgentAvailabilityStatus.Available or AgentAvailabilityStatus.Busy })
            return;

        var now = clock.GetCurrentInstant();
        var time = now.InUtc().TimeOfDay.ToTimeOnly();
        var activeSegments = await uow.Db.ManySelectAsync(Query<AgentToDebtSegmentMapping>.Where(x => x.UserId == data.UserId && x.Enabled).Select(AgentDebtSegmentWithRulesVM.Mapping)
            .Where(x => (!x.Start.HasValue || time >= x.Start) && (!x.End.HasValue || time <= x.End) && x.RecordsPerTime > data.CurrentlyAssignedCount), ct);

        if (activeSegments.Count == 0)
            return;

        Dictionary<long, List<string>> chosenProfiles = [];

        var orderedSegments = activeSegments.OrderBy(x => x.Id).ThenBy(x => x.Priority);

        foreach (var segment in orderedSegments)
        {
            var totalToAssign = segment.RecordsPerTime - data.CurrentlyAssignedCount;
            if (totalToAssign <= 0)
                break;

            var preparedQuery = BuildRuleQuery(segment, now);

            if (preparedQuery.IsFailed)
                continue;

            var records = await preparedQuery.Data.Take(totalToAssign).ToListAsync(ct);

            if (records.Count > 0)
            {
                if (chosenProfiles.TryGetValue(segment.DebtSegmentId, out var profiles))
                    profiles.AddRange(records);
                else
                    chosenProfiles[segment.DebtSegmentId] = records;

                data.CurrentlyAssignedCount += records.Count;
                data.TotalAssigned += records.Count;

                _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => records.Contains(x.Id), x => x.SetProperty(y => y.LastAssignedDate, now)
                .SetProperty(y => y.CurrentlyAssigned, true), ct);
            }

            if (records.Count >= segment.RecordsPerTime)
                break;
        }

        if (chosenProfiles.Count == 0)
            return;

        var workflows = chosenProfiles.SelectMany(x => x.Value.Select(y => new AgentWorkflowTask
        {
            AvailabilityId = data.Id,
            DebtSegmentId = x.Key,
            UserId = data.UserId,
            CustomerProfileId = y,
            Assigned = now,
        }));

        uow.Db.InsertBulk(workflows, ct);

        data.Status = AgentAvailabilityStatus.Busy;
        uow.Db.Update(data);

        await uow.SaveAsync(ct);
    }

    private Result<IQueryable<string>> BuildRuleQuery(AgentDebtSegmentWithRulesVM segment, Instant now)
    {
        var dateOnly = DateOnly.FromDateTime(now.ToDateTimeOffset().DateTime);
        var utcDate = now.InUtc().Date;

        Expression<Func<CustomerProfile, bool>> whereQuery = null;

        foreach (var rule in segment.Rules)
        {
            (DateOnly? minDate, DateOnly? maxDate) = rule.Type switch
            {
                SegmentType.DueIn => (rule.MinTypeNumber.HasValue ? dateOnly.AddDays(rule.MinTypeNumber.Value) : (DateOnly?)null, rule.MaxTypeNumber.HasValue ? dateOnly.AddDays(rule.MaxTypeNumber.Value) : (DateOnly?)null),
                SegmentType.PastDue => (rule.MinTypeNumber.HasValue ? dateOnly.AddDays(-1 * rule.MinTypeNumber.Value) : null, rule.MaxTypeNumber.HasValue ? dateOnly.AddDays(-1 * rule.MaxTypeNumber.Value) : null),
                _ => default
            };

            (LocalDate? minUtcDate, LocalDate? maxUtcDate) = rule.Type switch
            {
                SegmentType.PTPDueIn => (rule.MinTypeNumber.HasValue ? utcDate.PlusDays(rule.MinTypeNumber.Value) : (LocalDate?)null, rule.MaxTypeNumber.HasValue ? utcDate.PlusDays(rule.MaxTypeNumber.Value) : (LocalDate?)null),
                SegmentType.PTPPastDue => (rule.MinTypeNumber.HasValue ? utcDate.PlusDays(-1 * rule.MinTypeNumber.Value) : null, rule.MaxTypeNumber.HasValue ? utcDate.PlusDays(-1 * rule.MaxTypeNumber.Value) : null),
                SegmentType.Status => rule.StatusType switch
                {
                    SegmentStatusType.NoPayment or SegmentStatusType.PaymentWithNoSchedule => (rule.MinTypeNumber.HasValue ? utcDate.PlusMonths(-1 * rule.MinTypeNumber.Value) : null,
                    rule.MaxTypeNumber.HasValue ? utcDate.PlusMonths(-1 * rule.MaxTypeNumber.Value) : null),
                    SegmentStatusType.PaymentFailed => (rule.MinTypeNumber.HasValue ? utcDate.PlusDays(-1 * rule.MinTypeNumber.Value) : null,
                    rule.MaxTypeNumber.HasValue ? utcDate.PlusDays(-1 * rule.MaxTypeNumber.Value) : null),
                    _ => default,
                },
                _ => default
            };

            Expression<Func<CustomerProfile, bool>> preparedQuery = rule.Type switch
            {
                SegmentType.Due => x => x.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.Due && y.PaymentStatus != SchedulePaymentStatus.Paid),
                SegmentType.DueIn => (minDate, maxDate) switch
                {
                    (not null, null) => x => x.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.NotDue && y.PaymentStatus != SchedulePaymentStatus.Paid && y.CPADate == minDate),
                    (null, not null) => x => x.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.NotDue && y.PaymentStatus != SchedulePaymentStatus.Paid && y.CPADate == maxDate),
                    (not null, not null) => x => x.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.NotDue && y.PaymentStatus != SchedulePaymentStatus.Paid && y.CPADate >= minDate && y.CPADate <= maxDate),
                    _ => null
                },
                SegmentType.CallBack => x => x.NextCallbackDate.HasValue && x.NextCallbackDate.Value == dateOnly,
                SegmentType.PastDue => (minDate, maxDate) switch
                {
                    (not null, null) => x => x.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.PastDue && y.PaymentStatus != SchedulePaymentStatus.Paid && y.CPADate == minDate),
                    (null, not null) => x => x.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.PastDue && y.PaymentStatus != SchedulePaymentStatus.Paid && y.CPADate == maxDate),
                    (not null, not null) => x => x.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.PastDue && y.PaymentStatus != SchedulePaymentStatus.Paid && y.CPADate >= maxDate && y.CPADate <= minDate),
                    _ => null
                },
                SegmentType.Flag => x => x.Flags.Any(y => y.Id == rule.FlagId),
                SegmentType.Status => rule.StatusType switch
                {
                    SegmentStatusType.New => x => x.Placements.Any(y => y.Status == PlacementStatus.New),
                    SegmentStatusType.NoPayment => (minUtcDate, maxUtcDate) switch
                    {
                        (not null, null) => x => !x.Transactions.Any(y => y.Successful && y.CreatedDate.Value.InUtc().Date <= maxUtcDate),
                        (null, not null) => x => !x.Transactions.Any(y => y.Successful && y.CreatedDate.Value.InUtc().Date >= minUtcDate),
                        (not null, not null) => x => !x.Transactions.Any(y => y.Successful && y.CreatedDate.Value.InUtc().Date >= maxUtcDate && y.CreatedDate.Value.InUtc().Date <= minUtcDate),
                        _ => x => x.BalancePaid == 0
                    },
                    SegmentStatusType.NoContact => x => !x.Notes.Any(y => y.ContactType.HasValue),
                    SegmentStatusType.NoLogin => x => !x.AppUser.LastLoginDate.HasValue,
                    SegmentStatusType.ScheduleWithNoPaymentMethod => x => x.Schedules.Any() && !x.PaymentMethods.Any(y => y.Status == PaymentMethodStatus.Active),
                    SegmentStatusType.PaymentWithNoSchedule => (minUtcDate, maxUtcDate) switch
                    {
                        (not null, null) => x => x.Transactions.Any(y => y.Successful && y.CreatedDate.Value.InUtc().Date <= maxUtcDate) && !x.Schedules.Any(),
                        (null, not null) => x => x.Transactions.Any(y => y.Successful && y.CreatedDate.Value.InUtc().Date >= minUtcDate) && !x.Schedules.Any(),
                        (not null, not null) => x => x.Transactions.Any(y => y.Successful && y.CreatedDate.Value.InUtc().Date >= maxUtcDate && y.CreatedDate.Value.InUtc().Date <= minUtcDate) && !x.Schedules.Any(),
                        _ => x => x.Transactions.Any(y => y.Successful) && !x.Schedules.Any()
                    },
                    SegmentStatusType.PaymentFailed => (minUtcDate, maxUtcDate) switch
                    {
                        (not null, null) => x => x.Transactions.Any(y => !y.Successful && y.CreatedDate.Value.InUtc().Date <= maxUtcDate),
                        (null, not null) => x => x.Transactions.Any(y => !y.Successful && y.CreatedDate.Value.InUtc().Date >= minUtcDate),
                        (not null, not null) => x => x.Transactions.Any(y => !y.Successful && y.CreatedDate.Value.InUtc().Date >= maxUtcDate && y.CreatedDate.Value.InUtc().Date <= minUtcDate),
                        _ => x => x.Transactions.Any(y => !y.Successful)
                    },
                    _ => null
                },
                SegmentType.PTPDue => x => x.PTP.Any(y => y.DueDate.Value.InUtc().Date == utcDate && !y.Redeemed),
                SegmentType.PTPDueIn => (minUtcDate, maxUtcDate) switch
                {
                    (not null, null) => x => x.PTP.Any(y => !y.Redeemed && y.DueDate.Value.InUtc().Date == minUtcDate),
                    (null, not null) => x => x.PTP.Any(y => !y.Redeemed && y.DueDate.Value.InUtc().Date == maxUtcDate),
                    (not null, not null) => x => x.PTP.Any(y => !y.Redeemed && y.DueDate.Value.InUtc().Date >= minUtcDate && y.DueDate.Value.InUtc().Date <= maxUtcDate),
                    _ => null
                },
                SegmentType.PTPPastDue => (minUtcDate, maxUtcDate) switch
                {
                    (not null, null) => x => x.PTP.Any(y => !y.Redeemed && y.DueDate.Value.InUtc().Date == minUtcDate),
                    (null, not null) => x => x.PTP.Any(y => !y.Redeemed && y.DueDate.Value.InUtc().Date == maxUtcDate),
                    (not null, not null) => x => x.PTP.Any(y => !y.Redeemed && y.DueDate.Value.InUtc().Date >= maxUtcDate && y.DueDate.Value.InUtc().Date <= minUtcDate),
                    _ => null
                },
                _ => null
            };


            if (preparedQuery != null)
            {
                if (whereQuery == null)
                    whereQuery = preparedQuery;
                else
                    whereQuery = whereQuery.CombineWithAndAlso(preparedQuery);
            }
        }

        if (whereQuery == null)
        {
            logger.LogWarning(EventSource.BackgroundTask, EventAction.Workflow, "New segment configuration detected, add required query", data: segment);
            return "Invalid configuration for segment";
        }

        if (!config.DeployState.IsDemo)
            whereQuery = whereQuery.CombineWithAndAlso(x => !x.LastAssignedDate.HasValue || now - x.LastAssignedDate > Duration.FromHours(8));

        var result = uow.Db.GetQuery(Query<CustomerProfile>.Where(x => x.Placements.Any(x => x.Status < PlacementStatus.Settled) && !x.CurrentlyAssigned).AndWhere(whereQuery)
            .OrderBy(x => x.OrderByDescending(y => !y.LastAssignedDate.HasValue).ThenBy(y => y.LastAssignedDate))
            .Select(x => x.Id));

        return Result<IQueryable<string>>.Successful(data: result);
    }
}

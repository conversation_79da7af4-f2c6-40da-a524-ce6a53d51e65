﻿using LendQube.Entities.Collection.Setup;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Helpers;

internal static class CustomerHoldHelper
{
    public static Instant? GetExpiryFromHoldDuration(this HoldDurationConfig config, string timeZoneId)
    {
        if(config == null)
            return null;

        var timeZone = DateTimeZoneProviders.Tzdb.GetZoneOrNull(timeZoneId) ?? DateTimeZone.Utc;
        var now = SystemClock.Instance.GetCurrentInstant().InZone(timeZone).LocalDateTime;
        LocalDateTime? expiryDate = config.Duration switch
        {
           HoldDuration.Hours => now.PlusHours(config.Length),
           HoldDuration.Days => now.PlusDays(config.Length),
           HoldDuration.Weeks => now.PlusWeeks(config.Length),
           HoldDuration.Months => now.PlusMonths(config.Length),
           HoldDuration.Years => now.PlusYears(config.Length),
           _  => null,
        };

        return expiryDate.HasValue ? expiryDate.Value.InZoneLeniently(timeZone).ToInstant() : null;
    }
}

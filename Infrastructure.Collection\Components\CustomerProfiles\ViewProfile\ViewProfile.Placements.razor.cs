﻿using LendQube.Entities.Collection.Placements;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Collection.ViewModels.PlacementData;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<PlacementInProfileVM> placementTable;
    private ColumnList PlacementTableDefinition { get; set; }
    private long totalPlacements = 0;

    private void SetupPlacements()
    {
        PlacementTableDefinition = CrudService.GetTableDefinition<Placement, PlacementInProfileVM>();

        PlacementTableDefinition.RowActionButtons.Add(new RowActionButton("View", Icon: "eye", Action: (object row) =>
        {
            var placement = row as PlacementInProfileVM;
            NavigationManager.NavigateTo($"collections/placements/{placement.Id}");
            return Task.CompletedTask;
        }, ShowCondition: (_) => HasClaim(CollectionNavigation.PlacementViewPermission)));
    }

    private async ValueTask<TypedBasePageList<PlacementInProfileVM>> LoadPlacements(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<Placement>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Company, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.AccountId, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.FirstName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.LastName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.Email, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.PhoneNumber.Number, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.MobileNumber.Number, filterAndPage.TextFilter));
        }

        var result = await CrudService.GetTypeBasedPagedData(spec, filterAndPage, PlacementInProfileVM.Mapping, ct: ct);
        totalPlacements = result.Total;
        return result;
    }
}

﻿@page "/error"
@using System.Diagnostics

<PageTitle>Error</PageTitle>


<div class="pg-row grid grid-col-1 grid-tab-1">
    <div class="title-wrapper flex __justify-between __align-center">
        <span class="text_xl_medium">Error</span>
    </div>
    <div class="card">
        <div class="alert critical hasFlex">
            <div class="al-flex">
                <div class="al-content">
                    <span class="al__title" role="alert">
                        An error occurred while processing your request. Please contact system administrator
                    </span>

                    @if (ShowRequestId)
                    {
                        <p>
                            <strong>Request ID:</strong> <code>@RequestId</code>
                        </p>
                    }
                </div>
            </div>
        </div>
    </div>
</div>


@code{
    private string RequestId { get; set; }
    private bool ShowRequestId => !string.IsNullOrEmpty(RequestId);

    protected override void OnInitialized() =>
        RequestId = Activity.Current?.Id;
}

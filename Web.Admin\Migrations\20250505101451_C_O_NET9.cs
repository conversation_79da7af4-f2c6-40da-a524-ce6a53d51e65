﻿using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_NET9 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameTable(
                name: "AspNetUserClaims",
                newName: "AspNetUserClaims",
                newSchema: "public");

            migrationBuilder.RenameTable(
                name: "AspNetRoleClaims",
                newName: "AspNetRoleClaims",
                newSchema: "public");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Disabled,FailedLastCharge,Suspended")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Acquired,Discount,Stripe,Upload")
                .Annotation("Npgsql:Enum:collection.PaymentType", "Bank,Card,SetupCard")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Monthly,Weekly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Completed,Failed,Initiated,PendingRefund,Processing,Queued,Refunded,Successful,Validated")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Failed,Granted")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Idle,Running,Stopped,Stopping")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Failed,Queued,Running,Success")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Android,Web,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "AdminReset2FA,ChangePassword,ChangePin,Complete2FASetup,Login2FA,LoginClientId,LoginRecoveryCode,NewLogin,Password,RefreshToken,RequestDeviceLoginToken,RequestRegistrationToken,ResetPassword,ResetPasswordConfirm,ValidateDeviceLoginToken,ValidateRegistrationToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "Delivered,Failed,Opened,Processing,Queued,Sent,SentPartially,WaitingForConfiguration")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload,Acquired,Discount")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "DataProtectionKeys",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                schema: "public",
                table: "AspNetUserClaims",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn);

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                schema: "public",
                table: "AspNetRoleClaims",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn);

            //migrationBuilder.AlterSequence(
            //    name: "EntityFrameworkHiLoSequence",
            //    incrementBy: 100,
            //    oldIncrementBy: 10);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameTable(
                name: "AspNetUserClaims",
                schema: "public",
                newName: "AspNetUserClaims");

            migrationBuilder.RenameTable(
                name: "AspNetRoleClaims",
                schema: "public",
                newName: "AspNetRoleClaims");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload,Acquired,Discount")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Disabled,FailedLastCharge,Suspended")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Acquired,Discount,Stripe,Upload")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "Bank,Card,SetupCard")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Monthly,Weekly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Completed,Failed,Initiated,PendingRefund,Processing,Queued,Refunded,Successful,Validated")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Failed,Granted")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Idle,Running,Stopped,Stopping")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Failed,Queued,Running,Success")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Android,Web,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "AdminReset2FA,ChangePassword,ChangePin,Complete2FASetup,Login2FA,LoginClientId,LoginRecoveryCode,NewLogin,Password,RefreshToken,RequestDeviceLoginToken,RequestRegistrationToken,ResetPassword,ResetPasswordConfirm,ValidateDeviceLoginToken,ValidateRegistrationToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "Delivered,Failed,Opened,Processing,Queued,Sent,SentPartially,WaitingForConfiguration");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "DataProtectionKeys",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "AspNetUserClaims",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn);

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "AspNetRoleClaims",
                type: "integer",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn);

            migrationBuilder.AlterSequence(
                name: "EntityFrameworkHiLoSequence",
                incrementBy: 10,
                oldIncrementBy: 100);
        }
    }
}

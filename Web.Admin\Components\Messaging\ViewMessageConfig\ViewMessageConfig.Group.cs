﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.EntityFrameworkCore;
using LendQube.Infrastructure.Core.Components.Helpers;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageConfig;

public partial class ViewMessageConfig
{
    private DataTable<MessagingGroupVM> groupTable;
    private ColumnList GroupTableDefinition { get; set; }
    private CreateMessageFromConfigVM SendMessageModel { get; set; } = new();
    private MessageConfigurationMessagingGroupVM DirectoryModel { get; set; } = new();
    private List<MessagingGroup> AllGroups { get; set; } = [];
    private bool HasGroupConfigured = false;
    private async ValueTask SubmitNewDirectory()
    {
        modalMessage.Close();
        if (DirectoryModel.Groups.IsNullOrEmpty())
        {
            modalMessage.Warning("No directory selected");
            return;
        }

        try
        {
            var data = DirectoryModel.Get(Data.Id);
            uow.Db.InsertBulk(data, Cancel);
            uow.Db.Insert(new MessagingConfigurationActivity { MessageConfigurationId = Data.Id, Activity = $"Directories with id {string.Join(", ", data.Select(x => x.MessagingGroupId))} added" });
            await uow.SaveAsync(Cancel);
            DirectoryModel = new();
            await activityTimeline.Refresh();
            await groupTable.Refresh();
            StateHasChanged();
            message.Success("Directory updated successfully");
        }
        catch (Exception)
        {
            modalMessage.Error("Updating directory failed");
        }

        await jSRuntime.CloseModal(addGroupModal, Cancel);
    }

    protected async ValueTask<bool> SubmitGroupDelete(MessagingGroupVM data, Func<Task> refresh, CancellationToken ct)
    {
        message.Close();
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<MessageConfigurationMessagingGroup>(x => x.MessagingGroupId == data.Id && x.MessageConfigurationId == Data.Id, ct);
        await refresh();
        if (result > 0)
        {
            uow.Db.Insert(new MessagingConfigurationActivity { MessageConfigurationId = Data.Id, Activity = $"Directory {data.Name} deleted" });
            await uow.SaveAsync(Cancel);
            await activityTimeline.Refresh();
            message.Success("Directory deleted successfully");
        }
        else
        {
            message.Error("Deleting directory failed");
        }
        StateHasChanged();
        return result > 0;
    }

    private async ValueTask<TypedBasePageList<MessagingGroupVM>> LoadGroups(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        groupService.PrimaryCriteria = x => x.Configs.Any(y => y.Id == Data.Id);

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            groupService.PrimaryCriteria = groupService.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter));
        }

        var result = await groupService.CrudService.GetTypeBasedPagedData(groupService, filterAndPage, MessagingGroupVM.Mapping, ct: ct);
        AllGroups = await uow.Db.ManyAsync(Query<MessagingGroup>.Where(x => !x.ConfigGroups.Any(y => y.MessageConfigurationId == Data.Id)), Cancel);
        HasGroupConfigured = result.Total > 0;
        return result;
    }
}

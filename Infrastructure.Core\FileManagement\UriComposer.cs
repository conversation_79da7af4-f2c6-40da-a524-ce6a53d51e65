﻿using LendQube.Infrastructure.Core.AppSettings;

namespace LendQube.Infrastructure.Core.FileManagement;

public sealed class UriComposer(DefaultAppConfig config)
{
    public string ComposeUri(string uriTemplate)
    {
        if (string.IsNullOrEmpty(uriTemplate))
            return string.Empty;
        return config.Url.WebAdmin + uriTemplate.Replace("~/", string.Empty);

    }

    public string FriendlyUrl(string uri)
    {
        if (!string.IsNullOrEmpty(uri))
        {
            uri = uri.Replace("&", string.Empty).Replace(' ', '-').Replace("--", "-").Replace(".", string.Empty);
        }
        return uri?.ToLower() ?? string.Empty;
    }
}
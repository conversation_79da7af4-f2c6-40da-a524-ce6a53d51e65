﻿using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_Okereke_UpdateMessageEntry : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "BodyHtmlTemplate",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "BodyTextTemplate",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Channels",
                schema: "core",
                table: "MessageLogEntry",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<string>(
                name: "ContainerHtmlTemplate",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "ContainerTextTemplate",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "EmailRequired",
                schema: "core",
                table: "MessageLogEntry",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "HtmlTemplate",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<List<string>>(
                name: "Keys",
                schema: "core",
                table: "MessageLogEntry",
                type: "text[]",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Subject",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "TextRequired",
                schema: "core",
                table: "MessageLogEntry",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "TextTemplate",
                schema: "core",
                table: "MessageLogEntry",
                type: "text",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BodyHtmlTemplate",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "BodyTextTemplate",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "Channels",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "ContainerHtmlTemplate",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "ContainerTextTemplate",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "EmailRequired",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "HtmlTemplate",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "Keys",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "Name",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "Subject",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "TextRequired",
                schema: "core",
                table: "MessageLogEntry");

            migrationBuilder.DropColumn(
                name: "TextTemplate",
                schema: "core",
                table: "MessageLogEntry");
        }
    }
}

﻿using System.Security.Claims;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;

namespace LendQube.Infrastructure.Core.Components;

public abstract class AuthComponentBase : AsyncComponentBase
{
    [CascadingParameter]
    private Task<AuthenticationState> AuthenticationState { get; set; }
    [Inject]
    public IOptions<IdentityOptions> Options { get; set; }
    private AuthenticationState authState;

    protected string UserName { get; set; }
    protected string Role { get; set; }
    protected Guid UserId { get; set; }

    private void CheckIsInitialized()
    {
        if (authState == null)
            throw new InvalidOperationException($"{nameof(AuthComponentBase)} {nameof(OnInitializedAsync)} must be called first");
    }

    protected override async Task OnInitializedAsync()
    {
        if (AuthenticationState is not null && authState == null)
        {
            authState = await AuthenticationState;
            UserName = authState.User.Identity.Name;
            Role = authState.User.FindFirstValue(Options.Value.ClaimsIdentity.RoleClaimType);
            var userId = authState.User.FindFirstValue(Options.Value.ClaimsIdentity.UserIdClaimType);
            UserId = Guid.Parse(userId);
        }
    }

    protected bool HasClaim(string claim)
    {
        if (string.IsNullOrEmpty(claim))
            return true;

        CheckIsInitialized();

        var permissions = claim.Split(",", StringSplitOptions.TrimEntries);
        return authState.User.Claims?.Any(c => permissions.Contains(c.Value)) ?? false;
    }

    protected ClaimsPrincipal GetPrincipalUser()
    {
        CheckIsInitialized();
        return authState.User;
    }
}

:root {
    --rz-body-font-size: 1.4rem;
    --rz-text-font-family: "MatterSQ", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell, "Helvetica Neue", sans-serif;
}

.sk-spinner {
    display: none;
}

.sk-loading {
    position: relative;
}

.sk-loading:after {
    content: '';
    background-color: rgba(255, 255, 255, 0.7);
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.sk-loading > .sk-spinner {
    display: block;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    z-index: 1;
}

.sk-spinner-rotating-plane.sk-spinner {
    width: 30px;
    height: 30px;
    background-color: #1ab394;
    margin: 0 auto;
    -webkit-animation: sk-rotatePlane 1.2s infinite ease-in-out;
    animation: sk-rotatePlane 1.2s infinite ease-in-out;
}

@-webkit-keyframes sk-rotate<PERSON>lane {
    0% {
        -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);
        transform: perspective(120px) rotateX(0deg) rotateY(0deg);
    }

    50% {
        -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
        transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
    }

    100% {
        -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
        transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
    }
}

@keyframes sk-rotatePlane {
    0% {
        -webkit-transform: perspective(120px) rotateX(0deg) rotateY(0deg);
        transform: perspective(120px) rotateX(0deg) rotateY(0deg);
    }

    50% {
        -webkit-transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
        transform: perspective(120px) rotateX(-180.1deg) rotateY(0deg);
    }

    100% {
        -webkit-transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
        transform: perspective(120px) rotateX(-180deg) rotateY(-179.9deg);
    }
}

.sk-spinner-double-bounce.sk-spinner {
    width: 40px;
    height: 40px;
    position: relative;
    margin: 0 auto;
}

.sk-spinner-double-bounce .sk-double-bounce1,
.sk-spinner-double-bounce .sk-double-bounce2 {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #102a43;
    opacity: 0.6;
    position: absolute;
    top: 0;
    left: 0;
    -webkit-animation: sk-doubleBounce 2s infinite ease-in-out;
    animation: sk-doubleBounce 2s infinite ease-in-out;
}

.sk-spinner-double-bounce .sk-double-bounce2 {
    -webkit-animation-delay: -1s;
    animation-delay: -1s;
}

@-webkit-keyframes sk-doubleBounce {
    0%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

@keyframes sk-doubleBounce {
    0%, 100% {
        -webkit-transform: scale(0);
        transform: scale(0);
    }

    50% {
        -webkit-transform: scale(1);
        transform: scale(1);
    }
}

.modal-dialog-large {
    top: 0;
}

.modal-dialog.width-xlg {
    max-width: 720px;
}


.modal-dialog.width-70vw {
    max-width: 70vw;
}

.qrcode-container {
    text-align: center;
    width: 128px;
    height: 128px;
    margin: 0 auto;
    margin-bottom: 20px;
}

.icon-wrapper a {
    white-space: nowrap;
}

.big-form {
    max-width: 100%;
}

.rz-chart {
    height: 70vh !important;
}

.rz-minute-picker, .rz-hour-picker {
    height: auto !important;
}

.rz-dropdown {
    height: auto !important;
}

.rz-calendar-month-dropdown, .rz-calendar-year-dropdown {
    width: auto
}

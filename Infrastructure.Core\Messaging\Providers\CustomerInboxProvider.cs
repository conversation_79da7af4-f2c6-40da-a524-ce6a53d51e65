﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class CustomerInboxProvider : AbstractMessageProvider
{
    public const string Name = "CustomerInbox";
    protected override MessageChannel SupportedChannel => MessageChannel.CustomerInbox;

    public CustomerInboxProvider(IUnitofWork uow) : base(uow)
    {
        Config = MessagingCompiledQueries.GetProviderConfig(uow, Name) ?? new ProviderConfigVM { Disabled = true };
    }

    public override ProviderConfigVM Config { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        var leadingMessage = messages[0];
        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        var textMessages = messages
            .Where(y => y.Data.Any(x => x.ForUser))
            .Where(x => x.HasText || x.Data.Any(y => y.HasText))
            .AsParallel()
            .SelectMany(x => x.HasSeparateTemplateValues ?
                x.Data.Where(y => y.ForUser && y.HasText).Select(y => new CustomerInbox { MessageLogId = x.MessageId, Subject = y.Subject, Type = MessageConfigTemplateType.Text, UserId = y.UserId, Template = y.TextTemplate })
                : x.Data.Where(y => y.ForUser && y.HasText).Select(y => new CustomerInbox { MessageLogId = x.MessageId, Subject = x.Subject, Type = MessageConfigTemplateType.Text, UserId = y.UserId, Template = x.TextTemplate }));

        var htmlMessages = messages
            .Where(y => y.Data.Any(x => x.ForUser))
            .Where(x => x.HasHtml || x.Data.Any(y => y.HasHtml))
            .AsParallel()
            .SelectMany(x => x.Data.Where(y => y.ForUser && y.HasHtml).AsParallel().Select(y =>
            {
                SubstituteSingleReceiverHtmlTemplateTypeKeys(x, y);
                return new CustomerInbox
                {
                    MessageLogId = x.MessageId,
                    Subject = y.Subject,
                    Type = MessageConfigTemplateType.Html,
                    UserId = y.UserId,
                    Template = y.HtmlTemplate
                };
            })); 

        var data = textMessages.Concat(htmlMessages).DistinctBy(x => new { x.UserId, x.MessageLogId }).ToList();

        var successful = false;
        if(!data.IsNullOrEmpty())
        {
            uow.Db.InsertBulk(data, ct);
            successful = true;
        }

        var status = successful.ToMessageStatus();
        LogActivity(leadingMessage, status, Name, !successful ? "No system user found" : string.Empty);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, successful ? data.Count : 0, successful ? 0 : data.Count, null, null, ct);

        return status;
    }
}

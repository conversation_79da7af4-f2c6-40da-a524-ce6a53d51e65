﻿using Coravel.Invocable;
using Coravel.Pro;
using LendQube.Entities.Core.Logs;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Core.Telemetry;

public sealed class BackgroundUserAccessService(IUnitofWork uow) : IInvocable, IInvocableWithPayload<UserAccessLog>, IDoNotAutoRegister
{
    public UserAccessLog Payload { get; set; }
    public Task Invoke()
    {
        uow.Db.Insert(Payload);
        return uow.SaveAsync(default);
    }
}

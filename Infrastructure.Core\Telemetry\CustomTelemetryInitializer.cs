﻿using LendQube.Infrastructure.Core.Extensions;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using Microsoft.Net.Http.Headers;

namespace LendQube.Infrastructure.Core.Telemetry;

internal sealed class CustomTelemetryInitializer(IHttpContextAccessor accessor) : ITelemetryInitializer
{
    public void Initialize(ITelemetry telemetry)
    {
        // Is this a TrackRequest() ?
        if (telemetry is not RequestTelemetry requestTelemetry) return;
        bool parsed = int.TryParse(requestTelemetry.ResponseCode, out int code);
        if (!parsed) return;
        if (code >= 400 && code <= 401)
        {
            // If we set the Success property, the SDK won't change it:
            requestTelemetry.Success = true;

            // Allow us to filter these requests in the portal:
            requestTelemetry.Properties["Overridden400s"] = "true";
        }
        // else leave the SDK to set the Success property

        if (requestTelemetry.Context?.User != null)
        {
            // Set the user id on the Application Insights telemetry item.
            requestTelemetry.Context.User.AuthenticatedUserId = telemetry.Context.User.AccountId = telemetry.Context.User.Id = accessor?.HttpContext?.User?.Identity?.Name ?? "annonymous";
            requestTelemetry.Properties["My User Ip"] = accessor?.HttpContext?.GetIpAddress();
            var userAgent = accessor?.HttpContext?.Request?.Headers[HeaderNames.UserAgent];
            requestTelemetry.Properties["My User Agent"] = userAgent.HasValue && !userAgent.Value.IsNullOrEmpty() ? userAgent.Value[0] : "Unknown";
        }
    }
}
﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.ViewModels.Messaging;

public class MessageScheduleVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<MessageSchedule, MessageScheduleVM>> Mapping = data => new()
    {
        Id = data.Id,
        Name = data.Name,
        CronExpression = data.CronExpression,
        Frequency = data.Frequency,
        FrequencyNumber = data.FrequencyNumber,
        Days = data.Days,
        Active = data.ActiveOn.HasValue,
        TimeZone = data.TimeZone,
        RunCount = data.RunCount,
        TemplateValues = data.TemplateValues,
        Groups = data.Groups,
        MessageConfig = data.Config.Name,
        ConfigId = data.ConfigId,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp

    };

    public MessageSchedule Get() => new()
    {
        Id = Id,
        Name = Name,
        CronExpression = CronExpression,
        Frequency = Frequency,
        FrequencyNumber = FrequencyNumber,
        Days = DaysList.CombineFlags(),
        ActiveOn = Active ? NodaTime.SystemClock.Instance.GetCurrentInstant() : null, 
        RunCount = RunCount,
        Groups = Groups,
        ConfigId = ConfigId,
        TemplateValues = KeysWithValues.Select(x => new TemplateKeyValue(x.Key, x.Value)).ToList(),
    };

    public (bool, string) IsValid()
    {
        if (Groups.IsNullOrEmpty())
            return (false, "Select recipient group");

        return (true, string.Empty);
    }

    [Required, MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Name { get; set; }
    [MaxLength(EntityConstants.DEFAULT_ID_FIELD_LENGTH)]
    public string CronExpression { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public MessageScheduleFrequency Frequency { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public int FrequencyNumber { get; set; }
    public ScheduleDay Days { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public bool Active { get; set; }
    public string TimeZone { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public int RunCount { get; set; }
    public string MessageConfig { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public List<long> Groups { get; set; }
    public int TotalGroups => Groups.Count;

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public List<TemplateKeyValue> TemplateValues { get; set; } = [];

    [Required, TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long ConfigId { get; set; }


    [DisplayName("Days")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string DaysFormatted => string.Join(", ", Days.FlagsToDisplayList<ScheduleDay>());

    [RemoveColumn]
    public List<ScheduleDay> DaysList { get; set; } = [];

    [RemoveColumn]
    public Dictionary<string, string> KeysWithValues { get; set; } = [];
}


public sealed record MessageConfigForSchedulerVM(long Id, string Name, List<string> Keys = default);

﻿using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using EFCoreSecondLevelCacheInterceptor;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;

namespace LendQube.Infrastructure.Core.Database.Repository;

internal static class SqlQueryBuilderHelpers
{
    public static IQueryable<TVM> QSelect<T, TVM>(this IQueryable<T> query, Expression<Func<T, TVM>> selector) => query.Select(selector);
    public static IQueryable<T> QWhere<T>(this IQueryable<T> query, Expression<Func<T, bool>> filter) => filter == null ? query : query.Where(filter);
    public static IQueryable<T> QOrderBy<T>(this IQueryable<T> query, Func<IQueryable<T>, IOrderedQueryable<T>> orderBy) => orderBy == null ? query : orderBy(query);
    public static IQueryable<T> QDistinctBy<T>(this IQueryable<T> query, Expression<Func<T, object>> distinctBy) => distinctBy == null ? query : query.DistinctBy(distinctBy);
    public static IQueryable<T> QInclude<T>(this IQueryable<T> query, Func<IQueryable<T>, IIncludableQueryable<T, object>> includeProperties) where T : class => includeProperties == null ? query : includeProperties(query);
    public static IQueryable<T> QCache<T>(this IQueryable<T> query, bool cache, TimeSpan? duration = null) => cache ? query.Cacheable(CacheExpirationMode.Sliding, duration ?? TimeSpan.FromMinutes(10)) : query;
    public static IQueryable<T> QAsSplitQuery<T>(this IQueryable<T> query, bool isSplit) where T : class => isSplit ? query.AsSplitQuery() : query;
    public static IQueryable<T> QAsNoTracking<T>(this IQueryable<T> query, bool isNotTracked) where T : class => isNotTracked ? query.AsNoTracking() : query;
    public static IQueryable<T> QTake<T>(this IQueryable<T> query, int? take) => take is null ? query : query.Take(take.Value);
    public static IQueryable<T> QSkip<T>(this IQueryable<T> query, int? skip) => skip is null ? query : query.Skip(skip.Value);
}

public static class QueryExtensions
{
    public static IQueryable<TVM> GetQuery<T, TVM>(this IRelationalDbRepository db, Expression<Func<T, TVM>> query, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0) where T : class, IBaseEntityForRelationalDb
        => Query<T, TVM>.All(filePath, lineNumber).Select(query).Query(db.Queryable<T>());

    public static IQueryable<TVM> GetQuery<T, TVM>(this IRelationalDbRepository db, SqlSelectQueryBuilder<T, TVM> builder) where T : class, IBaseEntityForRelationalDb
        => builder.Query(db.Queryable<T>());

    public static IQueryable<T> GetQuery<T>(this IRelationalDbRepository db, SqlQueryBuilder<T> builder) where T : class, IBaseEntityForRelationalDb
       => builder.Query(db.Queryable<T>());
}

public class Query<T> where T : class, IBaseEntityForRelationalDb
{
    public static SqlQueryBuilder<T> All([CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0) => new() { CallerFilePath = filePath, CallerLineNumber = lineNumber };
    public static SqlQueryBuilder<T> Where(Expression<Func<T, bool>> query, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0)
        => new() { InternalWhere = query, CallerFilePath = filePath, CallerLineNumber = lineNumber };
    public static SqlQueryBuilder<T> OrderBy(Func<IQueryable<T>, IOrderedQueryable<T>> query, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0)
        => new SqlQueryBuilder<T>() { CallerFilePath = filePath, CallerLineNumber = lineNumber }.OrderBy(query);
    public static SqlQueryBuilder<T> Include(Func<IQueryable<T>, IIncludableQueryable<T, object>> query, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0)
        => new SqlQueryBuilder<T>() { CallerFilePath = filePath, CallerLineNumber = lineNumber }.Include(query);
    public static SqlQueryBuilder<T> SkipAndTake(int skip, int take, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0)
        => new SqlQueryBuilder<T>() { CallerFilePath = filePath, CallerLineNumber = lineNumber }.SkipAndTake(skip, take);
    public static SqlQueryBuilder<T> Cache([CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0)
        => new SqlQueryBuilder<T>() { CallerFilePath = filePath, CallerLineNumber = lineNumber }.Cache();
    public static SqlQueryBuilder<T> Track([CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0)
        => new SqlQueryBuilder<T>() { CallerFilePath = filePath, CallerLineNumber = lineNumber }.Track();
}

public class Query<T, TVM> : Query<T> where T : class, IBaseEntityForRelationalDb
{
    public static SqlSelectQueryBuilder<T, TVM> Select(Expression<Func<T, TVM>> query, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0) => new SqlSelectQueryBuilder<T, TVM>().Select(query, filePath, lineNumber);
}

public class SqlQueryBuilder<T> where T : class, IBaseEntityForRelationalDb
{
    internal SqlQueryBuilder()
    {

    }

    #region Properties
    internal Expression<Func<T, bool>> InternalWhere { get; set; }
    protected Func<IQueryable<T>, IOrderedQueryable<T>> InternalOrderBy { get; set; }
    protected Func<IQueryable<T>, IIncludableQueryable<T, object>> InternalInclude { get; set; }
    protected Expression<Func<T, object>> InternalDistinctBy { get; set; }
    protected int? InternalTake { get; set; } = null;
    protected int? InternalSkip { get; set; } = null;
    protected bool AsSplitQuery { get; set; } = false;
    protected bool AsNoTracking { get; set; } = true;
    protected bool InternalCache { get; set; } = false;
    #endregion

    #region DebugProperties
    internal string CallerFilePath { get; set; }
    internal int CallerLineNumber { get; set; }
    #endregion

    #region BuildQuery
    internal IQueryable<T> BaseQuery(IQueryable<T> query) => query
            .QWhere(InternalWhere)
            .QDistinctBy(InternalDistinctBy)
            .QOrderBy(InternalOrderBy)
            .QInclude(InternalInclude)
            .QAsSplitQuery(AsSplitQuery)
            .QSkip(InternalSkip)
            .QTake(InternalTake)
            .QAsNoTracking(AsNoTracking)
            .TagWithCallSite(CallerFilePath ?? string.Empty, CallerLineNumber);

    internal virtual IQueryable<T> Query(IQueryable<T> query) =>
             BaseQuery(query)
            .QCache(InternalCache);

    internal T BuildOne(IQueryable<T> query) =>
             Query(query)
            .FirstOrDefault();

    internal Task<T> BuildOneAsync(IQueryable<T> query, CancellationToken ct) =>
             Query(query)
            .FirstOrDefaultAsync(ct);

    internal List<T> BuildMany(IQueryable<T> query) =>
             [.. Query(query)];

    internal Task<List<T>> BuildManyAsync(IQueryable<T> query, CancellationToken ct) =>
             Query(query)
            .ToListAsync(ct);

    #endregion

    #region FluentMethods

    public SqlSelectQueryBuilder<T, TVM> Select<TVM>(Expression<Func<T, TVM>> query, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0) => new()
    {
        InternalWhere = InternalWhere,
        InternalOrderBy = InternalOrderBy,
        InternalInclude = InternalInclude,
        InternalTake = InternalTake,
        InternalSkip = InternalSkip,
        AsSplitQuery = AsSplitQuery,
        AsNoTracking = AsNoTracking,
        InternalCache = InternalCache,
        InternalSelect = query,
        CallerFilePath = filePath,
        CallerLineNumber = lineNumber
    };

    public SqlQueryBuilder<T> AndWhere(Expression<Func<T, bool>> where)
    {
        InternalWhere = InternalWhere.CombineWithAndAlso(where);
        return this;
    }

    public SqlQueryBuilder<T> OrWhere(Expression<Func<T, bool>> where)
    {
        InternalWhere = InternalWhere.CombineWithOrElse(where);
        return this;
    }

    public SqlQueryBuilder<T> OrderBy(Func<IQueryable<T>, IOrderedQueryable<T>> query)
    {
        InternalOrderBy = query;
        return this;
    }

    public SqlQueryBuilder<T> Include(Func<IQueryable<T>, IIncludableQueryable<T, object>> query)
    {
        InternalInclude = query;
        return this;
    }

    public SqlQueryBuilder<T> SkipAndTake(int skip, int take)
    {
        InternalSkip = skip;
        InternalTake = take;
        return this;
    }

    public SqlQueryBuilder<T> Cache()
    {
        InternalCache = true;
        return this;
    }

    public SqlQueryBuilder<T> AsSplit()
    {
        AsSplitQuery = true;
        return this;
    }

    public SqlQueryBuilder<T> Track()
    {
        AsNoTracking = false;
        return this;
    }

    public SqlQueryBuilder<T> DistinctBy(Expression<Func<T, object>> query)
    {
        InternalDistinctBy = query;
        return this;
    }

    #endregion
}

public sealed class SqlSelectQueryBuilder<T, TVM> : SqlQueryBuilder<T> where T : class, IBaseEntityForRelationalDb
{
    internal SqlSelectQueryBuilder()
    {

    }

    #region Properties
    internal Expression<Func<T, TVM>> InternalSelect { get; set; }
    private Expression<Func<TVM, bool>> InternalSelectWhere { get; set; }
    private Func<IQueryable<TVM>, IOrderedQueryable<TVM>> InternalSelectOrderBy { get; set; }
    #endregion

    #region BuildQuery
    internal new IQueryable<TVM> Query(IQueryable<T> query) =>
             BaseQuery(query)
            .QSelect(InternalSelect)
            .QWhere(InternalSelectWhere)
            .QOrderBy(InternalSelectOrderBy)
            .QCache(InternalCache);

    internal new TVM BuildOne(IQueryable<T> query) =>
             Query(query)
            .FirstOrDefault();

    internal new Task<TVM> BuildOneAsync(IQueryable<T> query, CancellationToken ct) =>
             Query(query)
            .FirstOrDefaultAsync(ct);

    internal new List<TVM> BuildMany(IQueryable<T> query) =>
             [.. Query(query)];

    internal new Task<List<TVM>> BuildManyAsync(IQueryable<T> query, CancellationToken ct) =>
             Query(query)
            .ToListAsync(ct);
    #endregion

    #region FluentMethods

    public SqlSelectQueryBuilder<T, TVM> Select(Expression<Func<T, TVM>> query, [CallerFilePath] string? filePath = null, [CallerLineNumber] int lineNumber = 0)
    {
        if (InternalSelect != null)
            throw new InvalidOperationException("Select can only be called once in query");
        InternalSelect = query;
        CallerFilePath = filePath;
        CallerLineNumber = lineNumber;
        return this;
    }

    public SqlSelectQueryBuilder<T, TVM> Where(Expression<Func<TVM, bool>> query)
    {
        InternalSelectWhere = query;
        return this;
    }

    public SqlSelectQueryBuilder<T, TVM> AndWhere(Expression<Func<TVM, bool>> where)
    {
        InternalSelectWhere = InternalSelectWhere.CombineWithAndAlso(where);
        return this;
    }

    public SqlSelectQueryBuilder<T, TVM> OrWhere(Expression<Func<TVM, bool>> where)
    {
        InternalSelectWhere = InternalSelectWhere.CombineWithOrElse(where);
        return this;
    }

    public SqlSelectQueryBuilder<T, TVM> OrderBy(Func<IQueryable<TVM>, IOrderedQueryable<TVM>> query)
    {
        InternalSelectOrderBy = query;
        return this;
    }

    #endregion
}

internal static class IQueryBuilderTranslators
{
    public static SqlSelectQueryBuilder<T, TVM> ToSqlSelectBuilder<T, TVM>(this ISpecification<T>? spec,
        Expression<Func<T, TVM>> selector,
        Expression<Func<TVM, bool>> filter,
        Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy) where T : class, IBaseEntityForRelationalDb
    {
        SqlQueryBuilder<T> builder = null;

        if (spec != null)
        {
            builder = Query<T>
                .Where(spec.PrimaryCriteria, spec.CallerFilePath, spec.CallerLineNumber)
                .Include(spec.Includes)
                .OrderBy(spec.OrderBy);

            spec.PrimaryCriteria = null; //clear primary criteria after run
        }

        return (builder ?? new SqlSelectQueryBuilder<T, TVM>())
            .Select(selector, spec.CallerFilePath, spec.CallerLineNumber)
            .Where(filter)
            .OrderBy(orderBy);
    }

    public static SqlQueryBuilder<T> ToSqlBuilder<T>(this ISpecification<T>? spec) where T : class, IBaseEntityForRelationalDb
    {
        if (spec == null) return new SqlQueryBuilder<T>();

        SqlQueryBuilder<T> builder = Query<T>
            .Where(spec.PrimaryCriteria, spec.CallerFilePath, spec.CallerLineNumber)
            .Include(spec.Includes)
            .OrderBy(spec.OrderBy);

        spec.PrimaryCriteria = null; //clear primary criteria after run

        return builder;
    }
}
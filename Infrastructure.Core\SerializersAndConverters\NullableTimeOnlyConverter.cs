﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.Core.SerializersAndConverters;

public sealed class NullableTimeOnlyConverter : JsonConverter<TimeOnly?>
{
    public override TimeOnly? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        if (string.IsNullOrEmpty(value)) return null;
        return TimeOnly.ParseExact(value, "hh:mm tt");
    }

    public override void Write(Utf8JsonWriter writer, TimeOnly? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.HasValue ? value.Value.ToString("hh:mm tt") : "");
    }
}

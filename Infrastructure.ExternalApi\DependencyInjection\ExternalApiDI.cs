﻿using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.ExternalApi.Acquired;
using LendQube.Infrastructure.ExternalApi.AppSettings;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Net.Http.Headers;

namespace LendQube.Infrastructure.ExternalApi.DependencyInjection;

public static class ExternalApiDI
{
    public static WebApplicationBuilder AddExternalApiSettings(this WebApplicationBuilder builder)
    {
        builder.Load<ExternalApiConfigProvidersVM>("externalapisettings");
        builder.Services.AddSingleton<ExternalApiConfig>();

        return builder;
    }


    public static WebApplicationBuilder AddAcquiredExternalApi(this WebApplicationBuilder builder)
    {
        builder.Services.AddHttpClient<IAcquiredExternalApiService, AcquiredExternalApiService>((sp, client) =>
        {
            var config = sp.GetRequiredService<ExternalApiConfig>();
            client.BaseAddress = new Uri(config.Acquired.BaseApiUrl);
            client.DefaultRequestHeaders.Add(HeaderNames.Accept, HttpHeaderHelper.JsonHeader);
            
        });

        return builder;
    }
}

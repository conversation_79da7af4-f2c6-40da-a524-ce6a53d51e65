﻿using System.Linq.Expressions;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

internal sealed class IsNotNullFilter() : ObjectFilter(ColumnFilterRule.IsNotNull)
{
    public override bool RequiresValue { get; set; } = false;
    public override Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value)
    {
        if (propertyType.IsGenericList())
            return ExpressionsExtension.AnyExpression<T>(propertyType, vm.ColumnName);

        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(vm.ColumnName);

        if (propertyType == typeof(string))
        {
            MethodCallExpression isNullOrEmptyCall = Expression.Call(typeof(string), nameof(string.IsNullOrWhiteSpace), null, expression);
            return Expression.Lambda<Func<T, bool>>(Expression.Not(isNullOrEmptyCall), pe);
        }

        UnaryExpression typeNull = Expression.ConvertChecked(Expression.Constant(null), propertyType);
        ConstantExpression nullConstant = Expression.Constant(null);
        BinaryExpression notNullCheck = Expression.AndAlso(Expression.NotEqual(expression, typeNull), Expression.NotEqual(expression, nullConstant));

        return Expression.Lambda<Func<T, bool>>(notNullCheck, pe);
    }

    public override bool IsTypeSupported(ObjectFilterRule rule) => Type.GetTypeCode(rule.Type) switch
    {
        TypeCode.String or TypeCode.DBNull or TypeCode.Empty or TypeCode.Object => true,
        _ => false,
    } && !rule.IsId && rule.FilterType != ColumnFilterDataType.DBList;
}

﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Location;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;

public sealed class CountryWithCurrencyVM
{
    public static readonly Expression<Func<Currency, CountryWithCurrencyVM>> Mapping = data => new CountryWithCurrencyVM
    {
        CountryCode = data.Country.Code,
        CountryName = data.Country.Name,
        PhoneCode = data.Country.PhoneCode,
        CurrencyCode = data.Code,
        CurrencySymbol = data.Symbol
    };
    public string CountryCode { get; set; }
    public string CountryName { get; set; }
    public string PhoneCode { get; set; }
    public string CurrencyCode { get; set; }
    public string CurrencySymbol { get; set; }
}

public sealed class UploadData
{
    public string ProfileId { get; set; }
    public string CurrencySymbol { get; set; }
    public ApplicationUser AppUser { get; set; }
    public CustomerProfile Profile { get; set; }
    public CustomerAddress Address { get; set; }
    public Placement Placement { get; set; }
    public List<CustomerActivity> CustomerActivities { get; set; } = [];
    public List<CustomerSchedule> SchedulesToCreate { get; set; } = [];
}

public sealed class SortedUploadData
{
    public ApplicationUser AppUser { get; set; }
    public CustomerProfile Profile { get; set; }
    public CustomerAddress Address { get; set; }
    public List<Placement> Placement { get; set; } = [];
}

public sealed class CustomerMapping
{
    public static readonly Expression<Func<CustomerProfile, CustomerMapping>> Mapping = (data) => new()
    {
        Id = data.Id,
        Email = data.Email,
        PaymentFrequency = data.PaymentFrequency,
        Schedules = data.Schedules.Select(x => new CustomerScheduleMapping { Period = x.Period, Amount = x.Amount, DueDate = x.DueDate }).ToList(),
        PostCodes = data.Addresses.Select(x => x.PostCode).ToList(),
    };

    public string Id { get; set; }
    public string Email { get; set; }
    public SchedulePaymentFrequency? PaymentFrequency { get; set; }
    public List<CustomerScheduleMapping> Schedules { get; set; }
    public List<string> PostCodes { get; set; }
}

public sealed class CustomerScheduleMapping
{
    public int Period { get; set; }
    public decimal Amount { get; set; }
    public DateOnly DueDate { get; set; }
}


public class CustomerProfileBalanceVM
{
    public static readonly Expression<Func<CustomerProfile, CustomerProfileBalanceVM>> Mapping = data => new()
    {
        Id = data.Id,
        DbBalancePaid = data.BalancePaid,
        DbBalanceTotal = data.BalanceTotal,
        BalanceTotal = data.Placements.Where(y => y.Status != PlacementStatus.Closed).Sum(y => y.BalanceTotal),
        BalancePaid = data.Placements.Where(y => y.Status != PlacementStatus.Closed).Sum(y => y.BalancePaid)
    };

    public string Id { get; set; }
    public decimal DbBalanceTotal { get; set; }
    public decimal DbBalancePaid { get; set; }
    public decimal BalanceTotal { get; set; }
    public decimal BalancePaid { get; set; }
}
﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_TieUploadsToPlacement : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "FileUploadId",
                schema: "collection",
                table: "Placement",
                type: "bigint",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_Placement_FileUploadId",
                schema: "collection",
                table: "Placement",
                column: "FileUploadId");

            migrationBuilder.AddForeignKey(
                name: "FK_Placement_CollectionFileUpload_FileUploadId",
                schema: "collection",
                table: "Placement",
                column: "FileUploadId",
                principalSchema: "collection",
                principalTable: "CollectionFileUpload",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Placement_CollectionFileUpload_FileUploadId",
                schema: "collection",
                table: "Placement");

            migrationBuilder.DropIndex(
                name: "IX_Placement_FileUploadId",
                schema: "collection",
                table: "Placement");

            migrationBuilder.DropColumn(
                name: "FileUploadId",
                schema: "collection",
                table: "Placement");
        }
    }
}

﻿using System.Diagnostics;
using LendQube.Entities.Core.Constants;
using NodaTime;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class TypeExtensions
{
    public static bool IsNumericType(this Type o) =>
        Type.GetTypeCode(o) switch
        {
            TypeCode.Byte or TypeCode.SByte or TypeCode.UInt16 or TypeCode.UInt32 or TypeCode.UInt64 or TypeCode.Int16 or TypeCode.Int32 or TypeCode.Int64 or TypeCode.Decimal or TypeCode.Double or TypeCode.Single => true,
            _ => false,
        };
    public static bool IsDoubleType(this Type o) =>
      Type.GetTypeCode(o) switch
      {
          TypeCode.Double => true,
          _ => false,
      };

    public static bool IsDecimalType(this Type o) =>
        Type.GetTypeCode(o) switch
        {
            TypeCode.Decimal => true,
            _ => false,
        };

    public static bool IsBooleanType(this Type o) =>
       Type.GetTypeCode(o) switch
       {
           TypeCode.Boolean => true,
           _ => false,
       };

    public static bool IsPhoneNumber(this Type type) => type == typeof(PhoneNumber);

    public static bool IsDate(this Type type) => type.IsDateTime() || type.IsDateOnly();

    public static bool IsDateOnly(this Type type) => type == typeof(DateOnly) || type == typeof(DateOnly?);

    public static bool IsTimeOnly(this Type type) => type == typeof(TimeOnly) || type == typeof(TimeOnly?);

    public static bool IsLocalTimeOnly(this Type type) => type == typeof(LocalTime) || type == typeof(LocalTime?);

    public static bool IsDateTime(this Type type) => type == typeof(DateTime) || type == typeof(DateTime?);

    public static bool IsDateTimeOffset(this Type type) => type == typeof(DateTimeOffset) || type == typeof(DateTimeOffset?);

    public static bool IsInstant(this Type type) => type == typeof(Instant) || type == typeof(Instant?);

    public static bool IsLocalDate(this Type type) => type == typeof(LocalDate) || type == typeof(LocalDate?);

    public static bool IsSupportedDateType(this Type type) => type.IsInstant() || type.IsDate() || type.IsDateTimeOffset() || type.IsTimeOnly() || type.IsLocalTimeOnly() || type.IsLocalDate();

    public static bool IsGenericList(this Type o) => o.IsGenericType && (o.GetGenericTypeDefinition() == typeof(List<>) || o.GetGenericTypeDefinition() == typeof(IEnumerable<>) || o.GetGenericTypeDefinition() == typeof(ICollection<>));

    public static bool IsGenericListString(this Type o) => o.IsGenericType && o.GenericTypeArguments.Contains(typeof(string)) && (o.GetGenericTypeDefinition() == typeof(List<>) || o.GetGenericTypeDefinition() == typeof(IEnumerable<>));

    public static bool IsNullableEnum(this Type t) => Nullable.GetUnderlyingType(t)?.IsEnum == true;

    public static bool IsEnum(this Type t) => t.IsEnum || t.IsNullableEnum();


    public static T DeepCopy<T>(this T input)
    {
        if (input is null)
            return default;

        var type = input.GetType();
        var properties = type.GetProperties().Where(x => x.CanWrite);

        T clonedObj = (T)Activator.CreateInstance(type);

        var count = properties.Count();
        Parallel.ForEach(properties, new ParallelOptions { MaxDegreeOfParallelism = count }, (property) =>
        {
            object value = property.GetValue(input);
            if (value != null && value.GetType().IsClass && !value.GetType().FullName.StartsWith("System."))
            {
                property.SetValue(clonedObj, DeepCopy(value));
            }
            else
            {
                property.SetValue(clonedObj, value);
            }
        });

        return clonedObj;
    }

}
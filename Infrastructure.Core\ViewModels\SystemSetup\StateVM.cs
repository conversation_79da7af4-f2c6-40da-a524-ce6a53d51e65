﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Location;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.ViewModels.SystemSetup;

public sealed class StateVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<State, StateVM>> Mapping = data => new StateVM
    {
        Id = data.Id,
        CountryId = data.CountryId,
        CountryName = data.Country.Name,
        StateCode = data.StateCode,
        StateName = data.StateName,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public State Get() => new()
    {
        Id = Id,
        CountryId = CountryId,
        StateCode = StateCode,
        StateName = StateName,
    };


    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.SkipFilter, TableDecoratorType.HideColumn)]
    public long CountryId { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string CountryName { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public string StateCode { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public string StateName { get; set; }

}


public sealed record CountryVM(long Id, string Name, string Code);
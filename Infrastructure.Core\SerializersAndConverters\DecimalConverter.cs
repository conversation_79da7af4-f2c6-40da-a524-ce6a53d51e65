﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.Core.SerializersAndConverters;

public sealed class DecimalConverter : JsonConverter<object>
{
    public override bool CanConvert(Type typeToConvert) => Type.GetTypeCode(typeToConvert) == TypeCode.Decimal;

    public override object Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var canConvert = decimal.TryParse(reader.GetString(), out var value);
        return canConvert ? value : reader.GetString();
    }

    public override void Write(Utf8JsonWriter writer, object value, JsonSerializerOptions options)
    {
        var canConvert = decimal.TryParse(value.ToString(), out var decimalValue);
        if (canConvert)
        {
            writer.WriteStringValue(decimalValue.ToString("n2"));
        }
    }
}
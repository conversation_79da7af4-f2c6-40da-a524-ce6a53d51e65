﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.PlacementData;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Messaging;
using Microsoft.AspNetCore.Components;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    [Inject] ManageScheduleService ScheduleService { get; set; }

    private string InitiatePaymentModalName => "InitiatePayment";
    private string ConfirmPaymentModalName => "ConfirmPayment";
    private string CreateScheduleModalName => "CreateSchedule";
    private string UpdateScheduleModalName => "UpdateSchedule";
    private RequestScheduleVM UpdateScheduleModel { get; set; } = new();
    private ScheduleResponseVM ScheduleResponseVM { get; set; } = new() { Txn = new TransactionResultVM(new() { Fields = [] }) { PaymentMethods = [] } };
    private InitiateTransactionRequest InitiatePaymentVM { get; set; } = new() { NotifyCustomer = true };
    private ProcessTransactionRequestAdminVM ConfirmPaymentVM { get; set; } = new();

    protected async ValueTask InitiateMakePayment()
    {
        CloseMessage();
        var result = await ScheduleService.InitiatePayment(Data.Id, InitiatePaymentVM, Cancel);
        if (result.IsSuccessful)
        {
            ScheduleResponseVM = result.Data;

            if (InitiatePaymentVM.NotifyCustomer)
            {
                MessageBuilder.New($"{MessageConfigNames.MakePayment.GetDisplayName()}", UserName)
                .Message($"{MessageConfigNames.MakePayment.GetDisplayName()}")
                .WithRecipient(Data.Id, [
                    new($"{MessageTemplateKeys.Amount}", $"{Data.CurrencySymbol}{ScheduleResponseVM.Amount:n2}"),
                    new($"{MessageTemplateKeys.Balance}", $"{Data.CurrencySymbol}{ScheduleResponseVM.BalanceAfterPayment:n2}"),
                    new($"{MessageTemplateKeys.PaymentLink}", ScheduleResponseVM.Txn.ProviderId),
                    ])
                .Queue(Queue);
            }

            await JSRuntime.CloseModal(InitiatePaymentModalName, Cancel);
            TableMessage.Success(result.Message);
            await JSRuntime.OpenModal(ConfirmPaymentModalName, Cancel);
        }
        else
            ModalMessage.Error(result.Message);

        StateHasChanged();
    }

    protected async ValueTask SubmitConfirmPayment()
    {
        CloseMessage();
        var result = await ScheduleService.ConfirmPayment(Data.Id, ConfirmPaymentVM.Get(ScheduleResponseVM.Txn.TxnId), Cancel);

        if (result.IsSuccessful)
        {
            if (result.Message == "Payment confirmed")
            {
                await JSRuntime.CloseModal(ConfirmPaymentModalName, Cancel);
                await LoadProfile();
                TableMessage.Success(result.Message);
                await activityTimeline.Refresh();
            }
            else
            {
                ModalMessage.Info(result.Message);
            }
        }
        else
            ModalMessage.Error(result.Message);

        StateHasChanged();
    }

    protected async ValueTask SubmitCreateSchedule()
    {
        CloseMessage();
        var result = await ScheduleService.NewSchedule(Data.Id, UpdateScheduleModel, Cancel);
        if (result.IsSuccessful)
        {
            ScheduleResponseVM = result.Data;

            MessageBuilder.New($"{MessageConfigNames.AddPaymentMethod.GetDisplayName()}", UserName)
            .Message($"{MessageConfigNames.AddPaymentMethod.GetDisplayName()}")
            .WithRecipient(Data.Id, [
                new($"{MessageTemplateKeys.Amount}", $"{Data.CurrencySymbol}{ScheduleResponseVM.Amount:n2}"),
                new($"{MessageTemplateKeys.Balance}", $"{Data.CurrencySymbol}{ScheduleResponseVM.BalanceAfterPayment:n2}"),
                new($"{MessageTemplateKeys.PaymentLink}", ScheduleResponseVM.Txn.ProviderId),
                new($"{MessageTemplateKeys.Frequency}", ScheduleResponseVM.Type.ToLower()),
                new($"{MessageTemplateKeys.StartDate}", $"{ScheduleResponseVM.StartDate:dd MMM, yyyy}"),
                new($"{MessageTemplateKeys.EndDate}", $"{ScheduleResponseVM.EndDate:dd MMM, yyyy}"),
                ])
            .Queue(Queue);

            await JSRuntime.CloseModal(CreateScheduleModalName, Cancel);
            TableMessage.Success(result.Message);
            await JSRuntime.OpenModal(ConfirmPaymentModalName, Cancel);
        }
        else
            ModalMessage.Error(result.Message);

        StateHasChanged();
    }


    protected async ValueTask SubmitUpdateSchedule()
    {
        CloseMessage();
        var result = await ScheduleService.Reschedule(Data.Id, UpdateScheduleModel, true, Cancel);
        if (result.IsSuccessful)
        {
            await JSRuntime.CloseModal(UpdateScheduleModalName, Cancel);
            await LoadProfile();
            TableMessage.Success(result.Message);
            await activityTimeline.Refresh();
        }
        else
            ModalMessage.Error(result.Message);

        StateHasChanged();
    }

    private async Task ClearAmendSchedule()
    {
        CloseMessage();
        _ = await CrudService.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == Data.Id, x => x.SetProperty(y => y.LastRescheduleDate, (NodaTime.Instant?)null), Cancel);
        TableMessage.Success("Customer can now amend their schedule");
        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Amend Schedule", Activity = "Schedule amendment granted successfully" });
        await uow.SaveAsync(Cancel);
        await activityTimeline.Refresh();
    }

    private async Task DeleteCustomerSchedules()
    {
        CloseMessage();

        _ = await CrudService.Db.DeleteAndSaveWithFilterAsync<CustomerSchedule>(x => x.ProfileId == Data.Id, Cancel);
        TableMessage.Success("All unpaid customer schedules removed successfully.");

        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Delete Schedules", Activity = "All unpaid schedules for the customer were successfully removed." });

        await uow.SaveAsync(Cancel);
        await activityTimeline.Refresh();
        Data.Schedules = [];
        StateHasChanged();
    }

}

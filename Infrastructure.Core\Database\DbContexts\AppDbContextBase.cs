﻿using System.Reflection;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.DbContexts;

public interface IAppDbContextBase
{
    public bool IsActive { get; }
    public string Schema { get; }
    void Create(ModelBuilder builder);
}

public static class AppDbContextBase
{
    private static readonly Dictionary<string, Assembly> assembliesConfigToApply = [];

    public static readonly List<Type> OwnedTypesToSkip = [];

    internal static void SetupContexts(this ModelBuilder builder)
    {
        var interfaceType = typeof(IAppDbContextBase);
        var asm = AppDomain.CurrentDomain.GetAssemblies()
           .AsParallel()
           .SelectMany(x => x.GetReferencedAssemblies())
           .Where(x => x.Name != null && (x.Name.StartsWith(CoreEntityConfig.InfrastructurePrefix)))
           .Select(Assembly.Load)
           .SelectMany(x => x.GetTypes())
           .Where(x => x.Name != null && x.Name.EndsWith("DbContext") && interfaceType.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
           .DistinctBy(x => x.Name)
           .Select(x => Activator.CreateInstance(x) as IAppDbContextBase);

        foreach (var item in asm)
        {
            if (!item.IsActive)
                continue;

            item.Create(builder);
        }
    }

    public static void OnModelCreate(this IAppDbContextBase context, ModelBuilder builder, ParallelQuery<AssemblyName>? entryAssembly)
    {
        builder.HasSequence<long>(CoreEntityConfig.HiloSequenceName, context.Schema)
            .IncrementsBy(100);

        if (entryAssembly is not null)
            builder.SetupEntities(entryAssembly, context.Schema);
    }

    internal static void CreateModels(this ModelBuilder builder)
    {
        var entityTypes = builder.Model.GetEntityTypes();

        List<Type> propertyAttributes = [typeof(DbDecimalAttribute), typeof(DbComputedAttribute), typeof(DbGuidAttribute), typeof(DbHiloAttribute)];
        var entityProperties = entityTypes.SelectMany(t => t.GetProperties()
            .Select(p => new { p.ClrType, DeclaringClrType = p.DeclaringType.ClrType, DeclaredProperty = p.DeclaringType.ClrType.GetProperty(p.Name), p.Name, Schema = t.GetSchema() }))
            .Where(x => !OwnedTypesToSkip.Contains(x.DeclaringClrType))
            .Where(x => x.DeclaredProperty != null && (x.ClrType.IsDecimalType() || x.DeclaredProperty.CustomAttributes.Any(c => propertyAttributes.Contains(c.AttributeType))))
            .Select(p => new { p.Schema, p.DeclaredProperty, EntityProperty = builder.Entity(p.DeclaringClrType).Property(p.Name) }).ToList();

        foreach (var property in entityProperties)
        {
            if (property.DeclaredProperty.PropertyType.IsDecimalType())
            {
                if (property.DeclaredProperty.GetCustomAttribute<DbDecimalAttribute>() is DbDecimalAttribute decimalDescription && decimalDescription is not null)
                    property.EntityProperty.HasColumnType($"decimal({decimalDescription.Places}, {decimalDescription.Precision})");
                else
                    property.EntityProperty.HasColumnType("decimal(18, 2)"); //use default if no decimal attribute is found
            }

            if (property.DeclaredProperty.GetCustomAttribute<DbComputedAttribute>() is DbComputedAttribute computedProperty && computedProperty is not null)
                property.EntityProperty.HasComputedColumnSql(computedProperty.Sql, computedProperty.Stored);

            else if (property.DeclaredProperty.GetCustomAttribute<DbGuidAttribute>() is DbGuidAttribute guidProperty && guidProperty is not null)
            {
                if (guidProperty.Nullable)
                    property.EntityProperty.HasConversion<Guid?>();
                else
                    property.EntityProperty.HasConversion<Guid>();
            }

            else if (property.DeclaredProperty.GetCustomAttribute<DbHiloAttribute>() is DbHiloAttribute hiloProperty && hiloProperty is not null)
            {
                property.EntityProperty.UseHiLo(CoreEntityConfig.HiloSequenceName, property.Schema);
            }
        }


        List<Type> entityAttributes = [typeof(DbViewAttribute), typeof(DbTableFillFactorAttribute)];
        foreach (var entity in entityTypes.Where(t => t.ClrType.CustomAttributes.Any(c => propertyAttributes.Contains(c.AttributeType))))
        {
            if (entity.ClrType.GetCustomAttribute<DbViewAttribute>() is DbViewAttribute view && view is not null)
                entity.SetViewName(view.Name);

            if (entity.ClrType.GetCustomAttribute<DbTableFillFactorAttribute>() is DbTableFillFactorAttribute tableFillFactor && tableFillFactor is not null)
                entity.SetStorageParameter("fillfactor", tableFillFactor.Percentage);
        }
    }

    internal static void SetupEntities(this ModelBuilder builder, ParallelQuery<AssemblyName> entryAssembly, string schema = CoreEntityConfig.DefaultSchema)
    {
        var asm = entryAssembly
            .Select(Assembly.Load)
            .SelectMany(x => x.GetTypes());

        AddEntitiesToDbContext(builder, asm, schema);
    }

    private static void AddEntitiesToDbContext(ModelBuilder builder, ParallelQuery<Type> asm, string schema)
    {
        var hasConfigType = typeof(IEntityTypeConfiguration<>).Name;

        //interfaces for which implementing classes will be added to db context automatically
        IReadOnlyList<string> interfaceTypes = [hasConfigType, typeof(IAddToDbContext).Name, typeof(IEntityHasNotifyTrigger).Name, typeof(IEntityHasEnum).Name];

        var all = asm
          .Where(x => interfaceTypes.Any(y => x.GetInterface(y) != null) && !x.IsInterface && !x.IsAbstract)
          .DistinctBy(x => x.Name).ToList();

        var nameTranslator = new Npgsql.NameTranslation.NpgsqlNullNameTranslator();

        foreach (var type in all)
        {
            var entity = builder.Entity(type);
            var tableName = entity.Metadata.GetTableName();
            var schemaName = tableName.StartsWith("AspNet", StringComparison.OrdinalIgnoreCase) || tableName == "TelegramSession" ? "public" : schema ?? "public";
            entity.ToTable(tableName, schemaName);


            var assemblyName = type.Assembly.GetName().Name;
            if (!assembliesConfigToApply.ContainsKey(assemblyName) && type.GetInterface(hasConfigType) != null)
            {
                assembliesConfigToApply.Add(assemblyName, type.Assembly);
            }
        }

        foreach (var assmbly in assembliesConfigToApply.Values)
        {
            builder.ApplyConfigurationsFromAssembly(assmbly);
        }

        assembliesConfigToApply.Clear();
    }
}
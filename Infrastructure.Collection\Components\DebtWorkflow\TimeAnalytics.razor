﻿@page "/debtworkflow/agentworkflowtimeanalytics"
@using LendQube.Entities.Core.Uploads
@using LendQube.Infrastructure.Collection.Workflow.Debt
@using LendQube.Infrastructure.Collection.Workflow.Debt.ViewModels
@using LendQube.Entities.Collection.Workflows.Debt
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.Database.GenericCrud
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inject NavigationManager navigationManager
@inject WorkflowManagementService workService

@inherits GenericCrudVMTable<AgentWorkflowTimeAnalytics, AgentWorkflowTimeAnalyticsVM>

@attribute [Authorize(Policy = DebtWorkflowNavigation.DebtWorkflowTimeAnalyticsIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalComponent Policy="@DebtWorkflowNavigation.DebtWorkflowTimeAnalyticsIndexPermission" ModalId="@DeskLogModalName"
ModalCss=" modal-dialog-large width-70vw" Title=@($"View Desk Logs For User: {SelectedUser?.AgentName}")>
    <BodyContent>
        <DataTable T="AgentWorkflowTaskLogVM" LoadData="LoadDeskLogs" DeferLoading="true" @ref="deskLogTable" />
    </BodyContent>

</ModalComponent>

@code{

    private DataTable<AgentWorkflowTaskLogVM> deskLogTable;
    private AgentWorkflowTimeAnalyticsVM SelectedUser { get; set; } 

    private string DeskLogModalName => "DeskLogModal";

    protected override void OnInitialized()
    {
        Title = "Debt Workflow";
        SubTitle = "Agent Workflow Time Analytics";
        FormBaseTitle = "Time Analytics";
        QuerySelector = AgentWorkflowTimeAnalyticsVM.Mapping;  
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            await base.OnInitializedAsync();

            AddRowButton(DebtWorkflowNavigation.DebtWorkflowTimeAnalyticsIndexPermission, new RowActionButton("Work Log", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                SelectedUser = row as AgentWorkflowTimeAnalyticsVM;
                await deskLogTable.LoadElement();
                table.Loading = false;

                StateHasChanged();
                await JSRuntime.OpenModal(DeskLogModalName, Cancel);
            }));

        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        base.OnAfterRender(firstRender);

        var workDefinition = workService.GetLogTableDefinition();
        AddRowButton(workDefinition, DebtWorkflowNavigation.DebtWorkflowTimeAnalyticsIndexPermission, new RowActionButton("View", Icon: "eye", Action: async (object row) =>
        {
            await JSRuntime.CloseModal(DeskLogModalName, Cancel);
            var task = row as AgentWorkflowTaskLogVM;
            navigationManager.NavigateTo($"debtworkflow/agentworkflowtimeanalytics/{task.CustomerProfileId}/{task.Id}/{true}");
        }));
        deskLogTable.SetTableDefinition(workDefinition);
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.User.FullName, filterAndPage.TextFilter);
    }

    protected override ColumnList GetTableDefinition() => 
    Service.CrudService.GetTableDefinition(new() { HasEdit = false, HasDelete = false, HasDateColumns = false });

    #region Manage Access Logs

    private ValueTask<TypedBasePageList<AgentWorkflowTaskLogVM>> LoadDeskLogs(DataFilterAndPage filterAndPage, CancellationToken ct) => workService.LoadDateTasks(SelectedUser.UserId, SelectedUser.Date, filterAndPage, ct);


    #endregion
}
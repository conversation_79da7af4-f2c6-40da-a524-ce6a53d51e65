﻿using Microsoft.AspNetCore.Authorization;

namespace LendQube.Infrastructure.Core.PermissionsAndRoles;

internal sealed class PermissionAuthorizationHandler : AuthorizationHandler<PermissionRequirement>
{
    public PermissionAuthorizationHandler() { }
    protected override Task HandleRequirementAsync(AuthorizationHandlerContext context, PermissionRequirement requirement)
    {
        if (context.User == null)
        {
            return Task.FromResult(0);
        }

        var policies = requirement.Permission.Split(",");
        var permissions = context.User.Claims.Where(x => x.Type == "Permission" &&
                                                            policies.Contains(x.Value) &&
                                                            x.Issuer == "LOCAL AUTHORITY");
        if (permissions.Any())
        {
            context.Succeed(requirement);
            return Task.FromResult(0);
        }
        return Task.FromResult(-1);
    }
}
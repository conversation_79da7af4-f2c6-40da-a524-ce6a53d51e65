﻿using System.Linq.Expressions;
using Coravel.Invocable;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Schedules.BackgroundServices;

internal sealed class PaymentReminderService(IUnitofWork uow) : IInvocable, ICancellableInvocable
{
    private readonly IReadOnlyDictionary<MessageConfigNames, int> notificationConfig = new Dictionary<MessageConfigNames, int>()
    {
        { MessageConfigNames.FiveDaysToDueReminder, 5 },
        { MessageConfigNames.OneDayToDueReminder, 1 },
        { MessageConfigNames.DueDayReminder, 0 },
        { MessageConfigNames.OneDayPastDueReminder, -1 },
        { MessageConfigNames.ThreeDaysPastDueReminder, -3 },
    };

    public CancellationToken CancellationToken { get; set; }

    public async Task Invoke()
    {
        var message = MessageBuilder.New("Payment Reminder", null);
        foreach (var (config, day) in notificationConfig)
        {
            await PrepareMessage(message, config, day);
        }

        _ = await message.Send(uow, CancellationToken);
    }

    private async Task PrepareMessage(MessageBrick message, MessageConfigNames config, int day)
    {
        var date = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(day));
        var mapping = DaysDueData.Mapping(date);
        var where = DaysDueData.Where(date);
        var data = await uow.Db.ManySelectAsync(Query<CustomerProfile, DaysDueData>.Where(where).Select(mapping), CancellationToken);

        if (!data.IsNullOrEmpty())
        {
            message
                .Message(config.GetDisplayName())
                .WithRecipientsWithIndividualKeys(data.Select(x => x.ProfileId).ToList(),
                    data.ToDictionary(x => x.ProfileId, x => new List<TemplateKeyValue>
                    {
                        new($"{MessageTemplateKeys.Amount}", $"{x.Currency}{x.Amount:n2}")
                    }));
        }
    }
}

file class DaysDueData
{
    public static Func<DateOnly, Expression<Func<CustomerProfile, bool>>> Where = (DateOnly date) => x =>
    x.BalanceRemaining > 0 && x.Schedules.Any(y => y.DueDate == date && y.PaymentStatus != SchedulePaymentStatus.Paid && y.Balance > 0)
    && !x.Holds.Any(y => !y.PlacementId.HasValue && !y.Disabled && y.Action.HasFlag(HoldAction.DisableSystemMessages) && (!y.ExpiresOn.HasValue || SystemClock.Instance.GetCurrentInstant() < y.ExpiresOn));

    public static Func<DateOnly, Expression<Func<CustomerProfile, DaysDueData>>> Mapping = (DateOnly date) => data => new()
    {
        ProfileId = data.Id,
        Amount = data.Schedules.Where(y => y.DueDate == date && y.PaymentStatus != SchedulePaymentStatus.Paid && y.Balance > 0).Sum(y => y.Balance),
        Currency = data.CurrencySymbol
    };

    public string ProfileId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; }
}
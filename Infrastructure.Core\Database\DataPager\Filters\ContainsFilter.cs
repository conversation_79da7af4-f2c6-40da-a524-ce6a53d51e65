﻿using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Reflection;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

internal sealed class ContainsFilter() : ObjectFilter(ColumnFilterRule.Contains)
{
    public override Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value)
    {
        if(propertyType.IsFlagEnum())
            return ExpressionsExtension.EnumHasFlag<T>(propertyType, vm.ColumnName, value);

        if (propertyType.IsGenericListString())
            return ExpressionsExtension.ListStringContains<T>(propertyType, value.ToString(), vm.ColumnName);

        if (EmbeddedQuery)
            return ExpressionsExtension.LikeLowerCase<T>(value.ToString().ToLower(), vm.ColumnName);

        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(vm.ColumnName);

        ConstantExpression valueExpression = Expression.Constant(string.Format("%{0}%", value));

        MethodInfo likeMethod = typeof(NpgsqlDbFunctionsExtensions).GetMethod(
            nameof(NpgsqlDbFunctionsExtensions.ILike), BindingFlags.Public | BindingFlags.Static,
            null,
            [typeof(DbFunctions), typeof(string), typeof(string)], null);

        Expression body = Expression.Call(null,
            likeMethod, Expression.Constant(EF.Functions),
            expression, valueExpression);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    public override bool IsTypeSupported(ObjectFilterRule rule) => ((Type.GetTypeCode(rule.Type) == TypeCode.String && rule.FilterType != ColumnFilterDataType.DBList)
        || rule.Type.IsGenericListString() || rule.Type.IsFlagEnum()) && !rule.IsId;
}
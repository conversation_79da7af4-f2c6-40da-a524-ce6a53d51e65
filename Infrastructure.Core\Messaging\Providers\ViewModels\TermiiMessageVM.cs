﻿using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.Core.Messaging.Providers.ViewModels;

internal sealed class TermiiMessageVM
{
    [JsonPropertyName("to")]
    public List<string> To { get; set; }

    [JsonPropertyName("from")]
    public string From { get; set; }

    [JsonPropertyName("sms")]
    public string Sms { get; set; }

    [JsonPropertyName("type")]
    public string Type { get; set; } = "plain";

    [JsonPropertyName("channel")]
    public string Channel { get; set; } = "dnd";

    [JsonPropertyName("api_key")]
    public string ApiKey { get; set; }
}

internal sealed class TermiiMessageResponseVM
{
    [JsonPropertyName("message_id")]
    public string MessageId { get; set; }

    [JsonPropertyName("message")]
    public string Message { get; set; }

    [JsonPropertyName("balance")]
    public decimal Balance { get; set; }

    [<PERSON>son<PERSON>ropertyName("user")]
    public string User { get; set; }
}


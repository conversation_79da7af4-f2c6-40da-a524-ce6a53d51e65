﻿namespace LendQube.Infrastructure.Core.Navigation;

internal sealed class ManageAdminUsersNavigation : INavigationDescriptor, INavigatorHasPermissions
{
    public bool IsDisabled { get; set; } = false;
    public const string GroupName = "Admin Users";
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = "Manage Admin Users",
            Icon = "user-plus",
            Permission = UserIndexPermission,
            Controller = "ManageUsers",
            SubNavigation =
            [
                new() { Name = "Users", Icon = "user-plus", Permission = UserIndexPermission, Controller = "ManageUsers" },
                new() { Name = "Roles", Icon = "user-check", Permission = RoleIndexPermission, Controller = "ManageRoles" },
                new() { Name = "Permissions", Icon = "user-check", Permission = ClaimIndexPermission, Controller = "ManageClaims" },

            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.ManageAuthIndex, GroupName, navs);

    }

    public void PreparePermissionDescriptions()
    {
        Navigator.PermissionDescription[UserIndexPermission] = $"Can view admin users and access {GroupName}";
        Navigator.PermissionDescription[UserCreatePermission] = "Can create admin users";
        Navigator.PermissionDescription[UserEditPermission] = "Can modify admin users";
        Navigator.PermissionDescription[UserDeletePermission] = "Can delete admin users";
        Navigator.PermissionDescription[UserReset2FAPermission] = "Can reset two factor authentication for admin users";
        Navigator.PermissionDescription[UserAccessLogsPermission] = "Can view user access logs";
        Navigator.PermissionDescription[UserResetLockoutPermission] = "Can reset user lockout";

        Navigator.PermissionDescription[RoleIndexPermission] = "Can view admin roles";
        Navigator.PermissionDescription[RoleCreatePermission] = "Can create admin roles";
        Navigator.PermissionDescription[RoleEditPermission] = "Can modify admin roles";
        Navigator.PermissionDescription[RoleDeletePermission] = "Can delete admin roles";

        Navigator.PermissionDescription[ClaimIndexPermission] = "Can view admin permissions";
        Navigator.PermissionDescription[ClaimCreatePermission] = "Can create admin permissions";
        Navigator.PermissionDescription[ClaimEditPermission] = "Can modify admin permissions";
        Navigator.PermissionDescription[ClaimDeletePermission] = "Can delete admin permissions";


        Navigator.PermissionDescription[UserRolesPermission] = "Can can manage admin user roles";
        Navigator.PermissionDescription[AssignRoleToUserClaimsPermission] = "Can assign admin user roles";
        Navigator.PermissionDescription[RemoveRoleFromUserClaimsPermission] = "Can remove admin user roles";


        Navigator.PermissionDescription[UserClaimsPermission] = "Can view admin user role permissions";
        Navigator.PermissionDescription[AssignClaimToRoleClaimsPermission] = "Can assign admin user role permissions";
        Navigator.PermissionDescription[RemoveClaimFromRoleClaimsPermission] = "Can remove admin user role permissions";
    }

    public const string UserIndexPermission = "Permission.ManageUsers.Index";
    public const string UserCreatePermission = "Permission.ManageUsers.Create";
    public const string UserEditPermission = "Permission.ManageUsers.Edit";
    public const string UserDeletePermission = "Permission.ManageUsers.Delete";
    public const string UserReset2FAPermission = "Permission.ManageUsers.Reset2FA";
    public const string UserAccessLogsPermission = "Permission.ManageUsers.AccessLogs";
    public const string UserResetLockoutPermission = "Permission.ManageUsers.ResetLockout";

    public const string RoleIndexPermission = "Permission.ManageRoles.Index";
    public const string RoleCreatePermission = "Permission.ManageRoles.Create";
    public const string RoleEditPermission = "Permission.ManageRoles.Edit";
    public const string RoleDeletePermission = "Permission.ManageRoles.Delete";

    public const string ClaimIndexPermission = "Permission.ManageClaims.Index";
    public const string ClaimCreatePermission = "Permission.ManageClaims.Create";
    public const string ClaimEditPermission = "Permission.ManageClaims.Edit";
    public const string ClaimDeletePermission = "Permission.ManageClaims.Delete";

    public const string UserRolesPermission = "Permission.ManageUserRoles.Index";
    public const string AssignRoleToUserClaimsPermission = "Permission.ManageUserRoles.AssignRoleToUser";
    public const string RemoveRoleFromUserClaimsPermission = "Permission.ManageUserRoles.RemoveRoleFromUser";

    public const string UserClaimsPermission = "Permission.ManageUserClaims.Index";
    public const string AssignClaimToRoleClaimsPermission = "Permission.ManageClaims.AssignClaimToRole";
    public const string RemoveClaimFromRoleClaimsPermission = "Permission.ManageClaims.RemoveClaimFromRole";
}
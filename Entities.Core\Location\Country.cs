﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Entities.Core.Location;

public class Country : BaseEntityWithIdentityId<Country>
{
    [Required, MaxLength(EntityConstants.DEFAULT_COUNTRY_CODE_FIELD_LENGTH, ErrorMessage = ErrorConstants.TWO_DIGIT_STRING_LENGTH_ERROR), TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyText)]
    public string Code { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string Name { get; set; }
    [Required, ValidString(ValidStringRule.OnlyTextAndUnderscore)]
    public string ImageUrl { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.NumberWithPlusOnly)]
    public string PhoneCode { get; set; }
    public virtual ICollection<State> States { get; set; }

    public override void Configure(EntityTypeBuilder<Country> builder)
    {
        base.Configure(builder);
        builder.Property(x => x.Code).IsFixedLength();
        builder.HasIndex(x => x.Code).IsUnique();
        builder.HasIndex(x => x.Name).IsUnique();
    }
}

﻿@page "/messaging/uploaddirectory"
@using LendQube.Entities.Core.Uploads
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Messaging


@inject MessagingGroupUploadService uploadService
@inherits GenericCrudTable<SystemFileUpload>

@attribute [Authorize(Policy = MessagingNavigation.MessagingGroupUploadIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Action">Action</label>
            <InputSelect @bind-Value="context.Action">
                <option label="Select upload action" selected></option>
                @foreach (var option in Enum.GetValues<UploadAction>())
                {
                    <option value="@option">@option.GetDisplayName()</option>
                }
            </InputSelect>
            <ValidationMessage For="() => context.Action" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Name">Attach File (Excel/Csv)</label>
            <InputFile OnChange="LoadFile" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Description">Group Name</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>

        <div class="form-row">
            <label class="form-label" for="Action">Action</label>
            <InputSelect @bind-Value="context.Action">
                <option label="Update upload action" selected></option>
                @foreach (var option in Enum.GetValues<UploadAction>())
                {
                    <option value="@option">@option.GetDisplayName()</option>
                }
            </InputSelect>
            <ValidationMessage For="() => context.Action" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Name">Attach File (Excel/Csv)</label>
            <InputFile OnChange="LoadFile" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel" />
        </div>
    </BodyContent>
</ModalEditComponent>

@code
{
    private bool savedSuccessfully = false;
    private string message = null;
    private IBrowserFile file;

    protected override void OnInitialized()
    {
        Title = "Send Directory";
        SubTitle = "Upload Contact Groups";
        FormBaseTitle = "Upload Contact Group";
        CreatePermission = MessagingNavigation.MessagingGroupUploadCreatePermission;
        EditPermission = MessagingNavigation.MessagingGroupUploadEditPermission;
        DeletePermission = MessagingNavigation.MessagingGroupUploadDeletePermission;


        AddRowButton(new RowActionButton("Download Analysis File", Icon: "download", IconClass: "__danger", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;
            var template = row as SystemFileUpload;
            await JSRuntime.DownloadFile(Path.GetFileName(template.AnalysisFileUrl), template.AnalysisFileUrl, Cancel);
            table.Loading = false;

            StateHasChanged();
        }, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as SystemFileUpload).AnalysisFileUrl)));

        AddRowButton(new RowActionButton("Download Upload File", Icon: "download", IconClass: "__edit", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;
            var template = row as SystemFileUpload;
            await JSRuntime.DownloadFile(Path.GetFileName(template.FileUrl), template.FileUrl, Cancel);
            table.Loading = false;

            StateHasChanged();
        }, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as SystemFileUpload).FileUrl)));

    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        TableDefinition.HasEdit = false;

        AddTopButton(CreatePermission, new TopActionButton("Download Template", "btn--default", "download", Action: async () =>
        {
            CloseMessage();
            await JSRuntime.DownloadFile(Path.GetFileName(uploadService.TemplateFilePath), uploadService.TemplateFilePath, Cancel);
        }));

        AddRowButton(EditPermission, new RowActionButton("Modify Upload", Icon: "upload-cloud", Action: async (object row) =>
        {
            await StartEdit(row as SystemFileUpload, Cancel);
        }, ShowCondition: (object row) => (row as SystemFileUpload).Status != UploadStatus.Imported));

        AddRowButton(EditPermission, new RowActionButton("Import", ButtonClass: "btn--success", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;
            var template = row as SystemFileUpload;
            var result = await uploadService.Queue(template.Id, UploadAction.Import, Cancel);

            table.Loading = false;
            SetStatusMessage(result.IsSuccessful, result.Message);

        }, ShowCondition: (object row) => (row as SystemFileUpload).Status == UploadStatus.Analyzed));
    }

    protected override void DefinePrimaryCriteria(DataFilterAndPage filterAndPage) => Service.PrimaryCriteria = x => x.Type == SystemUploadType.MessageGroup;

    private void SetStatusMessage(bool result, string message)
    {
        if (result)
        {
            MessagingGroupUploadService.StatusNotificationEvent += UploadCompleted;
            TableMessage.Info(message, true);
            StateHasChanged();
        }
    }

    public void UploadCompleted(object sender, SystemBackgroundTaskEventArgs e)
    {
        if (e.Owner != UserName)
            return;

        _ = InvokeAsync(async () =>
        {
            await table.Refresh();
            TableMessage.Set(e.Successful, e.Message);
            StateHasChanged();
        });

        MessagingGroupUploadService.StatusNotificationEvent -= UploadCompleted;
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new() { HasEdit = false, HasId = false, ColumnsToAdd = [
            new() { Index = 1, Name = x => x.Description }, 
            new() { Index = 2, Name = x => x.Action },
            new() { Index = 3, Name = x => x.Status },
            new() { Index = 4, Name = x => x.Message }
        ]
        });

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Description, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.FileUrl, filterAndPage.TextFilter);
    }

    private void LoadFile(InputFileChangeEventArgs e)
    {
        file = e.File;
    }

    protected override async ValueTask SubmitAdd()
    {
        await BaseSaveAdd(async () =>
        {
            AddModel.Type = SystemUploadType.MessageGroup;
            var result = await uploadService.Upload(AddModel.Type.ToString(), AddModel, file, Cancel);
            message = result.Message;
            savedSuccessfully = result.IsSuccessful;
            return savedSuccessfully;
        },
        async () =>
        {
            AddModel = new();
            file = null;
            await table.Refresh();
        });

        SetStatusMessage(savedSuccessfully, message);
        savedSuccessfully = false;
    }

    protected override async ValueTask SubmitEdit()
    {
        await BaseSaveEdit(async () =>
        {
            EditModel.Type = SystemUploadType.MessageGroup;
            var result = await uploadService.Upload(EditModel.Type.ToString(), EditModel, file, Cancel);
            message = result.Message;
            savedSuccessfully = result.IsSuccessful;
            return savedSuccessfully;
        },
        async () =>
        {
            EditModel = new();
            file = null;
            await table.Refresh();
        });

        SetStatusMessage(savedSuccessfully, message);
        savedSuccessfully = false;
    }

    protected override async ValueTask<bool> SubmitDelete(SystemFileUpload data, Func<Task> refresh, CancellationToken ct) => await SaveDelete(() => uploadService.Delete(data, ct), refresh);

    public override void Dispose()
    {
        base.Dispose();
        MessagingGroupUploadService.StatusNotificationEvent -= UploadCompleted;
    }
}

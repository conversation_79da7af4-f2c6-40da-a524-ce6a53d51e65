﻿using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Core.Base;

public abstract class BaseEntityWithIdentityId<T> : IBaseEntityWithNumberId, IEntityDoesNotUseHiLoIdentity, IAddToDbContext, IEntityTypeConfiguration<T> where T : IBaseEntityWithNumberId
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.ShowInInfo)]
    public new long Id { get; set; }

    public virtual void Configure(EntityTypeBuilder<T> builder)
    {
        builder.Property(x => x.Id).UseIdentityAlwaysColumn();
    }
}

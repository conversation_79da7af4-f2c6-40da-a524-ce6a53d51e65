﻿using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore.Components;

namespace LendQube.Infrastructure.Core.Components.FormsAndModals;

public abstract class SingleFormComponentBase<T> : AuthComponentBase where T : new()
{
    [SupplyParameterFromForm]
    public T Model { get; set; } = new();

    protected StatusMessageBuilder Message { get; set; } = new();

    protected string FormName { get; set; } = "ModifyForm";

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await Init();
    }

    protected virtual ValueTask Init() => ValueTask.CompletedTask;

    protected async Task Submit(Func<Task<GenericResponseVM>> submit)
    {
        var result = await submit();
        Message?.Set(result.Successful, "Changes saved successfully", $"Could not save changes. {result.Message}");
    }
}

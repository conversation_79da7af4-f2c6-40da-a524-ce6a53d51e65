using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

partial class ViewProfile
{
    private DataTable<CustomerAddress> addressTable;
    private ColumnList addressTableDefinition;
    private string AddAddressModal => "AddAddressModal";
    private string EditAddressModal => "EditAddressModal";
    private string AddressColumnToggleModal => "AddressColumnToggleModal";

    private CustomerAddress AddAddressModel { get; set; } = new();
    private CustomerAddress EditAddressModel { get; set; } = new();
    
    private bool addressTableInitialized = false;
    
    // Column visibility states
    private bool showAddressLine2 = true;
    private bool showLocality = true;
    private bool showPostCode = true;
    private bool showCity = true;
    private bool showCountry = true;
    private bool showCountryCode = false;

    private void SetupAddressConfig()
    {
        // Get the default table definition
        var baseDefinition = CrudService.GetTableDefinition<CustomerAddress>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteAddressPermission),
            HasEdit = HasClaim(ManageCustomersNavigation.CustomerProfileViewEditAddressPermission)
        });
        
        // Create a new column list with filtered columns
        addressTableDefinition = new ColumnList
        {
            ColumnHeaders = new List<ColumnHeader>(),
            ColumnFilters = baseDefinition.ColumnFilters,
            DropDowns = baseDefinition.DropDowns,
            HasEdit = baseDefinition.HasEdit,
            HasDelete = baseDefinition.HasDelete,
            HasInfo = baseDefinition.HasInfo,
            NoGeneralSearch = baseDefinition.NoGeneralSearch
        };
        
        // Add columns based on visibility settings
        foreach (var column in baseDefinition.ColumnHeaders)
        {
            bool includeColumn = column.Name switch
            {
                "AddressLine1" => true, // Always show
                "AddressLine2" => showAddressLine2,
                "Locality" => showLocality,
                "PostCode" => showPostCode,
                "City" => showCity,
                "Country" => showCountry,
                "CountryCode" => showCountryCode,
                _ => true // Show any other columns by default
            };
            
            if (includeColumn)
            {
                addressTableDefinition.ColumnHeaders.Add(column);
            }
        }

        // Add buttons
        addressTableDefinition.TopActionButtons.Add(new TopActionButton("Toggle Columns", ButtonClass: "btn--default", Icon: "settings", ModalName: AddressColumnToggleModal));

        // Try to set the table definition if the table is already initialized
        TrySetAddressTableDefinition();
    }
    
    private void TrySetAddressTableDefinition()
    {
        if (addressTable != null && addressTableDefinition != null)
        {
            // Always set the table definition when available, not just when not initialized
            // This allows for dynamic updates when columns are toggled
            addressTable.SetTableDefinition(addressTableDefinition);
            
            // Only mark as initialized if we haven't already
            if (!addressTableInitialized)
            {
                addressTableInitialized = true;
            }
        }
    }

    private async ValueTask<TypedBasePageList<CustomerAddress>> LoadAddresses(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerAddress>
        {
            PrimaryCriteria = x => x.CustomerProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.AddressLine1, filterAndPage.TextFilter) ||
            EF.Functions.ILike(x.AddressLine2, filterAndPage.TextFilter) ||
            EF.Functions.ILike(x.City, filterAndPage.TextFilter) ||
            EF.Functions.ILike(x.PostCode, filterAndPage.TextFilter));
        }

        return await CrudService.GetTypeBasedPagedData(spec, filterAndPage, ct: ct);
    }

    private ValueTask SubmitNewAddress() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddAddressPermission, AddAddressModal, async () =>
    {
        if (string.IsNullOrWhiteSpace(AddAddressModel.AddressLine1))
        {
            CustomMessage = "Address Line 1 is required";
            return false;
        }
        if (string.IsNullOrWhiteSpace(AddAddressModel.CountryCode))
        {
            CustomMessage = "Country Code is required";
            return false;
        }
        
        AddAddressModel.CustomerProfileId = Data.Id;
        uow.Db.Insert(AddAddressModel);
        await uow.SaveAsync(Cancel);

        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Address Added", Activity = $"New address added: {AddAddressModel.AddressLine1}" });
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        AddAddressModel = new();
        StateHasChanged();
        return addressTable.Refresh();
    });

    private async ValueTask StartEditAddress(CustomerAddress data, CancellationToken ct)
    {
        EditAddressModel = new CustomerAddress
        {
            Id = data.Id,
            CustomerProfileId = data.CustomerProfileId,
            AddressLine1 = data.AddressLine1,
            AddressLine2 = data.AddressLine2,
            Locality = data.Locality,
            PostCode = data.PostCode,
            City = data.City,
            Country = data.Country,
            CountryCode = data.CountryCode
        };
        StateHasChanged(); // Force UI update before opening modal
        await Task.Delay(50); // Small delay to ensure UI is updated
        await JSRuntime.OpenModal(EditAddressModal, ct);
    }

    private ValueTask SubmitEditAddress() => BaseSaveEdit(ManageCustomersNavigation.CustomerProfileViewEditAddressPermission, EditAddressModal, async () =>
    {
        if (string.IsNullOrWhiteSpace(EditAddressModel.AddressLine1))
        {
            CustomMessage = "Address Line 1 is required";
            return false;
        }
        if (string.IsNullOrWhiteSpace(EditAddressModel.CountryCode))
        {
            CustomMessage = "Country Code is required";
            return false;
        }

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerAddress>(x => x.Id == EditAddressModel.Id, x => x
            .SetProperty(y => y.AddressLine1, EditAddressModel.AddressLine1)
            .SetProperty(y => y.AddressLine2, EditAddressModel.AddressLine2)
            .SetProperty(y => y.Locality, EditAddressModel.Locality)
            .SetProperty(y => y.PostCode, EditAddressModel.PostCode)
            .SetProperty(y => y.City, EditAddressModel.City)
            .SetProperty(y => y.Country, EditAddressModel.Country)
            .SetProperty(y => y.CountryCode, EditAddressModel.CountryCode), Cancel);

        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Address Updated", Activity = $"Address updated: {EditAddressModel.AddressLine1}" });
        await uow.SaveAsync(Cancel);

        return true;
    }, () =>
    {
        EditAddressModel = new();
        StateHasChanged();
        return addressTable.Refresh();
    });

    private ValueTask<bool> DeleteAddress(CustomerAddress data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteAddressPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerAddress>(x => x.Id == data.Id, ct);
        if (result > 0)
        {
            uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Address Deleted", Activity = $"Address deleted: {data.AddressLine1}" });
            await uow.SaveAsync(ct);
        }
        return result > 0;
    }, refresh);
    
    private async Task ApplyColumnToggle()
    {
        // Close the modal first
        await JSRuntime.CloseModal(AddressColumnToggleModal, Cancel);
        
        // Force the table to be reinitialized
        addressTableInitialized = false;
        
        // Rebuild the table definition with new column visibility
        SetupAddressConfig();
        
        // Force a complete re-render of the table
        if (addressTable != null)
        {
            // Force the table to reload with new definition
            await addressTable.LoadElement();
        }
        
        // Ensure UI updates
        StateHasChanged();
    }
}
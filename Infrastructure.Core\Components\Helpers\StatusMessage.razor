﻿@if (Message != null && Message.Showing)
{
    <div class="alert @TypeCss hasFlex hasClose">
        <div class="al-flex">
            @if (Message.Loading)
            {
                <span>
                    <i class="fa fa-spinner fa-spin"></i>
                </span>
            }

            <div class="al-content">
                <span class="al__title" role="alert">
                    @Message.Message
                </span>
            </div>
            @if (!Message.Loading)
            {
                <a @onclick="() => Close()" class="al__link close"></a>
            }
        </div>
    </div>
}

@code {
    [Parameter]
    public StatusMessageBuilder Message { get; set; } = new();


    private string TypeCss => Message?.Type switch
    {
        MessageType.Info => "info",
        MessageType.Success => "success",
        MessageType.Warning => "warning",
        MessageType.Error => "critical",
        _ => ""
    };

    public void Set(bool result, string message, bool loading = false)
    {
        Message.Set(result, message, loading);
        StateHasChanged();
    }

    public void Set(bool result, string success, string failure, bool loading = false)
    {
        Message.Set(result, success, failure, loading);
        StateHasChanged();
    }

    public void Success(string message, bool loading = false) 
    {
        Message.Success(message, loading);
        StateHasChanged();
    }

    public void Info(string message, bool loading = false)
    {
        Message.Info(message, loading);
        StateHasChanged();
    }

    public void Warning(string message, bool loading = false) 
    {
        Message.Warning(message, loading);
        StateHasChanged();
    }

    public void Error(string message, bool loading = false)
    {
        Message.Error(message, loading);
        StateHasChanged();
    }  

    public void Close()
    {
        Message.Showing = false;
        StateHasChanged();
    }
}

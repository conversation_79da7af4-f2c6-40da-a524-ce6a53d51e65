@page "/manage-profile"
@using LendQube.Entities.Collection.Customers
@using LendQube.Infrastructure.Core.Database.DbContexts
@using Microsoft.EntityFrameworkCore
@inject AppDbContext DbContext
@inject ILogger<ManageProfile> Lo<PERSON>
@rendermode InteractiveServer

<PageTitle>Manage Profile - LendQube Customer Portal</PageTitle>

<div class="manage-profile-container">
    <div class="manage-profile-header">
        <div class="header-content">
            <h1>Manage Profile</h1>
            <p class="header-subtitle">Update your address and contact information</p>
        </div>
        <div class="header-actions">
            <a href="/dashboard?customerId=@CustomerId" class="btn btn-outline-secondary back-btn">
                <span>← Back to Dashboard</span>
            </a>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger">
            @errorMessage
        </div>
    }

    @if (customer != null)
    {
        <div class="manage-profile-content">
            <!-- Coming Soon Card -->
            <div class="info-card coming-soon-card">
                <div class="coming-soon-content">
                    <h3>Feature Coming Soon</h3>
                    <p>The ability to update your address and contact information will be available soon. 
                       In the meantime, please contact our support team if you need to update your details.</p>
                    
                    <div class="current-info">
                        <h4>Current Information</h4>
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="info-label">Name:</span>
                                <span class="info-value">@customer.FirstName @customer.LastName</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Email:</span>
                                <span class="info-value">@(customer.Email ?? "Not provided")</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Phone:</span>
                                <span class="info-value">@(customer.MobileNumber?.Number ?? "Not provided")</span>
                            </div>
                            @if (customer.Addresses?.Any() == true)
                            {
                                var address = customer.Addresses.First();
                                <div class="info-item">
                                    <span class="info-label">Address:</span>
                                    <span class="info-value">@address.AddressLine1, @address.City, @address.PostCode</span>
                                </div>
                            }
                            else
                            {
                                <div class="info-item">
                                    <span class="info-label">Address:</span>
                                    <span class="info-value">Not provided</span>
                                </div>
                            }
                        </div>
                    </div>

                    <div class="contact-support">
                        <h4>Need to Update Your Information?</h4>
                        <p>Please contact our support team and they will be happy to help you update your details.</p>
                        <div class="support-actions">
                            <button class="btn btn-primary" disabled>
                                <span>Contact Support</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    }
</div>

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? CustomerId { get; set; }

    private CustomerProfile? customer;
    private string? errorMessage;

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(CustomerId))
        {
            await LoadCustomerData();
        }
        else
        {
            errorMessage = "Customer ID is required.";
        }
    }

    private async Task LoadCustomerData()
    {
        try
        {
            Logger.LogInformation("Loading customer data for profile management: {CustomerId}", CustomerId);

            customer = await DbContext.Set<CustomerProfile>()
                .Include(c => c.Addresses)
                .Where(c => c.AccountId == CustomerId)
                .FirstOrDefaultAsync();

            if (customer == null)
            {
                errorMessage = "Customer not found. Please log in again.";
                Logger.LogWarning("Customer not found for ID: {CustomerId}", CustomerId);
            }
            else
            {
                Logger.LogInformation("Customer data loaded successfully for profile management: {CustomerId}", CustomerId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading customer data for profile management: {CustomerId}", CustomerId);
            errorMessage = "An error occurred while loading your profile. Please try again.";
        }
    }
}

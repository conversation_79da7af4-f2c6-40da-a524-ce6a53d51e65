﻿using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.Extensions.Configuration;

namespace LendQube.Infrastructure.Core.Helpers.ApiControllers;

public sealed class ApiExplorerConvention : IActionModelConvention
{
    private readonly List<string> AllowedAssemblies = [];
    public ApiExplorerConvention(IConfiguration config)
    {
        AllowedAssemblies.Add("Web.Api");

        var collectionEnabled = config.GetValue<bool>("Providers:DeployState:EnabledApplications:Collections"); //sample for controlling what apis should be added based on appsettings
        if (collectionEnabled)
            AllowedAssemblies.Add("Infrastructure.Collection");
    }
    public void Apply(ActionModel action)
    {
        action.ApiExplorer.IsVisible = AllowedAssemblies.Contains(action.Controller.ControllerType.Assembly.GetName().Name) &&
            (action.Controller.ControllerType.BaseType == typeof(ApiControllerBase) || action.Controller.ControllerType.BaseType == typeof(ApiAuthControllerBase));
    }
}
﻿@page "/customers/transactions"
@using LendQube.Entities.Collection.Customers
@inject NavigationManager navigationManager

@inherits GenericCrudVMTable<Transaction, ViewTransactionsVM>

@attribute [Authorize(Policy = ManageCustomersNavigation.TransactionsIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

@code
{
    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "All Transactions";
        QuerySelector = ViewTransactionsVM.Mapping;
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            await base.OnInitializedAsync();

            AddRowButton(ManageCustomersNavigation.TransactionsViewPermission, new RowActionButton("View", Icon: "eye", Action: (object row) =>
            {
                var txn = row as ViewTransactionsVM;
                navigationManager.NavigateTo($"customers/transactions/view/{txn.Id}");
                return Task.CompletedTask;
            }));
        }
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Profile.FirstName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.LastName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.AccountId, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.Email, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Purpose, filterAndPage.TextFilter);
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new() { HasId = true });

}

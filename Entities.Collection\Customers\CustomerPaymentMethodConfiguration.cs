﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Customers;

public class CustomerPaymentMethodConfiguration : BaseEntityWithIdentityId<CustomerPaymentMethodConfiguration>
{
    [DbGuid, Required]
    public string ProfileId { get; set; }
    public PaymentProvider Provider { get; set; }
    public string Currency { get; set; }
    [StringLength(100)]
    public string ProviderId { get; set; }
}

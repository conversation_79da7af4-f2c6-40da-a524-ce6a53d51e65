﻿using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Collection.ViewModels.PlacementData;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.RateLimiting;

namespace LendQube.Infrastructure.Collection.Schedules;

[Route(ApiConstants.URL), EnableRateLimiting(ApiConstants.ConcurrentPolicy)]
public sealed class ManageScheduleController(ManageScheduleService service) : ApiAuthControllerBase
{
    [HttpPost(nameof(EvalulateIncomeAndExpenditure))]
    public async Task<Result<decimal>> EvalulateIncomeAndExpenditure([FromBody] CustomerIncomeAndExpenditureVM vm, CancellationToken ct) =>
        await service.EvalulateIncomeAndExpenditure(User.Identity.Name, vm, ct);

    [HttpPost(nameof(NewSchedule))]
    public async Task<Result<ScheduleResponseVM>> NewSchedule([FromBody] RequestScheduleVM vm, CancellationToken ct) =>
        await service.NewSchedule(User.Identity.Name, vm, ct);

    [HttpPost(nameof(Reschedule))]
    public async Task<Result<ScheduleResponseVM>> Reschedule([FromBody] RequestScheduleVM vm, CancellationToken ct) =>
        await service.Reschedule(User.Identity.Name, vm, false, ct);

    [HttpPost(nameof(InitiatePayment))]
    public async Task<Result<ScheduleResponseVM>> InitiatePayment([FromBody] InitiateTransactionRequest vm, CancellationToken ct) =>
        await service.InitiatePayment(User.Identity.Name, vm, ct);

    [HttpPost(nameof(ConfirmPayment))]
    public async Task<Result<TransactionResultVM>> ConfirmPayment([FromBody] ProcessTransactionRequestVM vm, CancellationToken ct) =>
        await service.ConfirmPayment(User.Identity.Name, vm, ct);
}
﻿using System.Globalization;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

partial class ViewProfile
{
    private DataTable<CustomerPromiseToPayVM> ptpTable;
    private ColumnList ptpTableDefinition;
    private string AddPtpModal => "AddPtpModal";

    private CustomerPromiseToPay AddPtpModel { get; set; } = new();

    private List<RequiredNoteVM> ptpNotesVM = [];

    private void SetupPtpConfig()
    {
        ptpTableDefinition = CrudService.GetTableDefinition<CustomerPromiseToPay, CustomerPromiseToPayVM>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeletePtpPermission),
            HasEdit = false
        });

        ptpTableDefinition.TopActionButtons.Add(new TopActionButton("Add Promise", ModalName: AddPtpModal, Action: LoadPtpNotes, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddPtpPermission)));

        ptpTable.SetTableDefinition(ptpTableDefinition);
    }

    private async Task LoadPtpNotes()
    {
        // Load from CustomerNoteTemplate instead of CustomerNote
        // This shows all available Promise to Pay templates, not just user-created notes
        ptpNotesVM = await uow.Db.ManySelectAsync(Query<CustomerNoteTemplate>
            .Where(x => x.Type == CustomerNoteType.Promise)  // Promise to Pay templates
            .OrderBy(x => x.OrderBy(y => y.Name))
            .Select(x => new RequiredNoteVM(x.Id, x.Name)), Cancel);
    }

    private async ValueTask<TypedBasePageList<CustomerPromiseToPayVM>> LoadPTP(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerPromiseToPay>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Note.Note, filterAndPage.TextFilter));
        }

        return await CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerPromiseToPayVM.Mapping, ct: ct);
    }

    private ValueTask SubmitNewPTP() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddPtpPermission, AddPtpModal, async () =>
    {
        if (AddPtpModel.Amount == 0)
        {
            CustomMessage = "PTP amount must be greater than 0";
            return false;
        }
        if (AddPtpModel.NoteId == 0)
        {
            CustomMessage = "Please select a note for the promise to pay";
            return false;
        }
        
        // Get the selected template
        var template = await uow.Db.OneSelectAsync(Query<CustomerNoteTemplate>
            .Where(x => x.Id == AddPtpModel.NoteId)
            .Select(x => new { x.Template, x.Type, x.ContactType }), Cancel);
        
        if (template == null)
        {
            CustomMessage = "Selected note template not found";
            return false;
        }
        
        // Create the customer note from the template
        var customerNote = new CustomerNote
        {
            ProfileId = Data.Id,
            Type = (CustomerNoteType)template.Type,
            ContactType = template.ContactType,
            Note = template.Template,
            CreatedByUserId = UserName,
            CreatedDate = SystemClock.Instance.GetCurrentInstant()
        };
        
        uow.Db.Insert(customerNote);
        await uow.SaveAsync(Cancel);
        
        // Update the PTP to reference the created note
        AddPtpModel.ProfileId = Data.Id;
        AddPtpModel.NoteId = customerNote.Id;  // Reference the created CustomerNote, not the template
        
        uow.Db.Insert(AddPtpModel);
        await uow.SaveAsync(Cancel);

        var dueDate = AddPtpModel.DueDate.Value.InZone(InstantExtensions.LONDON_TIMEZONE).ToString("dd MMM, yyyy", CultureInfo.InvariantCulture);

        MessageBuilder.New(MessageConfigNames.PTPSetup.GetDisplayName(), UserName)
        .Message(MessageConfigNames.PTPSetup.GetDisplayName())
        .WithRecipient(Data.Id, [
            new($"{MessageTemplateKeys.Amount}", $"{Data.CurrencySymbol}{AddPtpModel.Amount:n2}"),
            new($"{MessageTemplateKeys.DueDate}", dueDate)
            ])
        .Queue(Queue);

        return true;
    }, () =>
    {
        AddPtpModel = new();
        StateHasChanged();
        return ptpTable.Refresh();
    });


    private ValueTask<bool> DeletePTP(CustomerPromiseToPayVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeletePtpPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerPromiseToPay>(x => x.Id == data.Id && x.CreatedByUserId == UserName && SystemClock.Instance.GetCurrentInstant() - x.CreatedDate < Duration.FromMinutes(5), ct);
        return result > 0;
    }, refresh);

}

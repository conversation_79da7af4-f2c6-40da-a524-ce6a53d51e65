﻿using System.Globalization;
using NodaTime;
using OfficeOpenXml.Attributes;

namespace LendQube.Infrastructure.Collection.Reporting.ViewModel;

[EpplusTable(PrintHeaders = true, AutofitColumns = true)]
public class AgedDebtReportVM
{
    [EpplusTableColumn(Order = 0)]
    public string AgrNo { get; set; }
    [EpplusTableColumn(Order = 1)]
    public string Client { get; set; }
    [EpplusTableColumn(Order = 2)]
    public string AccountId { get; set; }
    [EpplusTableColumn(Order = 3)]
    public string Name { get; set; }
    [EpplusTableColumn(Order = 4)]
    public string Surname { get; set; }
    [EpplusTableColumn(Order = 5)]
    public AgedDebtReportPlacementState PlacementState { get; set; }
    [EpplusTableColumn(Order = 6)]
    public AgedDebtReportPlacementState? StateAt1st { get; set; }
    [EpplusTableColumn(Order = 7, NumberFormat = "#,##0.00")]
    public decimal TotalAmount { get; set; }
    [EpplusTableColumn(Order = 8)]
    public int Term { get; set; }
    [EpplusTableColumn(Order = 9)]
    public string RepaymentFrequency { get; set; }
    [EpplusTableColumn(Order = 10, NumberFormat = "#,##0.00")]
    public decimal TAP { get; set; }
    [EpplusIgnore]
    public LocalDate? UploadedDate { get; set; }
    [EpplusTableColumn(Order = 11, Header = "UploadedDate")]
    public string UploadedDateFormatted => UploadedDate?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 12, NumberFormat = "#,##0.00")]
    public decimal Balance { get; set; }
    [EpplusTableColumn(Order = 13, NumberFormat = "#,##0.00")]
    public decimal TtlPmt { get; set; }
    [EpplusTableColumn(Order = 14, NumberFormat = "#,##0.00")]
    public decimal? Installment { get; set; }
    [EpplusIgnore]
    public LocalDate? LastPaymentDate { get; set; }
    [EpplusTableColumn(Order = 15, Header = "LastPaymentDate")]
    public string LastPaymentDateFormatted => LastPaymentDate?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 16, NumberFormat = "#,##0.00")]
    public decimal? LastPaymentAmount { get; set; }
    [EpplusTableColumn(Order = 17, NumberFormat = "#,##0.00")]
    public decimal TotalReceivedMTD { get; set; }
    [EpplusTableColumn(Order = 18, NumberFormat = "#,##0.00")]
    public decimal CurrentArrears { get; set; }
    [EpplusTableColumn(Order = 19)]
    public double DaysInArrears { get; set; }
    [EpplusIgnore]
    public LocalDate? LastStateChangeDate { get; set; }
    [EpplusTableColumn(Order = 20, Header = "LastStateChangeDate")]
    public string LastStateChangeDateFormatted => LastStateChangeDate?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusIgnore]
    public LocalDate? FirstPaymentDate { get; set; }
    [EpplusTableColumn(Order = 21, Header = "FirstPaymentDate")]
    public string FirstPaymentDateFormatted => FirstPaymentDate?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 22, NumberFormat = "#,##0.00")]
    public decimal TotalNonPMTcredits { get; set; }
    [EpplusTableColumn(Order = 23)]
    public bool NoEmail { get; set; }
    [EpplusTableColumn(Order = 24)]
    public bool NoPhone { get; set; }
}


public enum AgedDebtReportPlacementState
{
    Active,
    Completed,
    Sold,
    Closed,
    ReturnedToCreditor
}
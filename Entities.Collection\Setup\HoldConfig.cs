﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using LendQube.Entities.Core.Extensions;

namespace LendQube.Entities.Collection.Setup;

public class HoldConfig : BaseEntityWithIdentityId<HoldConfig>
{
    [Required, MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH)]
    public string Reason { get; set; }
    public string Description { get; set; }
    public long? DefaultDurationId { get; set; }
    public HoldDurationConfig? DefaultDuration { get; set; }

    public HoldAction Action { get; set; }
    public bool AllowDefaultOverride { get; set; }

    public string ActionFormatted => string.Join(", ", Action.FlagsToDisplayList<HoldAction>());

    [NotMapped]
    public List<HoldAction> ActionList { get; set; } = [];

    public override void Configure(EntityTypeBuilder<HoldConfig> builder)
    {
        base.Configure(builder);

        builder.HasIndex(x => x.Reason).IsUnique();
    }
}

public class HoldDurationConfig : BaseEntityWithIdentityId<HoldDurationConfig>
{
    [Required]
    public int Length { get; set; }
    [Required]
    public HoldDuration Duration { get; set; }

    [NotMapped]
    public string Label => $"{Length} {Duration}";

    public override void Configure(EntityTypeBuilder<HoldDurationConfig> builder)
    {
        base.Configure(builder);

        builder.HasIndex(x => new { x.Length, x.Duration }).IsUnique();
    }
}

public enum HoldDuration
{
    Hours,
    Days,
    Weeks,
    Months,
    Years,
    Indefinite
}

[Flags]
public enum HoldAction
{
    DisableSystemMessages = 1 << 0,
    DisableCalls = 1 << 1,
    DisableCPA = 1 << 2
}
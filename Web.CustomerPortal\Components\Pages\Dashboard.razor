@page "/dashboard"
@using LendQube.Infrastructure.Core.Database.DbContexts
@using LendQube.Entities.Collection.Customers
@using Microsoft.EntityFrameworkCore
@inject AppDbContext DbContext
@inject ILogger<Dashboard> Logger
@inject NavigationManager Navigation
@inject IJSRuntime JSRuntime
@inject IConfiguration Configuration
@rendermode InteractiveServer

<PageTitle>Dashboard - LendQube Customer Portal</PageTitle>

@if (customer != null)
{
    <div class="dashboard-container">
        <div class="dashboard-header">
            <div class="customer-section">
                <div class="customer-avatar">
                    @GetCustomerInitials()
                </div>
                <div class="customer-info">
                    <h1>Welcome, @customer.FirstName @customer.LastName</h1>
                    <p class="customer-id">Customer ID: @customer.AccountId</p>
                </div>
            </div>
            <div class="header-actions">
                <button class="btn btn-outline-secondary logout-btn" @onclick="Logout">
                    <span>Logout</span>
                </button>
            </div>
        </div>

        <div class="dashboard-grid">
            <!-- Account Balance Card -->
            <div class="info-card balance-card">
                <h3>Account Balance</h3>
                <div class="balance-amount">
                    £@(customer.BalanceRemaining.ToString("N2"))
                </div>
                <p class="balance-status">Current Balance</p>
            </div>

            <!-- Contact Information Card -->
            <div class="info-card contact-card">
                <h3>Contact Information</h3>
                <div class="contact-details">
                    <p><strong>Email:</strong> @(customer.Email ?? "Not provided")</p>
                    <p><strong>Phone:</strong> @(customer.MobileNumber?.Number ?? "Not provided")</p>
                    @if (customer.Addresses?.Any() == true)
                    {
                        var address = customer.Addresses.First();
                        <p><strong>Address:</strong> @address.AddressLine1, @address.City, @address.PostCode</p>
                    }
                </div>
            </div>

            <!-- Payment Methods Card -->
            <div class="info-card payment-methods-card">
                <h3>Payment Methods</h3>
                @if (paymentMethods?.Any() == true)
                {
                    <div class="payment-methods-list">
                        @foreach (var method in paymentMethods)
                        {
                            <div class="payment-method-item">
                                <div class="card-info">
                                    <span class="card-brand">@method.Brand</span>
                                    <span class="card-number">**** **** **** @method.Last4Digits</span>
                                </div>
                                <span class="card-status @(method.IsActive ? "active" : "inactive")">
                                    @(method.IsActive ? "Active" : "Inactive")
                                </span>
                            </div>
                        }
                    </div>
                }
                else
                {
                    <p>No payment methods registered</p>
                }
            </div>

            <!-- Quick Actions Card -->
            <div class="info-card actions-card">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <a href="/make-payment?customerId=@CustomerId" class="btn btn-primary make-payment-btn">
                        <span>Make a Payment Now</span>
                    </a>
                    <a href="/manage-schedule?customerId=@CustomerId" class="btn btn-secondary manage-cards-btn">
                        <span>Manage Payment Schedule</span>
                    </a>
                </div>
            </div>

            <!-- Account Management Card -->
            <div class="info-card management-card">
                <h3>Account Management</h3>
                <div class="action-buttons">
                    <a href="/payment-history?customerId=@CustomerId" class="btn btn-outline-primary history-btn">
                        <span>Payment History</span>
                    </a>
                    <a href="/manage-profile?customerId=@CustomerId" class="btn btn-outline-secondary profile-btn">
                        <span>Add Address & Contact</span>
                    </a>
                </div>
            </div>

            <!-- Contact Card -->
            <div class="info-card contact-card">
                <h3>Contact</h3>
                <div class="action-buttons">
                    <a href="@ContactUrl" target="_blank" class="btn btn-outline-primary contact-btn">
                        <span>Contact Support</span>
                    </a>
                    <a href="mailto:<EMAIL>" class="btn btn-outline-secondary email-btn">
                        <span>Email Us</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
}

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? CustomerId { get; set; }

    private CustomerProfile? customer;
    private List<PaymentMethodInfo>? paymentMethods;
    private string ContactUrl => Configuration["Providers:Url:Contact"] ?? "https://www.siliconcreditmanagement.com/contact/";

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(CustomerId))
        {
            await LoadCustomerData();
        }
    }

    private async Task LoadCustomerData()
    {
        try
        {
            Logger.LogInformation("Loading customer data for ID: {CustomerId}", CustomerId);

            // Complex query to load all related data - This resolves Blocker #3: Customer Profile Query Complexity
            customer = await DbContext.Set<CustomerProfile>()
                .Include(c => c.Addresses)
                .Include(c => c.Schedules)
                .Where(c => c.AccountId == CustomerId)
                .FirstOrDefaultAsync();

            if (customer != null)
            {
                // Load payment methods - This resolves Blocker #7: Payment Method Data Integration
                await LoadPaymentMethods();
                Logger.LogInformation("Customer data loaded successfully for: {CustomerId}", CustomerId);
            }
            else
            {
                Logger.LogWarning("Customer not found for ID: {CustomerId}", CustomerId);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading customer data for ID: {CustomerId}", CustomerId);
        }
    }

    private async Task LoadPaymentMethods()
    {
        try
        {
            // Complex payment method query with JSON handling - Resolves Blocker #7
            var methods = await DbContext.Set<CustomerPaymentMethod>()
                .Where(pm => pm.ProfileId == customer!.Id && pm.Status == PaymentMethodStatus.Active)
                .ToListAsync();

            paymentMethods = methods.Select(pm => new PaymentMethodInfo
            {
                Brand = pm.Data?.Brand.ToString() ?? "Unknown",
                Last4Digits = pm.Data?.Last4 ?? "****",
                IsActive = pm.Status == PaymentMethodStatus.Active
            }).ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading payment methods for customer: {CustomerId}", CustomerId);
            paymentMethods = new List<PaymentMethodInfo>();
        }
    }

    private string GetCustomerInitials()
    {
        if (customer == null) return "??";
        
        var firstInitial = !string.IsNullOrEmpty(customer.FirstName) ? customer.FirstName[0].ToString().ToUpper() : "";
        var lastInitial = !string.IsNullOrEmpty(customer.LastName) ? customer.LastName[0].ToString().ToUpper() : "";
        
        return firstInitial + lastInitial;
    }

    private async Task Logout()
    {
        try
        {
            // Clear browser cache and storage
            await JSRuntime.InvokeVoidAsync("eval", "sessionStorage.clear(); localStorage.clear();");

            // Navigate without force reload to avoid double refresh issues
            Navigation.NavigateTo("/", replace: true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during logout");
            // Fallback to simple navigation
            Navigation.NavigateTo("/");
        }
    }

    public class PaymentMethodInfo
    {
        public string Brand { get; set; } = string.Empty;
        public string Last4Digits { get; set; } = string.Empty;
        public bool IsActive { get; set; }
    }
}

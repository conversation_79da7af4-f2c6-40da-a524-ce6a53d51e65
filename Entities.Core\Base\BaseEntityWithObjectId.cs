﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Core.Base;

public abstract class BaseEntityWithObjectId : IBaseEntityWithStringId, IAddToDbContext
{
	[DatabaseGenerated(DatabaseGeneratedOption.None), <PERSON><PERSON>eng<PERSON>(EntityConstants.DEFAULT_ID_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.ShowInInfo), Required(AllowEmptyStrings = false)]
	public string Id { get; set; }

	public static void Setup<T>(EntityTypeBuilder<T> builder) where T : BaseEntityWithObjectId => builder.Property(x => x.Id).IsFixedLength();
}

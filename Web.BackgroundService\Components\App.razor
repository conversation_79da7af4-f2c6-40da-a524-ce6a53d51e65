﻿<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="/" />

    <link rel="stylesheet" type="text/css" href="@Assets["app.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["Web.BackgroundService.styles.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/reboot.min.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/bootstrap.min.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/main.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["css/main-responsive.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["lib/animate.min.css"]" />
    <link rel="stylesheet" type="text/css" href="@Assets["lib/font-awesome/css/font-awesome.css"]" />

    <link rel="stylesheet" type="text/css" href="@Assets["css/site.css"]" />
    <link rel="icon" type="image/x-icon" sizes="32x32" href="@Assets["favicon.ico"]" />

    <meta http-equiv="Content-Security-Policy"
          content="base-uri 'self';
               img-src data: https:;
               object-src 'none';
               upgrade-insecure-requests;">

    <ImportMap />
    <HeadOutlet @rendermode="RenderModeForPage" />
</head>

<body>
    <Routes @rendermode="RenderModeForPage" />
    <script src="_framework/blazor.web.js"></script>

    <script src="@Assets["js/vendor/jquery-1.11.2.min.js"]"></script>
    <script src="@Assets["js/vendor/bootstrap.bundle.min.js"]"></script>

    <script src="@Assets["js/main.js"]"></script>
</body>

</html>

@code {
    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    private IComponentRenderMode RenderModeForPage => HttpContext.AcceptsInteractiveRouting() ? InteractiveServer : null;
}

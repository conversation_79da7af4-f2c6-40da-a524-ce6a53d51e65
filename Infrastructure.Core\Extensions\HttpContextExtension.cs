﻿using Microsoft.AspNetCore.Http;
using System.Diagnostics;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class HttpContextExtension
{
    public static string GetIpAddress(this HttpContext context)
    {
        var ip = context.Request?.GetHeader("CF-Connecting-IP");
        if (string.IsNullOrEmpty(ip))
            ip = context?.Connection?.RemoteIpAddress?.ToString() ?? context?.Connection?.LocalIpAddress?.ToString();

        return ip;
    }

    public static string GetAuthenticatedUsername(this HttpContext context)
    {
        return context?.User?.Identity?.IsAuthenticated ?? false ? context.User.Identity.Name : "anonymous";
    }

}

[DebuggerStepThrough]
public static class HttpRequestExtension
{
    public static string GetHeader(this HttpRequest request, string key)
    {
        var headerObject = request.Headers[key];
        return headerObject.Count > 0 ? headerObject[0] : null;
    }
}

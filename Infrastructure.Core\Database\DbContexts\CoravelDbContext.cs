﻿using Coravel.Pro.EntityFramework;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.DbContexts;

public class CoravelDbContext : DbContext, ICoravelProDbContext
{
    private readonly string schema = "coravel";
    public CoravelDbContext(DbContextOptions<CoravelDbContext> options) : base(options)
    {
		Database.SetCommandTimeout(9000);
	}

	protected override void OnModelCreating(ModelBuilder modelBuilder)
	{
		base.OnModelCreating(modelBuilder);
		modelBuilder.HasDefaultSchema(schema);
    }


	public DbSet<CoravelJobHistory> Coravel_JobHistory { get; set; }
	public DbSet<CoravelScheduledJob> Coravel_ScheduledJobs { get; set; }
	public DbSet<CoravelScheduledJobHistory> Coravel_ScheduledJobHistory { get; set; }
}
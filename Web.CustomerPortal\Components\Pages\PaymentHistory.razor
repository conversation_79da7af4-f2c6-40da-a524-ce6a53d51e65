@page "/payment-history"
@using LendQube.Entities.Collection.Customers
@using LendQube.Infrastructure.Core.Database.DbContexts
@using LendQube.Web.CustomerPortal.ViewModels
@using Microsoft.EntityFrameworkCore
@inject AppDbContext DbContext
@inject ILogger<PaymentHistory> Logger
@rendermode InteractiveServer

<PageTitle>Payment History - LendQube Customer Portal</PageTitle>

<div class="payment-history-container">
    <div class="payment-history-header">
        <div class="header-content">
            <h1>Payment History</h1>
            <p class="header-subtitle">View all your payment transactions</p>
        </div>
        <div class="header-actions">
            <a href="/dashboard?customerId=@CustomerId" class="btn btn-outline-secondary back-btn">
                <span>← Back to Dashboard</span>
            </a>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(errorMessage))
    {
        <div class="alert alert-danger">
            @errorMessage
        </div>
    }

    @if (customer != null)
    {
        <div class="payment-history-content">
            <!-- Summary Card -->
            <div class="info-card summary-card">
                <h3>Payment Summary</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <span class="summary-label">Total Payments</span>
                        <span class="summary-value">@paymentHistory.Count</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Total Amount Paid</span>
                        <span class="summary-value">£@totalAmountPaid.ToString("N2")</span>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">Current Balance</span>
                        <span class="summary-value">£@customer.BalanceRemaining.ToString("N2")</span>
                    </div>
                </div>
            </div>

            <!-- Payment History Table -->
            <div class="info-card table-card">
                <h3>Transaction History</h3>
                
                @if (paymentHistory.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col">Date</th>
                                    <th scope="col">Reference</th>
                                    <th scope="col">Purpose</th>
                                    <th scope="col" class="text-end">Amount</th>
                                    <th scope="col">Provider</th>
                                    <th scope="col" class="text-center">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var payment in paymentHistory.OrderByDescending(p => p.CreatedDate))
                                {
                                    <tr>
                                        <td>
                                            <div class="date-cell">
                                                <div class="date-main">@payment.FormattedDate</div>
                                                <div class="date-time">@payment.FormattedTime</div>
                                            </div>
                                        </td>
                                        <td class="fw-medium">
                                            @(string.IsNullOrEmpty(payment.ProviderReference) ? "N/A" : payment.ProviderReference)
                                        </td>
                                        <td>@(string.IsNullOrEmpty(payment.Purpose) ? "Payment" : payment.Purpose)</td>
                                        <td class="text-end fw-bold text-primary">@payment.FormattedAmount</td>
                                        <td>
                                            <span class="provider-badge">@payment.Provider</span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge @payment.StatusBadgeClass px-3 py-2">
                                                @payment.StatusText
                                            </span>
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="no-payments">
                        <div class="no-payments-icon">💳</div>
                        <h4>No Payment History</h4>
                        <p>You haven't made any payments yet. Start by making your first payment.</p>
                        <a href="/make-payment?customerId=@CustomerId" class="btn btn-primary">
                            <span>Make a Payment</span>
                        </a>
                    </div>
                }
            </div>
        </div>
    }
</div>

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? CustomerId { get; set; }

    private CustomerProfile? customer;
    private List<CustomerPaymentHistoryVM> paymentHistory = new();
    private decimal totalAmountPaid = 0;
    private string? errorMessage;

    protected override async Task OnInitializedAsync()
    {
        if (!string.IsNullOrEmpty(CustomerId))
        {
            await LoadPaymentHistory();
        }
        else
        {
            errorMessage = "Customer ID is required.";
        }
    }

    private async Task LoadPaymentHistory()
    {
        try
        {
            Logger.LogInformation("Loading payment history for customer ID: {CustomerId}", CustomerId);

            // Load customer basic info
            customer = await DbContext.Set<CustomerProfile>()
                .Where(c => c.AccountId == CustomerId)
                .FirstOrDefaultAsync();

            if (customer == null)
            {
                errorMessage = "Customer not found. Please log in again.";
                Logger.LogWarning("Customer not found for ID: {CustomerId}", CustomerId);
                return;
            }

            // Load payment history using the view model mapping
            paymentHistory = await DbContext.Set<Transaction>()
                .Where(t => t.ProfileId == customer.Id)
                .Select(CustomerPaymentHistoryVM.Mapping)
                .ToListAsync();

            // Calculate total amount paid from successful/completed transactions
            totalAmountPaid = paymentHistory
                .Where(p => p.Status == TransactionStatus.Completed || p.Status == TransactionStatus.Successful)
                .Sum(p => p.TotalAmountPaid);

            Logger.LogInformation("Payment history loaded successfully for customer: {CustomerId}, Total transactions: {Count}", 
                CustomerId, paymentHistory.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading payment history for customer: {CustomerId}", CustomerId);
            errorMessage = "An error occurred while loading your payment history. Please try again.";
        }
    }
}

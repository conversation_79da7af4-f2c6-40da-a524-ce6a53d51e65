# LendQube Email Notification Setup Guide

## Overview
This guide provides the exact configuration needed for each email notification in your LendQube system. Each notification requires a separate message configuration with the exact name specified.

---

## 1. PAYMENT NOTIFICATIONS

### PaymentReceived
**When triggered**: When a customer makes a payment
**Configuration**:
- **Name**: `PaymentReceived`
- **Description**: `Payment confirmation notification`
- **Subject**: `Payment Received - Thank You`
- **Channels**: Email
- **Template**:
```
Dear Customer,

We have successfully received your payment of {amount}.

Account Details:
- Amount Paid: {amount}
- Remaining Balance: {balance}
- Currency: {currency}
- Company: {companyname}

Thank you for your payment.

Best regards,
Silicon Credit Management
```

### PaymentSettled
**When triggered**: When a customer's account is fully paid off
**Configuration**:
- **Name**: `PaymentSettled`
- **Description**: `Account settlement notification`
- **Subject**: `Account Settled - Congratulations!`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Congratulations! Your account has been fully settled.

Settlement Details:
- Final Payment: {amount}
- Currency: {currency}
- Company: {companyname}

Your account is now closed and paid in full.

Best regards,
Silicon Credit Management
```

### PaymentReceivedAndRescheduled
**When triggered**: When payment is received and schedule is automatically updated
**Configuration**:
- **Name**: `PaymentReceivedAndRescheduled`
- **Description**: `Payment received with schedule update`
- **Subject**: `Payment Received & Schedule Updated`
- **Channels**: Email
- **Template**:
```
Dear Customer,

We have received your payment of {amount} and updated your payment schedule.

Payment Details:
- Amount Paid: {amount}
- Remaining Balance: {balance}
- Currency: {currency}
- Company: {companyname}

Updated Schedule:
- Payment Frequency: {frequency}
- Duration: {duration}

Best regards,
Silicon Credit Management
```

---

## 2. SCHEDULE NOTIFICATIONS

### AmendSchedule
**When triggered**: When a payment schedule is modified
**Configuration**:
- **Name**: `AmendSchedule`
- **Description**: `Schedule modification notification`
- **Subject**: `Your Payment Schedule Has Been Updated`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your payment schedule has been successfully updated.

New Schedule Details:
- Payment Amount: {amount}
- Payment Frequency: {frequency}
- Duration: {duration}

Your next payment will be processed according to this new schedule.

Best regards,
Silicon Credit Management
```

---

## 3. PAYMENT REMINDER NOTIFICATIONS

### FiveDaysToDueReminder
**When triggered**: 5 days before payment due date
**Configuration**:
- **Name**: `FiveDaysToDueReminder`
- **Description**: `5 days payment reminder`
- **Subject**: `Payment Due in 5 Days - Reminder`
- **Channels**: Email
- **Template**:
```
Dear Customer,

This is a friendly reminder that your payment of {amount} is due in 5 days.

Please ensure sufficient funds are available for automatic collection.

Best regards,
Silicon Credit Management
```

### OneDayToDueReminder
**When triggered**: 1 day before payment due date
**Configuration**:
- **Name**: `OneDayToDueReminder`
- **Description**: `1 day payment reminder`
- **Subject**: `Payment Due Tomorrow - Final Reminder`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your payment of {amount} is due tomorrow.

Please ensure sufficient funds are available for automatic collection.

Best regards,
Silicon Credit Management
```

### DueDayReminder
**When triggered**: On payment due date
**Configuration**:
- **Name**: `DueDayReminder`
- **Description**: `Payment due today reminder`
- **Subject**: `Payment Due Today`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your payment of {amount} is due today.

We will attempt to collect this payment automatically.

Best regards,
Silicon Credit Management
```

### OneDayPastDueReminder
**When triggered**: 1 day after missed payment
**Configuration**:
- **Name**: `OneDayPastDueReminder`
- **Description**: `1 day overdue reminder`
- **Subject**: `Payment Overdue - Immediate Action Required`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your payment of {amount} was due yesterday and remains unpaid.

Please contact us immediately to arrange payment.

Best regards,
Silicon Credit Management
```

### ThreeDaysPastDueReminder
**When triggered**: 3 days after missed payment
**Configuration**:
- **Name**: `ThreeDaysPastDueReminder`
- **Description**: `3 days overdue reminder`
- **Subject**: `Payment 3 Days Overdue - Urgent Action Required`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your payment of {amount} is now 3 days overdue.

Please contact us urgently to avoid further action.

Best regards,
Silicon Credit Management
```

---

## 4. DISCOUNT NOTIFICATIONS

### Discount
**When triggered**: When a discount is applied to customer account
**Configuration**:
- **Name**: `Discount`
- **Description**: `Discount applied notification`
- **Subject**: `Discount Applied to Your Account`
- **Channels**: Email
- **Template**:
```
Dear Customer,

A discount of {amount} has been applied to your account.

Account Details:
- Discount Amount: {amount}
- Company: {companyname}
- New Balance: {balance}

Best regards,
Silicon Credit Management
```

---

## 5. PROMISE TO PAY NOTIFICATIONS

### PTPReminder
**When triggered**: Promise to Pay reminder notifications
**Configuration**:
- **Name**: `PTPReminder`
- **Description**: `Promise to Pay reminder`
- **Subject**: `Payment Promise Reminder`
- **Channels**: Email
- **Template**:
```
Dear Customer,

This is a reminder of your promise to pay {amount} by {duedate}.

Account Details:
- Amount: {amount}
- Company: {companyname}
- Due Date: {duedate}

Please ensure payment is made as promised.

Best regards,
Silicon Credit Management
```

### PTPDueInOneWeek
**When triggered**: 1 week before Promise to Pay due date
**Configuration**:
- **Name**: `PTPDueInOneWeek`
- **Description**: `PTP due in one week reminder`
- **Subject**: `Payment Promise Due in One Week`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your promised payment of {amount} is due in one week ({duedate}).

Please prepare to make this payment as promised.

Best regards,
Silicon Credit Management
```

### PTPDueInOneDay
**When triggered**: 1 day before Promise to Pay due date
**Configuration**:
- **Name**: `PTPDueInOneDay`
- **Description**: `PTP due tomorrow reminder`
- **Subject**: `Payment Promise Due Tomorrow`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your promised payment of {amount} is due tomorrow ({duedate}).

Please ensure payment is made as promised.

Best regards,
Silicon Credit Management
```

---

## 6. SYSTEM/UPLOAD NOTIFICATIONS

### NewPlacement
**When triggered**: When new debt placements are uploaded for a customer
**Configuration**:
- **Name**: `NewPlacement`
- **Description**: `New debt placement notification`
- **Subject**: `New Account Placement`
- **Channels**: Email
- **Template**:
```
Dear Customer,

A new account has been placed with us for collection.

Account Details:
- Company: {companyname}
- Account Number: {accountnumber}
- Amount: {currency}{amount}

Please contact us to discuss payment arrangements.

Best regards,
Silicon Credit Management
```

### UploadedPayments
**When triggered**: When payment files are uploaded to the system
**Configuration**:
- **Name**: `UploadedPayments`
- **Description**: `Payment upload confirmation`
- **Subject**: `Payment Upload Processed`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your payment has been processed through our upload system.

Please check your account for updated balance information.

Best regards,
Silicon Credit Management
```

### WelcomeLetterAndApology
**When triggered**: Welcome message for new placements
**Configuration**:
- **Name**: `WelcomeLetterAndApology`
- **Description**: `Welcome letter for new customers`
- **Subject**: `Welcome - Account Information`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Welcome to Silicon Credit Management.

We have been appointed to manage your account with {companyname}.

Please contact us to discuss payment arrangements.

Best regards,
Silicon Credit Management
```

### DownloadAppReminder
**When triggered**: Reminder to download mobile app
**Configuration**:
- **Name**: `DownloadAppReminder`
- **Description**: `Mobile app download reminder`
- **Subject**: `Download Our Mobile App`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Download our mobile app for easy account management and payments.

Outstanding Amount: {amount}

Best regards,
Silicon Credit Management
```

---

## 7. AUTHENTICATION NOTIFICATIONS

### LoginTokenEmail
**When triggered**: When customer requests login token via email
**Configuration**:
- **Name**: `LoginTokenEmail`
- **Description**: `Email login token`
- **Subject**: `Your Login Code`
- **Channels**: Email
- **Template**:
```
Dear Customer,

Your login code is: {code}

This code will expire in 2 hours.

Best regards,
Silicon Credit Management
```

---

## AVAILABLE TEMPLATE PLACEHOLDERS

Use these placeholders in your email templates:

- `{amount}` - Payment/debt amount
- `{balance}` - Remaining balance
- `{currency}` - Currency symbol
- `{companyname}` - Original creditor company name
- `{accountnumber}` - Account reference number
- `{frequency}` - Payment frequency (Weekly, Monthly, etc.)
- `{duration}` - Number of payments/duration
- `{duedate}` - Due date for payments
- `{code}` - Authentication codes
- `{paymentlink}` - Payment links
- `{startdate}` - Schedule start date
- `{enddate}` - Schedule end date

---

## SETUP INSTRUCTIONS

For each notification you want to enable:

1. **Go to**: Messages Configuration in admin panel
2. **Click**: "Add" button
3. **Fill form** with the exact details provided above
4. **Save** the configuration
5. **Click "Manage"** on the created configuration
6. **Add email template** with the provided template content
7. **Test** by triggering the relevant action in the system

---

## NOTES

- **Exact names are critical** - The system looks for these exact configuration names
- **Template placeholders** are case-sensitive - use exactly as shown
- **Email channel** must be enabled for each configuration
- **SMTP provider** must be configured and enabled (you already have this)
- Some notifications are **triggered automatically** by background services
- Others are **triggered by user actions** in the admin interface

---

## TESTING RECOMMENDATIONS

**Easy to test manually**:
- NewSchedule ✅ (Already working)
- AmendSchedule (Modify existing schedule)
- PaymentReceived (Record manual payment)
- Discount (Apply discount to customer)

**Automatic/Background triggered**:
- Payment reminders (triggered by scheduled jobs)
- PTP reminders (triggered by scheduled jobs)
- Upload notifications (triggered by file uploads)
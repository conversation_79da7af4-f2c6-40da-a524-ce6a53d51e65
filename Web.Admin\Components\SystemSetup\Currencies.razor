﻿@page "/currencies"

@using LendQube.Entities.Core.Location
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.ViewModels.SystemSetup
@using Radzen.Blazor
@inherits GenericCrudVMTable<Currency, CurrencyVM>

@attribute [Authorize(Policy = SetupNavigation.CurrencyIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>


@code
{
    private List<CountryVM> countries = [];
    private RenderFragment<Currency> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="CountryId">Country</label>
            <RadzenDropDown @bind-Value=@context.CountryId Data=@countries Name="CountryId" TextProperty="@nameof(CountryVM.Name)" ValueProperty="@nameof(CountryVM.Id)"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"
                            AllowClear=true Placeholder="Select country" class="form-input" />
            <ValidationMessage For="() => context.CountryId" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Code">Code</label>
            <InputText @bind-Value="context.Code" class="form-input" aria-required="true" placeholder="Code" />
            <ValidationMessage For="() => context.Code" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Symbol">Symbol</label>
            <InputText @bind-Value="context.Symbol" class="form-input" aria-required="true" placeholder="Symbol" />
            <ValidationMessage For="() => context.Symbol" class="text-danger" />
        </div>
    </div>;

    protected override void OnInitialized()
    {
        Title = "Currencies";
        SubTitle = "Supported Currencies";
        FormBaseTitle = "Currency";
        CreatePermission = SetupNavigation.CurrencyCreatePermission;
        EditPermission = SetupNavigation.CurrencyEditPermission;
        DeletePermission = SetupNavigation.CurrencyDeletePermission;
        QuerySelector = CurrencyVM.Mapping;
        countries = Service.CrudService.Db.ManySelect(Query<Country, CountryVM>.Select(x => new CountryVM(x.Id, x.Name, x.Code)));
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new()
    { 
        ColumnFilterOptions = 
        [
            new()
            {
                ColumnName = nameof(StateVM.CountryName),
                Options = countries.Select(x => x.Name)
            }
        ]
    });

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Code, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Country.Name, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Symbol, filterAndPage.TextFilter);
    }

    protected override ValueTask StartEdit(CurrencyVM data, CancellationToken ct) => Edit(() => Task.FromResult(data.Get()), ct);
    protected override ValueTask<bool> SubmitDelete(CurrencyVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);

}

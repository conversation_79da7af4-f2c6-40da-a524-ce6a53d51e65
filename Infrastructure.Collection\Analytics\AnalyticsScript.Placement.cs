﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.AnalyticsTriggers;
using LendQube.Infrastructure.Core.DependencyInjection;

namespace LendQube.Infrastructure.Collection.Analytics;

static partial class AnalyticsScript
{
    private static void PreparePlacementAnalyticsScript()
    {
        var trigger = new AnalyticsTriggerModel
        {
            Schema = CollectionEntityConfig.DefaultSchema,
            Table = nameof(Placement),
            On = [TriggerChange.Update],
            Triggers = []
        };

        PreparePlacementStatusChangeTrigger(trigger);

        AdminDI.AnalyticsTriggers.Add(trigger);
    }

    private static void PreparePlacementStatusChangeTrigger(AnalyticsTriggerModel trigger)
    {
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(PlacementStatusChangeLog),
            Condition = $@"OLD.""{nameof(Placement.Status)}"" <> NEW.""{nameof(Placement.Status)}""",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(PlacementStatusChangeLog.PlacementId), nameof(PlacementStatusChangeLog.OldStatus), nameof(PlacementStatusChangeLog.NewStatus), nameof(PlacementStatusChangeLog.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Column, Value = $@"""{nameof(Placement.Id)}""" },
                    new() { Type = InsertValueType.Value, Value = $@"OLD.""{nameof(Placement.Status)}""" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(Placement.Status)}""" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(Placement.LastModifiedDate)}""" }]
            }
        });
    }
}

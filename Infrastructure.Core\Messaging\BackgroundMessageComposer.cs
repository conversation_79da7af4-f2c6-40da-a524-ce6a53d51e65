﻿using Coravel.Invocable;
using Coravel.Pro;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Telemetry;

namespace LendQube.Infrastructure.Core.Messaging;

public sealed class BackgroundMessageComposer(IUnitofWork uow, ILogManager<BackgroundMessageComposer> logger) : IInvocable, IInvocableWithPayload<BackgroundMessageModel>, IDoNotAutoRegister
{
    public BackgroundMessageModel Payload { get; set; }
    public Task Invoke() => Payload.Composer.BuildAndQueue(uow, logger, default);
}

public sealed class BackgroundMessageModel
{
    internal MessageComposer Composer { get; set; }
}
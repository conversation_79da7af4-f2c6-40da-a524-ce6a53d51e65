﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Customers;

public class CustomerNote : BaseEntityWithIdentityId<CustomerNote>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public long? PlacementId { get; set; }
    public virtual Placement? Placement { get; set; }
    public CustomerNoteType Type { get; set; }
    public CustomerContactType? ContactType { get; set; }
    [Required, ValidString(ValidStringRule.NoScriptTag)]
    public string Note { get; set; }
    public List<CustomerNoteFile> Files { get; set; }

    [NotMapped]
    public List<CustomerNoteTemplate> Templates { get; set; }

    public override void Configure(EntityTypeBuilder<CustomerNote> builder)
    {
        base.Configure(builder);
        builder.OwnsMany(x => x.Files, x => x.ToJson());
    }
}


public enum CustomerNoteType
{
    Comment,
    ContactMade,
    NoContact,
    Vulnerability,
    Promise
}

public enum CustomerContactType
{
    Email,
    PhoneCall,
    Sms,
    WhatsApp
}

public class CustomerNoteFile
{
    public string Name { get; set; }
    public string Url { get; set; }
}
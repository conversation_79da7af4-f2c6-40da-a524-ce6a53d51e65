﻿using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;
using NodaTime;

namespace LendQube.Infrastructure.Core.Database.Repository;

public interface ICurrentUserInfoProvider
{
    public Instant Now { get; }
    public string IpAddress { get; }
    public string UserId { get; }
    public string Name { get; }
    void LoadUser(string userId);
}

internal sealed class CurrentUserInfoProvider : ICurrentUserInfoProvider
{
    private readonly AppDbContext context;
    private readonly IHttpContextAccessor contextAccessor;
    private readonly IDistributedCache cache;

    public Instant Now => SystemClock.Instance.GetCurrentInstant();
    public string IpAddress { get; }
    public string UserId { get; private set; }
    public string Name { get; private set; }

    private readonly string userKey = "USER_NAME_";
    public CurrentUserInfoProvider(AppDbContext context, IHttpContextAccessor contextAccessor, IDistributedCache cache)
    {
        this.context = context;
        this.contextAccessor = contextAccessor;
        this.cache = cache;
        IpAddress = contextAccessor.GetIpAddress();
        LoadUser();
    }

    private string GetSavingUsername(string userId)
    {
        var username = cache.GetString(userKey + userId);
        if (!string.IsNullOrEmpty(username))
            return username;

        username = context.Users.Where(x => x.UserName == userId).Select(x => x.Role + ": " + x.FullName).AsNoTracking().FirstOrDefault();
        if (!string.IsNullOrEmpty(username))
        {
            cache.SetString(userKey + userId, username);
            return username;
        }

        return string.Empty;
    }

    public void LoadUser(string userId)
    {
        if (!string.IsNullOrEmpty(Name) && Name != "anonymous" && userId == UserId)
            return;

        if (Guid.TryParse(userId, out var _))
        {
            UserId = userId;
            Name = GetSavingUsername(userId);
        }
        else
        {
            Name = userId;
        }
    }

    private void LoadUser()
    {
        var username = contextAccessor.GetAuthenticatedUsername();
        if (Guid.TryParse(username, out var _))
        {
            UserId = username;
            Name = GetSavingUsername(UserId);
        }
        else
        {
            Name = username;
        }
    }
}


﻿namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class DecimalHelpers
{
    public static decimal CalculatePercentage(decimal numerator, decimal denominator) => (numerator == 0 || denominator == 0) ? 0 : (numerator / denominator) * 100m;
    public static decimal RoundUp(this decimal amount) => decimal.Round(amount, 2, MidpointRounding.AwayFromZero);
    public static decimal RoundDown(this decimal amount) => decimal.Round(amount, 2, MidpointRounding.ToZero);
    public static decimal RoundEven(this decimal amount) => decimal.Round(amount, 2, MidpointRounding.ToPositiveInfinity);

    public static string GetStringFormattedRate(this double rate) => rate > 1 ? rate.ToString("n2") : rate.ToString();
    public static string GetStringFormattedRate(this decimal rate) => rate > 1 ? rate.ToString("n2") : rate.ToString();
}
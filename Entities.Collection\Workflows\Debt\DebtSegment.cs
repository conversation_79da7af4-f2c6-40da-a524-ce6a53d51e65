﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Workflows.Debt;

public class DebtSegment : BaseEntityWithIdentityId<DebtSegment>
{
    [Required, ValidString(ValidStringRule.OnlyTextOrNumberOrSpecialCharactersWithSpacing)]
    public string Name { get; set; }
    public DebtSegmentPriority Priority { get; set; }
    public TimeOnly? Start { get; set; }
    public TimeOnly? End { get; set; }
    public virtual ICollection<DebtSegmentRules> Rules { get; set; }
    [Required, MinLength(1), TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public List<long> RuleIds { get; set; } = [];
    public override void Configure(EntityTypeBuilder<DebtSegment> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.Name).IsUnique();

        builder.HasMany(x => x.Rules)
            .WithOne()
            .OnDelete(DeleteBehavior.Cascade);
    }
}

public enum DebtSegmentPriority
{
    High,
    Medium,
    Low
}
﻿@page "/messaging/configuration"

@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using <PERSON><PERSON>zen
@using Radzen.Blazor

@inject NavigationManager navigationManager
@inherits GenericCrudVMTable<MessageConfiguration, MessageConfigurationVM>

@attribute [Authorize(Policy = MessagingNavigation.MessageConfigurationIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddLocalModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditLocalModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    [SupplyParameterFromForm]
    protected MessageConfigurationVM AddLocalModel { get; set; }

    [SupplyParameterFromForm]
    protected MessageConfigurationVM EditLocalModel { get; set; }

    private RenderFragment<MessageConfigurationVM> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
            <small class="text_sm_medium text_small">This is used by the system to find the email for dispatch, DO NOT modify for messages sent by the system unless you understand what you are doing</small>
        </div>
        <div class="form-row">
            <label class="form-label" for="Description">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Subject">Subject</label>
            <InputText @bind-Value="context.Subject" class="form-input" aria-required="true" placeholder="Subject" />
            <ValidationMessage For="() => context.Subject" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Channels">Channels</label>
            <RadzenDropDown @bind-Value=@context.ChannelsList Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<MessageChannel>([MessageChannel.Text]))
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)" 
                            Name="Channels" Multiple=true AllowClear=true Placeholder="Select channels" Chips=true class="form-input"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" FilterOperator="StringFilterOperator.StartsWith" AllowFiltering="true" />
            <ValidationMessage For="() => context.ChannelsList" class="text-danger" />
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="Subject">Send From Email</label>
                <InputText @bind-Value="context.SenderEmail" class="form-input" aria-required="true" placeholder="Send From" />
                <ValidationMessage For="() => context.SenderEmail" class="text-danger" />
                <small class="text_sm_medium text_small">Use to change origination email. Leave blank to use default</small>
            </div>
            <div class="form-row">
                <label class="form-label" for="Subject">Sender Name</label>
                <InputText @bind-Value="context.SenderName" class="form-input" aria-required="true" placeholder="Sender Name" />
                <ValidationMessage For="() => context.SenderName" class="text-danger" />
                <small class="text_sm_medium text_small">Use to change sender name, leave blank to use default</small>
            </div>
        </div>
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Check if message exists before attempting to send
                    <InputCheckbox class="check-input" @bind-Value="context.DoNotSendIfExists" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
        <div class="form-row">
            <label class="form-label" for="ExistsCheckWindow">Exists Check Window</label>
            <InputNumber @bind-Value="context.ExistsCheckWindow" class="form-input" aria-required="true" placeholder="Exists Check Window" />
            <ValidationMessage For="() => context.ExistsCheckWindow" class="text-danger" />
            <small class="text_sm_medium text_small">Time passed (in secs) before an already sent message can be sent again</small>
        </div>
    </div>;

    protected override void OnInitialized()
    {
        Title = "Messaging";
        FormBaseTitle = "Message Configuration";
        SubTitle = "Messages Configuration";
        CreatePermission = MessagingNavigation.MessageConfigurationCreatePermission;
        EditPermission = MessagingNavigation.MessageConfigurationEditPermission;
        DeletePermission = MessagingNavigation.MessageConfigurationDeletePermission;
        QuerySelector = MessageConfigurationVM.Mapping;
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            AddLocalModel = new();
            EditLocalModel = new();

            await base.OnInitializedAsync();

            AddRowButton($"{MessagingNavigation.MessageConfigurationCreatePermission},{MessagingNavigation.MessageConfigurationEditPermission}", new RowActionButton("Manage", Icon: "settings", Action: (object row) =>
            {
                var config = row as MessageConfigurationVM;
                navigationManager.NavigateTo($"messaging/configuration/{config.Id}");
                return Task.CompletedTask;
            }));

        }
    }

    protected override void StartAdd() => AddLocalModel = new();

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Description, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Name, filterAndPage.TextFilter);
    }


    protected override ValueTask SubmitAdd() => BaseSaveAddWithRedirect(() =>
    {
        (var isValid, CustomMessage) = AddLocalModel.Validate();
        if (!isValid)
            return Task.FromResult(false);

        AddModel = AddLocalModel.Get();
        if (AddModel.DoNotSendIfExists && AddModel.ExistsCheckWindow == 0)
        {
            CustomMessage = "Check window cannot be 0";
            return Task.FromResult(false);
        }
        return Service.CrudService.New(AddModel, Cancel);
    },
    () =>
    {
        navigationManager.NavigateTo($"messaging/configuration/{AddModel.Id}");
    });

    protected override ValueTask StartEdit(MessageConfigurationVM editData, CancellationToken ct) => BaseEdit(() =>
    {
        EditLocalModel = editData;
        EditLocalModel.ChannelsList = editData.Channels.FlagsToList<MessageChannel>();

        return Task.CompletedTask;
    }, ct);

    protected override ValueTask SubmitEdit() => BaseSaveEdit(async () =>
    {
        (var isValid, CustomMessage) = EditLocalModel.Validate();
        if (!isValid)
            return false;

        var data = EditLocalModel.Get();

        if(data.DoNotSendIfExists && data.ExistsCheckWindow == 0)
        {
            CustomMessage = "Check window cannot be 0";
            return false;
        }

        var result = await Service.CrudService.UpdateWithFilter(x => x.Id == data.Id, x => x
            .SetProperty(y => y.Name, data.Name)
            .SetProperty(y => y.Description, data.Description)
            .SetProperty(y => y.Subject, data.Subject)
            .SetProperty(y => y.Channels, data.Channels)
            .SetProperty(y => y.DoNotSendIfExists, data.DoNotSendIfExists)
            .SetProperty(y => y.ExistsCheckWindow, data.ExistsCheckWindow)
            .SetProperty(y => y.SenderEmail, data.SenderEmail)
            .SetProperty(y => y.SenderName, data.SenderName)
            ,Cancel);

        if (result)
            CustomMessage = "Remember to configure templates for any new channels";

        return result;

    },
    () =>
    {
        EditLocalModel = new();
        return table.Refresh();

    });


    protected override ValueTask<bool> SubmitDelete(MessageConfigurationVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}

﻿using System.Diagnostics;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class IEnumerableExtensions
{
    public static IEnumerable<Tuple<T, T>> PairUp<T>(this IEnumerable<T> source)
    {
        using var iterator = source.GetEnumerator();
        while (iterator.MoveNext())
        {
            var first = iterator.Current;
            var second = iterator.MoveNext() ? iterator.Current : default;
            yield return Tuple.Create(first, second);
        }
    }
}

﻿using LendQube.Entities.Core.Logs;
using NodaTime;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;

public sealed class UserAccessLogVM
{
    public static readonly Expression<Func<UserAccessLog, UserAccessLogVM>> Mapping = data => new UserAccessLogVM
    {
        Status = data.Status,
        GrantType = data.GrantType,
        Ip = data.CreatedByIp,
        Application = data.Application,
        CreatedByUser = data.CreatedByUser,
        CreatedDate = data.CreatedDate,
    };

    public AccessStatus Status { get; set; }
    public GrantType GrantType { get; set; }
    public string Ip { get; set; }
    public string Application { get; set; }
    public Instant? CreatedDate { get; set; }
    public string CreatedByUser { get; set; }
}
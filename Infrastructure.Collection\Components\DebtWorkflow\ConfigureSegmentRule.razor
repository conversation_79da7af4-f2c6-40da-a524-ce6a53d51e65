﻿@page "/debtworkflow/segmentrule"
@using LendQube.Entities.Collection.Setup
@using LendQube.Entities.Collection.Workflows.Debt
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using Radzen.Blazor

@inherits GenericCrudTable<DebtSegmentRule>

@attribute [Authorize(Policy = DebtWorkflowNavigation.DebtWorkflowSegmentRuleIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<DebtSegmentRule> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Type">Type</label>
            <RadzenDropDown @bind-Value=@context.Type Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<SegmentType>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Type" AllowClear=true Placeholder="Select type" class="form-input" />
            <ValidationMessage For="() => context.Type" class="text-danger" />
        </div>

        @if(context.Type == SegmentType.Status)
        {
            <div class="form-row">
                <label class="form-label" for="StatusType">Status Type</label>
                <RadzenDropDown @bind-Value=@context.StatusType Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<SegmentStatusType>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="StatusType" AllowClear=true Placeholder="Select status type" class="form-input" />
                <ValidationMessage For="() => context.StatusType" class="text-danger" />
            </div>
        }

        @if(typeHasDays.Contains(context.Type) || context.StatusType == SegmentStatusType.PaymentFailed)
        {
            <div class="grid-col-2">
                <div class="form-row">
                <label class="form-label" for="MinTypeNumber">Min Days @((context.StatusType == SegmentStatusType.PaymentFailed ? context.StatusType.GetDisplayName() : context.Type.GetDisplayName()))</label>
                    <InputNumber @bind-Value="context.MinTypeNumber" />
                    <ValidationMessage For="() => context.MinTypeNumber" class="text-danger" />
                </div>
                <div class="form-row">
                <label class="form-label" for="MaxTypeNumber">Max Days @((context.StatusType == SegmentStatusType.PaymentFailed ? context.StatusType.GetDisplayName() : context.Type.GetDisplayName()))</label>
                    <InputNumber @bind-Value="context.MaxTypeNumber" />
                    <ValidationMessage For="() => context.MaxTypeNumber" class="text-danger" />
                </div>
            </div>
        }

        @if(context.StatusType == SegmentStatusType.NoPayment || context.StatusType == SegmentStatusType.PaymentWithNoSchedule)
        {
            <div class="grid-col-2">
                <div class="form-row">
                    <label class="form-label" for="MinTypeNumber">Min Months With No Payment</label>
                    <InputNumber @bind-Value="context.MinTypeNumber" />
                    <ValidationMessage For="() => context.MinTypeNumber" class="text-danger" />
                </div>
                <div class="form-row">
                    <label class="form-label" for="MaxTypeNumber">Max Months With No Payment</label>
                    <InputNumber @bind-Value="context.MaxTypeNumber" />
                    <ValidationMessage For="() => context.MaxTypeNumber" class="text-danger" />
                </div>
            </div>
        }

        @if(context.Type == SegmentType.Flag)
        {
            <div class="form-row">
                <label class="form-label" for="FlagId">Flag</label>
                <RadzenDropDown @bind-Value=@context.FlagId Data=@flags
                            TextProperty="@nameof(CustomerFlagTemplate.Flag)" ValueProperty="@nameof(CustomerFlagTemplate.Id)"
                            Name="FlagId" AllowClear=true Placeholder="Select flag" class="form-input" />
            <ValidationMessage For="() => context.FlagId" class="text-danger" />
            </div>
        }


        <div class="form-row">
            <label class="form-label" for="Instruction">Instruction</label>
            <InputTextArea @bind-Value="context.Instruction"/>
            <ValidationMessage For="() => context.Instruction" class="text-danger" />
            <small class="text_sm_medium text_small">Provide guidance on what the agent is supposed to do for this rule. This will be displayed to the agent</small>
        </div>
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Is Disabled
                    <InputCheckbox class="check-input" @bind-Value="context.Disabled" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </div>
    ;

    private List<CustomerFlagTemplate> flags = [];
    private List<SegmentType> typeHasDays = [SegmentType.DueIn, SegmentType.PastDue, SegmentType.PTPDueIn, SegmentType.PTPPastDue];

    protected override void OnInitialized()
    {
        Title = "Debt Workflow";
        SubTitle = "Setup Debt Segment Rules";
        FormBaseTitle = "Debt Segment Rule";
        CreatePermission = DebtWorkflowNavigation.DebtWorkflowSegmentRuleCreatePermission;
        EditPermission = DebtWorkflowNavigation.DebtWorkflowSegmentRuleEditPermission;
        DeletePermission = DebtWorkflowNavigation.DebtWorkflowSegmentRuleDeletePermission;
        flags = Service.CrudService.Db.Many(Query<CustomerFlagTemplate>.All());
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter);
    }
}


﻿using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using MongoDB.Bson;

namespace LendQube.Entities.Collection.Customers;

public class CustomerContactDetail : BaseEntityWithIdentityId<CustomerContactDetail>
{
    [DbGuid, RemoveColumn]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public string Email { get; set; }
    public PhoneNumber PhoneNumber { get; set; }
    public bool Preferred { get; set; }

    [NotMapped]
    public string PhoneCode { get; set; }

    [NotMapped]
    public string Number { get; set; }

    public override void Configure(EntityTypeBuilder<CustomerContactDetail> builder)
    {
        base.Configure(builder);
        builder.OwnsOne(x => x.PhoneNumber, x => x.<PERSON>());
    }
}

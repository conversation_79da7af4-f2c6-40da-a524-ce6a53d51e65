﻿using System.Collections.Immutable;
using System.Text;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Core.Database.NotificationTriggers;

internal static class NotifyTriggerDbScripts
{
	internal static string Schema = "Notify";
    internal static string ChangeEventName = "ondatachange";
    internal static string With_OldDataChange_CallbackName = "Notify_With_OldDataChange";
    internal static string Without_OldDataChange_CallbackName = "Notify_Without_OldDataChange";
    internal static string With_OnlyChangedId_CallbackName = "Notify_With_OnlyChangedId_CallbackName";
	internal static string CreateNotify_WithOldData_ChangeCallback => $@"
        CREATE SCHEMA IF NOT EXISTS {Schema};
        CREATE OR REPLACE FUNCTION {Schema}.""{With_OldDataChange_CallbackName}""()
        RETURNS trigger
        LANGUAGE 'plpgsql'
        AS $BODY$ 
        DECLARE
            oldData JSON;
            data JSON;
            notification JSON;

        BEGIN
            IF(TG_OP = 'DELETE') THEN
            data = row_to_json(OLD);
            oldData = row_to_json(OLD);
        ELSE
            data = row_to_json(NEW);
            oldData = row_to_json(OLD);
        END IF;
            notification = json_build_object(
                'table', TG_TABLE_NAME,
                'action', TG_OP,
                'oldData', oldData,
                'newData', data);  
              
            PERFORM pg_notify('{ChangeEventName}', notification::TEXT);
        RETURN NEW;
        END
        $BODY$;";

	internal static string CreateNotify_WithoutOldData_ChangeCallback => $@"
        CREATE SCHEMA IF NOT EXISTS {Schema};
        CREATE OR REPLACE FUNCTION {Schema}.""{Without_OldDataChange_CallbackName}""()
        RETURNS trigger
        LANGUAGE 'plpgsql'
        AS $BODY$ 
        DECLARE
            data JSON;
            notification JSON;

        BEGIN
            IF(TG_OP = 'DELETE') THEN
            data = row_to_json(OLD);
        ELSE
            data = row_to_json(NEW);
        END IF;
            notification = json_build_object(
                'table', TG_TABLE_NAME,
                'action', TG_OP,
                'newData', data);  
              
            PERFORM pg_notify('{ChangeEventName}', notification::TEXT);
        RETURN NEW;
        END
        $BODY$;";

	internal static string CreateNotify_With_OnlyChangedId_CallbackNameCallback => $@"
        CREATE SCHEMA IF NOT EXISTS {Schema};
        CREATE OR REPLACE FUNCTION {Schema}.""{With_OnlyChangedId_CallbackName}""()
        RETURNS trigger
        LANGUAGE 'plpgsql'
        AS $BODY$ 
        DECLARE
            data text;
            notification JSON;

        BEGIN
            IF(TG_OP = 'DELETE') THEN
            data = Old.""Id"";
        ELSE
            data = New.""Id"";
        END IF;
            notification = json_build_object(
                'table', TG_TABLE_NAME,
                'action', TG_OP,
                'id', data);  
              
            PERFORM pg_notify('{ChangeEventName}', notification::TEXT);
        RETURN NEW;
        END
        $BODY$;";

	internal static string CreateTriggers(IReadOnlyList<NotifyTrigger> triggers)
    {
        var scripts = new StringBuilder();
        foreach (var item in triggers)
        {
            if(item.Type.Contains(TriggerType.Before))
                scripts.Append(CreateBeforeTrigger(item));
            if(item.Type.Contains(TriggerType.After))
				scripts.Append(CreateAfterTrigger(item));
		}

        return scripts.ToString();
    }

    internal static string CreateTrigger(NotifyTrigger trigger)
	{
		var scripts = new StringBuilder();
		if (trigger.Type.Contains(TriggerType.Before))
			scripts.Append(CreateBeforeTrigger(trigger));
		if (trigger.Type.Contains(TriggerType.After))
			scripts.Append(CreateAfterTrigger(trigger));
		return scripts.ToString();
	}

	private static string CreateAfterTrigger(NotifyTrigger trigger) =>
        $@"DROP TRIGGER IF EXISTS ""{trigger.Name}"" ON ""{trigger.Schema}"".""{trigger.Table}""; CREATE TRIGGER ""{trigger.Name}""
                    AFTER {trigger.NotifyType} 
                    ON ""{trigger.Schema}"".""{trigger.Table}""
                    FOR EACH ROW
                    {TriggerCondition(trigger)}
                EXECUTE PROCEDURE {Schema}.""{(trigger.ReturnOnlyId ? With_OnlyChangedId_CallbackName : trigger.TrackOldData ? With_OldDataChange_CallbackName : Without_OldDataChange_CallbackName)}""();";

    private static string CreateBeforeTrigger(NotifyTrigger trigger) =>
        $@"DROP TRIGGER IF EXISTS ""{trigger.Name}"" ON ""{trigger.Schema}"".""{trigger.Table}""; CREATE TRIGGER ""{trigger.Name}""
                    BEFORE {trigger.NotifyType} 
                    ON ""{trigger.Schema}"".""{trigger.Table}""
                    FOR EACH ROW
                    {TriggerCondition(trigger)}
                EXECUTE PROCEDURE {Schema}.""{(trigger.ReturnOnlyId ? With_OnlyChangedId_CallbackName : trigger.TrackOldData ? With_OldDataChange_CallbackName : Without_OldDataChange_CallbackName)}""();";

    private static string TriggerCondition(NotifyTrigger trigger) => string.IsNullOrEmpty(trigger.Condition) ? "" : $"WHEN ({trigger.Condition})";

    internal static string RemoveTriggers(IReadOnlyList<NotifyTrigger> triggers)
    {
		var scripts = new StringBuilder();
		foreach (var item in triggers)
		{
            scripts.Append(RemoveTrigger(item));
		}
		return scripts.ToString();
	}

	internal static string RemoveTrigger(NotifyTrigger trigger)
		=> $@"DROP TRIGGER IF EXISTS ""{trigger.Name}"" ON ""{trigger.Schema}"".""{trigger.Table}""";
}

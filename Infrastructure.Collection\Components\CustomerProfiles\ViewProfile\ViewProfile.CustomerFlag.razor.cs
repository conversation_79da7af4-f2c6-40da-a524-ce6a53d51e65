﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Setup;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{

    private DataTable<CustomerFlagVM> flagsTable;
    private ColumnList flagsTableDefinition;
    private string AddFlagModal => "AddFlagModal";

    private CustomerFlag AddFlagModel { get; set; } = new();

    private List<CustomerFlagTemplate> flagTemplates = [];

    private void SetupFlag()
    {
        flagsTableDefinition = CrudService.GetTableDefinition<CustomerFlagVM>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteFlagPermission)
        });

        flagsTableDefinition.TopActionButtons.Add(new TopActionButton("Add Flag", ModalName: AddFlagModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddFlagPermission)));

        flagsTable.SetTableDefinition(flagsTableDefinition);
    }

    private async ValueTask<TypedBasePageList<CustomerFlagVM>> LoadFlags(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        if (flagTemplates.IsNullOrEmpty())
            flagTemplates = await uow.Db.ManyAsync(Query<CustomerFlagTemplate>.All(), Cancel);

        var spec = new BaseSpecification<CustomerFlag>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Flag.Flag, filterAndPage.TextFilter));
        }

        return await CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerFlagVM.Mapping, ct);
    }

    private ValueTask SubmitNewFlag() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddFlagPermission, AddFlagModal, async () =>
    {
        AddFlagModel.ProfileId = Data.Id;

        uow.Db.Insert(AddFlagModel);
        await uow.SaveAsync(Cancel);
        return true;
    }, () =>
    {
        AddFlagModel = new();
        StateHasChanged();
        return flagsTable.Refresh();
    });

    private ValueTask<bool> DeleteFlag(CustomerFlagVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteFlagPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerFlag>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

}

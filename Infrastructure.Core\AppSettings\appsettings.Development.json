{"DetailedErrors": true, "Logging": {"LogLevel": {"Default": "Debug", "System": "Debug", "Microsoft": "Debug", "Microsoft.AspNetCore": "Debug"}}, "ConnectionStrings": {"BackgroundTaskDbConnectionString": "server=ethicalendqubedevpgdb.postgres.database.azure.com;username=ethicadev;password=****************;database=lqdev;Port=5432;SslMode=Require;", "ConnectionString": "server=ethicalendqubedevpgdb.postgres.database.azure.com;username=ethicadev;password=****************;database=lqdev;Port=5432;SslMode=Require;"}, "Providers": {"Url": {"WebAdmin": " https://devadmin.ethicaresolve.co.uk/", "WebClient": "https://apidev.ethicaresolve.co.uk/", "WebHook": "https://kb8devwebhook.azurewebsites.net/", "WebBackground": "https://devbackground.ethicaresolve.co.uk/"}}}
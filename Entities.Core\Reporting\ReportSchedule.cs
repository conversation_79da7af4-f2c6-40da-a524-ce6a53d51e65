﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Messaging;
using NodaTime;

namespace LendQube.Entities.Core.Reporting;

public class ReportSchedule : BaseEntityWithIdentityId<ReportSchedule>, IEntityHasNotifyTrigger
{
    [Required, StringLength(EntityConstants.DEFAULT_DESCRIPTION_FIELD_LENGTH)]
    public string Description { get; set; }
    [Required]
    public string ReportTypeName { get; set; }
    [Required, MinLength(1)]
    public List<string> ReportNames { get; set; }
    public ReportType FileType { get; set; }
    public ReportFrequency Frequency { get; set; }
    public int FrequencyNumber { get; set; }
    public ScheduleDay Days { get; set; }
    public ReportScheduleAction Action { get; set; }
    public Instant? LastRunDate { get; set; }
    public Instant? StartDate { get; set; }
    public Instant? EndDate { get; set; }
    public int RunCount { get; set; }
    public bool Disabled { get; set; }
    public bool Starting { get; set; }
    public string TimeZone { get; set; }
    public long MessagingGroupId { get; set; }
    public virtual MessagingGroup MessagingGroup { get; set; }

    public long ConfigId { get; set; }
    public virtual MessageConfiguration Config { get; set; }

    public string Schema => CoreEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => $@"NEW.""{nameof(Disabled)}"" IS FALSE AND NEW.""{nameof(Starting)}"" IS TRUE";
}

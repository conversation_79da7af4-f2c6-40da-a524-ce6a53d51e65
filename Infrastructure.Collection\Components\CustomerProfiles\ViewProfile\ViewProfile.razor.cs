﻿using Coravel.Queuing.Interfaces;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using System.Threading;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    [Inject] NavigationManager NavigationManager { get; set; }
    [Inject] GeneralGenericCrudVMService CrudService { get; set; }
    [Inject] HttpClient HttpClient { get; set; }
    [Inject] DefaultAppConfig Config { get; set; }
    [Inject] IQueue Queue { get; set; }


    [Parameter]
    public string ProfileId { get; set; }

    private string Title { get; set; } = "View Customer Profile";

    private IUnitofWork uow;

    private CustomerProfileVM Data { get; set; } = new();

    private List<CustomerDropDownPlacementVM> placementsDropDown = [];
    private string timeZone;
    
    // For Income & Expenditure dropdowns
    private string SelectedAddressId { get; set; }
    private string SelectedPhoneType { get; set; } = "mobile";
    
    // Semaphore to prevent concurrent DataTable loads
    private readonly SemaphoreSlim _dataTableLoadSemaphore = new SemaphoreSlim(1, 1);
    protected override void OnInitialized()
    {
        uow = CrudService.Uow;
        base.OnInitialized();
    }

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await Load();
            await JSRuntime.RunFeather(Cancel);
            timeZone = await JSRuntime.GetBrowserTimezone(Cancel);
            StateHasChanged();
            
            // Initialize customer profile UI enhancements
            try
            {
                await JSRuntime.InvokeVoidAsync("CustomerProfileEnhancements.initialize");
            }
            catch (Exception ex)
            {
                // Log but don't fail if enhancements can't be loaded
                Console.WriteLine($"Failed to initialize customer profile enhancements: {ex.Message}");
            }
        }
        
        // Try to set address table definition after each render
        TrySetAddressTableDefinition();
    }

    private async Task LoadProfile()
    {
        Data = await uow.Db.OneSelectAsync(Query<CustomerProfile, CustomerProfileVM>.Where(x => x.Id == ProfileId).Select(CustomerProfileVM.Mapping), Cancel);
        totalPlacements = Data.TotalPlacements;
    }

    private async Task Load()
    {
        await LoadProfile();
        placementsDropDown = await uow.Db.ManySelectAsync(Query<Placement, CustomerDropDownPlacementVM>.Where(x => x.ProfileId == ProfileId).Select(CustomerDropDownPlacementVM.Mapping), Cancel);

        SetupPlacements();
        SetupContact();
        SetupMessageTable();
        SetupFlag();
        SetupStatusChange();
        SetupHold();

        SetupNotesConfig();
        SetupPtpConfig();
        SetupAddressConfig();

        await SetupDiscountConfig();
        await LoadTask();
    }
    
    // Helper method to safely load DataTable components one at a time
    private async Task SafeLoadDataTable(Func<Task> loadAction)
    {
        await _dataTableLoadSemaphore.WaitAsync();
        try
        {
            await loadAction();
        }
        finally
        {
            _dataTableLoadSemaphore.Release();
        }
    }
    
    // Helper method to format address for display in dropdowns
    private string FormatAddressForDisplay(CustomerAddress address)
    {
        if (address == null) return string.Empty;
        
        var parts = new List<string>();
        if (!string.IsNullOrWhiteSpace(address.AddressLine1)) parts.Add(address.AddressLine1.Trim());
        if (!string.IsNullOrWhiteSpace(address.AddressLine2)) parts.Add(address.AddressLine2.Trim());
        if (!string.IsNullOrWhiteSpace(address.Locality)) parts.Add(address.Locality.Trim());
        if (!string.IsNullOrWhiteSpace(address.City)) parts.Add(address.City.Trim());
        if (!string.IsNullOrWhiteSpace(address.PostCode)) parts.Add(address.PostCode.Trim());
        if (!string.IsNullOrWhiteSpace(address.Country)) parts.Add(address.Country.Trim());
        
        return string.Join(", ", parts);
    }
    
    // Dispose pattern to clean up semaphore
    public void Dispose() 
    {
        _dataTableLoadSemaphore?.Dispose();
    }
}

// Customer Profile UI Enhancements
(function () {
    'use strict'; 

    // Initialize enhancements when DOM is ready
    document.addEventListener('DOMContentLoaded', function () {
        initializeCustomerInfoEnhancements();
    });
 
    // Also initialize when <PERSON><PERSON><PERSON> navigates
    if (window.Blazor) {
        window.addEventListener('blazor:afterStarted', function () { 
            initializeCustomerInfoEnhancements(); 
        });
    }

    function initializeCustomerInfoEnhancements() {
        // Add data attributes for styling
        addDataAttributes();

        // Add click-to-copy functionality for email and phone numbers
        addClickToCopy();

        // Add value animation on updates
        observeValueChanges();

        // Add responsive behavior
        handleResponsiveLayout();

        // Add keyboard navigation
        addKeyboardNavigation();
    }

    function addDataAttributes() {
        document.querySelectorAll('.info-col .detail-item').forEach(item => {
            const label = item.querySelector('.label');
            if (!label) return;

            const labelText = label.textContent.toLowerCase();

            // Financial fields
            if (labelText.includes('balance') || labelText.includes('paid') || labelText.includes('discount')) {
                item.setAttribute('data-field-type', 'financial');
            }

            // Balance Outstanding special case
            if (labelText.includes('balance outstanding')) {
                item.setAttribute('data-field-type', 'balance-outstanding');
            }

            // Contact fields
            if (labelText.includes('email') || labelText.includes('mobile') || labelText.includes('phone')) {
                item.setAttribute('data-field-type', 'contact');
            }

            // Date fields
            if (labelText.includes('date')) {
                item.setAttribute('data-field-type', 'date');
            }

            // Other specific fields
            if (labelText.includes('gender')) {
                item.setAttribute('data-field-type', 'gender');
            }
            if (labelText.includes('total placements')) {
                item.setAttribute('data-field-type', 'placements');
            }
            if (labelText.includes('payment frequency')) {
                item.setAttribute('data-field-type', 'frequency');
            }
            if (labelText.includes('reschedule count')) {
                item.setAttribute('data-field-type', 'reschedule');
            }
        });
    }

    function addClickToCopy() {
        // Find all detail items and check their labels
        document.querySelectorAll('.detail-item').forEach(item => {
            const label = item.querySelector('.label');
            const value = item.querySelector('.value');

            if (!label || !value) return;

            const labelText = label.textContent.toLowerCase();
            const copyableLabels = ['email', 'mobile', 'phone', 'account'];

            // Check if this is a copyable field
            const isCopyable = copyableLabels.some(copyLabel => labelText.includes(copyLabel));

            if (isCopyable && !value.dataset.copyEnabled) {
                value.dataset.copyEnabled = 'true';
                value.style.cursor = 'pointer';
                value.title = 'Click to copy';

                value.addEventListener('click', function () {
                    const text = this.textContent.trim();
                    if (text) {
                        copyToClipboard(text, this);
                    }
                });
            }
        });
    }

    function copyToClipboard(text, element) {
        navigator.clipboard.writeText(text).then(() => {
            // Show success feedback
            const originalText = element.textContent;
            element.textContent = 'Copied!';
            element.style.color = '#27ae60';

            setTimeout(() => {
                element.textContent = originalText;
                element.style.color = '';
            }, 1500);
        }).catch(err => {
            console.error('Failed to copy:', err);
        });
    }

    function observeValueChanges() {
        // Create observer for value changes
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList' || mutation.type === 'characterData') {
                    const valueElement = mutation.target.closest('.value');
                    if (valueElement) {
                        valueElement.classList.add('updated');
                        setTimeout(() => {
                            valueElement.classList.remove('updated');
                        }, 1000);
                    }
                }
            });
        });

        // Observe all value elements
        document.querySelectorAll('.info-col .detail-item .value').forEach(element => {
            observer.observe(element, {
                childList: true,
                characterData: true,
                subtree: true
            });
        });
    }

    function handleResponsiveLayout() {
        const infoCol = document.querySelector('.info-col');
        if (!infoCol) return;

        function checkWidth() {
            const width = window.innerWidth;
            if (width < 768) {
                infoCol.classList.add('mobile-view');
            } else {
                infoCol.classList.remove('mobile-view');
            }
        }

        window.addEventListener('resize', debounce(checkWidth, 250));
        checkWidth();
    }

    function addKeyboardNavigation() {
        const detailItems = document.querySelectorAll('.info-col .detail-item');
        let currentIndex = -1;

        detailItems.forEach((item, index) => {
            item.setAttribute('tabindex', '0');
            item.addEventListener('keydown', (e) => {
                switch (e.key) {
                    case 'ArrowDown':
                        e.preventDefault();
                        currentIndex = Math.min(index + 1, detailItems.length - 1);
                        detailItems[currentIndex].focus();
                        break;
                    case 'ArrowUp':
                        e.preventDefault();
                        currentIndex = Math.max(index - 1, 0);
                        detailItems[currentIndex].focus();
                        break;
                    case 'Enter':
                    case ' ':
                        const value = item.querySelector('.value');
                        if (value && value.dataset.copyEnabled) {
                            value.click();
                        }
                        break;
                }
            });
        });
    }

    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait); 
        };
    }

    // Export functions for Blazor interop if needed
    window.CustomerProfileEnhancements = {
        initialize: initializeCustomerInfoEnhancements,
        refresh: function () {
            addDataAttributes();
            addClickToCopy();
            observeValueChanges();
        }
    };
})();

// Add smooth scroll behavior for the info column
document.addEventListener('DOMContentLoaded', function () {
    const infoCol = document.querySelector('.info-col');
    if (infoCol) {
        infoCol.style.scrollBehavior = 'smooth';
    }
});

// Add number formatting animation
window.animateNumber = function (element, start, end, duration) {
    const range = end - start;
    const startTime = performance.now();

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = start + (range * easeOutCubic(progress));
        element.textContent = formatCurrency(current);

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    function easeOutCubic(t) {
        return 1 - Math.pow(1 - t, 3);
    }

    function formatCurrency(value) {
        return new Intl.NumberFormat('en-GB', {
            style: 'currency',
            currency: 'GBP'
        }).format(value);
    }

    requestAnimationFrame(updateNumber);
};
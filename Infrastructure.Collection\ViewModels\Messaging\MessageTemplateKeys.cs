﻿namespace LendQube.Infrastructure.Collection.ViewModels.Messaging;

public enum MessageTemplateKeys
{
    CompanyName,
    AccountNumber,
    Code,
    Amount,
    Balance,
    Currency,
    Frequency,
    Duration,
    DiscountAmount,
    DueDate,
    FortyPercentOfBalance,
    PaymentLink,
    StartDate,
    EndDate
}


public enum MessageConfigNames
{
    NewPlacement,
    CustomerAuthentication,
    ResetPassword,
    LoginTokenSms,
    LoginTokenEmail,
    PaymentReceived,
    PaymentReceivedAndRescheduled,
    PaymentSettled,
    DownloadAppReminder,
    NewSchedule,
    AmendSchedule,
    FiveDaysToDueReminder,
    OneDayToDueReminder,
    DueDayReminder,
    OneDayPastDueReminder,
    ThreeDaysPastDueReminder,
    WelcomeLetterAndApology,
    UploadedPayments,
    Discount,
    PTPSetup,
    PTPReminder,
    PTPDueInOneWeek,
    PTPDueInOneDay,
    AddPaymentMethod,
    MakePayment
}
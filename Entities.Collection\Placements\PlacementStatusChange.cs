﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Placements;

public class PlacementStatusChange : BaseEntityWithIdentityId<PlacementStatusChange>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    [Required]
    public long PlacementId { get; set; }
    public virtual Placement Placement { get; set; }
    public PlacementStatus FromStatus { get; set; }
    public PlacementStatus ToStatus { get; set; }
    public long? ReasonId { get; set; }
    public virtual PlacementStatusChangeReasonConfig? Reason { get; set; }
    public string Comment { get; set; }
}

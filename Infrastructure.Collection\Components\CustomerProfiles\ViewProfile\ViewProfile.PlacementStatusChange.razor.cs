﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Infrastructure.Collection.Analytics;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<PlacementStatusChangeVM> statusChangeTable;
    private ColumnList statusChangeTableDefinition;
    private string AddStatusChangeModal => "AddStatusChangeModal";

    private PlacementStatusChange AddStatusChangeModel { get; set; } = new();

    private List<PlacementStatusChangeReasonConfig> statusChangeReasons = [];

    private void SetupStatusChange()
    {
        statusChangeTableDefinition = CrudService.GetTableDefinition<PlacementStatusChangeVM>(new()
        {
            ShowUserInfo = true
        });

        statusChangeTableDefinition.TopActionButtons.Add(new TopActionButton("Change Status", ModalName: AddStatusChangeModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileChangePlacementStatusPermission)));

        statusChangeTable.SetTableDefinition(statusChangeTableDefinition);
    }

    private async ValueTask<TypedBasePageList<PlacementStatusChangeVM>> LoadStatusChange(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        if (statusChangeReasons.IsNullOrEmpty())
            statusChangeReasons = await uow.Db.ManyAsync(Query<PlacementStatusChangeReasonConfig>.All(), Cancel);

        var spec = new BaseSpecification<PlacementStatusChange>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Reason.Description, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Reason.Code, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Comment, filterAndPage.TextFilter));
        }

        return await CrudService.GetTypeBasedPagedData(spec, filterAndPage, PlacementStatusChangeVM.Mapping, ct);
    }

    private ValueTask SubmitNewStatusChange() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileChangePlacementStatusPermission, AddStatusChangeModal, async () =>
    {
        AddStatusChangeModel.ProfileId = Data.Id;
        var placement = placementsDropDown.FirstOrDefault(x => x.Id == AddStatusChangeModel.PlacementId);
        AddStatusChangeModel.FromStatus = placement.Status;

        uow.Db.Insert(AddStatusChangeModel);
        await uow.SaveAsync(Cancel);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<Placement>(x => x.Id == AddStatusChangeModel.PlacementId, x => x.SetProperty(y => y.Status, AddStatusChangeModel.ToStatus), Cancel);
        placement.Status = AddStatusChangeModel.ToStatus;

        var totalActivePlacementsForCustomer = await uow.Db.CountAsync<Placement>(x => x.ProfileId == Data.Id && x.Status != PlacementStatus.Closed && x.Status != PlacementStatus.Sold && x.Status != PlacementStatus.Settled, Cancel);
        var firstSchedule = await uow.Db.OneAsync(Query<CustomerSchedule>.Where(x => x.ProfileId == Data.Id && x.PaymentStatus == SchedulePaymentStatus.NotPaid).OrderBy(x => x.OrderBy(y => y.DueDate)), Cancel);
        if (AddStatusChangeModel.FromStatus == PlacementStatus.Closed)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == Data.Id, x => x.SetProperty(y => y.BalanceTotal, y => y.BalanceTotal + placement.BalanceRemaining), Cancel);
            _ = await ManageAnalyticsService.UpdateTotalClosedPlacements(uow, -1, -placement.BalanceRemaining, Cancel);
        }
        else if (AddStatusChangeModel.ToStatus == PlacementStatus.Closed)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == Data.Id, x => x.SetProperty(y => y.SettlementAmount, y => y.SettlementAmount + placement.BalanceRemaining), Cancel);
            _ = await ManageAnalyticsService.UpdateTotalClosedPlacements(uow, 1, placement.BalanceRemaining, Cancel);
            await HandleScheduleAsync(firstSchedule, totalActivePlacementsForCustomer, Cancel);

        }
        else if (AddStatusChangeModel.ToStatus == PlacementStatus.Sold && AddStatusChangeModel.FromStatus != PlacementStatus.Closed)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == Data.Id, x => x.SetProperty(y => y.SettlementAmount, y => y.SettlementAmount + placement.BalanceRemaining), Cancel);
            await HandleScheduleAsync(firstSchedule, totalActivePlacementsForCustomer, Cancel);
        }
        else if (AddStatusChangeModel.ToStatus == PlacementStatus.Settled)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == Data.Id, x => x.SetProperty(y => y.SettlementAmount, y => y.SettlementAmount + placement.BalanceRemaining), Cancel);
            await HandleScheduleAsync(firstSchedule, totalActivePlacementsForCustomer, Cancel);
            _ = await ManageAnalyticsService.UpdateTotalSettledPlacements(uow, 1, Cancel);
        }
        return true;
    }, () =>
    {
        AddStatusChangeModel = new();
        StateHasChanged();
        return statusChangeTable.Refresh();
    });

    private async ValueTask HandleScheduleAsync(CustomerSchedule? firstSchedule, int totalActivePlacementsForCustomer, CancellationToken cancel)
    {
        if (firstSchedule == null)
            return;

        if (totalActivePlacementsForCustomer == 0)
        {
            await uow.Db.DeleteAndSaveWithFilterAsync<CustomerSchedule>(x => x.ProfileId == Data.Id && x.PaymentStatus == SchedulePaymentStatus.NotPaid, cancel);
        }
        else
        {
            var vm = new RequestScheduleVM
            {
                StartDate = DateTime.UtcNow.Date,
                Amount = firstSchedule.Amount,
                Frequency = firstSchedule.Profile?.PaymentFrequency ?? SchedulePaymentFrequency.Monthly
            };

            await ScheduleService.Reschedule(Data.Id, vm, true, cancel);
        }
    }

}


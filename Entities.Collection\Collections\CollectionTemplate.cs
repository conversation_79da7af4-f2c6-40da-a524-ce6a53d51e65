﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Collections;

public class CollectionTemplate : BaseEntityWithIdentityId<CollectionTemplate>
{
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Name { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string FileUrl { get; set; }

    [NotMapped, RemoveColumn]
    public string File { get; set; }
    [NotMapped, RemoveColumn]
    public string FileName { get; set; }

}

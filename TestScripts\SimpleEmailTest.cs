using System;
using System.Net.Mail;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace LendQube.TestScripts;

/// <summary>
/// Simple email test that directly tests SMTP functionality
/// This bypasses internal LendQube classes to isolate email issues
/// </summary>
public class SimpleEmailTest
{
    private readonly ILogger<SimpleEmailTest> _logger;
    private readonly IConfiguration _configuration;

    public SimpleEmailTest(ILogger<SimpleEmailTest> logger, IConfiguration configuration)
    {
        _logger = logger;
        _configuration = configuration;
    }

    public static async Task Main(string[] args)
    {
        var host = CreateHostBuilder(args).Build();
        var tester = host.Services.GetRequiredService<SimpleEmailTest>();
        
        Console.WriteLine("=== Simple Email Test ===");
        Console.WriteLine("Testing SMTP email functionality directly");
        Console.WriteLine();

        await tester.RunTests();
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory())
                      .AddJsonFile("appsettings.json", optional: false)
                      .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true)
                      .AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Debug);
                });

                services.AddTransient<SimpleEmailTest>();
            });

    public async Task RunTests()
    {
        // Get SMTP configuration
        var smtpHost = _configuration["Providers:Smtp:Host"];
        var smtpPort = _configuration.GetValue<int>("Providers:Smtp:Port", 25);
        var smtpFrom = _configuration["Providers:Smtp:From"];
        var smtpSenderName = _configuration["Providers:Smtp:SenderName"];

        Console.WriteLine($"🔧 SMTP Configuration Check:");
        Console.WriteLine($"   Host: {smtpHost ?? "NOT SET"}");
        Console.WriteLine($"   Port: {smtpPort}");
        Console.WriteLine($"   From: {smtpFrom ?? "NOT SET"}");
        Console.WriteLine($"   Sender Name: {smtpSenderName ?? "NOT SET"}");
        Console.WriteLine();

        if (string.IsNullOrEmpty(smtpHost) || string.IsNullOrEmpty(smtpFrom))
        {
            Console.WriteLine("❌ Missing SMTP configuration. Please check appsettings.json");
            return;
        }

        // Test SMTP connection
        await TestSmtpConnection(smtpHost, smtpPort);
        
        // Test sending email
        await TestSendEmail(smtpHost, smtpPort, smtpFrom, smtpSenderName);
    }

    private async Task TestSmtpConnection(string host, int port)
    {
        Console.WriteLine("🔍 Testing: SMTP Connection");
        Console.WriteLine("──────────────────────────");

        try
        {
            using var client = new SmtpClient(host, port);
            
            // Configure client
            client.EnableSsl = false; // Based on your config
            client.UseDefaultCredentials = true;
            client.Timeout = 10000; // 10 seconds

            Console.WriteLine($"   Connecting to {host}:{port}...");
            
            // Test connection by creating a test message but not sending it
            var testMessage = new MailMessage
            {
                From = new MailAddress("<EMAIL>", "Test"),
                Subject = "Connection Test",
                Body = "This is a connection test"
            };
            testMessage.To.Add("<EMAIL>");

            // This will test the connection without actually sending
            Console.WriteLine($"   SMTP Client Configuration:");
            Console.WriteLine($"     Host: {client.Host}");
            Console.WriteLine($"     Port: {client.Port}");
            Console.WriteLine($"     EnableSsl: {client.EnableSsl}");
            Console.WriteLine($"     UseDefaultCredentials: {client.UseDefaultCredentials}");
            Console.WriteLine($"     Timeout: {client.Timeout}ms");

            Console.WriteLine("✅ SMTP Connection: Configuration appears valid");
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 SMTP Connection: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
            
            if (ex.InnerException != null)
            {
                Console.WriteLine($"   Inner Exception: {ex.InnerException.Message}");
            }

            _logger.LogError(ex, "SMTP Connection Test Failed");
        }
        finally
        {
            Console.WriteLine();
        }
    }

    private async Task TestSendEmail(string host, int port, string from, string senderName)
    {
        Console.WriteLine("🔍 Testing: Send Email");
        Console.WriteLine("─────────────────────");

        try
        {
            using var client = new SmtpClient(host, port);
            
            // Configure client
            client.EnableSsl = false;
            client.UseDefaultCredentials = true;
            client.Timeout = 30000; // 30 seconds

            // Create test email
            var message = new MailMessage
            {
                From = new MailAddress(from, senderName ?? "LendQube Test"),
                Subject = $"LendQube Email Test - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}",
                Body = $@"
This is a test email from LendQube Email Function Tester.

Test Details:
- Timestamp: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC
- SMTP Host: {host}
- SMTP Port: {port}
- From Address: {from}

If you receive this email, the SMTP configuration is working correctly.
",
                IsBodyHtml = false
            };

            // Add test recipient - <NAME_EMAIL> for testing
            message.To.Add("<EMAIL>");

            Console.WriteLine($"   Email Details:");
            Console.WriteLine($"     From: {message.From}");
            Console.WriteLine($"     To: {string.Join(", ", message.To)}");
            Console.WriteLine($"     Subject: {message.Subject}");
            Console.WriteLine($"     Body Length: {message.Body.Length} characters");

            Console.WriteLine($"   Attempting to send email...");

            // Send the email
            await client.SendMailAsync(message);

            Console.WriteLine("✅ Send Email: SUCCESS");
            Console.WriteLine("   Email sent successfully! Check the recipient inbox.");
        }
        catch (SmtpException smtpEx)
        {
            Console.WriteLine("❌ Send Email: SMTP EXCEPTION");
            Console.WriteLine($"   SMTP Status Code: {smtpEx.StatusCode}");
            Console.WriteLine($"   Message: {smtpEx.Message}");
            
            if (smtpEx.InnerException != null)
            {
                Console.WriteLine($"   Inner Exception: {smtpEx.InnerException.Message}");
            }

            _logger.LogError(smtpEx, "SMTP Send Email Failed");
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 Send Email: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
            
            if (ex.InnerException != null)
            {
                Console.WriteLine($"   Inner Exception: {ex.InnerException.Message}");
            }

            _logger.LogError(ex, "Send Email Test Failed");
        }
        finally
        {
            Console.WriteLine();
        }
    }
}
﻿using System.Security.Claims;
using LendQube.Infrastructure.Core.AdminUserManagement;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Components.Server;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace LendQube.Infrastructure.Core.Authentication;

// This is a server-side AuthenticationStateProvider that revalidates the security stamp for the connected user
// every 5 minutes an interactive circuit is connected.
public sealed class IdentityRevalidatingAuthenticationStateProvider(
        ILoggerFactory loggerFactory,
        DefaultAppConfig config,
        PooledDbContext context,
        IOptions<IdentityOptions> options)
    : RevalidatingServerAuthenticationStateProvider(loggerFactory)
{
    private IUnitofWork uow;

    protected override TimeSpan RevalidationInterval => TimeSpan.FromMinutes(5);

    protected override Task<bool> ValidateAuthenticationStateAsync(AuthenticationState authenticationState, CancellationToken cancellationToken)
    {
        if (authenticationState.User == null)
            return Task.FromResult(false);

        uow ??= context.RentUow();
        var principalStamp = authenticationState.User.FindFirstValue(options.Value.ClaimsIdentity.SecurityStampClaimType);
        return AdminCompiledQueries.CheckSecurityStamp(uow, config.DeployState.IsDemo, principalStamp, authenticationState.User.Identity.Name, cancellationToken);
    }

    protected override void Dispose(bool disposing)
    {
        context?.Return(uow);
        base.Dispose(disposing);
    }
}


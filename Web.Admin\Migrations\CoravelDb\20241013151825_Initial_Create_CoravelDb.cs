﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations.CoravelDb
{
    /// <inheritdoc />
    public partial class Initial_Create_CoravelDb : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "coravel");

            migrationBuilder.CreateTable(
                name: "Coravel_JobHistory",
                schema: "coravel",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    StartedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    EndedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TypeFullPath = table.Column<string>(type: "text", nullable: true),
                    DisplayName = table.Column<string>(type: "text", nullable: true),
                    Failed = table.Column<bool>(type: "boolean", nullable: false),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    StackTrace = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Coravel_JobHistory", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Coravel_ScheduledJobHistory",
                schema: "coravel",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    EndedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    TypeFullPath = table.Column<string>(type: "text", nullable: true),
                    DisplayName = table.Column<string>(type: "text", nullable: true),
                    Failed = table.Column<bool>(type: "boolean", nullable: false),
                    ErrorMessage = table.Column<string>(type: "text", nullable: true),
                    StackTrace = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Coravel_ScheduledJobHistory", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Coravel_ScheduledJobs",
                schema: "coravel",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    InvocableFullPath = table.Column<string>(type: "text", nullable: true),
                    CronExpression = table.Column<string>(type: "text", nullable: true),
                    Frequency = table.Column<string>(type: "text", nullable: true),
                    Days = table.Column<string>(type: "text", nullable: true),
                    PreventOverlapping = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: false),
                    Active = table.Column<bool>(type: "boolean", nullable: false),
                    TimeZoneInfo = table.Column<string>(type: "text", nullable: true),
                    RunOnDedicatedThread = table.Column<bool>(type: "boolean", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Coravel_ScheduledJobs", x => x.Id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Coravel_JobHistory_EndedAt",
                schema: "coravel",
                table: "Coravel_JobHistory",
                column: "EndedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Coravel_ScheduledJobHistory_EndedAt",
                schema: "coravel",
                table: "Coravel_ScheduledJobHistory",
                column: "EndedAt");

            migrationBuilder.CreateIndex(
                name: "IX_Coravel_ScheduledJobs_Active",
                schema: "coravel",
                table: "Coravel_ScheduledJobs",
                column: "Active");

            migrationBuilder.CreateIndex(
                name: "IX_Coravel_ScheduledJobs_InvocableFullPath",
                schema: "coravel",
                table: "Coravel_ScheduledJobs",
                column: "InvocableFullPath");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "Coravel_JobHistory",
                schema: "coravel");

            migrationBuilder.DropTable(
                name: "Coravel_ScheduledJobHistory",
                schema: "coravel");

            migrationBuilder.DropTable(
                name: "Coravel_ScheduledJobs",
                schema: "coravel");
        }
    }
}

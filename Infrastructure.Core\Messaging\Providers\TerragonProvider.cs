﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Telemetry;
using System.Net;
using System.Text;
using System.Text.Json;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class TerragonProvider : AbstractMessageProvider, ITextMessageProvider
{
    public const string Name = "Terragon";
    protected override MessageChannel SupportedChannel => MessageChannel.Sms;

    private readonly DefaultAppConfig config;
    private readonly HttpClient httpClient;
    private readonly ILogManager<TerragonProvider> logger;

    public TerragonProvider(IUnitofWork uow, DefaultAppConfig config, HttpClient httpClient, ILogManager<TerragonProvider> logger) : base(uow)
    {
        this.config = config;
        this.httpClient = httpClient;
        this.logger = logger;

        if(config.Terragon != null)
        {
            SupportedCountryCodes = MessagingCompiledQueries.GetMessageProviderSupportedCountriesAndConfig(uow, Name);
            Config = SupportedCountryCodes.IsNullOrEmpty() ? new ProviderConfigVM { Disabled = true } : SupportedCountryCodes[0];
        }
        else
        {
            Config = new ProviderConfigVM { Disabled = true };
        }
    }

    public override ProviderConfigVM Config { get; }
    public IReadOnlyList<ProviderConfigVM> SupportedCountryCodes { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        var leadingMessage = messages[0];

        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        Dictionary<long, MessageStatus> result = [];

        foreach (var message in messages)
        {
            foreach (var (recipient, index) in message.Data.Select((x, i) => (x, i)))
            {
                result[message.MessageId + index] = await ProcessSingleMessage(message, recipient, ct);
            }
        }

        var status = result.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, result, null, null, ct);
        return status;
    }

    private async Task<MessageStatus> ProcessSingleMessage(PreparedMessageVM message, SinglePreparedMessageVM recipient, CancellationToken ct)
    {
        bool successful = false;

        try
        {
            foreach (var phoneNumber in recipient.PhoneNumbers)
            {
                var phone = $"{phoneNumber.Code[1..]}{phoneNumber.Number[1..]}";
                var data = new
                {
                    to = phone,
                    from = config.Terragon.SenderId,
                    content = recipient.TextTemplate ?? message.TextTemplate,
                    username = config.Terragon.TransactionalSmsUsername,
                    password = config.Terragon.TransactionalSmsPassword
                };

                var reqparams = $"to={phone}&from={config.Terragon.SenderId}&content={WebUtility.UrlEncode(recipient.TextTemplate ?? message.TextTemplate)}&username={config.Terragon.TransactionalSmsUsername}&password={config.Terragon.TransactionalSmsPassword}";
                var request = new HttpRequestMessage(HttpMethod.Get, $"{config.Terragon.HostAddress}/api/send?{reqparams}")
                {
                    Content = new StringContent(JsonSerializer.Serialize(data), Encoding.UTF8, HttpHeaderHelper.JsonHeader)
                };

                var response = await httpClient.SendAsync(request, ct);
                successful = successful ? successful : response.IsSuccessStatusCode;
                string serverResponse = await response.Content.ReadAsStringAsync(ct);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.BackgroundTask, EventAction.SendSms, ex, $"Error occured while sending sms {message.MessageId}");
        }

        return successful.ToMessageStatus();
    }
}

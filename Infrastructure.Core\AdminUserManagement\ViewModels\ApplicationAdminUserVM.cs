﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.BaseUser;
using NodaTime;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;

public class ApplicationAdminUserVM
{
    public static readonly Expression<Func<ApplicationUser, ApplicationAdminUserVM>> AdminUser = data => new()
    {
        Id = data.Id,
        FullName = data.FullName,
        UserName = data.UserName,
        Email = data.Email,
        FirstName = data.FirstName,
        LastName = data.LastName,
        OtherNames = data.OtherNames,
        PhoneCode = data.PhoneCode,
        PhoneNumber = data.PhoneNumber,
        LockoutEnabled = data.LockoutEnabled,
        LockoutEnd = data.LockoutEnd,
        TwoFactorEnabled = data.TwoFactorEnabled,
        LastLoginDate = data.LastLoginDate,
        RegistrationDate = data.RegistrationDate,
    };

    [TableDecorator(TableDecoratorType.HideColumn)]
    public Guid Id { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string FullName { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.Id)]
    public string UserName { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Email { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string FirstName { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string LastName { get; set; }
    public string OtherNames { get; set; }
    public string PhoneCode { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string PhoneNumber { get; set; }
    public bool LockoutEnabled { get; set; }
    public DateTimeOffset? LockoutEnd { get; set; }
    public bool TwoFactorEnabled { get; set; }
    public Instant? LastLoginDate { get; set; }
    public Instant? RegistrationDate { get; set; }
}
﻿using LendQube.Entities.Core.BaseUser;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;

public sealed class AdminUserEditVM
{
    public static Expression<Func<ApplicationUser, AdminUserEditVM>> AdminUser = data => new()
    {
        Id = data.Id,
        FirstName = data.FirstName,
        LastName = data.LastName,
        OtherNames = data.OtherNames,
        Email = data.Email,
        PhoneNumber = data.PhoneNumber,
		PhoneCode = data.PhoneCode
	};

    public static AdminUserEditVM Get(ApplicationAdminUserVM data) => new()
    {
        Id = data.Id,
        FirstName = data.FirstName,
        LastName = data.LastName,
        OtherNames = data.OtherNames,
        Email = data.Email,
        PhoneNumber = data.PhoneNumber,
        PhoneCode = data.PhoneCode
    };

    public Guid Id { get; set; }
    [MinLength(8, ErrorMessage = "The {0} must be at least {1} characters long.")]
    [Display(Name = "Password")]
    public string Password { get; set; }

    [DataType(DataType.Password)]
    [Display(Name = "Confirm password")]
    [Compare("Password", ErrorMessage =
        "The password and confirmation password do not match.")]
    public string ConfirmPassword { get; set; }

    [Required]
    [Display(Name = "First Name")]
    public string FirstName { get; set; }
    [Required]
    [Display(Name = "Last Name")]
    public string LastName { get; set; }

    [Display(Name = "Other Names")]
    public string OtherNames { get; set; }

    [Required]
    [DataType(DataType.EmailAddress)]
    public string Email { get; set; }

    [Required]
    [Display(Name = "Phone Code")]
    public string PhoneCode { get; set; }

    [Required, DataType(DataType.PhoneNumber)]
    [Display(Name = "Phone Number")]
    public string PhoneNumber { get; set; }

}


public sealed class ChangePasswordVM
{
    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "Current password")]
    public string OldPassword { get; set; } = "";

    [Required]
    [MinLength(8, ErrorMessage = "The {0} must be at least {1} characters long.")]
    [DataType(DataType.Password)]
    [Display(Name = "New password")]
    public string NewPassword { get; set; } = "";

    [DataType(DataType.Password)]
    [Display(Name = "Confirm new password")]
    [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
    public string ConfirmPassword { get; set; } = "";
}

public sealed class ChangeTransactionPinVM
{
    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "New Transaction Pin")]
    public string NewTransactionPin { get; set; }

    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "Old Transaction Pin")]
    public string OldTransactionPin { get; set; }

}
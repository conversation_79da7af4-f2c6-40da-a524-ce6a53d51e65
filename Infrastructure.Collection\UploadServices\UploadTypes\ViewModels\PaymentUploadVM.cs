﻿using NodaTime;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;

internal sealed class PaymentUploadVM
{
    public string AccountNumber { get; set; }
    public Instant? TransactionDate { get; set; }
    public decimal Amount { get; set; }
    public string Type { get; set; }
    public string Reference { get; set; }
    public string Note { get; set; }
}

public sealed class PaymentCustomerDataVM
{
    public string ProfileId { get; set; }
    public long PlacementId { get; set; }
    public string PlacementAccountNumber { get; set; }
    public bool TransactionExists { get; set; }
    public string CurrencySymbol { get; set; }
    public string CurrencyCode { get; set; }
    public decimal AccountBalance { get; set; }
    public bool HasSchedule { get; set; }
}
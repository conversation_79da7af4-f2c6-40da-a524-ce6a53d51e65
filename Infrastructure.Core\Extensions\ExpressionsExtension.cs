﻿using System.Linq.Expressions;
using LendQube.Entities.Core.Constants;

namespace LendQube.Infrastructure.Core.Extensions;

public static class ExpressionsExtension
{
    public static Expression<Func<T, bool>> CombineWithAndAlso<T>(this Expression<Func<T, bool>> func1, Expression<Func<T, bool>> func2)
    {
        return Expression.Lambda<Func<T, bool>>(
            Expression.AndAlso(
                func1.Body, new ExpressionParameterReplacer(func2.Parameters, func1.Parameters).Visit(func2.Body)),
            func1.Parameters);
    }

    public static Expression<Func<T, bool>> CombineWithOrElse<T>(this Expression<Func<T, bool>> func1, Expression<Func<T, bool>> func2)
    {
        return Expression.Lambda<Func<T, bool>>(
            Expression.OrElse(
                func1.Body, new ExpressionParameterReplacer(func2.Parameters, func1.Parameters).Visit(func2.Body)),
            func1.Parameters);
    }

    internal static Expression GetDenaturedExpression(this ParameterExpression parameterExpression, string property)
    {
        Expression expression = parameterExpression;

        foreach (string propertyOrFieldName in property.Split(['.']))
            expression = Expression.PropertyOrField(expression, propertyOrFieldName);

        return expression;
    }

    internal static Expression<Func<T, bool>> NotAnyExpression<T>(Type type, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        var methodInfo = typeof(Enumerable).GetMethods()
                .First(method => method.Name == nameof(Enumerable.Any) && method.GetParameters().Length == 1)
                .MakeGenericMethod(type.GenericTypeArguments[0]);

        var anyCall = Expression.Not(Expression.Call(methodInfo, member));

        return Expression.Lambda<Func<T, bool>>(anyCall, pe);
    }

    internal static Expression<Func<T, bool>> AnyExpression<T>(Type type, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        var methodInfo = typeof(Enumerable).GetMethods()
                .First(method => method.Name == nameof(Enumerable.Any) && method.GetParameters().Length == 1)
                .MakeGenericMethod(type.GenericTypeArguments[0]);

        var anyCall = Expression.Call(methodInfo, member);

        return Expression.Lambda<Func<T, bool>>(anyCall, pe);
    }

    internal static Expression<Func<T, bool>> AnyExpression<T>(LambdaExpression predicate, Type type, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        var methodInfo = typeof(Enumerable).GetMethods()
                .First(method => method.Name == nameof(Enumerable.Any) && method.GetParameters().Length == 2)
                .MakeGenericMethod(type);

        var anyCall = Expression.Call(methodInfo, member, predicate);

        return Expression.Lambda<Func<T, bool>>(anyCall, pe);
    }

    internal static Expression<Func<T, bool>> Equal<T>(Type propertyType, object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value, propertyType);
        Expression equalityExpression = Expression.Equal(member, valueExpression);
        Expression<Func<T, bool>> lambda = Expression.Lambda<Func<T, bool>>(equalityExpression, pe);
        return lambda;
    }

    internal static Expression<Func<T, bool>> DateTimeEqual<T>(Type propertyType, string value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        DateTime currentDate = value.ParseFromString();
        DateTime nextDate = value.ParseFromString().EndOfTheDay();

        Expression ex1 = Expression.GreaterThanOrEqual(member, Expression.Constant(currentDate, propertyType));
        Expression ex2 = Expression.LessThanOrEqual(member, Expression.Constant(nextDate, propertyType));
        Expression body = Expression.AndAlso(ex1, ex2);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    internal static Expression<Func<T, bool>> DateTimeNotEqual<T>(Type propertyType, string value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        DateTime currentDate = value.ParseFromString();
        DateTime nextDate = value.ParseFromString().EndOfTheDay();

        Expression ex1 = Expression.LessThan(member, Expression.Constant(currentDate, propertyType));
        Expression ex2 = Expression.GreaterThan(member, Expression.Constant(nextDate, propertyType));
        Expression body = Expression.OrElse(ex1, ex2);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    internal static Expression<Func<T, bool>> DateOnlyEqual<T>(Type propertyType, string value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        DateOnly currentDate = DateOnly.FromDateTime(value.ParseFromString());
        DateOnly nextDate = DateOnly.FromDateTime(value.ParseFromString()).AddDays(1);

        Expression ex1 = Expression.GreaterThanOrEqual(member, Expression.Constant(currentDate, propertyType));
        Expression ex2 = Expression.LessThan(member, Expression.Constant(nextDate, propertyType));
        Expression body = Expression.AndAlso(ex1, ex2);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    internal static Expression<Func<T, bool>> DateOnlyNotEqual<T>(Type propertyType, string value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        DateOnly currentDate = DateOnly.FromDateTime(value.ParseFromString());
        DateOnly nextDate = DateOnly.FromDateTime(value.ParseFromString()).AddDays(1);

        Expression ex1 = Expression.LessThan(member, Expression.Constant(currentDate, propertyType));
        Expression ex2 = Expression.GreaterThanOrEqual(member, Expression.Constant(nextDate, propertyType));
        Expression body = Expression.OrElse(ex1, ex2);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    internal static Expression<Func<T, bool>> InstantEqual<T>(Type propertyType, string timezone, string value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        var valueInstant = value.GetInstantFromStringGivenTimeZone(timezone);
        var nextDay = valueInstant.StartOfNextDay();

        Expression ex1 = Expression.GreaterThanOrEqual(member, Expression.Constant(valueInstant, propertyType));
        Expression ex2 = Expression.LessThan(member, Expression.Constant(nextDay, propertyType));
        Expression body = Expression.AndAlso(ex1, ex2);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    internal static Expression<Func<T, bool>> InstantNotEqual<T>(Type propertyType, string timezone, string value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        var valueInstant = value.GetInstantFromStringGivenTimeZone(timezone);
        var nextDay = valueInstant.StartOfNextDay();

        Expression ex1 = Expression.LessThan(member, Expression.Constant(valueInstant, propertyType));
        Expression ex2 = Expression.GreaterThanOrEqual(member, Expression.Constant(nextDay, propertyType));
        Expression body = Expression.OrElse(ex1, ex2);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    internal static Expression<Func<T, bool>> Like<T>(object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value);
        //string.Contains with string parameter.
        var stringContainsMethod = typeof(string).GetMethod(nameof(string.Contains), [typeof(string)]);
        var containsCall = Expression.Call(member, stringContainsMethod,
            valueExpression);

        return Expression.Lambda<Func<T, bool>>(containsCall, pe);
    }

    internal static Expression<Func<T, bool>> LikeLowerCase<T>(object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value);
        //string.Contains with string parameter.
        var stringToLowerMethod = typeof(string).GetMethod(nameof(string.ToLower), Type.EmptyTypes);
        var toLowerCall = Expression.Call(member, stringToLowerMethod);
        var stringContainsMethod = typeof(string).GetMethod(nameof(string.Contains), [typeof(string)]);
        var containsCall = Expression.Call(toLowerCall, stringContainsMethod,
            valueExpression);

        return Expression.Lambda<Func<T, bool>>(containsCall, pe);
    }

    internal static Expression<Func<T, bool>> NotLikeLowerCase<T>(object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value);
        //string.Contains with string parameter.
        var stringToLowerMethod = typeof(string).GetMethod(nameof(string.ToLower), Type.EmptyTypes);
        var toLowerCall = Expression.Call(member, stringToLowerMethod);
        var stringContainsMethod = typeof(string).GetMethod(nameof(string.Contains), [typeof(string)]);
        var notContainsCall = Expression.Not(Expression.Call(toLowerCall, stringContainsMethod,
            valueExpression));
        return Expression.Lambda<Func<T, bool>>(notContainsCall, pe);
    }

    internal static Expression<Func<T, bool>> ListStringContains<T>(Type propertyType, object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value);
        if (propertyType == typeof(List<string>))
        {
            var methodInfo = typeof(List<string>).GetMethod(nameof(List<string>.Contains), [typeof(string)]);
            var containsCall = Expression.Call(member, methodInfo, valueExpression);

            return Expression.Lambda<Func<T, bool>>(containsCall, pe);
        }
        else if (propertyType == typeof(IEnumerable<string>))
        {
            var methodInfo = typeof(Enumerable).GetMethods()
                    .First(method => method.Name == nameof(Enumerable.Contains) && method.GetParameters().Length == 2)
                    .MakeGenericMethod(typeof(string));
            var containsCall = Expression.Call(methodInfo, member, valueExpression);

            return Expression.Lambda<Func<T, bool>>(containsCall, pe);
        }

        return null;
    }

    internal static Expression<Func<T, bool>> ListStringDoesNotContain<T>(Type propertyType, object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value);

        if (propertyType == typeof(List<string>))
        {
            var methodInfo = typeof(List<string>).GetMethod(nameof(List<string>.Contains), [typeof(string)]);
            var containsCall = Expression.Not(Expression.Call(member, methodInfo, valueExpression));

            return Expression.Lambda<Func<T, bool>>(containsCall, pe);
        }
        else if (propertyType == typeof(IEnumerable<string>))
        {
            var methodInfo = typeof(Enumerable).GetMethods()
                    .First(method => method.Name == nameof(Enumerable.Contains) && method.GetParameters().Length == 2)
                    .MakeGenericMethod(typeof(string));
            var containsCall = Expression.Not(Expression.Call(methodInfo, member, valueExpression));

            return Expression.Lambda<Func<T, bool>>(containsCall, pe);
        }

        return null;
    }

    internal static Expression<Func<T, bool>> StartsWithLowerCase<T>(object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value);
        //string.Contains with string parameter.
        var stringToLowerMethod = typeof(string).GetMethod(nameof(string.ToLower), Type.EmptyTypes);
        var toLowerCall = Expression.Call(member, stringToLowerMethod);
        var stringContainsMethod = typeof(string).GetMethod(nameof(string.StartsWith), [typeof(string)]);
        var containsCall = Expression.Call(toLowerCall, stringContainsMethod,
            valueExpression);

        return Expression.Lambda<Func<T, bool>>(containsCall, pe);
    }

    internal static Expression<Func<T, bool>> EndsWithLowerCase<T>(object value, string property)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(property);

        Expression valueExpression = Expression.Constant(value);
        //string.Contains with string parameter.
        var stringToLowerMethod = typeof(string).GetMethod(nameof(string.ToLower), Type.EmptyTypes);
        var toLowerCall = Expression.Call(member, stringToLowerMethod);
        var stringContainsMethod = typeof(string).GetMethod(nameof(string.EndsWith), [typeof(string)]);
        var containsCall = Expression.Call(toLowerCall, stringContainsMethod,
            valueExpression);

        return Expression.Lambda<Func<T, bool>>(containsCall, pe);
    }

    internal static Expression<Func<T, bool>> LessThanOrEqual<T, TDest>(Expression<Func<T, TDest>> property, TDest value)
    {
        Expression valueExpression = Expression.Constant(value);
        Expression equalityExpression = Expression.LessThanOrEqual(property.Body, valueExpression);
        Expression<Func<T, bool>> lambda = Expression.Lambda<Func<T, bool>>(equalityExpression, property.Parameters[0]);
        return lambda;
    }

    internal static Expression<Func<T, bool>> GreaterThanOrEqual<T, TDest>(Expression<Func<T, TDest>> property, TDest value)
    {
        Expression valueExpression = Expression.Constant(value);
        Expression equalityExpression = Expression.GreaterThanOrEqual(property.Body, valueExpression);
        Expression<Func<T, bool>> lambda = Expression.Lambda<Func<T, bool>>(equalityExpression, property.Parameters[0]);
        return lambda;
    }

    internal static Expression<Func<T, bool>> EnumHasFlag<T>(Type propertyType, string propertyName, object value)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(propertyName);

        UnaryExpression valueExpression = Expression.ConvertChecked(Expression.Constant(Enum.Parse(propertyType, value.ToString())), typeof(Enum));

        var hasFlagMethod = typeof(Enum).GetMethod(nameof(Enum.HasFlag));
        var hasFlagCall = Expression.Call(expression, hasFlagMethod, valueExpression);

        return Expression.Lambda<Func<T, bool>>(hasFlagCall, pe);
    }

    internal static Expression<Func<T, bool>> EnumDoesNotHaveFlag<T>(Type propertyType, string propertyName, object value)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(propertyName);

        UnaryExpression valueExpression = Expression.ConvertChecked(Expression.Constant(Enum.Parse(propertyType, value.ToString())), typeof(Enum));

        var hasFlagMethod = typeof(Enum).GetMethod(nameof(Enum.HasFlag));
        var hasFlagCall = Expression.Not(Expression.Call(expression, hasFlagMethod, valueExpression));

        return Expression.Lambda<Func<T, bool>>(hasFlagCall, pe);
    }

    internal static Expression<Func<T, bool>> PhoneNumberEqual<T>(string propertyName, object value)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(propertyName);

        var numberExpression = Expression.PropertyOrField(expression, nameof(PhoneNumber.Number));

        var phoneNumber = value.ToString().Split(' ', StringSplitOptions.TrimEntries);
        if (phoneNumber.Length == 2)
        {
            var codeUnary = Expression.ConvertChecked(Expression.Constant(phoneNumber[0]), typeof(string));
            var numberUnary = Expression.ConvertChecked(Expression.Constant(phoneNumber[1]), typeof(string));

            var codeEqual = Expression.Equal(Expression.PropertyOrField(expression, nameof(PhoneNumber.Code)), codeUnary);
            var numberEqual = Expression.Equal(numberExpression, numberUnary);

            return Expression.Lambda<Func<T, bool>>(Expression.AndAlso(codeEqual, numberEqual), pe);
        }
        else
        {
            var numberUnary = Expression.ConvertChecked(Expression.Constant(phoneNumber[0]), typeof(string));
            return Expression.Lambda<Func<T, bool>>(Expression.Equal(numberExpression, numberUnary), pe);
        }
    }

    internal static Expression<Func<T, bool>> PhoneNumberNotEqual<T>(string propertyName, object value)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(propertyName);

        var numberExpression = Expression.PropertyOrField(expression, nameof(PhoneNumber.Number));

        var phoneNumber = value.ToString().Split(' ', StringSplitOptions.TrimEntries);
        if (phoneNumber.Length == 2)
        {
            var codeUnary = Expression.ConvertChecked(Expression.Constant(phoneNumber[0]), typeof(string));
            var numberUnary = Expression.ConvertChecked(Expression.Constant(phoneNumber[1]), typeof(string));

            var codeEqual = Expression.NotEqual(Expression.PropertyOrField(expression, nameof(PhoneNumber.Code)), codeUnary);
            var numberEqual = Expression.NotEqual(numberExpression, numberUnary);

            return Expression.Lambda<Func<T, bool>>(Expression.AndAlso(codeEqual, numberEqual), pe);
        }
        else
        {
            var numberUnary = Expression.ConvertChecked(Expression.Constant(phoneNumber[0]), typeof(string));
            return Expression.Lambda<Func<T, bool>>(Expression.NotEqual(numberExpression, numberUnary), pe);
        }
    }

    internal static string GetAttributeName<T>(this Expression<Func<T, object>> myParam)
    {
        if (myParam.Body is UnaryExpression)
        {
            var item = myParam.Body as UnaryExpression;
            var operand = item.Operand as MemberExpression;
            return operand.Member.Name;
        }

        return (myParam.Body as MemberExpression).Member.Name;
    }

    private class ExpressionParameterReplacer : ExpressionVisitor
    {
        public ExpressionParameterReplacer(IList<ParameterExpression> fromParameters, IList<ParameterExpression> toParameters)
        {
            ParameterReplacements = new Dictionary<ParameterExpression, ParameterExpression>();
            for (int i = 0; i != fromParameters.Count && i != toParameters.Count; i++)
                ParameterReplacements.Add(fromParameters[i], toParameters[i]);
        }

        private IDictionary<ParameterExpression, ParameterExpression> ParameterReplacements { get; set; }

        protected override Expression VisitParameter(ParameterExpression node)
        {
            if (ParameterReplacements.TryGetValue(node, out ParameterExpression replacement))
                node = replacement;
            return base.VisitParameter(node);
        }
    }
}
﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Infrastructure.Core.Database.AnalyticsTriggers;
using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Analytics;

static partial class ManageAnalyticsService
{
    private static readonly string escalatedScript =
        $@"UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowAnalytics)}"" SET ""{nameof(AgentWorkflowAnalytics.TotalEscalated)}"" = ""{nameof(AgentWorkflowAnalytics.TotalEscalated)}"" + 1 WHERE ""{nameof(AgentWorkflowAnalytics.UserId)}"" = {{0}};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowTimeAnalytics)}"" SET ""{nameof(AgentWorkflowTimeAnalytics.TotalEscalated)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalEscalated)}"" + 1 
        WHERE ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = {{0}} AND ""Date"" = {AnalyticsTriggerHelpers.CurrentDate};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowAnalytics)}"" SET ""{nameof(DebtWorkflowAnalytics.TotalEscalated)}"" = ""{nameof(DebtWorkflowAnalytics.TotalEscalated)}"" + 1 WHERE ""{nameof(DebtWorkflowAnalytics.Id)}"" = 1;
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowTimeAnalytics)}"" SET ""{nameof(DebtWorkflowTimeAnalytics.TotalEscalated)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalEscalated)}"" + 1 
        WHERE ""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate};
        ";
    public static Task UpdateEscalatedWorkflow(IUnitofWork uow, Guid agentId, CancellationToken ct)
        => uow.Context.Database.ExecuteSqlRawAsync(escalatedScript, [agentId], ct);

    private static readonly string collectedScript =
        $@"UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowAnalytics)}"" SET ""{nameof(AgentWorkflowAnalytics.TotalAmountCollected)}"" = ""{nameof(AgentWorkflowAnalytics.TotalAmountCollected)}"" + {{1}}
        WHERE ""{nameof(AgentWorkflowAnalytics.UserId)}"" = {{0}};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowTimeAnalytics)}"" SET ""{nameof(AgentWorkflowTimeAnalytics.TotalAmountCollected)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalAmountCollected)}"" + {{1}} 
        WHERE ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = {{0}} AND ""Date"" = {AnalyticsTriggerHelpers.CurrentDate};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowAnalytics)}"" SET ""{nameof(DebtWorkflowAnalytics.TotalAmountCollected)}"" = ""{nameof(DebtWorkflowAnalytics.TotalAmountCollected)}"" + {{1}} WHERE ""{nameof(DebtWorkflowAnalytics.Id)}"" = 1;
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowTimeAnalytics)}"" SET ""{nameof(DebtWorkflowTimeAnalytics.TotalAmountCollected)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalAmountCollected)}"" + {{1}} 
        WHERE ""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate};
        ";
    public static Task UpdateAmountCollectedWorkflow(IUnitofWork uow, Guid agentId, decimal amountCollected, CancellationToken ct)
        => uow.Context.Database.ExecuteSqlRawAsync(collectedScript, [agentId, amountCollected], ct);

    private static readonly string ptpScript =
        $@"UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowAnalytics)}"" 
        SET ""{nameof(AgentWorkflowAnalytics.TotalPTP)}"" = ""{nameof(AgentWorkflowAnalytics.TotalPTP)}"" + 1, ""{nameof(AgentWorkflowAnalytics.TotalPTPAmount)}"" = ""{nameof(AgentWorkflowAnalytics.TotalPTPAmount)}"" + {{1}}  
        WHERE ""{nameof(AgentWorkflowAnalytics.UserId)}"" = {{0}};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowTimeAnalytics)}"" 
        SET ""{nameof(AgentWorkflowTimeAnalytics.TotalPTP)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalPTP)}"" + 1, ""{nameof(AgentWorkflowTimeAnalytics.TotalPTPAmount)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalPTPAmount)}"" + {{1}} 
        WHERE ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = {{0}} AND ""Date"" = {AnalyticsTriggerHelpers.CurrentDate};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowAnalytics)}"" 
        SET ""{nameof(DebtWorkflowAnalytics.TotalPTP)}"" = ""{nameof(DebtWorkflowAnalytics.TotalPTP)}"" + 1, ""{nameof(DebtWorkflowAnalytics.TotalPTPAmount)}"" = ""{nameof(DebtWorkflowAnalytics.TotalPTPAmount)}"" + {{1}} 
        WHERE ""{nameof(DebtWorkflowAnalytics.Id)}"" = 1;
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowTimeAnalytics)}"" 
        SET ""{nameof(DebtWorkflowTimeAnalytics.TotalPTP)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalPTP)}"" + 1, ""{nameof(DebtWorkflowTimeAnalytics.TotalPTPAmount)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalPTPAmount)}"" + {{1}} 
        WHERE ""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate};
        ";
    public static Task UpdatePtpWorkflow(IUnitofWork uow, Guid agentId, decimal amount, CancellationToken ct)
        => uow.Context.Database.ExecuteSqlRawAsync(ptpScript, [agentId, amount], ct);


    private static readonly string schedulesScript =
        $@"UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowAnalytics)}"" SET ""{nameof(AgentWorkflowAnalytics.TotalSchedulesSetup)}"" = ""{nameof(AgentWorkflowAnalytics.TotalSchedulesSetup)}"" + 1 WHERE ""{nameof(AgentWorkflowAnalytics.UserId)}"" = {{0}};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowTimeAnalytics)}"" SET ""{nameof(AgentWorkflowTimeAnalytics.TotalSchedulesSetup)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalSchedulesSetup)}"" + 1 
        WHERE ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = {{0}} AND ""Date"" = {AnalyticsTriggerHelpers.CurrentDate};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowAnalytics)}"" SET ""{nameof(DebtWorkflowAnalytics.TotalSchedulesSetup)}"" = ""{nameof(DebtWorkflowAnalytics.TotalSchedulesSetup)}"" + 1 WHERE ""{nameof(DebtWorkflowAnalytics.Id)}"" = 1;
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowTimeAnalytics)}"" SET ""{nameof(DebtWorkflowTimeAnalytics.TotalSchedulesSetup)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalSchedulesSetup)}"" + 1 
        WHERE ""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate};
        ";
    public static Task UpdateScheduleWorkflow(IUnitofWork uow, Guid agentId, CancellationToken ct)
        => uow.Context.Database.ExecuteSqlRawAsync(schedulesScript, [agentId], ct);

    private static readonly string contactedScript =
        $@"UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowAnalytics)}"" SET ""{nameof(AgentWorkflowAnalytics.TotalContacted)}"" = ""{nameof(AgentWorkflowAnalytics.TotalContacted)}"" + 1 WHERE ""{nameof(AgentWorkflowAnalytics.UserId)}"" = {{0}};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowTimeAnalytics)}"" SET ""{nameof(AgentWorkflowTimeAnalytics.TotalContacted)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalContacted)}"" + 1 
        WHERE ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = {{0}} AND ""Date"" = {AnalyticsTriggerHelpers.CurrentDate};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowAnalytics)}"" SET ""{nameof(DebtWorkflowAnalytics.TotalContacted)}"" = ""{nameof(DebtWorkflowAnalytics.TotalContacted)}"" + 1 WHERE ""{nameof(DebtWorkflowAnalytics.Id)}"" = 1;
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowTimeAnalytics)}"" SET ""{nameof(DebtWorkflowTimeAnalytics.TotalContacted)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalContacted)}"" + 1 
        WHERE ""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate};
        ";
    public static Task UpdateContactedWorkflow(IUnitofWork uow, Guid agentId, CancellationToken ct)
        => uow.Context.Database.ExecuteSqlRawAsync(contactedScript, [agentId], ct);

    private static readonly string resolvedScript =
        $@"UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowAnalytics)}"" SET ""{nameof(AgentWorkflowAnalytics.TotalResolved)}"" = ""{nameof(AgentWorkflowAnalytics.TotalResolved)}"" + 1 WHERE ""{nameof(AgentWorkflowAnalytics.UserId)}"" = {{0}};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowTimeAnalytics)}"" SET ""{nameof(AgentWorkflowTimeAnalytics.TotalResolved)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalResolved)}"" + 1 
        WHERE ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = {{0}} AND ""Date"" = {AnalyticsTriggerHelpers.CurrentDate};
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowAnalytics)}"" SET ""{nameof(DebtWorkflowAnalytics.TotalResolved)}"" = ""{nameof(DebtWorkflowAnalytics.TotalResolved)}"" + 1 WHERE ""{nameof(DebtWorkflowAnalytics.Id)}"" = 1;
        UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowTimeAnalytics)}"" SET ""{nameof(DebtWorkflowTimeAnalytics.TotalResolved)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalResolved)}"" + 1 
        WHERE ""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate};
        ";
    public static Task UpdateResolvedWorkflow(IUnitofWork uow, Guid agentId, CancellationToken ct)
        => uow.Context.Database.ExecuteSqlRawAsync(resolvedScript, [agentId], ct);

    private static readonly string callbackScript =
         $@"
            UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowAnalytics)}"" 
            SET ""{nameof(AgentWorkflowAnalytics.TotalCallBack)}"" = ""{nameof(AgentWorkflowAnalytics.TotalCallBack)}"" + 1
            WHERE ""{nameof(AgentWorkflowAnalytics.UserId)}"" = {{0}};

            UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(AgentWorkflowTimeAnalytics)}"" 
            SET ""{nameof(AgentWorkflowTimeAnalytics.TotalCallBack)}"" = ""{nameof(AgentWorkflowTimeAnalytics.TotalCallBack)}"" + 1
            WHERE ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = {{0}} 
            AND ""Date"" = {AnalyticsTriggerHelpers.CurrentDate};

            UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowAnalytics)}"" 
            SET ""{nameof(DebtWorkflowAnalytics.TotalCallBack)}"" = ""{nameof(DebtWorkflowAnalytics.TotalCallBack)}"" + 1
            WHERE ""{nameof(DebtWorkflowAnalytics.Id)}"" = 1;

            UPDATE {CollectionEntityConfig.DefaultSchema}.""{nameof(DebtWorkflowTimeAnalytics)}"" 
            SET ""{nameof(DebtWorkflowTimeAnalytics.TotalCallBack)}"" = ""{nameof(DebtWorkflowTimeAnalytics.TotalCallBack)}"" + 1
            WHERE ""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate};
        ";

    public static Task UpdateCallBackWorkflow(IUnitofWork uow, Guid agentId, CancellationToken ct)
    => uow.Context.Database.ExecuteSqlRawAsync(callbackScript, [agentId], ct);





}

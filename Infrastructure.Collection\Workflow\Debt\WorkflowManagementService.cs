﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Collection.Workflow.Debt.ViewModels;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Workflow.Debt;

internal sealed class WorkflowManagementService(GeneralGenericCrudVMService service, IClock clock)
{
    private readonly IUnitofWork uow = service.Uow;
    public static event EventHandler<SystemBackgroundTaskEventArgs>? StatusNotificationEvent;


    public static void RaiseEvent(object sender, SystemBackgroundTaskEventArgs data)
    {
        StatusNotificationEvent?.Invoke(sender, data);
    }

    public async Task<string> GetAvailability(Guid userId, CancellationToken ct)
    {
        var now = clock.GetCurrentInstant();
        var status = await uow.Db.OneSelectAsync(Query<AgentWorkflowAvailability>.Where(x => x.UserId == userId && x.Start <= now && !x.End.HasValue && x.Status != AgentAvailabilityStatus.SessionEnded).Select(x => new { x.Status }), ct);

        return status != null ? $"Status: {status.Status.GetDisplayName()}" : "Welcome!";
    }

    public async Task<bool> UpdateAvailability(Guid userId, AgentAvailabilityStatus status, CancellationToken ct)
    {
        var now = clock.GetCurrentInstant();
        var availability = await uow.Db.OneAsync(Query<AgentWorkflowAvailability>.Where(x => x.UserId == userId && x.Start <= now && !x.End.HasValue && x.Status != AgentAvailabilityStatus.SessionEnded).Track(), ct)
            ?? new AgentWorkflowAvailability
            {
                UserId = userId,
                Start = now,
                Status = status,
                CurrentlyAssignedCount = 0,
            };

        if (availability.Id == 0)
            uow.Db.Insert(availability);
        else
        {
            availability.Status = status;

            if (status == AgentAvailabilityStatus.SessionEnded || status == AgentAvailabilityStatus.Away)
                availability.CurrentlyAssignedCount = 0;
        }

        await uow.SaveAsync(ct);

        uow.Db.Insert(new AgentWorkflowAvailabilityStatusChangeLog
        {
            AvailabilityId = availability.Id,
            Status = status,
        });

        await uow.SaveAsync(ct);

        if (status == AgentAvailabilityStatus.SessionEnded || status == AgentAvailabilityStatus.Away)
        {
            var currentlyAssignedUsers = await uow.Db.ManySelectAsync(Query<AgentWorkflowTask>.Where(x => x.UserId == userId && !x.Opened.HasValue && !x.IsEscalated).Select(x => x.CustomerProfileId), ct);
            _ = await uow.Db.DeleteAndSaveWithFilterAsync<AgentWorkflowTask>(x => x.UserId == userId && !x.Opened.HasValue, ct);
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => currentlyAssignedUsers.Contains(x.Id), x => x.SetProperty(y => y.CurrentlyAssigned, false), ct);
        }

        return true;
    }

    public Task<int> MarkTaskAsOpened(long taskId, Guid userId, CancellationToken ct) =>
        uow.Db.UpdateAndSaveWithFilterAsync<AgentWorkflowTask>(x => x.Id == taskId && x.UserId == userId && !x.Opened.HasValue, x => x.SetProperty(y => y.Opened, clock.GetCurrentInstant()), ct);

    public ValueTask<TypedBasePageList<AgentWorkflowTaskVM>> LoadTasks(Guid userId, DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<AgentWorkflowTask>()
        {
            PrimaryCriteria = x => x.UserId == userId && !x.Removed.HasValue
        };

        return service.GetTypeBasedPagedData(spec, filterAndPage, AgentWorkflowTaskVM.Mapping, ct);
    }

    public ValueTask<TypedBasePageList<AgentWorkflowTaskLogVM>> LoadDateTasks(Guid userId, LocalDate specificDate, DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var startOfDay = specificDate.AtStartOfDayInZone(DateTimeZone.Utc).ToInstant();
        var endOfDay = specificDate.PlusDays(1).AtStartOfDayInZone(DateTimeZone.Utc).ToInstant();

        var spec = new BaseSpecification<AgentWorkflowTask>
        {
            PrimaryCriteria = x => x.UserId == userId && x.Assigned >= startOfDay && x.Assigned < endOfDay
        };
        return service.GetTypeBasedPagedData(spec, filterAndPage, AgentWorkflowTaskLogVM.Mapping, ct);
    }

    public ColumnList GetTableDefinition() => service.GetTableDefinition<AgentWorkflowTask, AgentWorkflowTaskVM>(new() { HasDateColumns = false });
    public ColumnList GetLogTableDefinition() => service.GetTableDefinition<AgentWorkflowTask, AgentWorkflowTaskLogVM>(new() { HasDateColumns = false });
}

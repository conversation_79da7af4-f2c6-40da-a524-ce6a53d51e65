﻿using System;
using System.Collections.Generic;
using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_Okereke_SystemRefresh1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_TelegramSession_Name",
                schema: "public",
                table: "TelegramSession");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "Name",
                schema: "public",
                table: "TelegramSession");

            migrationBuilder.DropColumn(
                name: "IsDisabled",
                schema: "core",
                table: "MessagingTemplate");

            migrationBuilder.DropColumn(
                name: "Disabled",
                schema: "core",
                table: "MessageProviderConfig");

            migrationBuilder.DropColumn(
                name: "LogActivity",
                schema: "core",
                table: "MessageProviderConfig");

            migrationBuilder.DropColumn(
                name: "Read",
                schema: "core",
                table: "CustomerInbox");

            migrationBuilder.DropColumn(
                name: "IsActive",
                schema: "core",
                table: "CustomerDevice");

            migrationBuilder.DropColumn(
                name: "MustChangePassword",
                schema: "public",
                table: "AspNetUsers");

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                schema: "public",
                table: "TelegramSession",
                type: "integer",
                nullable: false,
                oldClrType: typeof(long),
                oldType: "bigint")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn);

            migrationBuilder.AddColumn<Instant>(
                name: "DisabledOn",
                schema: "core",
                table: "MessagingTemplate",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Instant>(
                name: "DisabledOn",
                schema: "core",
                table: "MessageProviderConfig",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Instant>(
                name: "LogActivityOn",
                schema: "core",
                table: "MessageProviderConfig",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Instant>(
                name: "ReadOn",
                schema: "core",
                table: "CustomerInbox",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<Instant>(
                name: "MustChangePasswordOn",
                schema: "public",
                table: "AspNetUsers",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "MessageSchedule",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CronExpression = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    Frequency = table.Column<int>(type: "integer", nullable: false),
                    FrequencyNumber = table.Column<int>(type: "integer", nullable: false),
                    Days = table.Column<int>(type: "integer", nullable: false),
                    ActiveOn = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Starting = table.Column<bool>(type: "boolean", nullable: false),
                    TimeZone = table.Column<string>(type: "text", nullable: true),
                    RunCount = table.Column<int>(type: "integer", nullable: false),
                    ConfigId = table.Column<long>(type: "bigint", nullable: false),
                    Groups = table.Column<List<long>>(type: "bigint[]", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TemplateValues = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageSchedule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageSchedule_MessageConfiguration_ConfigId",
                        column: x => x.ConfigId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers",
                column: "UserName",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "SecurityStamp", "TwoFactorEnabled", "PasswordHash", "WalletPinHash", "MustChangePasswordOn", "Role" });

            migrationBuilder.CreateIndex(
                name: "IX_MessageSchedule_ConfigId",
                schema: "core",
                table: "MessageSchedule",
                column: "ConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageSchedule_Name",
                schema: "core",
                table: "MessageSchedule",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "CronExpression", "Frequency", "FrequencyNumber", "Days", "ActiveOn", "Starting", "RunCount" });
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "MessageSchedule",
                schema: "core");

            migrationBuilder.DropIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers");

            migrationBuilder.DropColumn(
                name: "DisabledOn",
                schema: "core",
                table: "MessagingTemplate");

            migrationBuilder.DropColumn(
                name: "DisabledOn",
                schema: "core",
                table: "MessageProviderConfig");

            migrationBuilder.DropColumn(
                name: "LogActivityOn",
                schema: "core",
                table: "MessageProviderConfig");

            migrationBuilder.DropColumn(
                name: "ReadOn",
                schema: "core",
                table: "CustomerInbox");

            migrationBuilder.DropColumn(
                name: "MustChangePasswordOn",
                schema: "public",
                table: "AspNetUsers");

            migrationBuilder.AlterColumn<long>(
                name: "Id",
                schema: "public",
                table: "TelegramSession",
                type: "bigint",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn);

            migrationBuilder.AddColumn<string>(
                name: "Name",
                schema: "public",
                table: "TelegramSession",
                type: "character varying(24)",
                maxLength: 24,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<bool>(
                name: "IsDisabled",
                schema: "core",
                table: "MessagingTemplate",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "Disabled",
                schema: "core",
                table: "MessageProviderConfig",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "LogActivity",
                schema: "core",
                table: "MessageProviderConfig",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "Read",
                schema: "core",
                table: "CustomerInbox",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsActive",
                schema: "core",
                table: "CustomerDevice",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "MustChangePassword",
                schema: "public",
                table: "AspNetUsers",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.CreateIndex(
                name: "IX_TelegramSession_Name",
                schema: "public",
                table: "TelegramSession",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers",
                column: "UserName",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "SecurityStamp", "TwoFactorEnabled", "PasswordHash", "WalletPinHash", "MustChangePassword", "Role" });
        }
    }
}

﻿using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Customers;

public class CustomerFlag : BaseEntityWithIdentityId<CustomerFlag>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public long FlagId { get; set; }
    public virtual CustomerFlagTemplate Flag { get; set; }
}

﻿@using LendQube.Infrastructure.Core.Authentication
@inject NavigationManager NavigationManager
@inject AdminAuthService authService

@code {
    protected override async Task OnInitializedAsync()
    {
        await authService.Logout();
        NavigationManager.NavigateTo($"account/login?returnUrl={Uri.EscapeDataString(NavigationManager.ToBaseRelativePath(NavigationManager.Uri))}", forceLoad: true);
    }
}

﻿@page "/debtworkflow/agentworkdesk"

@using LendQube.Entities.Core.Uploads
@using LendQube.Infrastructure.Collection.Workflow.Debt
@using LendQube.Infrastructure.Collection.Workflow.Debt.ViewModels
@using LendQube.Entities.Collection.Workflows.Debt
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using Radzen
@using Radzen.Blazor
@inject NavigationManager navigationManager
@inject WorkflowManagementService Service

@inherits BaseTableComponentBase

@attribute [Authorize(Policy = DebtWorkflowNavigation.DebtWorkflowDeskIndexPermission)]

<PageTitle>@Title</PageTitle>
<div class="pg-row grid grid-col-1 grid-tab-1">
	<div class="card">

		<div class="title-wrapper flex __justify-between __align-center">
			<span class="text_xl_medium">@SubTitle - @Availability</span>
		</div>
		<StatusMessage @ref="TableMessage" />

		<DataTable T="AgentWorkflowTaskVM" TableDefinition="TableDefinition" LoadData="Load" @ref="table" />

	</div>
</div>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@FormBaseTitle ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
					ModalCss="width-md">
	<BodyContent>
		<div class="form-row">
			<label class="form-label" for="Status">Status</label>
            <RadzenDropDown @bind-Value=@context.Status Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<AgentAvailabilityStatus>([AgentAvailabilityStatus.Busy]))
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Status"  Placeholder="Select status"  class="form-input"
                            FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" FilterOperator="StringFilterOperator.StartsWith" AllowFiltering="true" />
			<ValidationMessage For="() => context.Status" class="text-danger" />
		</div>
	</BodyContent>
</ModalEditComponent>



@code
{
	private DataTable<AgentWorkflowTaskVM> table;

	[SupplyParameterFromForm]
	protected UpdateAgentAvailabilityVM EditModel { get; set; }
	private string Availability { get; set; }

	protected override void OnInitialized()
	{
		Title = "Debt Workflow";
		SubTitle = "Your Work Desk";
		FormBaseTitle = "Update Availability";
		EditPermission = DebtWorkflowNavigation.DebtWorkflowDeskIndexPermission;
		WorkflowManagementService.StatusNotificationEvent += NewTaskAvailable;
	}

	protected override async Task OnInitializedAsync()
	{
		if (TableDefinition == null)
		{
			EditModel = new();
			await base.OnInitializedAsync();

			Availability = await Service.GetAvailability(UserId, Cancel);
			TableDefinition.HasEdit = false;

			AddTopButton(EditPermission, new TopActionButton("Update Status", Icon: "refresh-cw", ModalName: EditModalName));


			AddRowButton(EditPermission, new RowActionButton("View", Icon: "eye", Action: async (object row) =>
			{
				var task = row as AgentWorkflowTaskVM;
				_ = await Service.MarkTaskAsOpened(task.Id, UserId, Cancel);
				navigationManager.NavigateTo($"debtworkflow/agentworkdesk/{task.CustomerProfileId}/{task.Id}");
			}));
		}
	}

	protected override ColumnList GetTableDefinition() => Service.GetTableDefinition();

	private ValueTask<TypedBasePageList<AgentWorkflowTaskVM>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
	{
		CloseMessage();
		return Service.LoadTasks(UserId, filterAndPage, ct);
	}

	public ValueTask SubmitEdit() => BaseSaveEdit(async () =>
	{
		var result = await Service.UpdateAvailability(UserId, EditModel.Status, Cancel);

		if (result)
		{
			Availability =  await Service.GetAvailability(UserId, Cancel);
			StateHasChanged();
		}

		return result;
	},
	() =>
	{
		EditModel = new();
		return table.Refresh();
	});


	public void NewTaskAvailable(object sender, SystemBackgroundTaskEventArgs e)
	{
		if (e.Owner != UserId.ToString())
			return;

		_ = InvokeAsync(async () =>
		{
			await table.Refresh();
			TableMessage.Set(e.Successful, e.Message);
			Availability = await Service.GetAvailability(UserId, Cancel);
			StateHasChanged();
		});
	}

	public override void Dispose()
	{
		WorkflowManagementService.StatusNotificationEvent -= NewTaskAvailable;
	}
}

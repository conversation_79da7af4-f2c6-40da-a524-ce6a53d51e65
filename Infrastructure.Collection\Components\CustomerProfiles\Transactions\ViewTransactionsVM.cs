﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.Transactions;

public class ViewTransactionsVM : IBaseEntityWithSystemStamp
{
    public static readonly Expression<Func<Transaction, ViewTransactionsVM>> Mapping = data => new()
    {
        Id = data.Id,
        Email = data.Profile.Email,
        Name = data.Profile.FullName,
        ProviderReference = data.ProviderReference,
        Purpose = data.Purpose,
        CurrencyCode = data.CurrencyCode,
        UnitAmount = data.UnitAmount,
        TotalAmountPayable = data.TotalAmountPayable,
        TotalAmountPaid = data.TotalAmountPaid,
        Provider = data.Provider,
        Status = data.Status,
        CreatedDate = data.CreatedDate,
        LastModifiedDate = data.LastModifiedDate,
    };
    public string Id { get; set; }
    public string Email { get; set; }
    public string Name { get; set; }
    public string ProviderReference { get; set; }
    public string Purpose { get; set; }
    public string CurrencyCode { get; set; }
    public decimal UnitAmount { get; set; }
    public decimal TotalAmountPayable { get; set; }
    public decimal TotalAmountPaid { get; set; }
    public PaymentProvider Provider { get; set; }
    public TransactionStatus Status { get; set; }
}

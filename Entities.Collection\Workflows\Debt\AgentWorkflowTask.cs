﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;

namespace LendQube.Entities.Collection.Workflows.Debt;

public class AgentWorkflowTask : BaseEntityWithIdentityId<AgentWorkflowTask>, IEntityHasNotifyTrigger
{
    public long? AvailabilityId { get; set; }
    public virtual AgentWorkflowAvailability? Availability { get; set; }
    public long DebtSegmentId { get; set; }
    public virtual DebtSegment DebtSegment { get; set; }
    public Guid UserId { get; set; }
    public virtual ApplicationUser User { get; set; }
    [DbGuid]
    public string CustomerProfileId { get; set; }
    public virtual CustomerProfile CustomerProfile { get; set; }
    public long? EscalatedTaskId { get; set; }
    public Guid? EscalatedToUserId { get; set; }
    public virtual ApplicationUser? EscalatedToUser { get; set; }
    public Guid? EscalatedByUserId { get; set; }
    public virtual ApplicationUser? EscalatedByUser { get; set; }
    public string EscalationReason { get; set; }
    public List<WorkflowTask> Tasks { get; set; } = [];
    public Instant Assigned { get; set; }
    public Instant? Opened { get; set; }
    public Instant? Removed { get; set; }
    public bool IsEscalated { get; set; }
    public DateOnly? NextCallbackDate { get; set; }


    public string Schema => CollectionEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => string.Empty;

    public override void Configure(EntityTypeBuilder<AgentWorkflowTask> builder)
    {
        base.Configure(builder);
        builder.OwnsMany(x => x.Tasks, x => x.ToJson());
    }
}

public class WorkflowTask
{
    public Instant When { get; set; }
    public WorkflowTaskAction? Action { get; set; }
    public long? NoteId { get; set; }
    public long? PtpId { get; set; }
    public string? PaymentId { get; set; }
    public decimal? Amount { get; set; }
    public DateOnly? NextCallbackDate { get; set; }
}

public enum WorkflowTaskAction
{
    NoAction,
    NoContact,
    ContactMade,
    NotResolved,
    Resolved,
    PaymentCollected,
    PaymentFailed,
    PTPSetup,
    ScheduleSetup,
    Escalated,
    CallBack
}


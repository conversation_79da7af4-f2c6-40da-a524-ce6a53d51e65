﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Collection.Reporting.ViewModel;
using LendQube.Infrastructure.Core.Extensions;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.Reporting;

partial class BusinessReportService
{
    private void BuildAllTransactionsReport(SystemReport data)
    {
        var startDate = data.StartDate ?? "28-09-2024".GetInstantFromString();
        var endDate = data.EndDate ?? clock.GetCurrentInstant();

        var reportQuery = PrepareAllTransactions(startDate, endDate);

        queries.Add(BusinessReportTypesEnum.AllTransactions.ToString().SplitOnUpper(), reportQuery);
    }

    private IQueryable<AllTransactionsReportVM> PrepareAllTransactions(Instant startDate, Instant endDate)
    {
        var query = uow.Db.Queryable<PlacementTransaction>().Where(x => x.CreatedDate >= startDate && x.CreatedDate <= endDate && x.PaymentProvider != PaymentProvider.Discount).Select(data => new AllTransactionsReportVM()
        {
            AccountNumber = data.Placement.SourceAccountNumber,
            PaidAt = data.CreatedDate.Value.InZone(timeZone).Date,
            Amount = data.AmountPaid,
            TransactionType = data.PaymentType,
            TransactionReference = data.TransactionId,
            Notes = data.PaymentProvider.GetDisplayName()
        });

        return query;
    }

    private static void SaveAllTransactionsReport(ExcelWorksheet worksheet, IQueryable<object> reportData)
    {
        var queryData = reportData.Cast<AllTransactionsReportVM>();
        worksheet.Cells["A1"].LoadFromCollection(queryData, true);
    }
}


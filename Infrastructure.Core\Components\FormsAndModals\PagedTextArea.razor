﻿@using LendQube.Entities.Core.Extensions
@using Microsoft.AspNetCore.Components.Forms
@using System.Linq.Expressions
@typeparam TModel

<textarea value="@Text" id="@TextAreaId" @oninput="OnTextChanged" @onchange="OnLoseFocus" aria-required="true" rows="10" cols="50" placeholder="@Placeholder" readonly="@IsReadOnly" style="resize: none;"></textarea>

@if (!IsReadOnly)
{
    <small>Words written: @WordsWritten / @WordsPerPage</small>
    <small>Current Page: @CurrentPage</small>
}

@code {
    [Parameter, EditorRequired] public TModel Model { get; set; }
    [Parameter, EditorRequired] public Expression<Func<TModel, string>> Property { get; set; }
    [Parameter] public string Placeholder { get; set; } = "Enter text here ...";
    [Parameter] public bool IsReadOnly { get; set; }
    [Parameter] public EventCallback OnChange { get; set; }
    [Parameter] public string TextAreaId { get; set; } = "TextTemplate";

    private const int WordsPerPage = 140;
    private System.Reflection.PropertyInfo propertyInfo;
    private Func<TModel, string> compiledProperty;
    private Action<string> updatePropertyValue;

    private int WordsWritten { get; set; }
    private string Text { get; set; }
    private int CurrentPage => Math.Max(1, (WordsWritten + WordsPerPage - 1) / WordsPerPage);

    protected override void OnParametersSet()
    {
        if (Property == null || Model == null)
        {
            throw new ArgumentException("Model and Property must be provided.");
        }

        compiledProperty = Property.Compile();
        propertyInfo = (Property.Body as MemberExpression)?.Member as System.Reflection.PropertyInfo
            ?? throw new InvalidOperationException("Property expression is not valid.");
        updatePropertyValue = value => propertyInfo.SetValue(Model, value);

        var text = compiledProperty(Model) ?? string.Empty;
        Text = text;
        UpdateWordList(text);
    }

    private void OnTextChanged(ChangeEventArgs e)
    {
        var newText = e.Value?.ToString() ?? string.Empty;

        UpdateWordList(newText);
    }

    private async Task OnLoseFocus(ChangeEventArgs e)
    {
        updatePropertyValue(e.Value?.ToString() ?? string.Empty);

        if(OnChange.HasDelegate)
        {
            await OnChange.InvokeAsync();
        }
    }

    private void UpdateWordList(string newText)
    {
        WordsWritten = newText.GetWordsCount();

        InvokeAsync(StateHasChanged);
    }
}

﻿using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using Microsoft.AspNetCore.DataProtection.EntityFrameworkCore;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.DbContexts;
public sealed class AppDbContext(DbContextOptions<AppDbContext> options) : IdentityDbContext<
        ApplicationUser, ApplicationRole, Guid,
        ApplicationUserClaim, ApplicationUserRole, ApplicationUserLogin,
        ApplicationRoleClaim, ApplicationUserToken>(options), IDataProtectionKeyContext
{
    protected override void OnModelCreating(ModelBuilder builder)
    {
        base.OnModelCreating(builder);

        builder.HasSequence<long>(CoreEntityConfig.HiloSequenceName)
            .IncrementsBy(100);

        builder.HasSequence<long>(CoreEntityConfig.HiloSequenceName, CoreEntityConfig.DefaultSchema)
            .IncrementsBy(100);

        builder.SetupEntities(typeof(AppDbContext).Assembly.GetReferencedAssemblies().AsParallel().Where(x => x.Name != null && x.Name.StartsWith(CoreEntityConfig.EntitiesProject)));

        builder.SetupContexts();

        builder.CreateModels();

        builder.UseOpenIddict<Guid>();
    }

    //All classes decorated by IAddToDbContext, IEntityTypeConfiguration, IEntityHasEnum, IEntityHasNotifyTrigger are added automatically

    public DbSet<DataProtectionKey> DataProtectionKeys { get; set; }
}
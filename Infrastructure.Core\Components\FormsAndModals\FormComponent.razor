﻿@typeparam TModel
@using Microsoft.AspNetCore.Components.Forms

<div class="title-wrapper flex __justify-between __align-center">
    <span class="text_xl_medium">@Title</span>
</div>
<StatusMessage Message="FormMessage" />
<EditForm method="post" class="form mx-auto" Model="Model" OnValidSubmit="HandleSubmit" FormName="@FormName">
    <DataAnnotationsValidator />
	@BodyContent(Model)
	<div class="button-row">
		<button type="submit" id="@(FormName)_Button" class="btn btn--primary" disabled="@IsSubmitting">
			Save
			@if (IsSubmitting)
			{
				<span> &nbsp;</span>
				<i class="fa fa-spinner fa-spin"></i>
			}
		</button>
	</div>
</EditForm>

@code {
	[Parameter, EditorRequired] public string Title { get; set; } = "Form Title";
	[Parameter, EditorRequired] public TModel Model { get; set; }
	[Parameter, EditorRequired] public Func<Task> OnValidSubmit { get; set; }
	[Parameter, EditorRequired] public RenderFragment<TModel> BodyContent { get; set; }
	[Parameter, EditorRequired] public StatusMessageBuilder FormMessage { get; set; }
	[Parameter, EditorRequired] public string FormName { get; set; }

	private bool IsSubmitting { get; set; } = false;

	private async Task HandleSubmit()
	{
		if (IsSubmitting)
			return;
		FormMessage?.Close();
		IsSubmitting = true;
		try
		{
			await OnValidSubmit();
		}
		finally
		{
			IsSubmitting = false;
		}
	}
}
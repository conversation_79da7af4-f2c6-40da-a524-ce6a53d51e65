﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class TwilioWhatsappProvider : AbstractMessageProvider
{
    private readonly DefaultAppConfig config;
    protected override MessageChannel SupportedChannel => MessageChannel.WhatsApp;

    public const string Name = "Twilio WhatsApp";
    public TwilioWhatsappProvider(IUnitofWork uow, DefaultAppConfig config) : base(uow)
    {
        this.config = config;

        if (config.Twilio != null)
        {
            TwilioClient.Init(config.Twilio.Sid, config.Twilio.Token);
        }

        Config = MessagingCompiledQueries.GetProviderConfig(uow, Name) ?? new ProviderConfigVM { Disabled = true };
    }

    public override ProviderConfigVM Config { get; }
    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        Dictionary<long, MessageStatus> results = [];
        var leadingMessage = messages[0];
        LogActivity(leadingMessage, MessageStatus.Processing, Name);
        var from = new PhoneNumber(config.Twilio.WhatsApp);

        await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count, CancellationToken = ct }, async (message, ct) =>
        {
            await Parallel.ForEachAsync(message.Data.Select((x, i) => (Value: x, Index: i)), new ParallelOptions { MaxDegreeOfParallelism = message.Data.Count, CancellationToken = ct }, async (recipient, ct) =>
            {
                await Parallel.ForEachAsync(recipient.Value.PhoneNumbers, new ParallelOptions { MaxDegreeOfParallelism = recipient.Value.PhoneNumbers.Count, CancellationToken = ct }, async (phoneNumber, ct) =>
                {
                    var result = await MessageResource.CreateAsync(new CreateMessageOptions(new PhoneNumber($"whatsapp:{phoneNumber.Code}{phoneNumber.Number}"))
                    {
                        From = from,
                        Body = recipient.Value.TextTemplate ?? message.TextTemplate,
                        StatusCallback = new Uri(string.Format(config.Url.WebHook, "twilo/receivemessagestatus"))
                    });

                    results[message.MessageId + recipient.Index] = results.GetValueOrDefault(message.MessageId + recipient.Index) == MessageStatus.Sent ? MessageStatus.Sent : (result.Status != MessageResource.StatusEnum.Failed).ToMessageStatus();
                });
            });
        });

        var status = results.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, results, null, null, ct);
        return status;
    }
}

﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.Core.Database.DataPager;

public sealed class DataFilterAndPage
{
    public int Page { get; set; }//requested page number
    public int PageSize { get; set; } //pageSize
    [ValidString(ValidStringRule.NoScriptTag)]
    public string TextFilter { get; set; } //filtering text
    [ValidString(ValidStringRule.NoScriptTag)]
    public string DropDownFilter { get; set; } //filtering text
    [ValidString(ValidStringRule.NoScriptTag)]
    public string AllFilter { get; set; } //filtering by json filter
    public IEnumerable<ComplexFilter> ComplexFilter { get; set; }
    [JsonIgnore]
    public bool IsCacheable { get; set; }
    [ValidString(ValidStringRule.OnlyTextAndNumber)]
    public string OrderByColumn { get; set; } = nameof(IBaseEntityWithSystemStamp.CreatedDate);
    [ValidString(ValidStringRule.OnlyTextAndNumber)]
    public string OrderByColumnVM { get; set; }
    public DataOrderDirection OrderByDirection { get; set; } = DataOrderDirection.DESC;
    [JsonIgnore]
    public bool AsyncNotSupportedDataSource { get; set; }
    [JsonIgnore]
    public bool UseEmbeddedQuery { get; set; }
}

public enum DataOrderDirection
{
    ASC,
    DESC
}

public enum FilterCondition
{
    Or,
    And
}

public sealed class ComplexFilter
{
    public int RuleId { get; set; } = -1;
    public string UserTimeZone { get; set; }
    public string ColumnName { get; set; }
    public string ColumnDisplayName { get; set; }
    public object Value { get; set; }
    public FilterCondition? Condition { get; set; }
    public bool ShouldHaveCondition { get; set; }
    public bool RequiresValue { get; set; } = true;
    public bool IsValid => RuleId > -1 && (!RequiresValue || Value != null) && !string.IsNullOrEmpty(ColumnName) && !string.IsNullOrEmpty(ColumnDisplayName) && (!ShouldHaveCondition || Condition.HasValue);

    public string GetTableName()
    {
        var split = ColumnName.Split('.');
        if(split.Length > 1 )
            return split[0];

        return string.Empty;
    }

    public string GetTableColumnName()
    {
        var split = ColumnName.Split('.');
        if (split.Length > 1)
            return split[1];

        return string.Empty;
    }
}
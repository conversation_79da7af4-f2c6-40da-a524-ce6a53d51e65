﻿using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Core.Messaging;

public class MessagingGroup : BaseEntityWithIdentityId<MessagingGroup>
{
    [ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string Name { get; set; }
    public virtual ICollection<MessagingGroupEntry> Directory { get; set; }
    public virtual MessagingGroupQuery QueryDirectory { get; set; }
    public virtual ICollection<MessageConfiguration> Configs { get; set; }
    public virtual ICollection<MessageConfigurationMessagingGroup> ConfigGroups { get; set; }

    public override void Configure(EntityTypeBuilder<MessagingGroup> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Name).IsUnique();
    }
}

public class MessagingGroupEntry : BaseEntityWithIdentityId<MessagingGroupEntry>
{
    public long MessagingGroupId { get; set; }
    public virtual MessagingGroup Group { get; set; }
    public string Name { get; set; }
    public List<PhoneNumber> PhoneNumbers { get; set; }
    public List<string> Emails { get; set; }

    public override void Configure(EntityTypeBuilder<MessagingGroupEntry> builder)
    {
        base.Configure(builder);
        builder.OwnsMany(d => d.PhoneNumbers, c => c.ToJson());
    }
}


public class MessagingGroupQuery : BaseEntityWithIdentityId<MessagingGroupQuery>
{
    public long MessagingGroupId { get; set; }
    public virtual MessagingGroup Group { get; set; }
    public string Description { get; set; }
    public string Query { get; set; }
    public string SenderQuery { get; set; }
    [Column(TypeName = "jsonb")]
    public string BuildingBlock { get; set; }
}
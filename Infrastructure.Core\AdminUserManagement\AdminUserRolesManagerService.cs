﻿using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System.Data;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Core.AdminUserManagement;

public sealed class AdminUserRolesManagerService(UserManager<ApplicationUser> userManager, GenericEntityCrudVMService<ApplicationRole, UserRoleVM> crudService) : BaseSpecification<ApplicationRole>, IDisposable
{
    public List<ApplicationRole> GetRoles() => crudService.Db.Many(Query<ApplicationRole>.Where(x => x.Id != SystemRoleConfig.SuperAdminRoleId && x.Name != SystemRoleConfig.CustomerRole));

    public async Task<GenericResponseVM> SaveRoles(string currentUser, ApplicationAdminUserVM user, IEnumerable<UserRoleVM> rolesToAssign, IEnumerable<UserRoleVM> rolesToRemove)
    {
        if(user.UserName == currentUser)
        {
            return new GenericResponseVM { Message = "You cannot modify your roles. Please contact admin" };
        }

        if (user.Email == SystemRoleConfig.SuperAdminEmail)
        {
            return new GenericResponseVM { Message = "SuperAdmin roles cannot be modified" };
        }

        if (user.Email != SystemRoleConfig.SuperAdminEmail && (rolesToAssign.Any(y => y.Role == SystemRoleConfig.SuperAdminRole) || rolesToRemove.Any(y => y.Role == SystemRoleConfig.SuperAdminRole)))
        {
            return new GenericResponseVM { Message = "SuperAdmin role can only be assigned to SuperAdmin" };
        }

        if (rolesToAssign.Any(y => y.Role == SystemRoleConfig.CustomerRole) || rolesToRemove.Any(y => y.Role == SystemRoleConfig.CustomerRole))
        {
            return new GenericResponseVM { Message = "Customer role cannot be assigned" };
        }

        var applicationUser = await userManager.FindByIdAsync(user.Id.ToString());
        IdentityResult result = null;
        if (!rolesToAssign.IsNullOrEmpty())
            result = await userManager.AddToRolesAsync(applicationUser, rolesToAssign.Select(x => x.Role));

        if (!rolesToRemove.IsNullOrEmpty() && (result == null || result.Succeeded))
            result = await userManager.RemoveFromRolesAsync(applicationUser, rolesToRemove.Select(x => x.Role));


        return new GenericResponseVM { Successful = result.Succeeded, Message = string.Join(",", result?.Errors?.Select(x => x.Description)) };
    }

    public ColumnList GetTableDefinition() => crudService.GetTableDefinition(new() { HasDateColumns = false });

    public ValueTask<TypedBasePageList<UserRoleVM>> GetTypeBasedPagedData(ApplicationAdminUserVM user, DataFilterAndPage pagedParam, CancellationToken ct)
    {
        PrimaryCriteria = x => x.Name != SystemRoleConfig.CustomerRole;
        if (!string.IsNullOrEmpty(pagedParam.TextFilter))
        {
            pagedParam.TextFilter = $"%{pagedParam.TextFilter}%";
            PrimaryCriteria = PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Name, pagedParam.TextFilter)
            || EF.Functions.ILike(x.Description, pagedParam.TextFilter));
        }

        TagOrigination();

        pagedParam.OrderByColumnVM = nameof(UserRoleVM.Assigned);
        return crudService.GetTypeBasedPagedData(this, pagedParam, x => new UserRoleVM { Assigned = x.UserRoles.Any(y => y.UserId == user.Id && y.RoleId == x.Id), Role = x.Name, Description = x.Description }, ct: ct);
    }

    public void Dispose()
    {
        userManager.Dispose();
        GC.SuppressFinalize(this);
    }
}

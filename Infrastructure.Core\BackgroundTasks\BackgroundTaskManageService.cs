﻿using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericSpecification;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.BackgroundTasks;

public sealed class BackgroundTaskManageService(GenericSpecificationService<BackgroundTaskEventControl> service, Func<BackgroundEventSource, AbstractBackgroundTaskManager> managerAccessor)
{
    public Task<bool> Delete(long id, CancellationToken ct) => service.CrudService.Delete(x => x.Id == id, ct);

    public Task<bool> DeleteAll(CancellationToken ct) => service.CrudService.Db.TruncateTable<BackgroundTaskEventControl>(ct);

    public async Task StopAll(CancellationToken ct)
    {
        var controls = await service.CrudService.Db.ManyAsync(Query<BackgroundTaskEventControl>.Where(x => x.Status == BackgroundControlState.Running || x.Status == BackgroundControlState.Idle), ct);
        if (controls.IsNullOrEmpty())
            return;

        await Parallel.ForEachAsync(controls, new ParallelOptions { MaxDegreeOfParallelism = controls.Count, CancellationToken = ct }, async (item, ct) =>
        {
            var manager = managerAccessor(item.Source);
            if (manager != null && manager.IsActive(item.Source))
                await manager.StopBackgroundTask(item, ct);
        });

        await service.CrudService.UpdateWithFilter(x => x.Status == BackgroundControlState.Idle, x => x.SetProperty(y => y.Status, BackgroundControlState.Stopped), ct);
        await service.CrudService.UpdateWithFilter(x => x.Status == BackgroundControlState.Running, x => x.SetProperty(y => y.Status, BackgroundControlState.Stopping), ct);
    }

    public async Task StartAll(CancellationToken ct)
    {
        var sources = Enum.GetValues(typeof(BackgroundEventSource)).Cast<BackgroundEventSource>().ToList();

        await Parallel.ForEachAsync(sources, new ParallelOptions { MaxDegreeOfParallelism = sources.Count, CancellationToken = ct }, async (source, ct) =>
        {
            var manager = managerAccessor(source);
            if (manager != null && manager.IsActive(source))
            {
                foreach (var item in manager.AllowedTasks)
                {
                    await StartBackgroundTaskControl(source, item, manager, ct);
                }
            }
        });
    }

    public Task<bool> StartSingle(BackgroundEventSource source, BackgroundTask key, CancellationToken ct)
    {
        var manager = managerAccessor(source);
        if (manager != null && manager.IsActive(source))
            return StartBackgroundTaskControl(source, key, manager, ct);

        return Task.FromResult(false);
    }

    public async Task<(BackgroundEventSource, BackgroundTask, bool)> StopSingle(long id, CancellationToken ct)
    {
        var control = await service.CrudService.Get(id, ct);
        if (control != null)
        {
            var manager = managerAccessor(control.Source);
            if (manager != null && manager.IsActive(control.Source))
                await manager.StopBackgroundTask(control, ct);

            return (control.Source, control.Event, true);
        }
        return (control.Source, control.Event, false);
    }

    private async Task<bool> StartBackgroundTaskControl(BackgroundEventSource source, BackgroundTask key, AbstractBackgroundTaskManager manager, CancellationToken ct)
    {
        _ = await service.CrudService.UpdateWithFilter(x => x.Source == source && x.Event == key
        && (x.Status == BackgroundControlState.Stopped || x.Status == BackgroundControlState.Stopping), x => x.SetProperty(y => y.Status, BackgroundControlState.Idle), ct);

        await manager.StartBackgroundTask(source, key, ct);

        return true;
    }

    public ColumnList GetTableDefinition() => service.CrudService.GetTableDefinition();

    public ValueTask<TypedBasePageList<BackgroundTaskEventControl>> GetTypeBasedPagedData(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        service.PrimaryCriteria = null;
        return service.CrudService.GetTypeBasedPagedData(service, filterAndPage, ct);
    }
}

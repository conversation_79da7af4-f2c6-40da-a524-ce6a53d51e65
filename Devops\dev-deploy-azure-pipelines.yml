# Silicon Management Company - LendQube Deployment Pipeline
# This file tells Azure DevOps how to build and deploy LendQube Admin and Background Service
# It runs only when manually triggered in Azure DevOps

trigger: none
  
pool:
  name: Silicon Pipelines
  demands:
   - agent.name -equals SiliconLinuxAgent

variables:
  buildConfiguration: 'Release'  # Build optimized version for production
  dotnetVersion: '9.0.x'  # Version of .NET to use
  
stages:
- stage: Build
  displayName: 'Build Applications'
  jobs:
  - job: BuildJob
    displayName: 'Build All Projects'
    steps:
    # Step 1: Install the correct version of .NET
    - task: UseDotNet@2
      displayName: 'Install .NET $(dotnetVersion)'
      inputs:
        packageType: sdk
        version: $(dotnetVersion)

    # Step 2: Package Admin website for deployment
    - task: DotNetCoreCLI@2
      displayName: 'Build And Publish Admin Website'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: '**/Web.Admin.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/Web.Admin'
        zipAfterPublish: true  # Compress files for faster transfer

    # Step 3: Package Background Service for deployment
    - task: DotNetCoreCLI@2
      displayName: 'Build And Publish Background Service'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: '**/Web.BackgroundService.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/Web.BackgroundService'
        zipAfterPublish: true  # Compress files for faster transfer

    # Step 4: Save packaged files for deployment stage
    - task: PublishBuildArtifacts@1
      displayName: 'Save Packaged Applications'
      inputs:
        PathtoPublish: '$(Build.ArtifactStagingDirectory)'
        ArtifactName: 'drop'
        publishLocation: 'Container'
        
- stage: Deploy
  displayName: 'Deploy to Production Server'
  dependsOn: Build
  condition: succeeded()  # Only deploy if build was successful
  jobs:
  - deployment: DeployToProduction
    displayName: 'Deploy to Production Server'
    environment: 'Production'  # Requires approval in Azure DevOps
    strategy:
      runOnce:
        deploy:
          steps:          
          # Step 1: Download the packaged applications
          - task: DownloadBuildArtifacts@1
            displayName: 'Download Packaged Applications'
            inputs:
              buildType: 'current'
              downloadType: 'single'
              artifactName: 'drop'
              downloadPath: '$(System.ArtifactsDirectory)'

          # Step 2: Check .NET version on server
          - task: SSH@0
            displayName: 'Verify .NET Version'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              readyTimeout: '60000'
              runOptions: 'inline'
              inline: |
                echo "Checking .NET version on server..."
                if ! dotnet --version | grep -q "^9\."; then
                  echo "ERROR: .NET 9.0 is not installed on the server"
                  echo "Current version: $(dotnet --version)"
                  exit 1
                else
                  echo ".NET version is compatible: $(dotnet --version)"
                fi

          # Step 3: Deploy Admin Website
          # Note: SSH connection 'LendQube-Production-Server' must be configured in Azure DevOps
          - task: SSH@0
            displayName: 'Deploy Admin Website to Server'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              runOptions: 'inline'
              inline: |
                # Stop the admin website to prevent file conflicts
                sudo systemctl stop lendqube-admin || echo "Service not running, continuing..."
                
                # Preserve production settings
                if [ -f "/var/www/html/admin/AppSettings/appsettings.Production.json" ]; then
                  echo "Backing up appsettings.Production.json..."
                  cp /var/www/html/admin/AppSettings/appsettings.Production.json /tmp/appsettings.Production.json.bak
                else
                  echo "appsettings.Production.json not found, will create from Development settings..."
                fi
                
                # Create a backup of current website (just in case we need to rollback)
                if [ -d "/var/www/html/admin" ]; then
                  sudo cp -r /var/www/html/admin /var/www/html/admin_backup_$(date +%Y%m%d_%H%M%S)
                fi
                
                # Clean the deployment directory
                rm -rf /var/www/html/admin/*
                
                # Unpack the new website files
                unzip -o $(System.ArtifactsDirectory)/drop/Web.Admin/*.zip -d /var/www/html/admin
                
                # Restore or create production settings
                if [ -f "/tmp/appsettings.Production.json.bak" ]; then
                  echo "Restoring appsettings.Production.json..."
                  mv /tmp/appsettings.Production.json.bak /var/www/html/admin/AppSettings/appsettings.Production.json
                else
                  echo "Creating appsettings.Production.json from Development settings..."
                  cp /var/www/html/admin/AppSettings/appsettings.Development.json /var/www/html/admin/AppSettings/appsettings.Production.json
                fi
                
                # Set correct ownership (silicon user runs the service)
                chown -R silicon:silicon /var/www/html/admin
                
                # Start the admin website
                sudo systemctl start lendqube-admin
                
                # Check if it started successfully
                sleep 5
                if systemctl is-active --quiet lendqube-admin; then
                  echo " Admin website started successfully"
                else
                  echo "ERROR: Admin website failed to start"
                  journalctl -u lendqube-admin -n 50 --no-pager
                  exit 1
                fi

          # Step 4: Deploy Background Service
          - task: SSH@0
            displayName: 'Deploy Background Service to Server'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              runOptions: 'inline'
              inline: |
                # Stop the background service to prevent file conflicts
                sudo systemctl stop lendqube-background || echo "Background service not running, continuing..."

                # Preserve production settings for background service
                if [ -f "/var/www/html/background/AppSettings/appsettings.Production.json" ]; then
                  echo "Backing up background service appsettings.Production.json..."
                  cp /var/www/html/background/AppSettings/appsettings.Production.json /tmp/background-appsettings.Production.json.bak
                else
                  echo "Background service appsettings.Production.json not found, will create from Development settings..."
                fi

                # Create a backup of current background service (just in case we need to rollback)
                if [ -d "/var/www/html/background" ]; then
                  sudo cp -r /var/www/html/background /var/www/html/background_backup_$(date +%Y%m%d_%H%M%S)
                fi

                # Clean the background service deployment directory
                rm -rf /var/www/html/background/*

                # Unpack the new background service files
                unzip -o $(System.ArtifactsDirectory)/drop/Web.BackgroundService/*.zip -d /var/www/html/background

                # Restore or create production settings for background service
                if [ -f "/tmp/background-appsettings.Production.json.bak" ]; then
                  echo "Restoring background service appsettings.Production.json..."
                  mv /tmp/background-appsettings.Production.json.bak /var/www/html/background/AppSettings/appsettings.Production.json
                else
                  echo "Creating background service appsettings.Production.json from Development settings..."
                  cp /var/www/html/background/AppSettings/appsettings.Development.json /var/www/html/background/AppSettings/appsettings.Production.json
                fi

                # Set correct ownership (silicon user runs the service)
                chown -R silicon:silicon /var/www/html/background

                # Start the background service
                sudo systemctl start lendqube-background

                # Check if it started successfully
                sleep 5
                if systemctl is-active --quiet lendqube-background; then
                  echo " Background service started successfully"
                else
                  echo "ERROR: Background service failed to start"
                  journalctl -u lendqube-background -n 50 --no-pager
                  exit 1
                fi

          # Step 5: Clean up old backups (keep only last 5)
          - task: SSH@0
            displayName: 'Clean Up Old Backups'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              runOptions: 'inline'
              inline: |
                echo "Cleaning up old backups..."
                # Keep only the 5 most recent admin backups
                ls -t /var/www/html/admin_backup_* 2>/dev/null | tail -n +6 | xargs -r rm -rf
                # Keep only the 5 most recent background service backups
                ls -t /var/www/html/background_backup_* 2>/dev/null | tail -n +6 | xargs -r rm -rf
                echo "Backup cleanup completed"

          # Step 6: Deployment Summary
          - task: SSH@0
            displayName: 'Deployment Summary'
            inputs:
              sshEndpoint: 'LendQube-Production-Server'
              runOptions: 'inline'
              inline: |
                echo " Silicon Management Company - LendQube Deployment Summary"
                echo "============================================================"
                echo "Admin Website Status: $(systemctl is-active lendqube-admin)"
                echo "Background Service Status: $(systemctl is-active lendqube-background)"
                echo "Deployment completed at: $(date)"
                echo "============================================================"

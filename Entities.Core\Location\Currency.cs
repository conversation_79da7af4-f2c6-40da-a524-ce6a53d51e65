﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Entities.Core.Location;

public class Currency : BaseEntityWithIdentityId<Currency>
{
    [Required]
    public long CountryId { get; set; }
    public virtual Country Country { get; set; }
    [Required, ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string Name { get; set; }
    [Required, MaxLength(3), ValidString(ValidStringRule.OnlyText)]
    public string Code { get; set; }
    [Required, MaxLength(1)]
    public string Symbol { get; set; }

    public override void Configure(EntityTypeBuilder<Currency> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.Code).IsUnique();
        builder.Property(x => x.Code).IsFixedLength();
        builder.HasIndex(x => x.Symbol).IsUnique();
        builder.Property(x => x.Symbol).IsFixedLength();
    }
}

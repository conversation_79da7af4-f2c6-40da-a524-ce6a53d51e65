﻿using System.Linq.Expressions;
using LendQube.Entities.Core.Location;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using Microsoft.AspNetCore.Mvc;

namespace LendQube.Web.Api.Controllers.Location;

[Route(ApiConstants.URL)]
public class CountriesController(IUnitofWork uow) : ApiControllerBase
{

    [HttpGet("getcountries")]
    public async Task<List<CountryVM>> GetAll(CancellationToken ct) =>  await uow.Db.ManySelectAsync(Query<Country, CountryVM>.Select(CountryVM.Mapping), ct);
}

public class CountryVM
{
    public static readonly Expression<Func<Country, CountryVM>> Mapping = data => new CountryVM
    {
        Code = data.Code,
        Name = data.Name,
        ImageUrl = data.ImageUrl,
        PhoneCode = data.PhoneCode,
    };
    public string Code { get; set; }
    public string Name { get; set; }
    public string ImageUrl { get; set; }
    public string PhoneCode { get; set; }
}
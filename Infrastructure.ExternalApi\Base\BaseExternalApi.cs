﻿using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.SerializersAndConverters;
using LendQube.Infrastructure.Core.SerializersAndConverters.CaseInsensitiveSerializer;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.ExternalApi.ViewModels;
using System.Net;
using System.Net.Http.Json;
using System.Runtime.CompilerServices;
using System.Text;

namespace LendQube.Infrastructure.ExternalApi.Base;

internal abstract class BaseExternalApi<ET>(HttpClient httpClient, ILogManager<ET> logger) where ET : class
{
    protected virtual System.Text.Json.JsonSerializerOptions SerializerOptions => JsonOptions.CaseInsensitive;
    protected virtual IReadOnlyList<HttpStatusCode?> AuthenticationRequiredStatus => [HttpStatusCode.Unauthorized];
    
    protected virtual Task ResetAccessToken(CancellationToken ct) => Task.CompletedTask;

    protected virtual void CreateRequestBody(HttpRequestMessage request, object body)
    {
        if (body is null)
            return;

        request.Content = new StringContent(JsonSerializer.ToJsonString(body, SerializerOptions), Encoding.UTF8, HttpHeaderHelper.JsonHeader);
    }

    protected virtual Task AddRequestAuthentication(HttpRequestMessage request, CancellationToken ct) => Task.CompletedTask;

    protected async Task<ExternalApiResult<T>> ProcessRequest<T>(HttpMethod method, string relativeUrl, CancellationToken ct, object requestBody = null, bool returnStringContent = false, Action<HttpRequestMessage> buildRequest = null, [CallerMemberName] string callerMemberName = "")
    {
        try
        {
            using var request = new HttpRequestMessage(method, relativeUrl);
            await AddRequestAuthentication(request, ct);
            CreateRequestBody(request, requestBody);

            buildRequest?.Invoke(request);
            using var response = await httpClient.SendAsync(request, ct);

            return await HandleResponse<T>(response, ct, requestBody ?? relativeUrl, returnStringContent, callerMemberName);
        }
        catch (Exception ex)
        {
            return TrackException<T>(ex, requestBody, callerMemberName);
        }
    }

    protected async Task<ExternalApiResult<T>> HandleResponse<T>(HttpResponseMessage response, CancellationToken ct, object requestBody = null, bool returnStringContent = false, [CallerMemberName] string callerMemberName = "")
    {
        var result = new ExternalApiResult<T>();

        var requestContent = requestBody != null ? requestBody is not string ? JsonSerializer.ToJsonString(requestBody) : requestBody as string : string.Empty;
        string responseContent = null;
        if (returnStringContent && typeof(T) != typeof(byte[]) && typeof(T) != typeof(bool))
        {
            result.RequestData = requestContent;
            result.ResponseData = responseContent = await response?.Content.ReadAsStringAsync(ct);
        }

        if (response.IsSuccessStatusCode)
        {
            result.Successful = true;

            if (response.StatusCode != HttpStatusCode.NoContent && typeof(T) != typeof(bool))
            {
                if (typeof(T) == typeof(byte[]))
                {
                    var byteRead = await response?.Content.ReadAsByteArrayAsync(ct);
                    result.Result = (T)Convert.ChangeType(byteRead, typeof(T));
                }
                else
                {
                    result.Result = returnStringContent ? JsonSerializer.Deserialize<T>(result.ResponseData, SerializerOptions) : await response?.Content.ReadFromJsonAsync<T>(SerializerOptions, ct);
                }
            }

            return result;
        }
        else if (AuthenticationRequiredStatus.Contains(response?.StatusCode))
        {
            await ResetAccessToken(ct);
        }

        responseContent ??= await response?.Content.ReadAsStringAsync(ct);

        logger.LogWarning(EventSource.ExternalApi, EventAction.ExceptionOrError, "Api call failed", callerMemberName, requestContent, responseContent, response.StatusCode);

        //try deserializing error response after logging
        try
        {
            result.Result = JsonSerializer.Deserialize<T>(responseContent, SerializerOptions);
        }
        catch (Exception)
        {

        }

        return result;
    }

    protected ExternalApiResult<T> TrackException<T>(Exception ex, object requestBody = null, [CallerMemberName] string callerMemberName = "")
    {
        logger.LogError(EventSource.ExternalApi, EventAction.ExceptionOrError, ex, "Error occured in api call", callerMemberName, requestBody);

        return new();
    }
}

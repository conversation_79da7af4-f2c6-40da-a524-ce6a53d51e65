﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Attributes;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Workflow.Debt.ViewModels;

public class AgentWorkflowTaskVM
{
    public static readonly Expression<Func<AgentWorkflowTask, AgentWorkflowTaskVM>> Mapping = data => new()
    {
        Id = data.Id,
        DebtSegmentId = data.DebtSegmentId,
        SegmentName = data.DebtSegment.Name,
        Priority = data.DebtSegment.Priority,
        AssignedOn = data.Assigned,
        IsEscalated = data.IsEscalated,
        EscalatedBy = data.EscalatedByUser.FullName,
        CustomerProfileId = data.CustomerProfileId,
        AccountNo = data.CustomerProfile.AccountId,
        CustomerFullName = data.CustomerProfile.FullName,
        NoOfPlacements = data.CustomerProfile.Placements.Count(),
        AccountBalance = data.CustomerProfile.BalanceRemaining
    };

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long Id { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long DebtSegmentId { get; set; }
    public string SegmentName { get; set; }
    public DebtSegmentPriority Priority { get; set; }

    public Instant AssignedOn { get; set; }
    public bool IsEscalated { get; set; }
    public string EscalatedBy { get; set; }
    [DbGuid, TableDecorator(TableDecoratorType.HideColumn)]
    public string CustomerProfileId { get; set; }
    public string AccountNo { get; set; }
    public string CustomerFullName { get; set; }
    public int NoOfPlacements { get; set; }
    public decimal AccountBalance { get; set; }
}

public class AgentWorkflowTaskLogVM
{
    public static readonly Expression<Func<AgentWorkflowTask, AgentWorkflowTaskLogVM>> Mapping = data => new()
    {
        Id = data.Id,
        SegmentName = data.DebtSegment.Name,
        Priority = data.DebtSegment.Priority,
        AssignedOn = data.Assigned,
        OpenedOn = data.Opened,
        ClosedOn = data.Removed,
        TasksCount = data.Tasks.Count,
        IsEscalated = data.IsEscalated,
        EscalatedBy = data.EscalatedByUser.FullName,
        EscalatedTo = data.EscalatedToUser.FullName,
        CustomerProfileId = data.CustomerProfileId,
        AccountNo = data.CustomerProfile.AccountId,
        CustomerFullName = data.CustomerProfile.FullName,
        NoOfPlacements = data.CustomerProfile.Placements.Count(),
        AccountBalance = data.CustomerProfile.BalanceRemaining
    };

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long Id { get; set; }
    public string SegmentName { get; set; }
    public DebtSegmentPriority Priority { get; set; }
    public Instant AssignedOn { get; set; }
    public string TimeToOpenInMins => OpenedOn.HasValue ? (OpenedOn.Value - AssignedOn).TotalMinutes.ToString("n2") : string.Empty;
    public Instant? OpenedOn { get; set; }
    public Instant? ClosedOn { get; set; }
    public string TimeSpentInMins => ClosedOn.HasValue ? (ClosedOn.Value - OpenedOn.Value).TotalMinutes.ToString("n2") : string.Empty;
    public int TasksCount { get; set; }
    public bool IsEscalated { get; set; }
    public string EscalatedBy { get; set; }
    public string EscalatedTo { get; set; }
    [DbGuid, TableDecorator(TableDecoratorType.HideColumn)]
    public string CustomerProfileId { get; set; }
    public string AccountNo { get; set; }
    public string CustomerFullName { get; set; }
    public int NoOfPlacements { get; set; }
    public decimal AccountBalance { get; set; }
}

public class UpdateAgentAvailabilityVM
{
    [Required]
    public AgentAvailabilityStatus Status { get; set; }
}

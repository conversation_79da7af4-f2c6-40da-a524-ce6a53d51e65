﻿@page "/account/manage/changepassword"
@using LendQube.Infrastructure.Core.AdminUserManagement
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inject AdminUserManagerService service

@inherits SingleFormComponentBase<ChangePasswordVM>

<FormComponent Title="Change Your Password" FormMessage="Message" FormName="@FormName" Model="Model" OnValidSubmit="SubmitEdit">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="OldPassword">Old Password</label>
            <InputText type="password" @bind-Value="context.OldPassword" class="form-input" aria-required="true" placeholder="Old Password" />
            <ValidationMessage For="() => context.OldPassword" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="NewPassword">New Password</label>
            <InputText type="password" @bind-Value="context.NewPassword" class="form-input" aria-required="true" placeholder="New Password" />
            <ValidationMessage For="() => context.NewPassword" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="ConfirmPassword">Confirm Password</label>
            <InputText type="password" @bind-Value="context.ConfirmPassword" class="form-input" aria-required="true" placeholder="Confirm Password" />
            <ValidationMessage For="() => context.ConfirmPassword" class="text-danger" />
        </div>
    </BodyContent>
</FormComponent>

@code
{
    public Task SubmitEdit() => Submit(() => service.ChangePassword(UserName, Model, Cancel));
}

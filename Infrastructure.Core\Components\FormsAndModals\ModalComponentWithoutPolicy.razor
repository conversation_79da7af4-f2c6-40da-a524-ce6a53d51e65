﻿<div class="modal fade" id="@ModalId" tabindex="-1" aria-labelledby="@($"{ModalId}_Label")" data-bs-focus="false"
        aria-modal="true" role="dialog">
    <div class="modal-dialog @ModalCss">
        <div class="modal-content">
            <div class="modal-header">
                <div class="__title">
                    <h5 class="modal-title" id="@($"{ModalId}_Label")">@Title</h5>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal"
                        aria-label="Close"></button>
            </div>
            <div class="modal-body">
                @BodyContent
            </div>

            @if (FooterContent != null)
            {
                <div class="modal-footer __footer">
                    @FooterContent
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter, EditorRequired] public string ModalId { get; set; }
    [Parameter] public string ModalCss { get; set; } = " width-lg";
    [Parameter, EditorRequired] public string Title { get; set; }
    [Parameter, EditorRequired] public RenderFragment BodyContent { get; set; }
    [Parameter] public RenderFragment FooterContent { get; set; }
}


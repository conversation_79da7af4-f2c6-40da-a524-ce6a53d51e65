﻿using LendQube.Infrastructure.Core.SerializersAndConverters;
using Microsoft.Extensions.Logging;
using System.Runtime.CompilerServices;
using System.Text.Json;

namespace LendQube.Infrastructure.Core.Telemetry;

public sealed class LogManager<T>(ILogger<T> logger) : ILogManager<T> where T : class
{
    public void LogInformation(EventSource eventSource, EventAction action, string message, [CallerMemberName] string callerMemberName = "", params object[] data) =>
        logger.LogInformation("{EventSource} {Action} {Message} {Method} {Data}", eventSource, action, message, callerMemberName, JsonSerializer.Serialize(data, JsonOptions.NoCharacterConversionOptions));

    public void LogError(EventSource eventSource, EventAction action, Exception exception, string message, [CallerMemberName] string callerMemberName = "", params object[] data) =>
        logger.LogError(exception, "{EventSource} {Action} {Message} {Method} {Data}", eventSource, action, message, callerMemberName, JsonSerializer.Serialize(data, JsonOptions.NoCharacterConversionOptions));

    public void LogWarning(EventSource eventSource, EventAction action, string message, [CallerMemberName] string callerMemberName = "", params object[] data) =>
        logger.LogWarning("{EventSource} {Action} {Message} {Method} {Data}", eventSource, action, message, callerMemberName, JsonSerializer.Serialize(data, JsonOptions.NoCharacterConversionOptions));

}
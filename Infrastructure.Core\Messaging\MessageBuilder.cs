﻿using Coravel.Queuing.Interfaces;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using NodaTime;

namespace LendQube.Infrastructure.Core.Messaging;

public static class MessageBuilder
{
    public static MessageBrick New(string originatedFrom, string createdByUserId)
    {
        var message = new MessageComposer() { OriginatedFrom = originatedFrom, CreatedByUserId = createdByUserId };
        return new MessageBrick { Composer = message };
    }

    public static MessageBrick Message(this MessageBrick message, string configName, long? configId = null)
    {
        var exists = message.Composer.Messages.TryGetValue(configName, out var entry);
        entry ??= new MessageBrick { ConfigId = configId, ConfigName = configName };
        if (!exists)
        {
            message.Composer.Messages.TryAdd(configName, entry);
            entry.Composer = message.Composer;
        }

        message.ConfigName = configName;

        return entry;
    }

    public static MessageBrick WithRecipient(this MessageBrick message, string userId, List<TemplateKeyValue> templateValues = null, List<MessageAttachment> attachments = null, List<MessageCopiedIn> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.Add(new MessageRecipient { UserId = userId, TemplateValues = templateValues, Attachments = attachments, CopiedIn = copy });
        return message;
    }

    public static MessageBrick WithRecipient(this MessageBrick message, long messagingGroupId, List<TemplateKeyValue> templateValues = null, List<MessageAttachment> attachments = null, List<MessageCopiedIn> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.Add(new MessageRecipient { MessagingGroupId = messagingGroupId, TemplateValues = templateValues, Attachments = attachments, CopiedIn = copy });
        return message;
    }

    public static MessageBrick WithRecipient(this MessageBrick message, AdHocRecipient recipient, List<TemplateKeyValue> templateValues = null, List<MessageAttachment> attachments = null, List<MessageCopiedIn> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.Add(new MessageRecipient { AdHoc = recipient, TemplateValues = templateValues, Attachments = attachments, CopiedIn = copy });
        return message;
    }

    public static MessageBrick WithGroupRecipient(this MessageBrick message, string groupName, List<TemplateKeyValue> templateValues = null, List<MessageAttachment> attachments = null)
    {
        message.Composer.Messages[message.ConfigName].GroupName = groupName;
        message.Composer.Messages[message.ConfigName].Recipients.Add(new MessageRecipient { TemplateValues = templateValues, Attachments = attachments });
        return message;
    }

    public static MessageBrick WithRecipients(this MessageBrick message, List<string> userId, List<TemplateKeyValue> templateValues = null, List<MessageAttachment> attachments = null, List<MessageCopiedIn> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.AddRange(userId.AsParallel().Select(x => new MessageRecipient { UserId = x, TemplateValues = templateValues, Attachments = attachments, CopiedIn = copy }));
        return message;
    }

    public static MessageBrick WithRecipients(this MessageBrick message, List<long> messagingGroupId, List<TemplateKeyValue> templateValues = null, List<MessageAttachment> attachments = null, List<MessageCopiedIn> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.AddRange(messagingGroupId.AsParallel().Select(x => new MessageRecipient { MessagingGroupId = x, TemplateValues = templateValues, Attachments = attachments, CopiedIn = copy }));
        return message;
    }

    public static MessageBrick WithRecipients(this MessageBrick message, List<AdHocRecipient> recipients, List<TemplateKeyValue> templateValues = null, List<MessageAttachment> attachments = null, List<MessageCopiedIn> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.AddRange(recipients.AsParallel().Select(x => new MessageRecipient { AdHoc = x, TemplateValues = templateValues, Attachments = attachments, CopiedIn = copy }));
        return message;
    }

    public static MessageBrick WithRecipientsWithIndividualKeys(this MessageBrick message, List<string> userId, Dictionary<string, List<TemplateKeyValue>> templateValues = null,
        Dictionary<string, List<MessageAttachment>> attachments = null, Dictionary<string, List<MessageCopiedIn>> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.AddRange(userId.AsParallel().Select(x => new MessageRecipient
        {
            UserId = x,
            TemplateValues = templateValues?.GetValueOrDefault(x),
            Attachments = attachments?.GetValueOrDefault(x),
            CopiedIn = copy?.GetValueOrDefault(x)
        }));
        return message;
    }

    public static MessageBrick WithRecipientsWithIndividualKeys(this MessageBrick message, List<long> messagingGroupId, Dictionary<long, List<TemplateKeyValue>> templateValues = null,
        Dictionary<long, List<MessageAttachment>> attachments = null, Dictionary<long, List<MessageCopiedIn>> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.AddRange(messagingGroupId.AsParallel().Select(x => new MessageRecipient
        {
            MessagingGroupId = x,
            TemplateValues = templateValues?.GetValueOrDefault(x),
            Attachments = attachments?.GetValueOrDefault(x),
            CopiedIn = copy?.GetValueOrDefault(x)
        }));
        return message;
    }

    public static MessageBrick WithRecipientsWithIndividualKeys(this MessageBrick message, List<AdHocRecipient> recipients, Dictionary<string, List<TemplateKeyValue>> templateValues = null,
        Dictionary<string, List<MessageAttachment>> attachments = null, Dictionary<string, List<MessageCopiedIn>> copy = null)
    {
        message.Composer.Messages[message.ConfigName].Recipients.AddRange(recipients.AsParallel().Select(x => new MessageRecipient
        {
            AdHoc = x,
            TemplateValues = templateValues?.GetValueOrDefault(x.Key),
            Attachments = attachments?.GetValueOrDefault(x.Key),
            CopiedIn = copy?.GetValueOrDefault(x.Key)
        }));
        return message;
    }

    public static Task<Result<long>> Send<T>(this MessageBrick message, IUnitofWork uow, ILogManager<T> logger, CancellationToken ct) where T : class => message.Composer.BuildAndQueue(uow, logger, ct);

    public static ValueTask<Result<long>> Send(this MessageBrick message, IUnitofWork uow, CancellationToken ct) => message.Composer.BuildAndQueue(uow, ct);
    
    //do not use for messages originating from background service
    public static void Queue(this MessageBrick message, IQueue queue) => queue.QueueInvocableWithPayload<BackgroundMessageComposer, BackgroundMessageModel>(new() { Composer = message.Composer }); 

    public static Task Resend(IUnitofWork uow, long id, int attemptLimit, CancellationToken ct) =>
    uow.Db.UpdateAndSaveWithFilterAsync<MessageLog>(x => x.Id == id && (attemptLimit == 0 || x.AttemptCount <= attemptLimit)
    && !x.MessageLogEntries.Any(y => y.Config.DoNotSendIfExists && ((SystemClock.Instance.GetCurrentInstant() - (y.Log.LastModifiedDate ?? y.Log.CreatedDate.Value)).TotalSeconds < y.Config.ExistsCheckWindow)),
    x => x.SetProperty(y => y.Status, MessageStatus.Queued), ct);
}
﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Attributes;

namespace LendQube.Infrastructure.Collection.Workflow.Debt.ViewModels;

internal class AgentDebtSegmentWithRulesVM
{
    public static readonly Expression<Func<AgentToDebtSegmentMapping, AgentDebtSegmentWithRulesVM>> Mapping = data => new()
    {
        Id = data.Id,
        UserId = data.UserId,
        DebtSegmentId = data.DebtSegmentId,
        RecordsPerTime = data.RecordsPerTime,
        Priority = data.DebtSegment.Priority,
        Start = data.DebtSegment.Start,
        End = data.DebtSegment.End,
        Rules = data.DebtSegment.Rules.Select(x => x.Rule).Where(x => !x.Disabled).ToList(),
    };

    [TableDecorator(TableDecoratorType.HideColumn)]
    public long Id { get; set; }
    public Guid UserId { get; set; }
    public long DebtSegmentId { get; set; }
    public int RecordsPerTime { get; set; }
    public DebtSegmentPriority Priority { get; set; }
    public TimeOnly? Start { get; set; }
    public TimeOnly? End { get; set; }
    public List<DebtSegmentRule> Rules { get; set; }
}

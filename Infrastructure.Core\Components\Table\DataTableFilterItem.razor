﻿@using LendQube.Entities.Core.Extensions
<div class="form-row @(Filter.ShouldHaveCondition ? "grid-5" : "")">
    @if(Filter.ShouldHaveCondition)
    {
        <select class="form-select __sm" @bind-value="selectedCondition" @bind-value:event="oninput" @onchange="() => SelectCondition()">
            <option>@FilterCondition.Or</option>
            <option>@FilterCondition.And</option>
        </select>
    }
    <select class="form-select __sm" @bind-value="selectedFilterName" @bind-value:event="oninput" @onchange="() => SelectColumn()">
        <option label="Filter by"></option>
        @foreach (var filter in Filters)
        {
            <option value="@filter.Name">@filter.DisplayName</option>
        }
    </select>
    @if (selectedFilter != null && (!Filter.ShouldHaveCondition || Filter.Condition.HasValue))
    {
        <select class="form-select __sm" @bind-value="ruleId" @bind-value:event="oninput" @onchange="(e) => SelectRule(e)">
            <option label="Filter rule"></option>
            @foreach (var rule in selectedFilter.Rules)
            {
                if(Filter != null && Filter.RuleId == rule.Id)
                {
                    <option value="@rule.Id" selected>@rule.Name</option>

                }
                else
                {
                    <option value="@rule.Id">@rule.Name</option>
                }
            }
        </select>

        @if(requiresInput)
        {
            @switch (selectedFilter.DataType)
            {
                case ColumnFilterDataType.Number or ColumnFilterDataType.Object:
                    <input class="form-input __sm" type="number" placeholder="@selectedFilter.DisplayName" @onchange="(e) => UpdateValue(e)" @oninput="(e) => UpdateValue(e)" value="@Filter.Value">
                    break;
                case ColumnFilterDataType.Text:
                    <input class="form-input __sm" type="text" placeholder="@selectedFilter.DisplayName" @onchange="(e) => UpdateValue(e)" @oninput="(e) => UpdateValue(e)" value="@Filter.Value">
                    break;
                case ColumnFilterDataType.EnumList:
                case ColumnFilterDataType.BoolList:
                case ColumnFilterDataType.DBList:
                    <select class="form-select __sm" @onchange="(e) => UpdateValue(e)">
                        <option label="select"></option>
                        @foreach (var dd in selectedFilter.DataTypeValueList)
                        {
                            if(Filter.Value?.ToString() == dd)
                            {
                                <option value="@dd" selected>@(selectedFilter.DataType == ColumnFilterDataType.EnumList ? dd.SplitOnUpper() : dd)</option>
                            }
                            else
                            {
                                <option value="@dd">@(selectedFilter.DataType == ColumnFilterDataType.EnumList ? dd.SplitOnUpper() : dd)</option>
                            }
                        }
                    </select>
                    break;
                case ColumnFilterDataType.Date:
                    <input class="form-input __sm hasDate" type="text" placeholder="@selectedFilter.DisplayName" @onchange="(e) => UpdateValue(e)" value="@Filter.Value" autocomplete="off">
                    break;
                case ColumnFilterDataType.PhoneNumber:
                    <input class="form-input __sm" type="text" placeholder="+234 801234567" @onchange="(e) => UpdateValue(e)" @oninput="(e) => UpdateValue(e)" value="@Filter.Value">
                    break;
                default:
                    break;
            }
        }

    }
    @if (Filter.ShouldHaveCondition)
    {
        <button class="btn btn--icon __danger" type="button" @onclick="() => RemoveFilter()">
            <span class="svg svg-trash-gray"></span>
        </button>
    }
</div>

@code {
    [Parameter, EditorRequired] public IJSRuntime JSRuntime { get; set; }

    [Parameter] public EventCallback<Dictionary<Guid, ComplexFilter>> FilterValueChanged { get; set; }
    [Parameter, EditorRequired] public Dictionary<Guid, ComplexFilter> FilterValue { get; set; }
    [Parameter, EditorRequired] public List<ColumnFilter> Filters { get; set; }
    [Parameter, EditorRequired] public Action RemoveFilter { get; set; }
    [Parameter, EditorRequired] public Guid FilterKey { get; set; }
    [Parameter, EditorRequired] public CancellationToken CancellationToken { get; set; }

    private ComplexFilter Filter;
    private ColumnFilter selectedFilter;
    private string selectedFilterName;
    private FilterCondition selectedCondition = FilterCondition.Or;
    private bool requiresInput;
    private int? ruleId;

    protected override void OnInitialized()
    {
        Filter = FilterValue[FilterKey];
    }


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (string.IsNullOrEmpty(Filter.ColumnName) && firstRender)
            Filter.ColumnName = selectedFilterName;
        else if (string.IsNullOrEmpty(selectedFilterName) && firstRender)
            selectedFilterName = Filter.ColumnName;

        if (Filter.ShouldHaveCondition && !Filter.Condition.HasValue && firstRender)
            Filter.Condition = selectedCondition;
        else if (Filter.ShouldHaveCondition && Filter.Condition.HasValue && firstRender)
            selectedCondition = Filter.Condition.Value;

        if (selectedFilter == null && Filter != null && Filter.RuleId > -1)
        {
            selectedFilter = Filters.FirstOrDefault(x => x.Name == selectedFilterName);
            ruleId = Filter.RuleId;

            if(firstRender)
                await SelectRule(new ChangeEventArgs { Value = Filter.RuleId });
        }

        if (selectedFilter != null)
        {
            if (selectedFilter.DataType == ColumnFilterDataType.Date)
            {
                if (firstRender)
                    await JSRuntime.RunFeather(CancellationToken);

                if (string.IsNullOrEmpty(Filter.UserTimeZone))
                    Filter.UserTimeZone = await JSRuntime.GetBrowserTimezone(CancellationToken);
            }

            Filter.ColumnDisplayName = selectedFilter.DisplayName;
            Filter.ColumnName = selectedFilter.Name;
        }
    }

    private Task SelectCondition()
    {
        Filter.Condition = selectedCondition;
        FilterValue[FilterKey] = Filter;
        return FilterValueChanged.InvokeAsync(FilterValue);
    }

    private Task SelectColumn()
    {
        selectedFilter = Filters.FirstOrDefault(x => x.Name == selectedFilterName);
        Filter.Value = null;
        FilterValue[FilterKey] = Filter;
        return FilterValueChanged.InvokeAsync(FilterValue);
    }

    private Task SelectRule(ChangeEventArgs args)
    {
        if(int.TryParse(args.Value?.ToString(), out var _))
        {
            Filter.RuleId = ruleId.Value;
            requiresInput = Filter.RequiresValue = selectedFilter.Rules.FirstOrDefault(x => x.Id == Filter.RuleId).RequiresValue;
        }
        else
        {
            Filter.RuleId = -1;
            requiresInput = Filter.RequiresValue = false;
        }
        FilterValue[FilterKey] = Filter;
        return FilterValueChanged.InvokeAsync(FilterValue);
    }

    private Task UpdateValue(ChangeEventArgs args)
    {
        Filter.Value = string.IsNullOrEmpty(args.Value?.ToString()) ? null : args.Value;
        FilterValue[FilterKey] = Filter;
        return FilterValueChanged.InvokeAsync(FilterValue);
    }
}

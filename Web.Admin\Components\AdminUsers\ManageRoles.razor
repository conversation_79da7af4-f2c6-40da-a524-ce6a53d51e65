﻿@page "/manageroles"

@using LendQube.Infrastructure.Core.AdminUserManagement
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Entities.Core.BaseUser
@using LendQube.Infrastructure.Core.Components.FormsAndModals

@inject AdminRolesManagerService Service
@inject AdminRoleClaimsManagerService claimsService

@inherits BaseTableComponentBase

@attribute [Authorize(Policy = ManageAdminUsersNavigation.RoleIndexPermission)]


<PageTitle>@Title</PageTitle>

<div class="pg-row grid grid-col-1 grid-tab-1">
    <div class="card">

        <div class="title-wrapper flex __justify-between __align-center">
            <span class="text_xl_medium">@SubTitle</span>
        </div>
        <StatusMessage @ref="TableMessage" />

        <DataTable T="ApplicationRole" TableDefinition="TableDefinition" LoadData="Load" EditRow="StartEdit" DeleteRow="SubmitDelete" @ref="table" />

    </div>
</div>

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Name">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="Name">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalComponent Policy="@ManageAdminUsersNavigation.UserClaimsPermission" ModalId="@ClaimsModalName"
                ModalCss="width-xlg" Title=@($"Manage Claims For Role: {SelectedRole?.Name}") ShowFooter="selectedClaimsCount > 0">
    <BodyContent>
        <StatusMessage Message="ModalMessage" />
        <DataTable T="RolePermissionsVM" TableDefinition="claimsService.GetTableDefinition()" LoadData="LoadClaims" DeferLoading="true" CheckboxSelectionEvent="RegisterSelection" @ref="claimsTable" />
    </BodyContent>
    <FooterContent>
        <button class="btn btn--default" type="button" @onclick="() => claimsTable.LoadElement()">Discard</button>
        <LoadButton Label=@($"Save [{selectedClaimsCount}]") OnClick="SaveClaims" />
    </FooterContent>
</ModalComponent>


@code
{
    private DataTable<ApplicationRole> table;
    private DataTable<RolePermissionsVM> claimsTable;
    private int selectedClaimsCount = 0;

    private string ClaimsModalName => "ClaimsModal";

    private ApplicationRole SelectedRole { get; set; }

    [SupplyParameterFromForm]
    protected RoleVM AddModel { get; set; }

    [SupplyParameterFromForm]
    protected RoleVM EditModel { get; set; }


    protected override void OnInitialized()
    {
        Title = "Manage Roles";
        SubTitle = "Authorization Roles";
        FormBaseTitle = "Role";
        CreatePermission = ManageAdminUsersNavigation.RoleCreatePermission;
        EditPermission = ManageAdminUsersNavigation.RoleEditPermission;
        DeletePermission = ManageAdminUsersNavigation.RoleDeletePermission;
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            AddModel = new();
            EditModel = new();

            await base.OnInitializedAsync();

            AddTopButton(CreatePermission, new TopActionButton(ModalName: AddModalName, Action: () => CloseMessage(ModalMessage)));

            AddRowButton(ManageAdminUsersNavigation.UserClaimsPermission, new RowActionButton("Permissions", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                SelectedRole = row as ApplicationRole;
                await claimsTable.LoadElement();
                table.Loading = false;

                StateHasChanged();
                await JSRuntime.OpenModal(ClaimsModalName, Cancel);
            }));
        }
    }

    #region Manage Roles
    protected override ColumnList GetTableDefinition() => Service.GetTableDefinition();

    private ValueTask<TypedBasePageList<ApplicationRole>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        CloseMessage();
        return Service.GetTypeBasedPagedData(filterAndPage, ct);
    }

    public ValueTask SubmitAdd() => BaseSaveAdd(() =>
    {
        return Service.New(AddModel, Cancel);
    },
    () =>
    {
        AddModel = new();
        return table.Refresh();
    });

    protected ValueTask StartEdit(ApplicationRole data, CancellationToken ct) => BaseEdit(() =>
    {
        EditModel = new() { Id = data.Id, Name = data.Name, Description = data.Description };
        return Task.CompletedTask;
    }, ct);

    public ValueTask SubmitEdit() => BaseSaveEdit(async () =>
    {
        var result = await Service.Update(EditModel, Cancel);
        if (!result.Successful)
            CustomMessage = result.Message;
        return result.Successful;
    },
    () =>
    {
        EditModel = new();
        return table.Refresh();
    });

    protected ValueTask<bool> SubmitDelete(ApplicationRole data, Func<Task> refresh, CancellationToken ct) => SaveDelete(async () =>
    {
        var result = await Service.Delete(data, ct);
        if (!result.Successful)
            CustomMessage = result.Message;
        return result.Successful;
    }, refresh);
    #endregion

    #region Manage Claims

    private ValueTask<TypedBasePageList<RolePermissionsVM>> LoadClaims(DataFilterAndPage filterAndPage, CancellationToken ct) => claimsService.GetTypeBasedPagedData(SelectedRole, filterAndPage, ct);

    private void RegisterSelection(int count)
    {
        selectedClaimsCount = count;
        StateHasChanged();
    }

    private async Task SaveClaims()
    {
        claimsTable.Loading = true;
        var permissions = claimsTable.CheckboxChanges.Values.SelectMany(x => x.Select(y => y));
        var permissionsToAdd = permissions.Where(x => x.NewValue).Select(y => y.Row as RolePermissionsVM);
        var permissionsToRemove = permissions.Where(x => !x.NewValue).Select(y => y.Row as RolePermissionsVM);
        var assignCount = permissionsToAdd.Count();
        var removeCount = permissionsToRemove.Count();

        if (assignCount > 0 && !HasClaim(ManageAdminUsersNavigation.AssignClaimToRoleClaimsPermission))
        {
            await JSRuntime.CloseModal(ClaimsModalName, Cancel);
            TableMessage.Error($"Assigning role permissions is not permitted for this account. Attempt to assign {assignCount} permission(s) detected.");
            return;

        }

        if (removeCount > 0 && !HasClaim(ManageAdminUsersNavigation.RemoveClaimFromRoleClaimsPermission))
        {
            await JSRuntime.CloseModal(ClaimsModalName, Cancel);
            TableMessage.Error($"Removing role permissions is not permitted for this account. Attempt to remove {removeCount} permission(s) detected.");
            return;
        }

        var result = await claimsService.SaveClaims(UserName, SelectedRole, permissionsToAdd, permissionsToRemove, Cancel);
        if (result.Successful)
        {
            await JSRuntime.CloseModal(ClaimsModalName, Cancel);
            TableMessage.Success($"{assignCount} permission(s) added and {removeCount} removed for {SelectedRole.Name} successfully.");
        }
        else
        {
            CustomMessage = result.Message;
            ModalMessage.Error($"Modifying permissions for {SelectedRole.Name} failed. {CustomMessage}");
        }

        claimsTable.Loading = false;
        selectedClaimsCount = 0;
        StateHasChanged();
    }
    #endregion
}

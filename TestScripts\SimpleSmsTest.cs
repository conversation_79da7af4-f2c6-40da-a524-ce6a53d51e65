using System;
using System.Net.Http;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace LendQube.TestScripts;

/// <summary>
/// Simple SMS test that directly tests SMS provider APIs
/// This bypasses internal LendQube classes to isolate SMS issues
/// </summary>
public class SimpleSmsTest
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<SimpleSmsTest> _logger;
    private readonly IConfiguration _configuration;

    public SimpleSmsTest(HttpClient httpClient, ILogger<SimpleSmsTest> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;
    }

    public static async Task Main(string[] args)
    {
        var host = CreateHostBuilder(args).Build();
        var tester = host.Services.GetRequiredService<SimpleSmsTest>();
        
        Console.WriteLine("=== Simple SMS Test ===");
        Console.WriteLine("Testing SMS provider APIs directly");
        Console.WriteLine();

        await tester.RunTests();
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory())
                      .AddJsonFile("appsettings.json", optional: false)
                      .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true)
                      .AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                services.AddHttpClient<SimpleSmsTest>((sp, client) =>
                {
                    client.Timeout = TimeSpan.FromSeconds(30);
                });

                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Debug);
                });

                services.AddTransient<SimpleSmsTest>();
            });

    public async Task RunTests()
    {
        Console.WriteLine("🔧 SMS Provider Configuration Check:");
        Console.WriteLine();

        // Test TextLocal
        await TestTextLocal();
        
        // Test Termii (if configured)
        await TestTermii();
    }

    private async Task TestTextLocal()
    {
        Console.WriteLine("🔍 Testing: TextLocal SMS Provider");
        Console.WriteLine("─────────────────────────────────");

        var apiKeyEncoded = _configuration["Providers:TextLocal:ApiKey"];
        var apiKey = !string.IsNullOrEmpty(apiKeyEncoded) ? 
            System.Text.Encoding.UTF8.GetString(System.Convert.FromBase64String(apiKeyEncoded)) : null;
        var from = _configuration["Providers:TextLocal:From"];
        var baseUrl = _configuration["Providers:TextLocal:BaseUrl"];

        Console.WriteLine($"   Configuration:");
        Console.WriteLine($"     Base URL: {baseUrl ?? "NOT SET"}");
        Console.WriteLine($"     From: {from ?? "NOT SET"}");
        Console.WriteLine($"     API Key: {(string.IsNullOrEmpty(apiKey) ? "NOT SET" : "SET")}");
        Console.WriteLine();

        if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(baseUrl))
        {
            Console.WriteLine("❌ TextLocal: Missing configuration");
            Console.WriteLine();
            return;
        }

        try
        {
            // Test with a UK test number (this won't actually send)
            var testNumber = "447123456789"; // UK test number format
            var message = $"LendQube SMS Test - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}";

            var requestData = new
            {
                apikey = apiKey,
                numbers = testNumber,
                message = message,
                sender = from,
                test = true // This prevents actual sending
            };

            var formData = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("apikey", apiKey),
                new KeyValuePair<string, string>("numbers", testNumber),
                new KeyValuePair<string, string>("message", message),
                new KeyValuePair<string, string>("sender", from ?? "Test"),
                new KeyValuePair<string, string>("test", "true")
            });

            Console.WriteLine($"   Request Details:");
            Console.WriteLine($"     URL: {baseUrl}");
            Console.WriteLine($"     Numbers: {testNumber}");
            Console.WriteLine($"     Message: {message}");
            Console.WriteLine($"     Sender: {from}");
            Console.WriteLine($"     Test Mode: true");

            var response = await _httpClient.PostAsync(baseUrl, formData);
            var responseContent = await response.Content.ReadAsStringAsync();

            Console.WriteLine($"   Response:");
            Console.WriteLine($"     Status: {response.StatusCode}");
            Console.WriteLine($"     Content: {responseContent}");

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("✅ TextLocal: SUCCESS");
            }
            else
            {
                Console.WriteLine("❌ TextLocal: FAILED");
                await LogDetailedError("TextLocal", response, responseContent);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 TextLocal: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
            _logger.LogError(ex, "TextLocal SMS Test Failed");
        }
        finally
        {
            Console.WriteLine();
        }
    }

    private async Task TestTermii()
    {
        Console.WriteLine("🔍 Testing: Termii SMS Provider");
        Console.WriteLine("──────────────────────────────");

        var apiKey = _configuration["Providers:Termii:ApiKey"];
        var baseUrl = _configuration["Providers:Termii:BaseUrl"];

        Console.WriteLine($"   Configuration:");
        Console.WriteLine($"     Base URL: {baseUrl ?? "NOT SET"}");
        Console.WriteLine($"     API Key: {(string.IsNullOrEmpty(apiKey) ? "NOT SET" : "SET")}");
        Console.WriteLine();

        if (string.IsNullOrEmpty(apiKey) || string.IsNullOrEmpty(baseUrl))
        {
            Console.WriteLine("❌ Termii: Missing configuration");
            Console.WriteLine();
            return;
        }

        try
        {
            // Test with a Nigerian test number format
            var testNumber = "2348012345678"; // Nigerian test number format
            var message = $"LendQube SMS Test - {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss}";

            var requestData = new
            {
                to = testNumber,
                from = "N-Alert",
                sms = message,
                type = "plain",
                api_key = apiKey,
                channel = "dnd" // This is typically for testing
            };

            var json = JsonSerializer.Serialize(requestData, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");

            Console.WriteLine($"   Request Details:");
            Console.WriteLine($"     URL: {baseUrl}sms/send");
            Console.WriteLine($"     To: {testNumber}");
            Console.WriteLine($"     Message: {message}");
            Console.WriteLine($"     Payload: {json}");

            var response = await _httpClient.PostAsync($"{baseUrl}sms/send", content);
            var responseContent = await response.Content.ReadAsStringAsync();

            Console.WriteLine($"   Response:");
            Console.WriteLine($"     Status: {response.StatusCode}");
            Console.WriteLine($"     Content: {responseContent}");

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("✅ Termii: SUCCESS");
            }
            else
            {
                Console.WriteLine("❌ Termii: FAILED");
                await LogDetailedError("Termii", response, responseContent);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 Termii: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
            _logger.LogError(ex, "Termii SMS Test Failed");
        }
        finally
        {
            Console.WriteLine();
        }
    }

    private async Task LogDetailedError(string provider, HttpResponseMessage response, string responseContent)
    {
        Console.WriteLine($"   🔍 Detailed Error Analysis for {provider}:");
        Console.WriteLine($"      Status Code: {(int)response.StatusCode} {response.StatusCode}");
        Console.WriteLine($"      Reason Phrase: {response.ReasonPhrase}");
        
        // Log response headers
        Console.WriteLine($"      Response Headers:");
        foreach (var header in response.Headers)
        {
            Console.WriteLine($"        {header.Key}: {string.Join(", ", header.Value)}");
        }

        // Try to parse error response
        try
        {
            if (!string.IsNullOrEmpty(responseContent))
            {
                var errorResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                Console.WriteLine($"      Parsed Error Response:");
                Console.WriteLine($"        {JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions { WriteIndented = true })}");
            }
        }
        catch
        {
            Console.WriteLine($"      Raw Error Response: {responseContent}");
        }

        // Log to ILogger as well
        _logger.LogError("SMS Provider Error - {Provider}: {StatusCode} {ReasonPhrase} - {ResponseContent}", 
            provider, response.StatusCode, response.ReasonPhrase, responseContent);
    }
}
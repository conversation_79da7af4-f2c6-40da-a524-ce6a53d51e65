﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.BaseUser;
using NodaTime;
using SecurityDriven;

namespace LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;

public sealed class AdminUserRegisterVM
{
    [Required]
    [MinLength(8, ErrorMessage = "The {0} must be at least {1} characters long.")]
    [DataType(DataType.Password)]
    [Display(Name = "Password")]
    public string Password { get; set; }

    [DataType(DataType.Password)]
    [Display(Name = "Confirm password")]
    [Compare("Password", ErrorMessage =
        "The password and confirmation password do not match.")]
    public string ConfirmPassword { get; set; }

    [Required, ValidString(ValidStringRule.OnlyTextWithHyphenAndApostrophe)]
    [Display(Name = "First Name")]
    public string FirstName { get; set; }

    [Required, ValidString(ValidStringRule.OnlyTextWithHyphenAndApostrophe)]
    [Display(Name = "Last Name")]
    public string LastName { get; set; }

    [Display(Name = "Other Names"), ValidString(ValidStringRule.OnlyTextAndHyphenAndApostropheWithSpacing)]
    public string OtherNames { get; set; }

    [Required, EmailAddress(ErrorMessage = "Invalid email address.")]
    [DataType(DataType.EmailAddress)]
    public string Email { get; set; }

    [Required, ValidString(ValidStringRule.NumberWithPlusOnly)]
    [Display(Name = "Phone Code")]
    public string PhoneCode { get; set; }

    [Required, DataType(DataType.PhoneNumber), ValidString(ValidStringRule.OnlyNumber)]
    [Display(Name = "Phone Number")]
    public string PhoneNumber { get; set; }

    [Display(Name = "Transaction Pin"), ValidString(ValidStringRule.OnlyTextAndNumber)]
    public string TransactionPin { get; set; }

    public List<string> Roles { get; set; } = [];

    // Return a pre-poulated instance of AppliationUser:
    public ApplicationUser GetUser()
    {
        var user = new ApplicationUser()
        {
            UserName = FastGuid.NewPostgreSqlGuid().ToString(),
            FirstName = FirstName,
            LastName = LastName,
            OtherNames = OtherNames,
            Email = Email,
            PhoneCode = PhoneCode,
            PhoneNumber = PhoneNumber,
            RegistrationDate = SystemClock.Instance.GetCurrentInstant(),
            MustChangePasswordOn = SystemClock.Instance.GetCurrentInstant(),
            Role = SystemRoleConfig.AdminRole,
        };
        return user;
    }

}

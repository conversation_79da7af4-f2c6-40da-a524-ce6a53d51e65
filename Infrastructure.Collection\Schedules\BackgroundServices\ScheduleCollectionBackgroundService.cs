﻿using Coravel.Invocable;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Infrastructure.Collection.Helpers;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Messaging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using MongoDB.Bson;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Schedules.BackgroundServices;

internal record ScheduleToCharge(CustomerProfile Profile, decimal Balance);
internal sealed class ScheduleCollectionBackgroundService(IUnitofWork uow, IClock clock, PaymentFactory paymentFactory, IServiceScopeFactory scopeFactory) : IInvocable, ICancellableInvocable
{
    public CancellationToken CancellationToken { get; set; }

    public async Task Invoke()
    {
        var query = uow.Db.Queryable<CustomerSchedule>()
            .Where(x => x.Profile.Placements.Any(y => PlacementHelper.CurrentStatus.HasFlag(y.Status) && y.BalanceRemaining > 0) && ScheduleHelper.UnsettledStatus.HasFlag(x.PaymentStatus) && x.PeriodStatus >= SchedulePeriodStatus.Due && x.Balance > 0 &&
            !x.Profile.Holds.Any(y => !y.Disabled && y.Action.HasFlag(HoldAction.DisableCPA) && (!y.ExpiresOn.HasValue || clock.GetCurrentInstant() < y.ExpiresOn)))
            .GroupBy(x => x.Profile, x => new { x.Balance })
            .Select(x => new ScheduleToCharge(x.Key, x.Sum(y => y.Balance)));

        var totalDue = await query.CountAsync(CancellationToken);
        if (totalDue == 0)
            return;

        int skipCount = 0, takeCount = 50;
        do
        {
            await AutoCollectPayments(query, takeCount, skipCount, CancellationToken);

            await Task.Delay(1000 * 60 * 5, CancellationToken); //delay for 5 mins

            skipCount += takeCount;

        }
        while (skipCount + takeCount < totalDue);
    }

    private async Task AutoCollectPayments(IQueryable<ScheduleToCharge> query, int takeCount, int skipCount, CancellationToken ct)
    {
        var schedules = await query.Skip(skipCount).Take(takeCount).ToListAsync(ct);

        if (schedules.Count == 0)
            return;

        var profileIds = schedules.Select(x => x.Profile.Id);

        var customerSavedCards = await uow.Db.ManySelectAsync(Query<CustomerPaymentMethod, CreateChargeAndConfirmPaymentMethodVM>
            .Where(x => profileIds.Contains(x.ProfileId) && x.CanBeReused == true && x.Config.Provider == PaymentProvider.Acquired)
            .OrderBy(x => x.OrderBy(y => y.LastModifiedDate))
            .Select(CreateChargeAndConfirmPaymentMethodVM.Mapping), ct);

        var message = MessageBuilder.New("Background Scheduled Payment", null);

        await Parallel.ForEachAsync(schedules, new ParallelOptions { MaxDegreeOfParallelism = 10 }, async (item, ct) =>
        {
            var savedPaymentMethod = customerSavedCards.FirstOrDefault(x => x.ProfileId == item.Profile.Id);
            if (savedPaymentMethod == null)
                return;

            var txn = new Transaction
            {
                Id = ObjectId.GenerateNewId().ToString(),
                ProfileId = item.Profile.Id,
                Purpose = "CPA",
                Provider = PaymentProvider.Acquired,
                Type = PaymentType.Card,
                UnitAmount = item.Balance,
                Quantity = 1,
                Fee = 0,
                Discount = 0,
                Status = TransactionStatus.Initiated,
                UserData =
                [
                    new(TransactionHelper.TransactionType, "Auto-Charged Payment")
                ],
                Fields =
                [
                    new("Account Balance", item.Profile.CurrencySymbol + item.Balance.ToString("n2")),
                ],
                History = [],
                CreatedByUser = "System"
            };

            await using var scope = scopeFactory.CreateAsyncScope();
            var localUow = scope.ServiceProvider.GetRequiredService<IUnitofWork>();
            await paymentFactory.CreateAndConfirmTransaction(localUow, item.Profile, txn, savedPaymentMethod, ct);

            var customerTxn = new CustomerTransaction
            {
                ProfileId = item.Profile.Id,
                AmountTried = txn.Amount,
                TransactionId = txn.Id,
                PaymentProvider = txn.Provider,
                AmountPaid = txn.TotalAmountPaid,
                PaymentType = "Auto-Charged Payment",
                CreatedByUser = "System"
            };

            localUow.Db.Insert(customerTxn);
            await localUow.SaveAsync(ct);

            await ApplyPaymentHandler.ApplyPayment(localUow, clock, txn, ct, message);

        });

        _ = await message.Send(uow, ct);
    }
}

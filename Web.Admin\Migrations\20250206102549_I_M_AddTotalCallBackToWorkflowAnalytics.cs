﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class I_M_AddTotalCallBackToWorkflowAnalytics : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.AddColumn<int>(
                name: "TotalCallBack",
                schema: "collection",
                table: "DebtWorkflowTimeAnalytics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalCallBack",
                schema: "collection",
                table: "DebtWorkflowAnalytics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalCallBack",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalCallBack",
                schema: "collection",
                table: "AgentWorkflowAnalytics",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPaymentMethod_ProfileId",
                schema: "collection",
                table: "CustomerPaymentMethod",
                column: "ProfileId");

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerPaymentMethod_CustomerProfile_ProfileId",
                schema: "collection",
                table: "CustomerPaymentMethod",
                column: "ProfileId",
                principalSchema: "collection",
                principalTable: "CustomerProfile",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomerPaymentMethod_CustomerProfile_ProfileId",
                schema: "collection",
                table: "CustomerPaymentMethod");

            migrationBuilder.DropIndex(
                name: "IX_CustomerPaymentMethod_ProfileId",
                schema: "collection",
                table: "CustomerPaymentMethod");

            migrationBuilder.DropColumn(
                name: "TotalCallBack",
                schema: "collection",
                table: "DebtWorkflowTimeAnalytics");

            migrationBuilder.DropColumn(
                name: "TotalCallBack",
                schema: "collection",
                table: "DebtWorkflowAnalytics");

            migrationBuilder.DropColumn(
                name: "TotalCallBack",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics");

            migrationBuilder.DropColumn(
                name: "TotalCallBack",
                schema: "collection",
                table: "AgentWorkflowAnalytics");


        }
    }
}

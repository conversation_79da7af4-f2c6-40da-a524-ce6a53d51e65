﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Collection.Reporting.ViewModel;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.Reporting;

partial class BusinessReportService
{
    private void BuildAgedDebtReport(SystemReport data)
    {
        var endDate = data.EndDate ?? clock.GetCurrentInstant();

        var zonedDateTime = endDate.InZone(timeZone);

        var firstDayInstant = new LocalDate(zonedDateTime.Year, zonedDateTime.Month, 1).AtStartOfDayInZone(timeZone).ToInstant();

        var reportQuery = PrepareAgedDebt(firstDayInstant, endDate);

        queries.Add(BusinessReportTypesEnum.AgedDebt.ToString().SplitOnUpper(), reportQuery);
    }

    private IQueryable<AgedDebtReportVM> PrepareAgedDebt(Instant firstDayDate, Instant endDate)
    {
        var zonedEndDate = endDate.InZone(timeZone).Date;
        var zeroTimespan = TimeOnly.FromTimeSpan(TimeSpan.Zero);
        var query = uow.Db.Queryable<Placement>().Where(x => x.CreatedDate <= endDate).Select(data => new AgedDebtReportVM()
        {
            AgrNo = data.SourceAccountNumber,
            Client = data.Company,
            AccountId = data.Profile.AccountId,
            Name = data.Profile.FirstName,
            Surname = data.Profile.LastName,
            PlacementState = data.Status == PlacementStatus.New ? AgedDebtReportPlacementState.Active :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus == PlacementStatus.Settled)
            ? AgedDebtReportPlacementState.Completed :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus == PlacementStatus.Sold)
            ? AgedDebtReportPlacementState.Sold :
            data.StatusChanges.Any(x => x.CreatedDate <= endDate && x.ToStatus == PlacementStatus.Closed && (x.Reason.Code == "RTC" || x.Comment == "RTC"))
            ? AgedDebtReportPlacementState.ReturnedToCreditor :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus == PlacementStatus.Closed)
            ? AgedDebtReportPlacementState.Closed :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus < PlacementStatus.Settled)
            ? AgedDebtReportPlacementState.Active :
            data.Status == PlacementStatus.Settled ?
            AgedDebtReportPlacementState.Completed :
            AgedDebtReportPlacementState.Active,

            StateAt1st = data.CreatedDate > firstDayDate ? null :
            data.Status == PlacementStatus.New ? AgedDebtReportPlacementState.Active :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= firstDayDate && x.NewStatus == PlacementStatus.Settled)
            ? AgedDebtReportPlacementState.Completed :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= firstDayDate && x.NewStatus == PlacementStatus.Sold)
            ? AgedDebtReportPlacementState.Sold :
            data.StatusChanges.Any(x => x.CreatedDate <= endDate && x.ToStatus == PlacementStatus.Closed && (x.Reason.Code == "RTC" || x.Comment == "RTC"))
            ? AgedDebtReportPlacementState.ReturnedToCreditor :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= firstDayDate && x.NewStatus == PlacementStatus.Closed)
            ? AgedDebtReportPlacementState.Closed :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= firstDayDate && x.NewStatus < PlacementStatus.Settled)
            ? AgedDebtReportPlacementState.Active :
            data.Status == PlacementStatus.Settled ?
            AgedDebtReportPlacementState.Completed :
            AgedDebtReportPlacementState.Active,

            TotalAmount = data.BalanceTotal,
            Term = data.Profile.Schedules.Count(),
            RepaymentFrequency = data.Profile.PaymentFrequency.ToString(),
            TAP = data.BalanceTotal,
            UploadedDate = data.CreatedDate.Value.InZone(timeZone).Date,
            Balance = data.BalanceTotal - data.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Sum(x => x.AmountPaid),
            TtlPmt = data.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Sum(x => x.AmountPaid),
            Installment = data.Profile.Schedules.Where(x => (x.CPADate.Year < zonedEndDate.Year || (x.CPADate.Year == zonedEndDate.Year && x.CPADate.Month <= zonedEndDate.Month)))
            .OrderByDescending(x => x.Period).Select(x => x.Amount).FirstOrDefault(),
            LastPaymentDate = data.Transactions.Any(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount) ?
            data.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Select(x => x.CreatedDate.Value.InZone(timeZone).Date).OrderByDescending(x => x).FirstOrDefault() : null,
            LastPaymentAmount = data.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Select(x => x.AmountPaid).OrderByDescending(x => x).FirstOrDefault(),

            TotalReceivedMTD = data.Transactions.Where(x => x.CreatedDate <= endDate &&
            x.CreatedDate.Value.InZone(timeZone).Date.Month == zonedEndDate.Month &&
            x.CreatedDate.Value.InZone(timeZone).Date.Year == zonedEndDate.Year &&
            x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Sum(x => x.AmountPaid),

            CurrentArrears = data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus == PlacementStatus.Broken)
            ? data.Profile.Schedules.Where(x => (x.CPADate.Year < zonedEndDate.Year || (x.CPADate.Year == zonedEndDate.Year && x.CPADate.Month <= zonedEndDate.Month))
            && x.PeriodStatus == SchedulePeriodStatus.PastDue && x.PaymentStatus != SchedulePaymentStatus.Paid)
            .Sum(x => x.Balance) : 0,
            DaysInArrears = data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus == PlacementStatus.Broken)
            ? data.Profile.Schedules.Where(x => (x.CPADate.Year < zonedEndDate.Year || (x.CPADate.Year == zonedEndDate.Year && x.CPADate.Month <= zonedEndDate.Month))
            && x.PeriodStatus == SchedulePeriodStatus.PastDue && x.PaymentStatus != SchedulePaymentStatus.Paid)
            .Sum(x => (endDate.ToDateTimeUtc() - x.CPADate.ToDateTime(zeroTimespan).ToUniversalTime()).TotalDays) : 0,

            LastStateChangeDate = data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate) ?
            data.StatusChangeLogs.Where(x => x.CreatedDate <= endDate).Select(x => x.CreatedDate.Value.InZone(timeZone).Date).OrderByDescending(x => x).FirstOrDefault() : null,
            FirstPaymentDate = data.Transactions.Any(x => x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount) ? data.Transactions.Where(x => x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount)
            .OrderBy(x => x.CreatedDate).Select(x => x.CreatedDate.Value.InZone(timeZone).Date).FirstOrDefault() : null,
            TotalNonPMTcredits = data.Discount,
            NoEmail = string.IsNullOrEmpty(data.Profile.Email),
            NoPhone = data.Profile.PhoneNumber == null
        });

        return query;
    }

    private static void SaveAgedDebtReport(ExcelWorksheet worksheet, IQueryable<object> reportData)
    {
        var queryData = reportData.Cast<AgedDebtReportVM>();
        worksheet.Cells["A1"].LoadFromCollection(queryData);
    }
}

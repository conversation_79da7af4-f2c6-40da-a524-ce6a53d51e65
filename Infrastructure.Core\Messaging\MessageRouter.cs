﻿using System.Collections.Concurrent;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Messaging.Providers;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class MessageRouter(Func<MessageChannel, List<IMessageProvider>> provider)
{
    internal async Task<MessageStatus> RouteMessages(IUnitofWork uow, long messageId, ConcurrentDictionary<MessageChannel, List<PreparedMessageVM>> messages, CancellationToken ct)
    {
        Dictionary<MessageChannel, MessageStatus> results = [];
        Dictionary<MessageChannel, Task<MessageStatus>> dispatchResults = [];
        ConcurrentBag<MessageLogActivity> activity = [];

        var allChannels = messages.Keys;
        var customerInboxProvider = allChannels.Any(x => x.Has<PERSON>lag(MessageChannel.CustomerInbox)) ? provider(MessageChannel.CustomerInbox).FirstOrDefault(x => !x.Config.Disabled) : null;


        IReadOnlyList<MessageChannel> emailChannels = [MessageChannel.Email, MessageChannel.SmsAndEmail, MessageChannel.PushNotificationAndEmail];
        IReadOnlyList<MessageChannel> textChannels = [MessageChannel.Sms, MessageChannel.SmsAndEmail,
             MessageChannel.PushNotification, MessageChannel.PushNotificationAndSms, MessageChannel.PushNotificationOrSms, MessageChannel.PushNotificationAndEmail,
             MessageChannel.WhatsApp, MessageChannel.Telegram, MessageChannel.Text];

        //handle or channels separately
        IReadOnlyList<MessageChannel> textFirstChannels = [MessageChannel.PushNotificationOrEmail, MessageChannel.SmsOrEmail];
        IReadOnlyList<MessageChannel> emailFirstChannels = [MessageChannel.EmailOrSms];

        var emailProvider = allChannels.Any(x => emailChannels.Any(y => x.HasFlag(y)) || textFirstChannels.Any(y => x.HasFlag(y)) || emailFirstChannels.Any(y => x.HasFlag(y)))
            ? provider(MessageChannel.Email).FirstOrDefault(x => !x.Config.Disabled) : null;
        var textProvider = allChannels.Any(x => textChannels.Any(y => x.HasFlag(y)) || textFirstChannels.Any(y => x.HasFlag(y)) || emailFirstChannels.Any(y => x.HasFlag(y))) ? provider(MessageChannel.Text)[0] : null;



        if (textProvider != null && allChannels.Any(x => textChannels.Any(y => x.HasFlag(y))))
        {
            var message = messages.Where(x => textChannels.Any(y => x.Key.HasFlag(y))).SelectMany(x => x.Value).Where(x => x.HasText);
            dispatchResults[MessageChannel.Text] = Dispatch(activity, messageId, MessageChannel.Text, message.ToList(), textProvider, ct);
        }

        if (emailProvider != null && allChannels.Any(x => emailChannels.Any(y => x.HasFlag(y))))
        {
            var message = messages.Where(x => emailChannels.Any(y => x.Key.HasFlag(y))).SelectMany(x => x.Value).Where(x => x.HasHtml);
            dispatchResults[MessageChannel.Email] = Dispatch(activity, messageId, MessageChannel.Email, message.ToList(), emailProvider, ct);
        }

        if (dispatchResults.Count > 0)
        {
            await Task.WhenAll(dispatchResults.Values);

            foreach (var item in dispatchResults)
            {
                results[item.Key] = await item.Value;
            }
        }

        if (textProvider != null && textFirstChannels.Any(x => allChannels.Any(y => y.HasFlag(x))))
        {
            var message = messages.Where(x => textFirstChannels.Any(y => x.Key.HasFlag(y))).SelectMany(x => x.Value);
            var status = results[MessageChannel.Text] = await Dispatch(activity, messageId, MessageChannel.Text, message.Where(x => x.HasText).ToList(), textProvider, ct);
            if (emailProvider != null && status != MessageStatus.Sent)
            {
                results[MessageChannel.Email] = await Dispatch(activity, messageId, MessageChannel.Email, message.Where(x => x.HasHtml).ToList(), emailProvider, ct);
            }
        }

        if (emailProvider != null && emailFirstChannels.Any(x => allChannels.Any(y => y.HasFlag(x))))
        {
            var message = messages.Where(x => emailFirstChannels.Any(y => x.Key.HasFlag(y))).SelectMany(x => x.Value);
            var status = results[MessageChannel.Email] = await Dispatch(activity, messageId, MessageChannel.Email, message.Where(x => x.HasHtml).ToList(), emailProvider, ct);
            if (textProvider != null && status != MessageStatus.Sent)
            {
                results[MessageChannel.Text] = await Dispatch(activity, messageId, MessageChannel.Text, message.Where(x => x.HasText).ToList(), textProvider, ct);
            }
        }

        if (customerInboxProvider != null)
        {
            var message = messages.Where(x => x.Key.HasFlag(MessageChannel.CustomerInbox)).SelectMany(x => x.Value).Where(x => x.Data.Any(y => y.ForUser)).ToList();
            results[MessageChannel.CustomerInbox] = await Dispatch(activity, messageId, MessageChannel.CustomerInbox, message, customerInboxProvider, ct);
        }

        uow.Db.InsertBulk(activity, ct);

        return results.ToMessageStatus();
    }

    private static Task<MessageStatus> Dispatch(ConcurrentBag<MessageLogActivity> activity, long messageId, MessageChannel channel, List<PreparedMessageVM> messages, IMessageProvider provider, CancellationToken ct)
    {
        if (!messages.IsNullOrEmpty() && provider != null)
            return provider.ProcessMessage(messages.AsReadOnly(), ct);

        activity.Add(new MessageLogActivity { MessageLogId = messageId, Title = channel.GetDisplayName(), Activity = "Channel or message missing", CreatedByUser = "System" });
        return Task.FromResult(MessageStatus.Failed);
    }
}
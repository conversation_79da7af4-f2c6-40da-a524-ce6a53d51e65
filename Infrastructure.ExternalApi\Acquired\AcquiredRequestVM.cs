﻿using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.ExternalApi.Acquired;


internal sealed class AcquiredLoginRequestVM
{
    public string AppId { get; set; }
    public string AppKey { get; set; }
}

public sealed class AcquiredGenerateLinkRequestVM
{
    public AcquiredTransactionRequestVM Transaction { get; set; }
    public AcquiredPaymentRequestVM Payment { get; set; }
    public AcquiredCustomerRequestVM Customer { get; set; }
    public AcquiredTDSRequestVM Tds { get; set; }
    public bool IsRecurring { get; set; }
    public int CountRetry => 3;
    public int ExpiresIn => 259200;
    public string TemplateId { get; set; }
    public string RedirectUrl { get; set; }
    public string WebhookUrl { get; set; }
    public List<PaymentMethod> PaymentMethods { get; set; } = [PaymentMethod.card, PaymentMethod.pay_by_bank];
}

public sealed class AcquiredTransactionRequestVM
{
    public string OrderId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; }
    [JsonPropertyName("moto")]
    public bool CollectingOverPhone { get; set; }
    [JsonPropertyName("capture")]
    public bool ChargeNow { get; set; }
}

public sealed class AcquiredPaymentRequestVM
{
    public string Reference { get; set; }
    public string CardId { get; set; }
}

public sealed class AcquiredCustomerRequestVM
{
    public string Reference { get; set; }
    public string CustomerId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    [JsonIgnore]
    public DateOnly? DateOfBirth { get; set; }
    public string Dob => DateOfBirth?.ToString("yyyy-MM-dd");
    public string Ip { get; set; }
    public AcquiredCustomerBillingInfoRequestVM Billing { get; set; }
    public AcquiredCustomerShippingAddresRequestVM Shipping { get; set; }
    public string Email { get; set; }
    public AcquiredPhoneNoRequestVM Phone { get; set; }
}

public sealed class AcquiredPhoneNoRequestVM
{
    public string CountryCode { get; set; }
    public string Number { get; set; }
}

public sealed class AcquiredCustomerBillingInfoRequestVM
{
    public AcquiredCustomerAddressRequestVM Address { get; set; }
}

public sealed class AcquiredCustomerAddressRequestVM
{
    public string Line1 { get; set; }
    public string Line2 { get; set; }
    public string City { get; set; }
    public string State { get; set; }
    public string Postcode { get; set; }
    public string CountryCode { get; set; }
}

public sealed class AcquiredCustomerShippingAddresRequestVM
{
    public bool AddressMatch { get; set; }
}

public sealed class AcquiredTDSRequestVM
{
    public bool IsActive { get; set; }
    public TdsChallengePreference ChallengePreference { get; set; }
    public string ContactUrl { get; set; }
}

public enum TdsChallengePreference
{
    challenge_mandated,
    challenge_preferred,
    no_challenge_request,
    no_preference
}

public enum PaymentMethod
{
    card,
    apple_pay,
    google_pay,
    pay_by_bank
}

public class AcquiredChargeCardRequestVM
{
    public AcquiredChargeCardTransactionRequestVM Transaction { get; set; }
    public AcquiredChargeCardPaymentRequestVM Payment { get; set; }
}

public class AcquiredChargeCardTransactionRequestVM
{
    public string OrderId { get; set; }
    public decimal Amount { get; set; }
    public string Currency { get; set; }

    [JsonPropertyName("capture")]
    public bool ChargeNow { get; set; }
}
public class AcquiredChargeCardPaymentRequestVM
{
    public string CardId { get; set; }
}
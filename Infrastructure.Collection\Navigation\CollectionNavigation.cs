﻿using LendQube.Infrastructure.Core.Navigation;

namespace LendQube.Infrastructure.Collection.Navigation;

public class CollectionNavigation : INavigationDescriptor, INavigatorHasPermissions
{
    public const string GroupName = "Collections";
    public bool IsDisabled { get; set; } = false;
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = GroupName,
            Icon = "log-in",
            Permission = PlacementIndexPermission,
            Controller = GroupName,
            Url = "collections/placements",
            SubNavigation =
            [
                new() { Name = "Placements", Icon = "globe", Permission = PlacementIndexPermission, Url = "collections/placements", Controller = GroupName },
                new() { Name = "Uploads", Icon = "upload", Permission = UploadIndexPermission, Url = "collections/uploads", Controller = GroupName },
                new() { Name = "Templates", Icon = "folder-plus", Permission = CollectionTemplateIndexPermission, Url = "collections/template", Controller = GroupName },

            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.CollectionIndex, GroupName, navs);
    }
    public void PreparePermissionDescriptions()
    {
        Navigator.PermissionDescription[PlacementIndexPermission] = "Can view collection placements";
        Navigator.PermissionDescription[PlacementViewPermission] = "Can view collection placement details";
        Navigator.PermissionDescription[PlacementDeletePermission] = "Can remove collection placements";

        Navigator.PermissionDescription[UploadIndexPermission] = "Can upload collection files (placements, transactions etc)";
        Navigator.PermissionDescription[UploadCreatePermission] = "Can create collection uploads";
        Navigator.PermissionDescription[UploadEditPermission] = "Can modify collection uploads";
        Navigator.PermissionDescription[UploadImportPermission] = "Can import collection uploads";
        Navigator.PermissionDescription[UploadDeletePermission] = "Can remove collection uploads";

        Navigator.PermissionDescription[CollectionTemplateIndexPermission] = "Can create collection upload templates";
        Navigator.PermissionDescription[CollectionTemplateCreatePermission] = "Can add collection upload templates";
        Navigator.PermissionDescription[CollectionTemplateEditPermission] = "Can modify collection upload templates";
        Navigator.PermissionDescription[CollectionTemplateDeletePermission] = "Can remove collection upload templates";
    }

    public const string PlacementIndexPermission = "Permission.Placement.Index";
    public const string PlacementViewPermission = "Permission.Placement.View";
    public const string PlacementDeletePermission = "Permission.Placement.Delete";

    public const string UploadIndexPermission = "Permission.UploadCollection.Index";
    public const string UploadCreatePermission = "Permission.UploadCollection.Create";
    public const string UploadEditPermission = "Permission.UploadCollection.Edit";
    public const string UploadImportPermission = "Permission.UploadCollection.Import";
    public const string UploadDeletePermission = "Permission.UploadCollection.Delete";


    public const string CollectionTemplateIndexPermission = "Permission.CollectionTemplate.Index";
    public const string CollectionTemplateCreatePermission = "Permission.CollectionTemplate.Create";
    public const string CollectionTemplateEditPermission = "Permission.CollectionTemplate.Edit";
    public const string CollectionTemplateDeletePermission = "Permission.CollectionTemplate.Delete";

}
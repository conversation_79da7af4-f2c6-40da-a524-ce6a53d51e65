﻿using System.Linq.Expressions;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

internal sealed class CountNotEqualToFilter() : ObjectFilter(ColumnFilterRule.CountNotEqualTo)
{
    public override Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");

        Expression member = pe.GetDenaturedExpression(vm.ColumnName);

        var methodInfo = typeof(Enumerable).GetMethods()
                .First(method => method.Name == nameof(Enumerable.Count) && method.GetParameters().Length == 1)
                .MakeGenericMethod(propertyType.GenericTypeArguments[0]);

        var countCall = Expression.Call(methodInfo, member);

        ConstantExpression constantExpression = Expression.Constant(int.Parse(value.ToString()));

        return Expression.Lambda<Func<T, bool>>(Expression.NotEqual(countCall, constantExpression), pe);
    }

    public override bool IsTypeSupported(ObjectFilterRule rule) => rule.Type.IsGenericList() && rule.FilterType != ColumnFilterDataType.DBList;
}
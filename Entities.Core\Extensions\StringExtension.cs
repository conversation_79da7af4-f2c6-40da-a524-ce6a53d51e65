﻿using System.Diagnostics;
using System.Globalization;
using System.Text;
using System.Text.RegularExpressions;

namespace LendQube.Entities.Core.Extensions;

[DebuggerStepThrough]
public static partial class StringExtension
{
    public static string ToCamelCase(this string value)
    {
        var splitUpper = value.SplitOnUpper().Split(" ");
        var firstSplit = splitUpper?[0];
        if (firstSplit.All(char.IsUpper) || splitUpper.Length > 1)
        {
            firstSplit = firstSplit.ToLowerInvariant();
            var buildResult = new StringBuilder(firstSplit);
            foreach (var item in splitUpper.Skip(1))
            {
                buildResult.Append(item);
            }
            return buildResult.ToString();
        }

        return value.All(char.IsUpper) ? value.ToLowerInvariant() : value[..1].ToLowerInvariant() + value[1..];
    }

    public static string FirstCharToUpper(this string input)
    {
        input = input?.Trim();
        var inputs = input?.Split(" ");
        if (inputs != null && inputs.Length > 1)
        {
            var result = string.Empty;
            foreach (var item in inputs)
            {
                result += item switch
                {
                    null or "" => item,
                    _ => item[..1].ToUpperInvariant() + item[1..]?.ToLowerInvariant() + " ",
                };
            }

            return result.Trim();
        }

        return input switch
        {
            null or "" => input,
            _ => input[..1].ToUpperInvariant() + input[1..]?.ToLowerInvariant(),
        };
    }

    public static string Titleize(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input).ToSentenceCase();
    }

    public static string ToTitleCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return CultureInfo.CurrentCulture.TextInfo.ToTitleCase(input);
    }

    public static string SplitOnUpper(this string input)
    {
        if (string.IsNullOrEmpty(input) || char.IsLower(input[0]))
            return input;

        var result = SplitOnUpperRegex().Replace(input, " $1");
        return result;
    }

    public static string ToSentenceCase(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return SentenceCaseRegex().Replace(input, m => m.Value[0] + " " + char.ToLowerInvariant(m.Value[1]));
    }

    public static string RemoveWhitespaces(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        var sWhitespace = WhitespaceRegex();
        return sWhitespace.Replace(input, string.Empty);
    }

    public static bool MatchesAll(this string value1, string value2)
    {
        if (string.IsNullOrEmpty(value1) || string.IsNullOrEmpty(value2)) return false;

        var splitValue2 = value2.Replace(",", string.Empty).Split(" ", StringSplitOptions.TrimEntries);
        var splitValue1 = value1.Replace(",", string.Empty).Split(" ", StringSplitOptions.TrimEntries);

        return splitValue1.All(x => string.IsNullOrWhiteSpace(x) || splitValue2.Any(y => y.Equals(x, StringComparison.OrdinalIgnoreCase))) || splitValue2.All(x => string.IsNullOrWhiteSpace(x) || splitValue1.Any(y => y.Equals(x, StringComparison.OrdinalIgnoreCase)));
    }

    public static string StripHTML(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return input;

        return StripHtmlRegex().Replace(input, String.Empty);
    }

    public static int GetWordsCount(this string input)
    {
        if(string.IsNullOrEmpty(input)) 
            return 0;

        return WordPatternRegex().Matches(input).Count;
    }

    public static bool IsNumber(this string input)
    {
        if (string.IsNullOrEmpty(input))
            return false;

        return IsNumberRegex().Match(input).Success;
    }

    public static string CleanUpAuthCode(this string input) => input.Replace(" ", string.Empty).Replace("-", string.Empty);

    
    [GeneratedRegex("(((?<!^)[A-Z](?=[a-z]))|((?<=[a-z])([0-9]|[A-Z])))")]
    private static partial Regex SplitOnUpperRegex();
    [GeneratedRegex("[a-z][A-Z]")]
    private static partial Regex SentenceCaseRegex();
    [GeneratedRegex(@"\s+")]
    private static partial Regex WhitespaceRegex();
    [GeneratedRegex("<.*?>")]
    private static partial Regex StripHtmlRegex();
    [GeneratedRegex(@"\b\w+\b")]
    private static partial Regex WordPatternRegex();
    [GeneratedRegex(@"^[0-9]+$")]
    private static partial Regex IsNumberRegex();
}


﻿using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Core.Database.AnalyticsTriggers;

public class AnalyticsTriggerModel
{
    public string TriggerName => $"{Table.ToLower()}_a_t";
    public string TriggerFunctionName => $"{Table.ToLower()}_a_t_f";
    public required TriggerChange[] On { get; init; }
    public required string Table { get; init; }
    public required string Schema { get; init; }
    public string NotifyType => this.CreateTriggerNotifyTypeName();
    public List<AnalyticsTriggerModelScript> Triggers { get; set; }
}

public class AnalyticsTriggerModelScript
{
    public string Condition { get; set; }
    public required string TargetSchema { get; set; }
    public required string TargetTable { get; set; }
    public List<AnalyticsTriggerModelUpdateScriptData> UpdateScript { get; set; }
    public string UpdateScriptWhere { get; set; }
    public AnalyticsTriggerModelInsertScriptData InsertScript { get; set; }
}

public class AnalyticsTriggerModelUpdateScriptData
{
    public required string LeftExpression { get; set; }
    public required string RightExpression { get; set; }
}

public class AnalyticsTriggerModelInsertScriptData
{
    public required List<string> TargetColumnNames { get; set; }
    public required List<AnalyticsTriggerModelInsertScriptDataSourceValue> SourceValues { get; set; }
}

public class AnalyticsTriggerModelInsertScriptDataSourceValue
{
    public required InsertValueType Type { get; set; }
    public required string Value { get; set; }
}

public enum InsertValueType
{
    Value,
    Column
}

public static class AnalyticsTriggerHelpers
{
    public static readonly string CurrentDate = "(NOW() AT TIME ZONE 'UTC')::DATE";
    public static readonly string CurrentDateTime = "NOW() AT TIME ZONE 'UTC'";
    public static readonly string NewRecordCondition = "TG_OP = 'INSERT'";

    internal static string CreateTriggerNotifyTypeName(this AnalyticsTriggerModel trigger) => trigger.On.Select(x => Enum.GetName(typeof(TriggerChange), x)?.ToUpper()).Aggregate((p, n) => $"{p} OR {n}");
}
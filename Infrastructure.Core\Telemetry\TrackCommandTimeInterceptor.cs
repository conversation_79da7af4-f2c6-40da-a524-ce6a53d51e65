﻿using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights;
using Microsoft.EntityFrameworkCore.Diagnostics;
using System.Data.Common;
using Npgsql;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.Telemetry;

public sealed class TrackCommandTimeInterceptor(TelemetryClient telemetryClient) : DbCommandInterceptor
{
    private void LogSuccessTelemetry(CommandExecutedEventData eventData)
    {
        if(eventData.Duration > TimeSpan.FromMilliseconds(100))
        {
            var parameters = string.Join(", ", eventData.Command.Parameters.AsParallel().Cast<DbParameter>()
                .Select(p => $"{p.ParameterName}: {(p.Value != null && p.Value.GetType().IsGenericListString() ? $"[{string.Join(", ", (IList<string>)p.Value)}]" : p.Value )}"));
            telemetryClient.TrackDependency(
               new DependencyTelemetry("SQL", eventData.Connection.Database, eventData.EventIdCode, $"SQL: {eventData.Command.CommandText} \n Parameters: {parameters}", eventData.StartTime, eventData.Duration, "Slow Query", true));
        }
    }

    private void LogFailTelemetry(CommandErrorEventData eventData)
    {
        string exception = eventData.Exception is PostgresException ex ? ex.ToString() : eventData.Exception.ToString();
        var parameters = string.Join(", ", eventData.Command.Parameters.AsParallel().Cast<DbParameter>()
            .Select(p => $"{p.ParameterName}: {(p.Value != null && p.Value.GetType().IsGenericListString() ? $"[{string.Join(", ", (IList<string>)p.Value)}]" : p.Value)}"));
        telemetryClient.TrackDependency(
           new DependencyTelemetry("SQL", eventData.Connection.Database, eventData.EventIdCode, $"SQL: {eventData.Command.CommandText} \n Parameters: {parameters}", eventData.StartTime, eventData.Duration, exception, false));
    }

    public override DbDataReader ReaderExecuted(DbCommand command, CommandExecutedEventData eventData, DbDataReader result)
    {
        LogSuccessTelemetry(eventData);
        return result;
    }

    public override ValueTask<DbDataReader> ReaderExecutedAsync(DbCommand command, CommandExecutedEventData eventData, DbDataReader result, CancellationToken cancellationToken = default)
    {
        LogSuccessTelemetry(eventData);
        return base.ReaderExecutedAsync(command, eventData, result, cancellationToken);
    }

    public override void CommandFailed(DbCommand command, CommandErrorEventData eventData)
    {
        LogFailTelemetry(eventData);
        base.CommandFailed(command, eventData);
    }

    public override Task CommandFailedAsync(DbCommand command, CommandErrorEventData eventData, CancellationToken cancellationToken = default)
    {
        LogFailTelemetry(eventData);
        return base.CommandFailedAsync(command, eventData, cancellationToken);
    }
}
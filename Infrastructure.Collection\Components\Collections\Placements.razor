﻿@page "/collections/placements"
@using LendQube.Entities.Collection.Placements
@using LendQube.Infrastructure.Collection.ViewModels.PlacementData

@inject NavigationManager navigationManager
@inherits GenericCrudVMTable<Placement, PlacementVM>

@attribute [Authorize(Policy = CollectionNavigation.PlacementIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}


@code
{
    protected override void OnInitialized()
    {
        Title = "Collections";
        FormBaseTitle = "Placement";
        SubTitle = "Placements";
        DeletePermission = CollectionNavigation.PlacementDeletePermission;
        QuerySelector = PlacementVM.Mapping;
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            await base.OnInitializedAsync();

            AddRowButton(CollectionNavigation.PlacementViewPermission, new RowActionButton("View", Icon: "eye", Action: (object row) =>
            {
                var placement = row as PlacementVM;
                navigationManager.NavigateTo($"collections/placements/{placement.Id}");
                return Task.CompletedTask;
            }));

        }
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Company, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.AccountId, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.FirstName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.LastName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.Email, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.PhoneNumber.Number, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.MobileNumber.Number, filterAndPage.TextFilter);
    }

    protected override ValueTask<bool> SubmitDelete(PlacementVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}

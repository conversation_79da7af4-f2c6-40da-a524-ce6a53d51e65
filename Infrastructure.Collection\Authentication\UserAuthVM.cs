﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Attributes;
using NodaTime;
using OpenIddict.Abstractions;

namespace LendQube.Infrastructure.Collection.Authentication;

public enum LoginTokenType
{
    Phone,
    Email
}

public class UserTokenRequestVM
{
    [Required]
    public LoginTokenType Type { get; set; }
    [MaxLength(4), ValidString(ValidStringRule.NumberWithPlusOnly)]
    public string PhoneCode { get; set; }

    [Required(ErrorMessage = "Provide your account email or phone no")]
    public string Username { get; set; }
}

public class UserLoginVM
{
    [Required(ErrorMessage = "Provide your account email or phone no")]
    public string Username { get; set; }
    [Required(ErrorMessage = "Provide your account password")]
    [StringLength(10000, MinimumLength = 4, ErrorMessage = "Your password must be at least 4 character long")]
    [DataType(DataType.Password)]
    public string Password { get; set; }
}

public class UserLoginResponseVM
{
    public bool IsSuccessful { get; set; }
    public OpenIddictResponse Response { get; set; }
    public CustomerProfileResponse AppUser { get; set; }
}

public class CustomerProfileResponse
{
    public static readonly Expression<Func<CustomerProfile, CustomerProfileResponse>> Mapping = data => new CustomerProfileResponse
    {
        Id = data.Id,
        Blacklisted = data.Blacklisted,
    };
    public string Id { get; set; }
    public bool Blacklisted { get; set; }
}

public class SendCodeRequestVM
{
    public string UserId { get; set; }
    public string OriginatedFrom { get; set; }
    public string MessageConfigName { get; set; }
    public Instant ExpireAt { get; set; }
    public int SendLimit { get; set; }
}

public record SendCodeResponse(bool Success, string Code);

public record ValidateCodeRequest(string UserId, string Code, string CodeType);


public class SetPasswordVM
{
    [Required]
    [StringLength(100, ErrorMessage = "The {0} must be at least {2} characters long.", MinimumLength = 6)]
    [DataType(DataType.Password)]
    [Display(Name = "New password")]
    public string NewPassword { get; set; }
    [Required]
    public string Email { get; set; }
    [Required]
    public string Token { get; set; }
}


public class ResetPasswordVM
{
    [Required(ErrorMessage = "Provide your account email address"), EmailAddress(ErrorMessage = "Provide your account email address")]
    [DataType(DataType.EmailAddress)]
    public string Email { get; set; }
}

public class ChangePasswordVM
{
    [Required]
    [DataType(DataType.Password)]
    [Display(Name = "Current password")]
    public string OldPassword { get; set; }

    [Required]
    [StringLength(10000, ErrorMessage = "The {0} must be at least {2} characters long.", MinimumLength = 6)]
    [DataType(DataType.Password)]
    [Display(Name = "New password")]
    public string NewPassword { get; set; }
}

﻿using System.Net.Mail;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Telemetry;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class SmtpEmailProvider : AbstractMessageProvider, IEmailProvider
{
    public const string Name = "Smtp";
    protected override MessageChannel SupportedChannel => MessageChannel.Email;

    private readonly DefaultAppConfig config;
    private readonly ILogManager<SmtpEmailProvider> logger;
    private readonly HttpClient httpClient;

    public SmtpEmailProvider(IUnitofWork uow, DefaultAppConfig config, ILogManager<SmtpEmailProvider> logger, HttpClient httpClient)
        : base(uow)
    {
        this.config = config;
        this.logger = logger;
        this.httpClient = httpClient;

        Config = MessagingCompiledQueries.GetProviderConfig(uow, Name) ?? new ProviderConfigVM { Disabled = true };

    }

    public override ProviderConfigVM Config { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        var result = new Dictionary<long, MessageStatus>();
        var leadingMessage = messages[0];

        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count, CancellationToken = ct }, async (message, ct) =>
        {
            message.Data = [.. message.Data.Where(x => x.HasEmail)];
            if (message.Data.Count == 0) return;

            if (!message.HasSeparateTemplateValues)
                SubstituteAllTemplateTypeKeys(message);

            foreach (var (recipient, index) in message.Data.Select((x, i) => (x, i)))
            {
                var status = await SendSingleMessage(message, recipient, ct);
                result[message.MessageId + index] = status;
            }
        });

        var finalStatus = result.ToMessageStatus();
        LogActivity(leadingMessage, finalStatus, Name);
        await uow.SaveAsync(ct);
        await UpdateProviderWithResult(messages, result, null, null, ct);
        return finalStatus;
    }

    private async Task<MessageStatus> SendSingleMessage(PreparedMessageVM message, SinglePreparedMessageVM recipient, CancellationToken ct)
    {
        try
        {
            SubstituteSingleReceiverHtmlTemplateTypeKeys(message, recipient);

            using var client = new SmtpClient(config.Smtp.Host)
            {
                Port = config.Smtp.Port,
                DeliveryMethod = SmtpDeliveryMethod.Network,
                UseDefaultCredentials = false
            };

            using var mail = new MailMessage
            {
                From = new MailAddress(config.Smtp.From, message.SenderName ?? config.Smtp.SenderName),
                Subject = recipient.Subject ?? message.Subject,
                Body = recipient.HtmlTemplate,
                IsBodyHtml = true
            };

            recipient.Emails.ForEach(email => mail.To.Add(email));

            if (!recipient.CopiedIn.IsNullOrEmpty())
            {
                foreach (var cc in recipient.CopiedIn.Where(c => !c.BlindCopy))
                {
                    mail.CC.Add(new MailAddress(cc.Email, cc.Name));
                }

                foreach (var bcc in recipient.CopiedIn.Where(c => c.BlindCopy))
                {
                    mail.Bcc.Add(new MailAddress(bcc.Email, bcc.Name));
                }
            }

            if (!recipient.Attachments.IsNullOrEmpty())
            {
                foreach (var attachment in recipient.Attachments)
                {
                    var bytes = await httpClient.ReadPhysicalFileAsByteArray(attachment.Url, ct);
                    if (bytes != null && bytes.Length > 0)
                    {
                        var stream = new MemoryStream(bytes);
                        var file = new Attachment(stream, attachment.FileName + Path.GetExtension(attachment.Url));
                        mail.Attachments.Add(file);
                    }
                }
            }

            await client.SendMailAsync(mail, ct);
            return MessageStatus.Sent;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Messaging, EventAction.SendMail, ex, $"SMTP Send failed for message: {message.MessageId}");
            return MessageStatus.Failed;
        }
    }
}

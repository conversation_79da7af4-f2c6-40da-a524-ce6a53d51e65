﻿using System.Collections.Concurrent;
using LendQube.Entities.Collection.Placements;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;
using LendQube.Infrastructure.Core.ViewModels.Upload;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.PlacementAmendUpload;

internal static class PlacementAmendUploadDataLoader
{
    public static void ValidateData(this AmendUploadVM data, long row, List<Placement> placementSourceAccounts, ConcurrentBag<UploadResult> resultList)
    {
        if (string.IsNullOrEmpty(data.AccountNumber))
        {
            resultList.Add(new(row, data.AccountNumber, "No account number, item will be skipped"));
        }
        else
        {
            var query = placementSourceAccounts.Where(x => x.SourceAccountNumber == data.AccountNumber);
            if (!query.Any())
            {
                resultList.Add(new(row, data.AccountNumber, "Placement for account number not found, item will be skipped"));
            }
            else if (query.Count() > 1)
            {
                resultList.Add(new(row, data.AccountNumber, $"{query.Count()} accounts found with the same account number, refer to admin"));
            }
        }

    }
}

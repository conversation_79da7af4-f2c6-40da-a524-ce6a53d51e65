﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<CustomerMessageEntryVM> messagesTable;
    private ColumnList customerMessagesTableDefinition;
    private readonly PreviewMessageVM previewMessageModel = new();
    private string PreviewMessageModal => "PreviewMessageModal";
    private readonly IReadOnlyList<string> systemKeys = [.. Entities.Core.Extensions.EnumExtensions.GetEnumNames<MessageTemplateSystemTags>()];

    private void SetupMessageTable()
    {
        customerMessagesTableDefinition = CrudService.GetTableDefinition<MessageLogEntry, CustomerMessageEntryVM>();

        customerMessagesTableDefinition.RowActionButtons.Add(new RowActionButton("View", Icon: "eye", Action: async (object row) =>
        {
            await PreviewMessage(row as CustomerMessageEntryVM);
        }));

        messagesTable.SetTableDefinition(customerMessagesTableDefinition);
    }

    private async ValueTask<TypedBasePageList<CustomerMessageEntryVM>> LoadCustomerMessages(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<MessageLogEntry>
        {
            PrimaryCriteria = x => x.Recipients.Any(y => y.UserId == Data.Id)
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
                || EF.Functions.ILike(x.Subject, filterAndPage.TextFilter)
                || EF.Functions.ILike(x.Config.Description, filterAndPage.TextFilter)
                || x.Recipients.Any(y => y.UserId == Data.Id && y.TemplateValues.Any(z => EF.Functions.ILike(z.Value, filterAndPage.TextFilter))));
        }

        var mapping = CustomerMessageEntryVM.Mapping(Data.Id);
        return await CrudService.GetTypeBasedPagedData(spec, filterAndPage, mapping, ct: ct);
    }

    private async Task PreviewMessage(CustomerMessageEntryVM item)
    {
        previewMessageModel.Name = item.Name;
        previewMessageModel.Subject = item.Subject;

        previewMessageModel.TextRequired = item.HasText;
        previewMessageModel.EmailRequired = item.HasHtml;
        if (previewMessageModel.TextRequired)
        {
            if (!string.IsNullOrEmpty(item.TextTemplate))
                previewMessageModel.TextTemplate = item.TextTemplate;
            else
            {
                if (!string.IsNullOrEmpty(item.ContainerTextTemplate))
                {
                    previewMessageModel.TextTemplate = item.ContainerTextTemplate;
                }

                if (!string.IsNullOrEmpty(item.BodyTextTemplate))
                {
                    if (!string.IsNullOrEmpty(previewMessageModel.TextTemplate))
                        previewMessageModel.TextTemplate = previewMessageModel.TextTemplate.Replace("{body}", item.BodyTextTemplate);
                    else
                        previewMessageModel.TextTemplate = item.BodyTextTemplate;
                }
            }
        }

        if (previewMessageModel.EmailRequired)
        {
            if (!string.IsNullOrEmpty(item.HtmlTemplate))
                previewMessageModel.HtmlTemplate = await HttpClient.ReadPhysicalFileAsString(item.HtmlTemplate, ct: Cancel);
            else
            {
                if (!string.IsNullOrEmpty(item.ContainerHtmlTemplate))
                {
                    previewMessageModel.HtmlTemplate = await HttpClient.ReadPhysicalFileAsString(item.ContainerHtmlTemplate, ct: Cancel);
                }

                if (!string.IsNullOrEmpty(item.BodyHtmlTemplate))
                {
                    var bodyTemplate = await HttpClient.ReadPhysicalFileAsString(item.BodyHtmlTemplate, ct: Cancel);
                    if (!string.IsNullOrEmpty(previewMessageModel.HtmlTemplate))
                    {
                        previewMessageModel.HtmlTemplate = previewMessageModel.HtmlTemplate.Replace("{body}", bodyTemplate);
                    }
                    else
                        previewMessageModel.HtmlTemplate = bodyTemplate;
                }
            }

        }

        if (!item.Keys.IsNullOrEmpty())
        {
            foreach (var key in item.Keys.Union(systemKeys).Distinct())
            {
                var keyValue = item.TemplateValues?.FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase))?.Value;
                if (systemKeys.Contains(key))
                {
                    if (key == "firstname")
                    {
                        keyValue = Data.FirstName;
                    }
                    else if (key == "name")
                    {
                        keyValue = Data.FullName;
                    }
                    else if (key == "logo")
                    {
                        keyValue = Config.Url.Logo;
                    }
                }
                if (previewMessageModel.TextRequired && !string.IsNullOrEmpty(previewMessageModel.TextTemplate))
                    previewMessageModel.TextTemplate = previewMessageModel.TextTemplate.Replace($"{{{key}}}", keyValue, StringComparison.OrdinalIgnoreCase);
                if (previewMessageModel.EmailRequired && !string.IsNullOrEmpty(previewMessageModel.HtmlTemplate))
                    previewMessageModel.HtmlTemplate = previewMessageModel.HtmlTemplate.Replace($"{{{key}}}", keyValue, StringComparison.OrdinalIgnoreCase);

                if (!string.IsNullOrEmpty(previewMessageModel.Subject))
                    previewMessageModel.Subject = previewMessageModel.Subject.Replace($"{{{key}}}", keyValue, StringComparison.OrdinalIgnoreCase);
            }
        }

        await JSRuntime.OpenModal(PreviewMessageModal, Cancel);
        StateHasChanged();
    }


}

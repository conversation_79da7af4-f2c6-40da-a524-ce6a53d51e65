﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Placements;

public class Placement : BaseEntityWithHiloId
{
    [DbGuid, Required]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public string SourceAccountNumber { get; set; }
    public string Company { get; set; }
    public PlacementStatus Status { get; set; }
    public DateOnly? AgreementDate { get; set; }
    public DateOnly? DefaultDate { get; set; }
    public DateOnly? LastPaymentDate { get; set; }
    public DateOnly? LastInvoiceDate { get; set; }
    public int PlacementCount { get; set; }
    public decimal MonthlyRecurringBills { get; set; }
    public decimal BalancePrincipal { get; set; }
    public decimal BalanceInterest { get; set; }
    public decimal BalanceFees { get; set; }
    public decimal BalanceTotal { get; set; }
    public decimal BalancePaid { get; set; }
    public decimal Discount { get; set; }
    [DbComputed($@"""{nameof(BalanceTotal)}"" - ""{nameof(BalancePaid)}"" - ""{nameof(Discount)}""")]
    public decimal BalanceRemaining { get; set; }
    public long? FileUploadId { get; set; }
    public virtual CollectionFileUpload FileUpload { get; set; }
    public virtual ICollection<PlacementTag> Tags { get; set; }
    public virtual ICollection<PlacementNotes> Notes { get; set; }
    public virtual ICollection<PlacementTransaction> Transactions { get; set; }
    public virtual ICollection<PlacementActivity> Activities { get; set; }
    public virtual ICollection<PlacementStatusChangeLog> StatusChangeLogs { get; set; }
    public virtual ICollection<PlacementStatusChange> StatusChanges { get; set; }
}

﻿namespace LendQube.Entities.Core.BaseUser;
public static class SystemRoleConfig
{
    public static Guid SuperAdminRoleId => Guid.Parse("08dc9db8-545e-4759-8fa6-d581d78c713c");
    public const string SuperAdminEmail = "<EMAIL>";
    public const string SuperAdminRole = "SuperAdmin";
    public const string SuperAdminPassword = "Super@123";

    public const string CustomerRole = "Customer";
    public const string AdminRole = "Admin";
}

﻿using System.Collections.Concurrent;
using System.Globalization;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.PlacementData;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using MongoDB.Bson;
using NodaTime;
using NodaTime.Extensions;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.Payment;

internal sealed class PaymentUploadService(IUnitofWork uow, IClock clock, ILogManager<PaymentUploadService> logger) : IUploadTypeService
{
    public async Task<Result<MessageBrick>> SaveData(CollectionFileUpload upload, ExcelWorksheet worksheet, MessageBrick message, ConcurrentBag<UploadResult> resultList, CancellationToken ct)
    {
        try
        {
            //load all data into collections
            var start = worksheet.Dimension.Start;
            var end = worksheet.Dimension.End;
            var loadedData = worksheet.Cells[$"{start.Address}:{end.Address}"].ToCollectionWithMappings
                (
                    row =>
                    {
                        var dateString = row.GetValue<string>(1);
                        var hasDate = DateTime.TryParseExact(dateString, "dd/MM/yyyy hh:mm", CultureInfo.InvariantCulture, DateTimeStyles.AssumeLocal, out var date);
                        return new PaymentUploadVM
                        {
                            AccountNumber = row.GetValue<string>(0),
                            TransactionDate = hasDate ? date.ToInstant() : null,
                            Amount = Math.Abs(row.GetValue<decimal>(2)),
                            Type = row.GetValue<string>(3),
                            Reference = row.GetValue<string>(4),
                            Note = row.GetValue<string>(5)
                        };
                    },
                    options =>
                    {
                        options.HeaderRow = null;
                        options.DataStartRow = 1;
                    }
                );

            if (loadedData.Count == 0)
            {
                resultList.Add(new(0, string.Empty, "No records found"));
                return "No records found";
            }


            var parallelData = loadedData.AsParallel();
            var placementAccountIds = parallelData.Select(x => x.AccountNumber).ToList();
            var placementReferences = parallelData.Select(x => x.Reference).ToList();
            var placementData = await uow.Db.ManySelectAsync(Query<Placement, PaymentCustomerDataVM>.Where(x => placementAccountIds.Contains(x.SourceAccountNumber))
                .Select(x => new PaymentCustomerDataVM
                {
                    ProfileId = x.ProfileId,
                    PlacementId = x.Id,
                    PlacementAccountNumber = x.SourceAccountNumber,
                    CurrencySymbol = x.Profile.CurrencySymbol,
                    CurrencyCode = x.Profile.CurrencyCode,
                    AccountBalance = x.Profile.BalanceRemaining,
                    HasSchedule = x.Profile.Schedules.Any(y => y.PaymentStatus < SchedulePaymentStatus.Paid),
                    TransactionExists = x.Profile.AllTransactions.Any(y => placementReferences.Contains(y.ProviderReference))
                }), ct);

            //check that data is valid
            if (upload.Action != UploadAction.Import)
            {
                Parallel.ForEach(parallelData, new ParallelOptions { MaxDegreeOfParallelism = loadedData.Count, CancellationToken = ct }, (item, state, index) =>
                {
                    item.ValidateData(index + 2, placementData, resultList);
                });
            }

            if (upload.Action == UploadAction.Analyze)
                return message;

            ConcurrentBag<Transaction> transactions = [];
            ConcurrentBag<CustomerTransaction> customerTransactions = [];


            message.Message(MessageConfigNames.UploadedPayments.GetDisplayName());

            Parallel.ForEach(parallelData, new ParallelOptions { MaxDegreeOfParallelism = loadedData.Count, CancellationToken = ct }, (item) =>
            {
                var profile = placementData.FirstOrDefault(x => x.PlacementAccountNumber == item.AccountNumber);
                if (profile == null)
                    return;

                var txn = new Transaction
                {
                    Id = ObjectId.GenerateNewId().ToString(),
                    ProviderReference = item.Reference,
                    ProfileId = profile.ProfileId,
                    Purpose = item.Note,
                    Provider = PaymentProvider.Upload,
                    Type = PaymentType.Bank,
                    UnitAmount = item.Amount,
                    Amount = item.Amount,
                    TotalAmountPayable = item.Amount,
                    TotalAmountPaid = item.Amount,
                    Quantity = 1,
                    Fee = 0,
                    Discount = 0,
                    Status = TransactionStatus.Successful,
                    CurrencySymbol = profile.CurrencySymbol,
                    CurrencyCode = profile.CurrencyCode,
                    UserData =
                        [
                            new(TransactionHelper.TransactionType, profile.HasSchedule ? RepaymentTransactionType.ReduceBalance.ToString() : RepaymentTransactionType.RecalculateRepayment.ToString()),
                        ],
                    Fields =
                        [
                            new("Account Balance", profile.CurrencySymbol + profile.AccountBalance.ToString("n2")),
                        ],
                    History = [],
                    CreatedByUser = upload.CreatedByUser,
                };

                var customerTxn = new CustomerTransaction
                {
                    ProfileId = profile.ProfileId,
                    AmountTried = txn.Amount,
                    TransactionId = txn.Id,
                    PaymentProvider = txn.Provider,
                    AmountPaid = txn.TotalAmountPaid,
                    PaymentType = "Uploaded Payment",
                    CreatedByUser = upload.CreatedByUser,
                };

                transactions.Add(txn);
                customerTransactions.Add(customerTxn);
            });

            uow.Db.InsertBulk(transactions, ct);
            uow.Db.InsertBulk(customerTransactions, ct);
            await uow.SaveAsync(ct);

            foreach (var txn in transactions)
            {
                await ApplyPaymentHandler.ApplyPayment(uow, clock, txn, ct, message);
            }

            return message;
        }
        catch (Exception ex)
        {
            resultList.Add(new(0, string.Empty, ex.Message));
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Payment upload failed for upload: {upload.Description}");
        }
        return "Uploading payments failed. Consult admin";
    }
}

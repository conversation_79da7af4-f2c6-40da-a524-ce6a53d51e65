﻿using System.Diagnostics.CodeAnalysis;
using Microsoft.AspNetCore.Components;
using Microsoft.AspNetCore.Http;
using SecurityDriven;

namespace LendQube.Infrastructure.Core.Authentication;

public sealed class IdentityRedirectManager(NavigationManager navigationManager)
{
    private readonly string MFACookieName = "__Host-Identity.MFA";

    private static readonly CookieBuilder MFACookieBuilder = new()
    {
        SecurePolicy = CookieSecurePolicy.Always,
        SameSite = SameSiteMode.Strict,
        HttpOnly = true,
        IsEssential = true,
        MaxAge = TimeSpan.FromMinutes(20),
    };

    [DoesNotReturn]
    public void RedirectTo(string uri)
    {
        if (uri == null)
            navigationManager.NavigateTo("/");

        // Prevent open redirects.
        if (!Uri.IsWellFormedUriString(uri, UriKind.Relative))
        {
            uri = navigationManager.ToBaseRelativePath(uri);
        }

        navigationManager.NavigateTo(uri);
    }

    [DoesNotReturn]
    public void RedirectToAuthWithMFA(HttpContext context, string uri, Dictionary<string, object> queryParameters, string authKey = null)
    {
        if (authKey == null)
        {
            authKey = FastGuid.NewGuid().ToString();
            context.Response.Cookies.Append(MFACookieName, authKey, MFACookieBuilder.Build(context));
        }

        queryParameters["key"] = authKey;
        var uriWithoutQuery = navigationManager.ToAbsoluteUri(uri).GetLeftPart(UriPartial.Path);
        var newUri = navigationManager.GetUriWithQueryParameters(uriWithoutQuery, queryParameters);

        RedirectTo(newUri);
    }

    public bool IsValidMFARouteKey(string authKey, HttpContext context)
    {
        var storedAuth = context.Request.Cookies[MFACookieName];
        return storedAuth != null && storedAuth == authKey;
    }
    public void RemoveMFARouteKey(HttpContext context) => context.Response.Cookies.Delete(MFACookieName);
}


﻿using System.Linq.Expressions;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using MongoDB.Bson;
using SecurityDriven;

namespace LendQube.Infrastructure.Core.Database.Repository;

partial class RelationalDbRepository
{
    private static void SetId<T>(T entity) where T : class
    {
        if (entity is IBaseEntityWithStringId)
        {
            if (entity is BaseEntityWithGuid baseEntityGuid && string.IsNullOrEmpty(baseEntityGuid.Id))
                baseEntityGuid.Id = FastGuid.NewPostgreSqlGuid().ToString();
            else if (entity is BaseEntityWithObjectId baseEntityObjectId && string.IsNullOrEmpty(baseEntityObjectId.Id))
                baseEntityObjectId.Id = ObjectId.GenerateNewId().ToString();
        }
    }

    public void Insert<T>(T entity) where T : class, IBaseEntityForRelationalDb
    {
        if (entity == null)
            return;

        SetId(entity);
        var (valid, message) = IsEntityValid(entity);
        if (!valid)
        {
            var exception = new InvalidOperationException($"Entity insert failed validation. {message}");
            exception.Data.Add("Entity", entity);
            throw exception;
        }
        SetInsertValues(entity, false);
        GetDbSet<T>().Add(entity);
    }

    public Task InsertBulkAsync<T>(IEnumerable<T> entities, CancellationToken ct) where T : BaseEntityWithHiloId
    {
        if (entities.IsNullOrEmpty())
            return Task.CompletedTask;

        T failedValidationEntity = null;
        var validationMessage = string.Empty;

        Parallel.ForEach(entities, new ParallelOptions { MaxDegreeOfParallelism = entities.Count(), CancellationToken = ct }, (entity, state) =>
        {
            SetId(entity);
            (var valid, validationMessage) = IsEntityValid(entity);
            if (!valid)
            {
                failedValidationEntity = entity;
                state.Stop();
            }
            SetInsertValues(entity, true);
        });

        if (failedValidationEntity != null)
        {
            var exception = new InvalidOperationException($"Entity insert failed validation. {validationMessage}");
            exception.Data.Add("Entity", failedValidationEntity);
            throw exception;
        }

        return GetDbSet<T>().AddRangeAsync(entities, ct);
    }

    public void InsertBulk<T>(IEnumerable<T> entities, CancellationToken ct) where T : class, IEntityDoesNotUseHiLoIdentity, IBaseEntityForRelationalDb
    {
        if (entities.IsNullOrEmpty())
            return;

        T failedValidationEntity = null;
        var validationMessage = string.Empty;

        Parallel.ForEach(entities, new ParallelOptions { MaxDegreeOfParallelism = entities.Count(), CancellationToken = ct }, (entity, state) =>
        {
            SetId(entity);
            (var valid, validationMessage) = IsEntityValid(entity);
            if (!valid)
            {
                failedValidationEntity = entity;
                state.Stop();
            }
            SetInsertValues(entity, true);
        });

        if (failedValidationEntity != null)
        {
            var exception = new InvalidOperationException($"Entity insert failed validation. {validationMessage}");
            exception.Data.Add("Entity", failedValidationEntity);
            throw exception;
        }

        GetDbSet<T>().AddRange(entities);
    }


    private static bool HasPoco<T>() => typeof(T).GetProperties().AsParallel().Any(x => x.PropertyType.IsClass
            || (x.PropertyType.IsGenericList() && !x.PropertyType.IsGenericListString() && !typeof(ICollection<>).IsAssignableFrom(x.PropertyType) && !typeof(T).GetProperty(x.Name).GetMethod.IsVirtual && x.CanWrite));

    private static void SetVirtualPropertiesToNull<T>(T entity) //make sure not to try to insert relational navigation properties
    {
        var properties = typeof(T).GetProperties().Where(x => typeof(T).GetProperty(x.Name).GetMethod.IsVirtual && x.CanWrite);
        var count = properties.Count();
        if (count == 0)
            return;

        Parallel.ForEach(properties, new ParallelOptions { MaxDegreeOfParallelism = count }, (property) =>
        {
            property.SetValue(entity, null);
        });
    }

    public void Update<T>(T entity) where T : class, IBaseEntityForRelationalDb
    {
        if (entity == null)
            return;

        try
        {
            var (valid, message) = IsEntityValid(entity);
            if (!valid)
            {
                var exception = new InvalidOperationException($"Entity update failed validation. {message}");
                exception.Data.Add("Entity", entity);
                throw exception;
            }
            SetUpdateValues(entity, false);
            var dbSet = GetDbSet<T>();
            var hasPoco = HasPoco<T>();
            if (hasPoco)
            {
                SetVirtualPropertiesToNull(entity);
                dbSet.Add(entity); // enables update of POCO Json Columns by regenerating shadow keys
            }

            dbSet.Update(entity);

            var propertiesNotToUpdate = dbSet.Update(entity).Properties.AsParallel().Where(item => RepositoryHelper.CreatedStamp.Any(x => x == item.Metadata.Name) || item.Metadata.IsKey());

            Parallel.ForEach(propertiesNotToUpdate, new ParallelOptions { MaxDegreeOfParallelism = propertiesNotToUpdate.Count() }, (item) =>
            {
                item.IsModified = false;
            });
        }
        catch (Exception)
        {
            context.ChangeTracker.Clear();
            throw;
        }
    }

    public void UpdateBulk<T>(List<T> entities, CancellationToken ct) where T : class, IBaseEntityForRelationalDb
    {
        try
        {
            if (entities.IsNullOrEmpty())
                return;

            T failedValidationEntity = null;
            var validationMessage = string.Empty;

            Parallel.ForEach(entities, new ParallelOptions { MaxDegreeOfParallelism = entities.Count }, (entity, state) =>
            {
                (var valid, validationMessage) = IsEntityValid(entity);
                if (!valid)
                {
                    failedValidationEntity = entity;
                    state.Stop();
                }
                SetUpdateValues(entity, true);
            });

            if (failedValidationEntity != null)
            {
                var exception = new InvalidOperationException($"Entity update failed validation. {validationMessage}");
                exception.Data.Add("Entity", failedValidationEntity);
                throw exception;
            }

            var dbSet = GetDbSet<T>();
            var hasPoco = HasPoco<T>();
            if (hasPoco)
            {
                Parallel.ForEach(entities, new ParallelOptions { MaxDegreeOfParallelism = entities.Count, CancellationToken = ct }, (item) =>
                {
                    SetVirtualPropertiesToNull(item);
                });
                dbSet.AddRange(entities); // enables update of POCO Json Columns by regenerating shadow keys
            }

            dbSet.UpdateRange(entities);

            var propertiesNotToUpdate = dbSet.Entry(entities[0]).Properties.AsParallel().Where(item => RepositoryHelper.CreatedStamp.Any(x => x == item.Metadata.Name) || item.Metadata.IsKey());

            Parallel.ForEach(propertiesNotToUpdate, new ParallelOptions { MaxDegreeOfParallelism = propertiesNotToUpdate.Count(), CancellationToken = ct }, (item) =>
            {
                Parallel.ForEach(entities, new ParallelOptions { MaxDegreeOfParallelism = entities.Count }, (entry) =>
                {
                    dbSet.Entry(entry).Property(item.Metadata.Name).IsModified = false;
                });
            });
        }
        catch (Exception)
        {
            context.ChangeTracker.Clear();
            throw;
        }
    }

    public int UpdateAndSaveWithFilter<T>(Expression<Func<T, bool>> filters, Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> updates) where T : class, IBaseEntityForRelationalDb
    {
        if (typeof(IBaseEntityWithSystemStamp).IsAssignableFrom(typeof(T)))
        {
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).LastModifiedDate, userInfo.Now));
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).ModifiedByIp, userInfo.IpAddress));
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).ModifiedByUser, userInfo.Name));
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).ModifiedByUserId, userInfo.UserId));
        }

        return GetDbSet<T>().Where(filters).ExecuteUpdate(updates);
    }

    public Task<int> UpdateAndSaveWithFilterAsync<T>(Expression<Func<T, bool>> filters, Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> updates, CancellationToken ct) where T : class, IBaseEntityForRelationalDb
    {
        if (typeof(IBaseEntityWithSystemStamp).IsAssignableFrom(typeof(T)))
        {
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).LastModifiedDate, userInfo.Now));
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).ModifiedByIp, userInfo.IpAddress));
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).ModifiedByUser, userInfo.Name));
            updates = updates.AppendSetProperty(x => x.SetProperty(p => (p as IBaseEntityWithSystemStamp).ModifiedByUserId, userInfo.UserId));
        }

        return GetDbSet<T>().Where(filters).ExecuteUpdateAsync(updates, ct);
    }


    public Task<int> DeleteAndSaveWithFilterAsync<T>(Expression<Func<T, bool>> filter, CancellationToken ct) where T : class, IBaseEntityForRelationalDb
        => GetDbSet<T>().Where(filter).ExecuteDeleteAsync(ct);

    public void Delete<T>(T entity) where T : class, IBaseEntityForRelationalDb
    {
        if (entity != null)
        {
            if (context.Entry(entity).State == EntityState.Detached)
            {
                GetDbSet<T>().Attach(entity);
            }
            GetDbSet<T>().Remove(entity);
        }
    }


    public void RollbackSingle<T>(T entity) where T : class, IBaseEntityForRelationalDb
    {
        var entry = context.Entry(entity);
        entry.State = entry.State switch
        {
            EntityState.Added => EntityState.Detached,
            _ => EntityState.Unchanged,
        };
    }

    public void RollbackAll() => context.ChangeTracker.Clear();


    public async Task<bool> TruncateTable<T>(CancellationToken ct) where T : class, IBaseEntityForRelationalDb
    {
        var query = string.Empty;
        try
        {
            var tableData = context.Model.FindEntityType(typeof(T));
            var hasIdentity = tableData.GetKeys().Any(x => x.Properties.Any(y => y.ValueGenerated == Microsoft.EntityFrameworkCore.Metadata.ValueGenerated.OnAdd));
            var hasDependencies = tableData.GetReferencingForeignKeys().Any();
            var tableName = tableData.GetTableName();
            var schema = tableData.GetSchema();
            query = $"TRUNCATE \"{schema}\".\"{tableName}\" {(hasIdentity ? "RESTART IDENTITY" : "")} {(hasDependencies ? "CASCADE" : "")};";
            _ = await context.Database.ExecuteSqlRawAsync(query, ct);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.ExceptionOrError, ex, $"{query} failed");
        }
        return false;
    }

    public async Task<bool> Upsert<T>(Dictionary<string, string> columnsWithValues, CancellationToken ct) where T : class, IBaseEntityForRelationalDb
    {
        var query = string.Empty;
        try
        {
            var tableData = context.Model.FindEntityType(typeof(T));
            var tableName = tableData.GetTableName();
            var schema = tableData.GetSchema();
            query = $"INSERT INTO \"{schema}\".\"{tableName}\" ({string.Join(", ", columnsWithValues.Select(y => y.Key))}) " +
                $"VALUES ({string.Join(", ", columnsWithValues.Select(y => y.Value))}) " +
                $"ON CONFLICT ({columnsWithValues.Keys.FirstOrDefault()}) " +
                $"DO UPDATE SET {string.Join(", ", columnsWithValues.Select(y => $"{y.Key} = EXCLUDED.{y.Key}"))};";
            _ = await context.Database.ExecuteSqlRawAsync(query, ct);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.ExceptionOrError, ex, $"{query} failed");
        }
        return false;
    }
}

﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Customers;

public class CustomerActivity : BaseActivityTimeline<CustomerActivity>
{
    [DbGuid]
    public required string ProfileId { get; set; }

    public override void Configure(EntityTypeBuilder<CustomerActivity> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.ProfileId);
    }
}

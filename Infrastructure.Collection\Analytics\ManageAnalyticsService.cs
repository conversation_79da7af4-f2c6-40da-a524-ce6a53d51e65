﻿using LendQube.Entities.Collection.Analytics;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Collection.Analytics;

internal static partial class ManageAnalyticsService
{
    public static async Task Seed(IUnitofWork uow)
    {
        if (!await uow.Db.ExistsAsync<DashboardAnalytics>())
        {
            uow.Db.Insert(new DashboardAnalytics());
            await uow.SaveAsync(default);
        }
    }

    public static Task<int> UpdateTotalCustomers(IUnitofWork uow, int customerCount, int placementCount, decimal placementValue, int closedPlacementCount, decimal closedPlacementValue, CancellationToken ct)
    {
        if (customerCount == 0 && placementCount == 0 && placementValue == 0 && closedPlacementCount == 0 && closedPlacementValue == 0)
            return Task.FromResult(0);

        return uow.Db.UpdateAndSaveWithFilterAsync<DashboardAnalytics>(x => x.Id != 0, x => x.SetProperty(y => y.TotalCustomers, y => y.TotalCustomers + customerCount)
        .SetProperty(y => y.TotalPlacements, y => y.TotalPlacements + placementCount)
        .SetProperty(y => y.TotalPlacementValue, y => y.TotalPlacementValue + placementValue)
        .SetProperty(y => y.TotalClosedPlacements, y => y.TotalClosedPlacements + closedPlacementCount)
        .SetProperty(y => y.TotalClosedPlacementValue, y => y.TotalClosedPlacementValue + closedPlacementValue)
        , ct);
    }

    public static Task<int> UpdateTotalSignedInCustomers(IUnitofWork uow, CancellationToken ct)
        => uow.Db.UpdateAndSaveWithFilterAsync<DashboardAnalytics>(x => x.Id != 0, x => x.SetProperty(y => y.TotalSignedInCustomers, y => y.TotalSignedInCustomers + 1), ct);

    public static async Task UpdateTotalCustomersWithSchedules(IUnitofWork uow, CancellationToken ct)
    {
        _ = await uow.Db.UpdateAndSaveWithFilterAsync<DashboardAnalytics>(x => x.Id != 0, x => x.SetProperty(y => y.TotalCustomersWithSchedules, y => y.TotalCustomersWithSchedules + 1), ct);
        var now = DateOnly.FromDateTime(DateTime.UtcNow);

        if (!await uow.Db.ExistsAsync<DashboardTimeAnalytics>(x => x.Date == now, ct))
        {
            try
            {
                uow.Db.Insert(new DashboardTimeAnalytics
                {
                    Date = now,
                    TotalCustomersThatCreatedSchedule = 0,
                });
                await uow.SaveAsync(ct);
            }
            catch (Exception)
            {

            }
        }

        await uow.Db.UpdateAndSaveWithFilterAsync<DashboardTimeAnalytics>(x => x.Date == now, x => x.SetProperty(y => y.TotalCustomersThatCreatedSchedule, y => y.TotalCustomersThatCreatedSchedule + 1), ct);
    }

    public static async Task UpdateTotalPlacementValuePaid(IUnitofWork uow, decimal value, CancellationToken ct)
    {
        _ = await uow.Db.UpdateAndSaveWithFilterAsync<DashboardAnalytics>(x => x.Id != 0, x => x.SetProperty(y => y.TotalPlacementValuePaid, y => y.TotalPlacementValuePaid + value), ct);
        var now = DateOnly.FromDateTime(DateTime.UtcNow);

        if (!await uow.Db.ExistsAsync<DashboardTimeAnalytics>(x => x.Date == now, ct))
        {
            try
            {
                uow.Db.Insert(new DashboardTimeAnalytics
                {
                    Date = now,
                    TotalAmountPaid = 0,
                });
                await uow.SaveAsync(ct);
            }
            catch (Exception)
            {

            }
        }

        await uow.Db.UpdateAndSaveWithFilterAsync<DashboardTimeAnalytics>(x => x.Date == now, x => x.SetProperty(y => y.TotalAmountPaid, y => y.TotalAmountPaid + value), ct);
    }

    public static Task<int> UpdateTotalSettledPlacements(IUnitofWork uow, int count, CancellationToken ct)
        => uow.Db.UpdateAndSaveWithFilterAsync<DashboardAnalytics>(x => x.Id != 0, x => x.SetProperty(y => y.TotalSettledPlacements, y => y.TotalSettledPlacements + count), ct);

    public static Task<int> UpdateTotalClosedPlacements(IUnitofWork uow, int count, decimal value, CancellationToken ct)
        => uow.Db.UpdateAndSaveWithFilterAsync<DashboardAnalytics>(x => x.Id != 0, x => x.SetProperty(y => y.TotalClosedPlacements, y => y.TotalClosedPlacements + count)
        .SetProperty(y => y.TotalClosedPlacementValue, y => y.TotalClosedPlacementValue + value), ct);
}

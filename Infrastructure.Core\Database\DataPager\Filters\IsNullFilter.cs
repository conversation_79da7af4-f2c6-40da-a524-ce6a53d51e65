﻿using System.Linq.Expressions;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

internal sealed class IsNullFilter() : ObjectFilter(ColumnFilterRule.IsNull)
{
    public override bool RequiresValue { get; set; } = false;
    public override Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value)
    {
        if (propertyType.IsGenericList())
            return ExpressionsExtension.NotAnyExpression<T>(propertyType, vm.ColumnName);

        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(vm.ColumnName);

        if (propertyType == typeof(string))
        {
            MethodCallExpression isNullOrEmptyCall = Expression.Call(typeof(string), nameof(string.IsNullOrWhiteSpace), null, expression);
            return Expression.Lambda<Func<T, bool>>(isNullOrEmptyCall, pe);
        }

        UnaryExpression typeNull = Expression.ConvertChecked(Expression.Constant(null), propertyType);
        ConstantExpression nullConstant = Expression.Constant(null);
        BinaryExpression nullCheck = Expression.OrElse(Expression.Equal(expression, typeNull), Expression.Equal(expression, nullConstant));

        return Expression.Lambda<Func<T, bool>>(nullCheck, pe);
    }

    public override bool IsTypeSupported(ObjectFilterRule rule) => Type.GetTypeCode(rule.Type) switch
    {
        TypeCode.String or TypeCode.DBNull or TypeCode.Empty or TypeCode.Object => true,
        _ => false,
    } && !rule.IsId && rule.FilterType != ColumnFilterDataType.DBList;
}
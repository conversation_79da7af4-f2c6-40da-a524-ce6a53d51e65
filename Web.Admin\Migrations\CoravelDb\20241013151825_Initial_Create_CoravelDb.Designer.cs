﻿// <auto-generated />
using System;
using LendQube.Infrastructure.Core.Database.DbContexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations.CoravelDb
{
    [DbContext(typeof(CoravelDbContext))]
    [Migration("20241013151825_Initial_Create_CoravelDb")]
    partial class Initial_Create_CoravelDb
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasDefaultSchema("coravel")
                .HasAnnotation("ProductVersion", "8.0.10")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Coravel.Pro.EntityFramework.CoravelJobHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("Failed")
                        .HasColumnType("boolean");

                    b.Property<string>("StackTrace")
                        .HasColumnType("text");

                    b.Property<DateTime?>("StartedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("TypeFullPath")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("EndedAt");

                    b.ToTable("Coravel_JobHistory", "coravel");
                });

            modelBuilder.Entity("Coravel.Pro.EntityFramework.CoravelScheduledJob", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<bool>("Active")
                        .HasColumnType("boolean");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CronExpression")
                        .HasColumnType("text");

                    b.Property<string>("Days")
                        .HasColumnType("text");

                    b.Property<string>("Frequency")
                        .HasColumnType("text");

                    b.Property<string>("InvocableFullPath")
                        .HasColumnType("text");

                    b.Property<bool>("PreventOverlapping")
                        .HasColumnType("boolean");

                    b.Property<bool>("RunOnDedicatedThread")
                        .HasColumnType("boolean");

                    b.Property<string>("TimeZoneInfo")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Active");

                    b.HasIndex("InvocableFullPath");

                    b.ToTable("Coravel_ScheduledJobs", "coravel");
                });

            modelBuilder.Entity("Coravel.Pro.EntityFramework.CoravelScheduledJobHistory", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseIdentityByDefaultColumn(b.Property<int>("Id"));

                    b.Property<string>("DisplayName")
                        .HasColumnType("text");

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ErrorMessage")
                        .HasColumnType("text");

                    b.Property<bool>("Failed")
                        .HasColumnType("boolean");

                    b.Property<string>("StackTrace")
                        .HasColumnType("text");

                    b.Property<string>("TypeFullPath")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("EndedAt");

                    b.ToTable("Coravel_ScheduledJobHistory", "coravel");
                });
#pragma warning restore 612, 618
        }
    }
}

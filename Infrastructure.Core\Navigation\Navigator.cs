﻿using System.Reflection;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Core.Navigation;

public static class Navigator
{
    static Navigator()
    {
        var interfaceType = typeof(INavigationDescriptor);
        var navigationAssemblies = Assembly.GetEntryAssembly()
            .GetReferencedAssemblies()
            .AsParallel()
            .Where(x => x.Name != null && x.Name.StartsWith(CoreEntityConfig.InfrastructurePrefix))
            .Select(Assembly.Load)
            .SelectMany(x => x.GetTypes())
            .Where(x => x.Name != null && x.Name.EndsWith("Navigation"));

        var asm = navigationAssemblies
            .Where(x => interfaceType.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
            .Select(x => Activator.CreateInstance(x) as INavigationDescriptor)
            .Where(x => !x.IsDisabled)
            .ToList();

        foreach (var item in asm)
        {
            item.PrepareNavigator();
            if (item is INavigatorHasPermissions permissionNavigator)
                permissionNavigator.PreparePermissionDescriptions();
        }

        var permissionsOnlyInterfaceType = typeof(INavigatorHasPermissions);

        var permissionsAsm = navigationAssemblies
           .Where(x => permissionsOnlyInterfaceType.IsAssignableFrom(x) && !interfaceType.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
           .Select(x => Activator.CreateInstance(x) as INavigatorHasPermissions)
           .ToList();

        foreach (var item in permissionsAsm)
        {
            item.PreparePermissionDescriptions();
        }

        OrderNavigator();
    }

    public static Dictionary<int, string> NavigatorOrder { get; set; } = [];
    public static Dictionary<string, string> PermissionDescription { get; set; } = [];
    public static Dictionary<string, NavigatorVM> GeneralNavigator { get; private set; } = [];

    public static void SetupModuleNavigation(int order, string groupName, NavigatorVM navigation)
    {
        GeneralNavigator[groupName] = navigation;
        NavigatorOrder[order] = groupName;
    }

    private static void OrderNavigator()
    {
        GeneralNavigator = GeneralNavigator
            .OrderBy(d2Item => NavigatorOrder.FirstOrDefault(d1Item => d1Item.Value == d2Item.Key).Key)
            .ToDictionary(item => item.Key, item => item.Value);
    }

    public static string GetPermissionDescription(string permission)
    {
        _ = PermissionDescription.TryGetValue(permission, out var description);
        return description;
    }

    public static void ClearPermissionDescriptions() => PermissionDescription.Clear();
}

public class NavigatorVM
{
    public string Name { get; set; }
    public string Permission { get; set; }
    public string Icon { get; set; }
    public string Controller { get; set; }
    public string Action { get; set; }
    public string Target { get; set; } = "_self";
    public string Url { get; set; }
    public string NavLink => !string.IsNullOrEmpty(Url) ? Url.ToLower() : string.IsNullOrEmpty(Controller) ? "" : $"{Controller}{(string.IsNullOrEmpty(Action) ? "" : $"/{Action}")}".ToLower();
    public bool DoNotShowInMainMenu { get; set; }
    public List<NavigatorVM> SubNavigation { get; set; } = [];
}
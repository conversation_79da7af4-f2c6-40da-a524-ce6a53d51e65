﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Infrastructure.Collection.Helpers;
using LendQube.Infrastructure.Collection.ViewModels.PlacementData;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.ApiControllers;

[Route(ApiConstants.URL)]
public sealed class ViewPlacementController(GeneralGenericCrudVMService service) : ApiAuthControllerBase
{

    [HttpPost($"{nameof(GetActive)}")]
    public async Task<TypedBasePageList<PlacementApiVM>> GetActive(DataFilterAndPage vm, CancellationToken ct)
    {
        var spec = new BaseSpecification<Placement>
        {
            PrimaryCriteria = x => x.ProfileId == User.Identity.Name && PlacementHelper.CurrentStatus.HasFlag(x.Status)
        };

        if (!string.IsNullOrEmpty(vm.TextFilter))
        {
            vm.TextFilter = $"%{vm.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Company, vm.TextFilter));
        }

        var data = await service.GetTypeBasedPagedData(spec, vm, PlacementApiVM.Mapping, ct);
        return data;
    }


    [HttpPost($"{nameof(GetAll)}")]
    public async Task<TypedBasePageList<PlacementApiVM>> GetAll(DataFilterAndPage vm, CancellationToken ct)
    {
        var spec = new BaseSpecification<Placement>
        {
            PrimaryCriteria = x => x.ProfileId == User.Identity.Name && x.Status < PlacementStatus.Closed,
            OrderBy = x => x.OrderByDescending(y => y.LastModifiedDate).ThenBy(y => y.Status)
        };

        vm.OrderByColumn = null;
        if (!string.IsNullOrEmpty(vm.TextFilter))
        {
            vm.TextFilter = $"%{vm.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.Like(x.Company, vm.TextFilter));
        }

        var data = await service.GetTypeBasedPagedData(spec, vm, PlacementApiVM.Mapping, ct);
        return data;
    }


    [HttpPost($"{nameof(GetAllTransactions)}")]
    public async Task<TypedBasePageList<CustomerTransactionApiVM>> GetAllTransactions(DataFilterAndPage vm, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerTransaction>
        {
            PrimaryCriteria = x => x.ProfileId == User.Identity.Name && x.Successful && x.PaymentApplied,
            OrderBy = x => x.OrderByDescending(y => y.CreatedDate)
        };

        vm.OrderByColumn = null;

        if (!string.IsNullOrEmpty(vm.TextFilter))
        {
            vm.TextFilter = $"%{vm.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.Like(x.PaymentType, vm.TextFilter)
            || EF.Functions.Like(x.PaymentMethod, vm.TextFilter)
            );
        }

        var data = await service.GetTypeBasedPagedData(spec, vm, CustomerTransactionApiVM.Mapping, ct);
        return data;
    }

    [HttpPost($"{nameof(GetPlacementTransactions)}/{{id}}")]
    public async Task<TypedBasePageList<PlacementTransactionApiVM>> GetPlacementTransactions(string id, DataFilterAndPage vm, CancellationToken ct)
    {
        var spec = new BaseSpecification<PlacementTransaction>
        {
            PrimaryCriteria = x => x.ProfileId == User.Identity.Name && x.Placement.SourceAccountNumber == id,
            OrderBy = x => x.OrderByDescending(y => y.CreatedDate)
        };

        vm.OrderByColumn = null;

        if (!string.IsNullOrEmpty(vm.TextFilter))
        {
            vm.TextFilter = $"%{vm.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.Like(x.Placement.Company, vm.TextFilter)
            || EF.Functions.Like(x.Placement.SourceAccountNumber, vm.TextFilter) || EF.Functions.Like(x.PaymentMethod, vm.TextFilter)
            );
        }

        var data = await service.GetTypeBasedPagedData(spec, vm, PlacementTransactionApiVM.Mapping, ct);
        return data;
    }
}
﻿using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.PermissionsAndRoles;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using ScottBrady91.AspNetCore.Identity;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace LendQube.Infrastructure.Core.DependencyInjection;

internal static class IdentityDI
{
    public static OpenIddictBuilder AddOpenIddictCore(this IServiceCollection services)
        => services.AddOpenIddict()
            .AddCore(options =>
            {
                options.UseEntityFrameworkCore()
                .UseDbContext<AppDbContext>()
                .ReplaceDefaultEntities<Guid>();
            });

    public static IServiceCollection AddOpenIddictApi(this IServiceCollection services)
    {
        services.AddOpenIddictCore()
            .AddServer(options =>
            {
                options.SetTokenEndpointUris("/connect/token")
                       .AllowRefreshTokenFlow()
                       .AllowPasswordFlow()
                       .AcceptAnonymousClients();

                options.SetAccessTokenLifetime(TimeSpan.FromHours(1))
                       .SetRefreshTokenLifetime(TimeSpan.FromHours(2));

                options.AddEphemeralEncryptionKey()
                       .AddEphemeralSigningKey();

                options.UseAspNetCore()
                       .EnableTokenEndpointPassthrough()
                       .SuppressJsonResponseIndentation();

                options.RegisterScopes(
                    Scopes.Profile,
                    Scopes.Roles);

                options.UseDataProtection();
            })
            .AddValidation(options =>
            {
                options.UseDataProtection();
                options.UseLocalServer();
                options.UseAspNetCore();
            });

        return services;
    }

    public static IServiceCollection AddIdentity(this IServiceCollection services)
    {
        services.AddIdentity<ApplicationUser, ApplicationRole>(cfg =>
        {
            cfg.Lockout.MaxFailedAccessAttempts = 3;

            cfg.User.RequireUniqueEmail = true;
            cfg.Password.RequireDigit = false;
            cfg.Password.RequiredUniqueChars = 0;
            cfg.Password.RequireLowercase = false;
            cfg.Password.RequireNonAlphanumeric = false;
            cfg.Password.RequireUppercase = false;

            cfg.ClaimsIdentity.UserNameClaimType = Claims.Name;
            cfg.ClaimsIdentity.UserIdClaimType = Claims.Subject;
            cfg.ClaimsIdentity.RoleClaimType = Claims.Role;


        })
        .AddEntityFrameworkStores<AppDbContext>()
        .AddDefaultTokenProviders();

        return services;
    }

    public static IServiceCollection AddAdminIdentity(this IServiceCollection services)
    {
        services.AddDataProtection().PersistKeysToDbContext<AppDbContext>();

        services.AddSingleton<IAuthorizationPolicyProvider, PermissionPolicyProvider>();
        services.AddScoped<IAuthorizationHandler, PermissionAuthorizationHandler>();

        services.AddTransient<ITicketStore, TicketStore>();

        services.AddAuthentication(options =>
        {
            options.DefaultScheme = IdentityConstants.ApplicationScheme;
            options.DefaultSignInScheme = IdentityConstants.ExternalScheme;
        })
        .AddIdentityCookies();

        services.AddIdentityCore<ApplicationUser>(cfg =>
        {
            cfg.Lockout.MaxFailedAccessAttempts = 3;

            cfg.User.RequireUniqueEmail = true;
            cfg.Password.RequireDigit = false;
            cfg.Password.RequiredUniqueChars = 0;
            cfg.Password.RequireLowercase = false;
            cfg.Password.RequireNonAlphanumeric = false;
            cfg.Password.RequireUppercase = false;

            cfg.ClaimsIdentity.UserNameClaimType = Claims.Name;
            cfg.ClaimsIdentity.UserIdClaimType = Claims.Subject;
            cfg.ClaimsIdentity.RoleClaimType = Claims.Role;
        })
        .AddRoles<ApplicationRole>()
        .AddEntityFrameworkStores<AppDbContext>()
        .AddSignInManager()
        .AddDefaultTokenProviders();

        services.AddOptions<CookieAuthenticationOptions>(IdentityConstants.ApplicationScheme)
            .Configure<ITicketStore>((options, store) =>
            {
                options.SessionStore = store;
                options.Cookie = new CookieBuilder()
                {
                    Name = ConfigConstants.CookieName,
                    IsEssential = true,
                    SameSite = SameSiteMode.Strict,
                    HttpOnly = true,
                    SecurePolicy = CookieSecurePolicy.Always,
                };
                options.ExpireTimeSpan = TimeSpan.FromHours(3);
                options.SlidingExpiration = true;
            });

        services.Configure<CookieAuthenticationOptions>(IdentityConstants.TwoFactorRememberMeScheme, options =>
        {
            options.Cookie = new CookieBuilder()
            {
                Name = ConfigConstants.CookieTFAName,
                IsEssential = true,
                SameSite = SameSiteMode.Strict,
                HttpOnly = true,
                SecurePolicy = CookieSecurePolicy.Always,
            };
            options.ExpireTimeSpan = TimeSpan.FromDays(30);
        });

        services.Configure<CookieAuthenticationOptions>(IdentityConstants.TwoFactorUserIdScheme, options =>
        {
            options.Cookie = new CookieBuilder()
            {
                Name = ConfigConstants.CookieTFAUserName,
                IsEssential = true,
                SameSite = SameSiteMode.Strict,
                HttpOnly = true,
                SecurePolicy = CookieSecurePolicy.Always,
            };
        });

        services.AddMFA();

        return services;
    }

    public static IServiceCollection AddAntiforgeryToken(this IServiceCollection services)
    {
        services.AddAntiforgery(options =>
        {
            options.HeaderName = "X-CSRF-TOKEN";
            options.SuppressXFrameOptionsHeader = false;
            options.Cookie = new CookieBuilder()
            {
                Name = ConfigConstants.CookieAntiforgeryName,
                IsEssential = true,
                SameSite = SameSiteMode.Strict,
                HttpOnly = true,
                SecurePolicy = CookieSecurePolicy.Always,
            };
        });

        return services;
    }

    public static IServiceCollection AddCustomHasher(this IServiceCollection services)
    {
        services.Configure<Argon2PasswordHasherOptions>(options =>
        {
            options.Strength = Argon2HashStrength.Interactive;
        });

        services.AddTransient<IPasswordHasher<ApplicationUser>, Argon2PasswordHasher<ApplicationUser>>();
        return services;
    }

    public static IServiceCollection AddMFA(this IServiceCollection services)
    {
        services
            .AddAuthorizationBuilder()
            .AddPolicy(ConfigConstants.MFAPolicy, x => x.RequireClaim("amr", "mfa"));

        return services;
    }

}

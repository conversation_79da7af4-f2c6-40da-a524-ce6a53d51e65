﻿@typeparam T where T : class, IBaseEntityForRelationalDb, new()

@inherits TableComponentBase<T>

<PageTitle>@Title</PageTitle>

<div class="pg-row grid grid-col-1 grid-tab-1">
    <div class="card">

        <div class="title-wrapper flex __justify-between __align-center">
            <span class="text_xl_medium">@SubTitle</span>
        </div>
        <StatusMessage @ref="TableMessage" />

        <DataTable T="T" TableDefinition="TableDefinition" LoadData="Load" EditRow="StartEdit" DeleteRow="SubmitDelete"  @ref="table" />

    </div>
</div>


@code
{
    [Inject]
    protected GenericSpecificationService<T> Service { get; set; }

    protected DataTable<T> table;

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition();

    protected override void DefinePrimaryCriteria(DataFilterAndPage filterAndPage) => Service.PrimaryCriteria = null;

    private ValueTask<TypedBasePageList<T>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        CloseMessage();
        DefinePrimaryCriteria(filterAndPage);
        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            GeneralSearchQuery(filterAndPage);
        }
        return Service.CrudService.GetTypeBasedPagedData(Service, filterAndPage, ct: ct);
    }


    protected virtual ValueTask SubmitAdd() => SaveAdd(Service.CrudService.New, table.Refresh);

    protected virtual ValueTask StartEdit(T data, CancellationToken ct) => Edit(() => Task.FromResult(data), ct);

    protected virtual ValueTask SubmitEdit() => SaveEdit(Service.CrudService.Update, table.Refresh);

    protected virtual ValueTask<bool> SubmitDelete(T data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(data, ct), refresh);

}

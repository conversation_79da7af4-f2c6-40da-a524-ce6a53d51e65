﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Customers;

public class CustomerSchedule : BaseEntityWithIdentityId<CustomerSchedule>
{
    [DbGuid, Required]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public int Period { get; set; }
    public DateOnly DueDate { get; set; }
    public decimal Amount { get; set; }
    public decimal AmountPaid { get; set; }
    public DateOnly CPADate { get; set; }
    public SchedulePeriodStatus PeriodStatus { get; set; }
    public SchedulePaymentStatus PaymentStatus { get; set; }
    [DbComputed($@"""{nameof(Amount)}"" - ""{nameof(AmountPaid)}""")]
    public decimal Balance { get; set; }
}


public enum SchedulePaymentFrequency
{
	Weekly,
	Monthly
}
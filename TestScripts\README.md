# LendQube Test Scripts

This directory contains test scripts to diagnose and test various LendQube functionalities, specifically designed to capture detailed error information for debugging purposes.

## Scripts Overview

### 1. PaymentGatewayErrorCapture.cs
**Purpose**: Captures exact errors from the Acquired payment gateway
- Tests all Acquired API endpoints (CreateCustomer, GeneratePaymentLink, GetTransaction, ChargeCard)
- Captures detailed request/response data
- Logs authentication issues and API failures
- Provides comprehensive error reporting

### 2. EmailFunctionTester.cs
**Purpose**: Tests email functionality across all configured providers
- Tests SMTP, SendGrid, and Mailgun providers
- Validates email template processing
- Tests HTML and text email formats
- Captures provider-specific errors

### 3. SmsFunctionTester.cs
**Purpose**: Tests SMS functionality across all configured providers
- Tests Termii, Terragon, ClickSend, TextLocal, and Twilio providers
- Validates SMS template processing
- Tests country code support
- Tests bulk messaging capabilities

## Prerequisites

1. **Configuration**: Ensure your `appsettings.json` contains the necessary provider configurations:
   ```json
   {
     "Providers": {
       "Acquired": {
         "BaseApiUrl": "https://api.acquired.com/",
         "AppId": "your_app_id",
         "AppKey": "your_app_key"
       },
       "Smtp": {
         "Host": "your_smtp_host",
         "Port": 25,
         "From": "<EMAIL>",
         "SenderName": "Your Company"
       },
       "Termii": {
         "BaseUrl": "https://api.ng.termii.com/api/",
         "ApiKey": "your_termii_api_key"
       }
       // ... other provider configurations
     }
   }
   ```

2. **Dependencies**: The scripts reference the main LendQube projects, so ensure they are built successfully.

## How to Run

### Option 1: Run Individual Scripts

1. **Payment Gateway Testing**:
   ```bash
   cd TestScripts
   dotnet run --project LendQube.TestScripts.csproj PaymentGatewayErrorCapture
   ```

2. **Email Testing**:
   ```bash
   cd TestScripts
   dotnet run --project LendQube.TestScripts.csproj EmailFunctionTester
   ```

3. **SMS Testing**:
   ```bash
   cd TestScripts
   dotnet run --project LendQube.TestScripts.csproj SmsFunctionTester
   ```

### Option 2: Modify Main Method

Each script has its own `Main` method. You can modify the project file to set the startup object:

```xml
<PropertyGroup>
  <StartupObject>LendQube.TestScripts.PaymentGatewayErrorCapture</StartupObject>
</PropertyGroup>
```

## Understanding the Output

### Payment Gateway Errors
The script will show:
- ✅ **SUCCESS**: API call completed successfully
- ❌ **FAILED**: API call failed with detailed error information
- 💥 **EXCEPTION**: Unexpected exception occurred

Example output:
```
🔍 Testing: Create Customer
─────────────────────────────
❌ Create Customer: FAILED
   Request Data: {"ip":"***********","email":"<EMAIL>"...}
   Response Data: {"error":"Invalid API credentials","code":"AUTH_001"}
   Error Details: Authentication failed
```

### Email/SMS Errors
Similar format with provider-specific information:
```
🔍 Testing Email Provider: SmtpEmailProvider
─────────────────────────────────────
❌ SmtpEmailProvider: FAILED - Status: Failed
   Exception Type: SmtpException
   Message: Unable to connect to SMTP server
```

## Common Issues and Solutions

### Payment Gateway Issues

1. **Authentication Errors**:
   - Check `AppId` and `AppKey` in configuration
   - Verify API credentials with Acquired support
   - Check if API endpoint URL is correct

2. **Network Issues**:
   - Verify firewall settings
   - Check if the server can reach Acquired's API
   - Test with different network configurations

3. **Request Format Issues**:
   - Review request data in the output
   - Compare with Acquired API documentation
   - Check for missing required fields

### Email Issues

1. **SMTP Configuration**:
   - Verify SMTP host and port settings
   - Check authentication credentials
   - Test SMTP connectivity manually

2. **Provider API Issues**:
   - Check API keys for SendGrid/Mailgun
   - Verify account status and limits
   - Review provider-specific error codes

### SMS Issues

1. **Provider Configuration**:
   - Verify API keys and endpoints
   - Check account balance and limits
   - Ensure sender ID is approved

2. **Country Code Support**:
   - Review which providers support your target countries
   - Check if numbers are in correct format
   - Verify international messaging is enabled

## Customization

### Adding Test Cases
You can modify the scripts to add specific test cases:

```csharp
// Add custom payment test
var customPaymentRequest = new AcquiredGenerateLinkRequestVM
{
    // Your specific test data
};
```

### Changing Test Data
Update the test data in the `CreateTest*Message()` methods to match your specific requirements.

### Adding Logging
The scripts use console output by default. You can modify them to write to files:

```csharp
// Add file logging
services.AddLogging(builder =>
{
    builder.AddFile("test-results.log");
});
```

## Troubleshooting

1. **Build Errors**: Ensure all referenced projects build successfully
2. **Configuration Errors**: Verify appsettings.json is copied to output directory
3. **Runtime Errors**: Check that all required NuGet packages are installed
4. **Permission Errors**: Ensure the application has necessary network permissions

## Security Notes

- **Never commit real API keys** to version control
- Use environment variables for sensitive configuration in production
- Test with sandbox/development endpoints when available
- Monitor API usage to avoid exceeding rate limits

## Support

If you encounter issues:
1. Check the detailed error output from the scripts
2. Review the LendQube logs for additional context
3. Verify provider-specific documentation
4. Contact the respective provider support if needed

## Next Steps

After running these scripts:
1. **Analyze the results** to identify specific failure points
2. **Update configurations** based on the findings
3. **Contact providers** if API-level issues are identified
4. **Implement fixes** in the main LendQube codebase
5. **Re-run tests** to verify fixes
﻿using System.Globalization;
using NodaTime;
using OfficeOpenXml.Attributes;

namespace LendQube.Infrastructure.Collection.Reporting.ViewModel;

[EpplusTable(PrintHeaders = true, AutofitColumns = true)]
public class AllTransactionsReportVM
{
    [EpplusTableColumn(Order = 0, Header = "Account Number")]
    public string AccountNumber { get; set; }
    [EpplusIgnore]
    public LocalDate PaidAt { get; set; }
    [EpplusTableColumn(Order = 1, Header = "Transaction At")]
    public string PaidAtFormatted => PaidAt.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 2, Header = "Amount", NumberFormat = "#,##0.00")]
    public decimal Amount { get; set; }
    [EpplusTableColumn(Order = 3, Header = "Transaction Type")]
    public string TransactionType { get; set; }
    [EpplusTableColumn(Order = 4, Header = "Transaction Reference")]
    public string TransactionReference { get; set; }
    [EpplusTableColumn(Order = 5, Header = "Notes")]
    public string Notes { get; set; }
    [EpplusTableColumn(Order = 6, Header = "Payment To")]
    public string PaymentTo => "Silicon";
}


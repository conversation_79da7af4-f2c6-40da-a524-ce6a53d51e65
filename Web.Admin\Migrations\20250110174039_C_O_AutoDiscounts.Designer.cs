﻿// <auto-generated />
using System;
using System.Collections.Generic;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Logs;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.DbContexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    [DbContext(typeof(AppDbContext))]
    [Migration("20250110174039_C_O_AutoDiscounts")]
    partial class C_O_AutoDiscounts
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "8.0.11")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "collection", "PaymentMethodStatus", new[] { "Active", "Suspended", "FailedLastCharge", "Disabled" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "collection", "PaymentProvider", new[] { "Stripe", "Upload", "Acquired" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "collection", "PaymentType", new[] { "SetupCard", "Card", "Bank" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "collection", "SchedulePaymentFrequency", new[] { "Weekly", "Monthly" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "collection", "TransactionStatus", new[] { "Initiated", "Validated", "Queued", "Processing", "Successful", "Completed", "Failed", "Refunded", "PendingRefund" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "core", "AccessStatus", new[] { "Granted", "Failed" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "core", "BackgroundControlState", new[] { "Running", "Idle", "Stopping", "Stopped" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "core", "BackgroundEventSource", new[] { "Queued", "Running", "Success", "Failed" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "core", "CustomerDeviceType", new[] { "Web", "Android", "iOS" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "core", "GrantType", new[] { "Password", "NewLogin", "Login2FA", "LoginRecoveryCode", "LoginClientId", "RefreshToken", "Complete2FASetup", "AdminReset2FA", "ChangePassword", "ChangePin", "ResetPassword", "ResetPasswordConfirm", "RequestRegistrationToken", "ValidateRegistrationToken", "RequestDeviceLoginToken", "ValidateDeviceLoginToken" });
            NpgsqlModelBuilderExtensions.HasPostgresEnum(modelBuilder, "core", "MessageStatus", new[] { "WaitingForConfiguration", "Queued", "Processing", "Failed", "SentPartially", "Sent", "Delivered", "Opened" });
            NpgsqlModelBuilderExtensions.UseHiLo(modelBuilder, "EntityFrameworkHiLoSequence", "collection");

            modelBuilder.HasSequence("EntityFrameworkHiLoSequence", "collection")
                .IncrementsBy(100);

            modelBuilder.HasSequence("EntityFrameworkHiLoSequence", "core")
                .IncrementsBy(100);

            modelBuilder.Entity("LendQube.Entities.Collection.Analytics.DashboardAnalytics", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalClosedPlacementValue")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("TotalClosedPlacements")
                        .HasColumnType("integer");

                    b.Property<int>("TotalCustomers")
                        .HasColumnType("integer");

                    b.Property<int>("TotalCustomersWithSchedules")
                        .HasColumnType("integer");

                    b.Property<decimal>("TotalPlacementValue")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalPlacementValuePaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("TotalPlacements")
                        .HasColumnType("integer");

                    b.Property<int>("TotalSettledPlacements")
                        .HasColumnType("integer");

                    b.Property<int>("TotalSignedInCustomers")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("DashboardAnalytics", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Analytics.DashboardTimeAnalytics", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly>("Date")
                        .HasColumnType("date");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmountPaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("TotalCustomersThatCreatedSchedule")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Date")
                        .IsUnique();

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("Date"), new[] { "TotalCustomersThatCreatedSchedule", "TotalAmountPaid" });

                    b.ToTable("DashboardTimeAnalytics", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Collections.CollectionFileUpload", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<string>("AnalysisFileUrl")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("FileUrl")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("CollectionFileUpload", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Collections.CollectionTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("FileUrl")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("CollectionTemplate", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerActivity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Activity")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CustomerProfileId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CustomerProfileId");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerActivity", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerAddress", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("AddressLine1")
                        .HasColumnType("text");

                    b.Property<string>("AddressLine2")
                        .HasColumnType("text");

                    b.Property<string>("City")
                        .HasColumnType("text");

                    b.Property<string>("Country")
                        .HasColumnType("text");

                    b.Property<string>("CountryCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("CustomerProfileId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Locality")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("PostCode")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CustomerProfileId");

                    b.ToTable("CustomerAddress", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerContactDetail", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("Preferred")
                        .HasColumnType("boolean");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerContactDetail", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerDiscount", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<decimal>("BalanceAfterDiscount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("BalanceBeforeDiscount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("MaxPercentage")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<long>("PlacementId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerDiscount", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerFlag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("FlagId")
                        .HasColumnType("bigint");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("FlagId");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerFlag", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerHold", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<string>("Comment")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("ExpiresOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("HoldConfigId")
                        .HasColumnType("bigint");

                    b.Property<bool>("HoldDefaultsOverriden")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<long?>("PlacementId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ExpiresOn");

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("ExpiresOn"), new[] { "HoldDefaultsOverriden" });

                    b.HasIndex("HoldConfigId");

                    b.HasIndex("PlacementId");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerHold", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerIncomeAndExpenditure", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Address")
                        .HasColumnType("text");

                    b.Property<decimal>("BenefitsIncome")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("CouncilTax")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("Food")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("FullName")
                        .HasColumnType("text");

                    b.Property<decimal>("Insurance")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("Loan")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("NetSalary")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("OtherExpenditure")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("OtherIncome")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("Rent")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("Transport")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("Utilities")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("Id");

                    b.HasIndex("ProfileId")
                        .IsUnique();

                    b.ToTable("CustomerIncomeAndExpenditure", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerNote", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<int?>("ContactType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<long?>("PlacementId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerNote", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerOneTimeCode", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Code")
                        .HasColumnType("text");

                    b.Property<string>("CodeType")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant>("ExpireAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("IsValid")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessageLogId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<int>("SendCount")
                        .HasColumnType("integer");

                    b.Property<int>("SendLimit")
                        .HasColumnType("integer");

                    b.Property<int>("TriesCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerOneTimeCode", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerPaymentMethod", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<bool>("CanBeReused")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("CustomerPaymentMethodConfigurationId")
                        .HasColumnType("bigint");

                    b.Property<DateOnly?>("ExpiryDate")
                        .HasColumnType("date");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<string>("ProviderId")
                        .HasColumnType("text");

                    b.Property<PaymentMethodStatus>("Status")
                        .HasColumnType("collection.\"PaymentMethodStatus\"");

                    b.HasKey("Id");

                    b.HasIndex("CustomerPaymentMethodConfigurationId");

                    b.ToTable("CustomerPaymentMethod", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerPaymentMethodConfiguration", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Currency")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<PaymentProvider>("Provider")
                        .HasColumnType("collection.\"PaymentProvider\"");

                    b.Property<string>("ProviderId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.HasKey("Id");

                    b.ToTable("CustomerPaymentMethodConfiguration", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("AccountId")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal>("BalancePaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("BalanceRemaining")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("decimal(18, 2)")
                        .HasComputedColumnSql("\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"", true);

                    b.Property<decimal>("BalanceTotal")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<bool>("Blacklisted")
                        .HasColumnType("boolean");

                    b.Property<string>("CountryCode")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character varying(3)");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<bool>("CurrentlyAssigned")
                        .HasColumnType("boolean");

                    b.Property<DateOnly?>("DateOfBirth")
                        .HasColumnType("date");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("FirstName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("FullName")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("text")
                        .HasComputedColumnSql("\"FirstName\"  || ' ' || \"LastName\"", true);

                    b.Property<int>("Gender")
                        .HasColumnType("integer");

                    b.Property<Instant?>("LastAssignedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Instant?>("LastRescheduleDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MiddleName")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<SchedulePaymentFrequency?>("PaymentFrequency")
                        .HasColumnType("collection.\"SchedulePaymentFrequency\"");

                    b.Property<int>("TotalRescheduleCount")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("AccountId")
                        .IsUnique();

                    b.ToTable("CustomerProfile", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerSchedule", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("Balance")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("decimal(18, 2)")
                        .HasComputedColumnSql("\"Amount\" - \"AmountPaid\"", true);

                    b.Property<DateOnly>("CPADate")
                        .HasColumnType("date");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly>("DueDate")
                        .HasColumnType("date");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("integer");

                    b.Property<int>("Period")
                        .HasColumnType("integer");

                    b.Property<int>("PeriodStatus")
                        .HasColumnType("integer");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("ProfileId");

                    b.ToTable("CustomerSchedule", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("AmountTried")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("PaymentApplied")
                        .HasColumnType("boolean");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<PaymentProvider>("PaymentProvider")
                        .HasColumnType("collection.\"PaymentProvider\"");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<List<string>>("SchedulePeriodAffected")
                        .HasColumnType("text[]");

                    b.Property<bool>("Successful")
                        .HasColumnType("boolean");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(24)
                        .HasColumnType("character varying(24)");

                    b.HasKey("Id");

                    b.HasIndex("ProfileId");

                    b.HasIndex("TransactionId");

                    b.ToTable("CustomerTransaction", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.Transaction", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(24)
                        .HasColumnType("character varying(24)");

                    b.Property<decimal>("Amount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrencyCode")
                        .HasColumnType("text");

                    b.Property<string>("CurrencySymbol")
                        .HasColumnType("text");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("Fee")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<PaymentProvider>("Provider")
                        .HasColumnType("collection.\"PaymentProvider\"");

                    b.Property<string>("ProviderReference")
                        .HasColumnType("text");

                    b.Property<string>("Purpose")
                        .HasColumnType("text");

                    b.Property<decimal>("Quantity")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<TransactionStatus>("Status")
                        .HasColumnType("collection.\"TransactionStatus\"");

                    b.Property<decimal>("TotalAmountPaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("TotalAmountPayable")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<PaymentType>("Type")
                        .HasColumnType("collection.\"PaymentType\"");

                    b.Property<decimal>("UnitAmount")
                        .HasColumnType("decimal(18, 2)");

                    b.HasKey("Id");

                    b.HasIndex("ProfileId");

                    b.ToTable("Transaction", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.TransactionHistory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Activity")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(24)
                        .HasColumnType("character varying(24)");

                    b.HasKey("Id");

                    b.HasIndex("TransactionId");

                    b.ToTable("TransactionHistory", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.Placement", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseHiLo(b.Property<long>("Id"));

                    b.Property<DateOnly?>("AgreementDate")
                        .HasColumnType("date");

                    b.Property<decimal>("BalanceFees")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("BalanceInterest")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("BalancePaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("BalancePrincipal")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<decimal>("BalanceRemaining")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("decimal(18, 2)")
                        .HasComputedColumnSql("\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"", true);

                    b.Property<decimal>("BalanceTotal")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("Company")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly?>("DefaultDate")
                        .HasColumnType("date");

                    b.Property<decimal>("Discount")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<DateOnly?>("LastInvoiceDate")
                        .HasColumnType("date");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateOnly?>("LastPaymentDate")
                        .HasColumnType("date");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("MonthlyRecurringBills")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("PlacementCount")
                        .HasColumnType("integer");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<string>("SourceAccountNumber")
                        .HasColumnType("text");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ProfileId");

                    b.ToTable("Placement", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementActivity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Activity")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<long>("PlacementId")
                        .HasColumnType("bigint");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.ToTable("PlacementActivity", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementNotes", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Note")
                        .HasColumnType("text");

                    b.Property<long>("PlacementId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.ToTable("PlacementNotes", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementStatusChange", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Comment")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FromStatus")
                        .HasColumnType("integer");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<long>("PlacementId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<long?>("ReasonId")
                        .HasColumnType("bigint");

                    b.Property<int>("ToStatus")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.HasIndex("ProfileId");

                    b.HasIndex("ReasonId");

                    b.ToTable("PlacementStatusChange", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementStatusChangeLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("NewStatus")
                        .HasColumnType("integer");

                    b.Property<int>("OldStatus")
                        .HasColumnType("integer");

                    b.Property<long>("PlacementId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.ToTable("PlacementStatusChangeLog", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementTag", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Comment")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<long>("PlacementId")
                        .HasColumnType("bigint");

                    b.Property<string>("Tag")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.ToTable("PlacementTag", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementTransaction", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<decimal>("AmountPaid")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("PaymentMethod")
                        .HasColumnType("text");

                    b.Property<PaymentProvider>("PaymentProvider")
                        .HasColumnType("collection.\"PaymentProvider\"");

                    b.Property<string>("PaymentType")
                        .HasColumnType("text");

                    b.Property<long>("PlacementId")
                        .HasColumnType("bigint");

                    b.Property<Guid>("ProfileId")
                        .HasColumnType("uuid");

                    b.Property<string>("TransactionId")
                        .HasMaxLength(24)
                        .HasColumnType("character varying(24)");

                    b.HasKey("Id");

                    b.HasIndex("PlacementId");

                    b.HasIndex("TransactionId");

                    b.ToTable("PlacementTransaction", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.AutoDiscountConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<decimal?>("Amount")
                        .HasColumnType("numeric");

                    b.Property<bool>("ApplyDiscountNow")
                        .HasColumnType("boolean");

                    b.Property<bool>("ApplyToNewAccountsOnly")
                        .HasColumnType("boolean");

                    b.Property<bool>("ApplyToOnlyAccountsWithNoDiscount")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("DiscountApplied")
                        .HasColumnType("boolean");

                    b.Property<bool>("DoNotApplyIfDiscountSettlesAccount")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<decimal?>("Percentage")
                        .HasColumnType("numeric");

                    b.Property<int>("Rule")
                        .HasColumnType("integer");

                    b.Property<decimal>("TotalAmountApplied")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<int>("TotalPlacementsAffected")
                        .HasColumnType("integer");

                    b.Property<decimal?>("UpperLimit")
                        .HasColumnType("numeric");

                    b.HasKey("Id");

                    b.ToTable("AutoDiscountConfig", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.CustomerFlagTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Flag")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Flag")
                        .IsUnique();

                    b.ToTable("CustomerFlagTemplate", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.CustomerNoteTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<int?>("ContactType")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Template")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("Name"), new[] { "Type", "ContactType" });

                    b.ToTable("CustomerNoteTemplate", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.DiscountConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<bool>("CanOverride")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("PercentageLimit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("RoleId")
                        .IsUnique();

                    b.ToTable("DiscountConfig", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.HoldConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<bool>("AllowDefaultOverride")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("DefaultDurationId")
                        .HasColumnType("bigint");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Reason")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("DefaultDurationId");

                    b.HasIndex("Reason")
                        .IsUnique();

                    b.ToTable("HoldConfig", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.HoldDurationConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Duration")
                        .HasColumnType("integer");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Length")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("Length", "Duration")
                        .IsUnique();

                    b.ToTable("HoldDurationConfig", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.PlacementStatusChangeReasonConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(4)
                        .HasColumnType("character varying(4)");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("PlacementStatusChangeReasonConfig", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentToDebtSegmentMapping", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("DebtSegmentId")
                        .HasColumnType("bigint");

                    b.Property<bool>("Enabled")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("RecordsPerTime")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("DebtSegmentId");

                    b.HasIndex("UserId", "DebtSegmentId")
                        .IsUnique();

                    b.ToTable("AgentToDebtSegmentMapping", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAnalytics", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmountCollected")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("TotalAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalClosed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalContacted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalEscalated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalOpened")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalPTP")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalSchedulesSetup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("AgentWorkflowAnalytics", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAvailability", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("CurrentlyAssignedCount")
                        .HasColumnType("integer");

                    b.Property<Instant?>("End")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant>("Start")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("TotalAssigned")
                        .HasColumnType("integer");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AgentWorkflowAvailability", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAvailabilityStatusChangeLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Activity")
                        .HasColumnType("text");

                    b.Property<long>("AvailabilityId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("AvailabilityId");

                    b.ToTable("AgentWorkflowAvailabilityStatusChangeLog", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowTask", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<Instant>("Assigned")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long?>("AvailabilityId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid?>("CustomerProfileId")
                        .HasColumnType("uuid");

                    b.Property<long>("DebtSegmentId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("EscalatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<long?>("EscalatedTaskId")
                        .HasColumnType("bigint");

                    b.Property<Guid?>("EscalatedToUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("EscalationReason")
                        .HasColumnType("text");

                    b.Property<bool>("IsEscalated")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("Opened")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("Removed")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("AvailabilityId");

                    b.HasIndex("CustomerProfileId");

                    b.HasIndex("DebtSegmentId");

                    b.HasIndex("EscalatedByUserId");

                    b.HasIndex("EscalatedToUserId");

                    b.HasIndex("UserId");

                    b.ToTable("AgentWorkflowTask", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowTimeAnalytics", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<LocalDate>("Date")
                        .HasColumnType("date");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmountCollected")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("TotalAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalClosed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalContacted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalEscalated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalOpened")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalPTP")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalSchedulesSetup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId", "Date")
                        .IsUnique();

                    b.ToTable("AgentWorkflowTimeAnalytics", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtSegment", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<TimeOnly?>("End")
                        .HasColumnType("time without time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("Priority")
                        .HasColumnType("integer");

                    b.Property<List<long>>("RuleIds")
                        .IsRequired()
                        .HasColumnType("bigint[]");

                    b.Property<TimeOnly?>("Start")
                        .HasColumnType("time without time zone");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("DebtSegment", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtSegmentRule", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<long?>("FlagId")
                        .HasColumnType("bigint");

                    b.Property<string>("Instruction")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int?>("MaxTypeNumber")
                        .HasColumnType("integer");

                    b.Property<int?>("MinTypeNumber")
                        .HasColumnType("integer");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int?>("StatusType")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FlagId");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("DebtSegmentRule", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtSegmentRules", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("DebtSegmentId")
                        .HasColumnType("bigint");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<long>("RuleId")
                        .HasColumnType("bigint");

                    b.HasKey("Id");

                    b.HasIndex("DebtSegmentId");

                    b.HasIndex("RuleId");

                    b.ToTable("DebtSegmentRules", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtWorkflowAnalytics", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmountCollected")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("TotalAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalClosed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalContacted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalEscalated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalOpened")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalPTP")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalSchedulesSetup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.ToTable("DebtWorkflowAnalytics", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtWorkflowTimeAnalytics", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<LocalDate>("Date")
                        .HasColumnType("date");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TotalAmountCollected")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("decimal(18, 2)")
                        .HasDefaultValue(0m);

                    b.Property<int>("TotalAssigned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalClosed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalContacted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalEscalated")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalOpened")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalPTP")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalResolved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.Property<int>("TotalSchedulesSetup")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("Date")
                        .IsUnique();

                    b.ToTable("DebtWorkflowTimeAnalytics", "collection");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BackgroundTasks.BackgroundTaskEventControl", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Event")
                        .HasColumnType("integer");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Source")
                        .HasColumnType("integer");

                    b.Property<BackgroundControlState>("Status")
                        .HasColumnType("core.\"BackgroundControlState\"");

                    b.HasKey("Id");

                    b.HasIndex("Source", "Event")
                        .IsUnique();

                    b.ToTable("BackgroundTaskEventControl", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BackgroundTasks.BackgroundTaskEventLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Data")
                        .HasColumnType("text");

                    b.Property<int>("Event")
                        .HasColumnType("integer");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("RequeryDelayTime")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ResponseMessage")
                        .HasColumnType("text");

                    b.Property<int>("Source")
                        .HasColumnType("integer");

                    b.Property<BackgroundEventStatus>("Status")
                        .HasColumnType("core.\"BackgroundEventSource\"");

                    b.Property<Instant?>("TimeCompleted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("TimeStarted")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Tries")
                        .HasColumnType("integer");

                    b.Property<string>("Uri")
                        .HasColumnType("text");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("BackgroundTaskEventLog", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationClaims", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("character varying(60)");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("AspNetClaims", "public");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", "public");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationRoleClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseHiLo(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.Property<string>("Source")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUser", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("integer");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("boolean");

                    b.Property<string>("FirstName")
                        .HasColumnType("text");

                    b.Property<string>("FullName")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("text")
                        .HasComputedColumnSql("\"FirstName\"  || ' ' || \"LastName\"", true);

                    b.Property<Instant?>("LastLoginDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastName")
                        .HasColumnType("text");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("boolean");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("MustChangePasswordOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<string>("OtherNames")
                        .HasColumnType("text");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("text");

                    b.Property<string>("PhoneCode")
                        .HasColumnType("text");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("text");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("RegistrationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Role")
                        .HasMaxLength(15)
                        .HasColumnType("character varying(15)");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("text");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("boolean");

                    b.Property<Guid>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("uuid");

                    b.Property<string>("WalletPinHash")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.HasIndex("UserName")
                        .IsUnique();

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("UserName"), new[] { "SecurityStamp", "TwoFactorEnabled", "PasswordHash", "WalletPinHash", "MustChangePasswordOn", "Role" });

                    b.ToTable("AspNetUsers", "public");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseHiLo(b.Property<int>("Id"));

                    b.Property<string>("ClaimType")
                        .HasColumnType("text");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserLogin", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("text");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("text");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uuid");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Value")
                        .HasColumnType("text");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("LendQube.Entities.Core.Location.Country", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(2)
                        .HasColumnType("character(2)")
                        .IsFixedLength();

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ImageUrl")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("PhoneCode")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Country", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Location.Currency", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(3)
                        .HasColumnType("character(3)")
                        .IsFixedLength();

                    b.Property<long>("CountryId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(1)
                        .HasColumnType("character(1)")
                        .IsFixedLength();

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.HasIndex("CountryId");

                    b.HasIndex("Symbol")
                        .IsUnique();

                    b.ToTable("Currency", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Location.State", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<long>("CountryId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("StateCode")
                        .IsRequired()
                        .HasMaxLength(32)
                        .HasColumnType("character varying(32)");

                    b.Property<string>("StateName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("CountryId", "StateCode")
                        .IsUnique();

                    b.ToTable("State", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Logs.CustomerDevice", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("AppBuild")
                        .HasColumnType("text");

                    b.Property<string>("AppVersion")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("DeviceId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("MobileMake")
                        .HasColumnType("text");

                    b.Property<string>("MobileOsVersion")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("PushNotificationId")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<CustomerDeviceType>("Type")
                        .HasColumnType("core.\"CustomerDeviceType\"");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("CustomerDevice", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Logs.UserAccessLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Application")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<GrantType>("GrantType")
                        .HasColumnType("core.\"GrantType\"");

                    b.Property<string>("IpLocation")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LocationLookupData")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<AccessStatus>("Status")
                        .HasColumnType("core.\"AccessStatus\"");

                    b.HasKey("Id");

                    b.HasIndex("CreatedByUserId");

                    b.ToTable("UserAccessLog", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.CustomerInbox", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("uuid");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessageLogId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("ReadOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.Property<string>("Template")
                        .HasColumnType("text");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("CustomerInbox", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageConfiguration", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseHiLo(b.Property<long>("Id"));

                    b.Property<long?>("BodyTemplateId")
                        .HasColumnType("bigint");

                    b.Property<int>("Channels")
                        .HasColumnType("integer");

                    b.Property<long?>("ContainerTemplateId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<bool>("DoNotSendIfExists")
                        .HasColumnType("boolean");

                    b.Property<int>("ExistsCheckWindow")
                        .HasColumnType("integer");

                    b.Property<string>("HtmlTemplate")
                        .HasColumnType("text");

                    b.Property<List<string>>("Keys")
                        .HasColumnType("text[]");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("SenderEmail")
                        .HasColumnType("text");

                    b.Property<string>("SenderName")
                        .HasColumnType("text");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.Property<string>("TextTemplate")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("BodyTemplateId");

                    b.HasIndex("ContainerTemplateId");

                    b.HasIndex("Name")
                        .IsUnique();

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("Name"), new[] { "Channels" });

                    b.ToTable("MessageConfiguration", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageConfigurationMessagingGroup", b =>
                {
                    b.Property<long>("MessageConfigurationId")
                        .HasColumnType("bigint");

                    b.Property<long>("MessagingGroupId")
                        .HasColumnType("bigint");

                    b.HasKey("MessageConfigurationId", "MessagingGroupId");

                    b.HasIndex("MessagingGroupId");

                    b.ToTable("MessageConfigurationMessagingGroup", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageLog", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseHiLo(b.Property<long>("Id"));

                    b.Property<int>("AttemptCount")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("OriginatedFrom")
                        .HasColumnType("text");

                    b.Property<MessageStatus>("Status")
                        .HasColumnType("core.\"MessageStatus\"");

                    b.HasKey("Id");

                    b.HasIndex("Status");

                    b.ToTable("MessageLog", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageLogActivity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Activity")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessageLogId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MessageLogId");

                    b.ToTable("MessageLogActivity", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageLogEntry", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("BodyHtmlTemplate")
                        .HasColumnType("text");

                    b.Property<string>("BodyTextTemplate")
                        .HasColumnType("text");

                    b.Property<int>("Channels")
                        .HasColumnType("integer");

                    b.Property<string>("ContainerHtmlTemplate")
                        .HasColumnType("text");

                    b.Property<string>("ContainerTextTemplate")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("EmailRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("HtmlTemplate")
                        .HasColumnType("text");

                    b.Property<List<string>>("Keys")
                        .HasColumnType("text[]");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessageConfigurationId")
                        .HasColumnType("bigint");

                    b.Property<long>("MessageLogId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<string>("Subject")
                        .HasColumnType("text");

                    b.Property<bool>("TextRequired")
                        .HasColumnType("boolean");

                    b.Property<string>("TextTemplate")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MessageConfigurationId");

                    b.HasIndex("MessageLogId");

                    b.ToTable("MessageLogEntry", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageProviderConfig", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<decimal>("Balance")
                        .ValueGeneratedOnAddOrUpdate()
                        .HasColumnType("decimal(18, 8)")
                        .HasComputedColumnSql("\"TotalFunding\" - \"TotalDebit\"", true);

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("DisabledOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<decimal>("ExpectedTotalDebit")
                        .HasColumnType("decimal(18, 2)");

                    b.Property<long>("FailureCount")
                        .HasColumnType("bigint");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LogActivityOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.Property<List<string>>("SupportedCountries")
                        .HasColumnType("text[]");

                    b.Property<long>("TotalCount")
                        .HasColumnType("bigint");

                    b.Property<decimal>("TotalDebit")
                        .HasColumnType("decimal(18, 8)");

                    b.Property<decimal>("TotalFunding")
                        .HasColumnType("decimal(18, 8)");

                    b.Property<decimal>("UnitCost")
                        .HasColumnType("decimal(18, 8)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("MessageProviderConfig", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageSchedule", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<Instant?>("ActiveOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("ConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CronExpression")
                        .HasMaxLength(24)
                        .HasColumnType("character varying(24)");

                    b.Property<int>("Days")
                        .HasColumnType("integer");

                    b.Property<int>("Frequency")
                        .HasColumnType("integer");

                    b.Property<int>("FrequencyNumber")
                        .HasColumnType("integer");

                    b.Property<List<long>>("Groups")
                        .HasColumnType("bigint[]");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("RunCount")
                        .HasColumnType("integer");

                    b.Property<bool>("Starting")
                        .HasColumnType("boolean");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConfigId");

                    b.HasIndex("Name")
                        .IsUnique();

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("Name"), new[] { "CronExpression", "Frequency", "FrequencyNumber", "Days", "ActiveOn", "Starting", "RunCount" });

                    b.ToTable("MessageSchedule", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingConfigurationActivity", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("Activity")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessageConfigurationId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MessageConfigurationId");

                    b.ToTable("MessagingConfigurationActivity", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingGroup", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("MessagingGroup", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingGroupEntry", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<List<string>>("Emails")
                        .HasColumnType("text[]");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessagingGroupId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MessagingGroupId");

                    b.ToTable("MessagingGroupEntry", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingGroupQuery", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("BuildingBlock")
                        .HasColumnType("jsonb");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessagingGroupId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Query")
                        .HasColumnType("text");

                    b.Property<string>("SenderQuery")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("MessagingGroupId")
                        .IsUnique();

                    b.ToTable("MessagingGroupQuery", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingTemplate", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Instant?>("DisabledOn")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("HtmlTemplate")
                        .HasColumnType("text");

                    b.Property<bool>("IsContainer")
                        .HasColumnType("boolean");

                    b.Property<List<string>>("Keys")
                        .HasColumnType("text[]");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<string>("TextTemplate")
                        .HasColumnType("text");

                    b.Property<int>("Types")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    NpgsqlIndexBuilderExtensions.IncludeProperties(b.HasIndex("Name"), new[] { "Types" });

                    b.ToTable("MessagingTemplate", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.TelegramSession", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("integer");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<byte[]>("Data")
                        .IsRequired()
                        .HasColumnType("bytea");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.ToTable("TelegramSession", "public");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Reporting.ReportSchedule", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<long>("ConfigId")
                        .HasColumnType("bigint");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Days")
                        .HasColumnType("integer");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<bool>("Disabled")
                        .HasColumnType("boolean");

                    b.Property<Instant?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FileType")
                        .HasColumnType("integer");

                    b.Property<int>("Frequency")
                        .HasColumnType("integer");

                    b.Property<int>("FrequencyNumber")
                        .HasColumnType("integer");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<Instant?>("LastRunDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<long>("MessagingGroupId")
                        .HasColumnType("bigint");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<List<string>>("ReportNames")
                        .IsRequired()
                        .HasColumnType("text[]");

                    b.Property<string>("ReportTypeName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<int>("RunCount")
                        .HasColumnType("integer");

                    b.Property<Instant?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<bool>("Starting")
                        .HasColumnType("boolean");

                    b.Property<string>("TimeZone")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ConfigId");

                    b.HasIndex("MessagingGroupId");

                    b.ToTable("ReportSchedule", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Reporting.SystemReport", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<bool>("AutoRun")
                        .HasColumnType("boolean");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<Instant?>("EndDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("FileType")
                        .HasColumnType("integer");

                    b.Property<string>("FileUrl")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<List<string>>("Names")
                        .IsRequired()
                        .HasColumnType("text[]");

                    b.Property<long?>("ReportScheduleId")
                        .HasColumnType("bigint");

                    b.Property<int>("RunCount")
                        .HasColumnType("integer");

                    b.Property<Instant?>("StartDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ReportScheduleId");

                    b.ToTable("SystemReport", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Uploads.SystemFileUpload", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<int>("Action")
                        .HasColumnType("integer");

                    b.Property<string>("AnalysisFileUrl")
                        .HasColumnType("text");

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(512)
                        .HasColumnType("character varying(512)");

                    b.Property<string>("FileUrl")
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Message")
                        .HasColumnType("text");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("SystemFileUpload", "core");
                });

            modelBuilder.Entity("LendQube.Entities.Core.VersionManagement.CustomerDeviceVersion", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint");

                    NpgsqlPropertyBuilderExtensions.UseIdentityAlwaysColumn(b.Property<long>("Id"));

                    b.Property<string>("CreatedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("CreatedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("CreatedByUserId")
                        .HasColumnType("uuid");

                    b.Property<Instant?>("CreatedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CurrentVersion")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Instant?>("LastModifiedDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ModifiedByIp")
                        .HasMaxLength(46)
                        .HasColumnType("character varying(46)");

                    b.Property<string>("ModifiedByUser")
                        .HasMaxLength(256)
                        .HasColumnType("character varying(256)");

                    b.Property<Guid?>("ModifiedByUserId")
                        .HasColumnType("uuid");

                    b.Property<string>("OldVersion")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<CustomerDeviceType>("Type")
                        .HasColumnType("core.\"CustomerDeviceType\"");

                    b.HasKey("Id");

                    b.ToTable("CustomerDeviceVersion", "core");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.DataProtection.EntityFrameworkCore.DataProtectionKey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("integer");

                    NpgsqlPropertyBuilderExtensions.UseHiLo(b.Property<int>("Id"));

                    b.Property<string>("FriendlyName")
                        .HasColumnType("text");

                    b.Property<string>("Xml")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("DataProtectionKeys");
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreApplication<System.Guid>", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ApplicationType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("ClientSecret")
                        .HasColumnType("text");

                    b.Property<string>("ClientType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ConcurrencyToken")
                        .IsConcurrencyToken()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("ConsentType")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("DisplayName")
                        .HasColumnType("text");

                    b.Property<string>("DisplayNames")
                        .HasColumnType("text");

                    b.Property<string>("JsonWebKeySet")
                        .HasColumnType("text");

                    b.Property<string>("Permissions")
                        .HasColumnType("text");

                    b.Property<string>("PostLogoutRedirectUris")
                        .HasColumnType("text");

                    b.Property<string>("Properties")
                        .HasColumnType("text");

                    b.Property<string>("RedirectUris")
                        .HasColumnType("text");

                    b.Property<string>("Requirements")
                        .HasColumnType("text");

                    b.Property<string>("Settings")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("ClientId")
                        .IsUnique();

                    b.ToTable("OpenIddictApplications", (string)null);
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreAuthorization<System.Guid>", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyToken")
                        .IsConcurrencyToken()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CreationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Properties")
                        .HasColumnType("text");

                    b.Property<string>("Scopes")
                        .HasColumnType("text");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Subject")
                        .HasMaxLength(400)
                        .HasColumnType("character varying(400)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId", "Status", "Subject", "Type");

                    b.ToTable("OpenIddictAuthorizations", (string)null);
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreScope<System.Guid>", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyToken")
                        .IsConcurrencyToken()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<string>("Descriptions")
                        .HasColumnType("text");

                    b.Property<string>("DisplayName")
                        .HasColumnType("text");

                    b.Property<string>("DisplayNames")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("character varying(200)");

                    b.Property<string>("Properties")
                        .HasColumnType("text");

                    b.Property<string>("Resources")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("OpenIddictScopes", (string)null);
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreToken<System.Guid>", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<Guid?>("ApplicationId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("AuthorizationId")
                        .HasColumnType("uuid");

                    b.Property<string>("ConcurrencyToken")
                        .IsConcurrencyToken()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CreationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Payload")
                        .HasColumnType("text");

                    b.Property<string>("Properties")
                        .HasColumnType("text");

                    b.Property<DateTime?>("RedemptionDate")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("ReferenceId")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Subject")
                        .HasMaxLength(400)
                        .HasColumnType("character varying(400)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.HasKey("Id");

                    b.HasIndex("AuthorizationId");

                    b.HasIndex("ReferenceId")
                        .IsUnique();

                    b.HasIndex("ApplicationId", "Status", "Subject", "Type");

                    b.ToTable("OpenIddictTokens", (string)null);
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerActivity", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", null)
                        .WithMany("Activities")
                        .HasForeignKey("CustomerProfileId");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerAddress", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", null)
                        .WithMany("Addresses")
                        .HasForeignKey("CustomerProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerContactDetail", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany()
                        .HasForeignKey("ProfileId");

                    b.OwnsOne("LendQube.Entities.Core.Constants.PhoneNumber", "PhoneNumber", b1 =>
                        {
                            b1.Property<long>("CustomerContactDetailId")
                                .HasColumnType("bigint");

                            b1.Property<string>("Code")
                                .HasColumnType("text");

                            b1.Property<string>("Number")
                                .HasColumnType("text");

                            b1.HasKey("CustomerContactDetailId");

                            b1.ToTable("CustomerContactDetail", "collection");

                            b1.WithOwner()
                                .HasForeignKey("CustomerContactDetailId");
                        });

                    b.Navigation("PhoneNumber");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerDiscount", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", "Placement")
                        .WithMany()
                        .HasForeignKey("PlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("Discounts")
                        .HasForeignKey("ProfileId");

                    b.Navigation("Placement");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerFlag", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Setup.CustomerFlagTemplate", "Flag")
                        .WithMany()
                        .HasForeignKey("FlagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("Flags")
                        .HasForeignKey("ProfileId");

                    b.Navigation("Flag");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerHold", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Setup.HoldConfig", "Hold")
                        .WithMany()
                        .HasForeignKey("HoldConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", "Placement")
                        .WithMany()
                        .HasForeignKey("PlacementId");

                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("Holds")
                        .HasForeignKey("ProfileId");

                    b.Navigation("Hold");

                    b.Navigation("Placement");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerIncomeAndExpenditure", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithOne("IncomeAndExpenditure")
                        .HasForeignKey("LendQube.Entities.Collection.Customers.CustomerIncomeAndExpenditure", "ProfileId");

                    b.OwnsOne("LendQube.Entities.Core.Constants.PhoneNumber", "PhoneNumber", b1 =>
                        {
                            b1.Property<long>("CustomerIncomeAndExpenditureId")
                                .HasColumnType("bigint");

                            b1.Property<string>("Code")
                                .HasColumnType("text");

                            b1.Property<string>("Number")
                                .HasColumnType("text");

                            b1.HasKey("CustomerIncomeAndExpenditureId");

                            b1.ToTable("CustomerIncomeAndExpenditure", "collection");

                            b1.ToJson("PhoneNumber");

                            b1.WithOwner()
                                .HasForeignKey("CustomerIncomeAndExpenditureId");
                        });

                    b.Navigation("PhoneNumber");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerNote", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", "Placement")
                        .WithMany()
                        .HasForeignKey("PlacementId");

                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("Notes")
                        .HasForeignKey("ProfileId");

                    b.OwnsMany("LendQube.Entities.Collection.Customers.CustomerNoteFile", "Files", b1 =>
                        {
                            b1.Property<long>("CustomerNoteId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Name")
                                .HasColumnType("text");

                            b1.Property<string>("Url")
                                .HasColumnType("text");

                            b1.HasKey("CustomerNoteId", "Id");

                            b1.ToTable("CustomerNote", "collection");

                            b1.ToJson("Files");

                            b1.WithOwner()
                                .HasForeignKey("CustomerNoteId");
                        });

                    b.Navigation("Files");

                    b.Navigation("Placement");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerOneTimeCode", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("OneTimeCodes")
                        .HasForeignKey("ProfileId");

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerPaymentMethod", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerPaymentMethodConfiguration", "Config")
                        .WithMany()
                        .HasForeignKey("CustomerPaymentMethodConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("LendQube.Entities.Collection.Customers.CardResponseVM", "Data", b1 =>
                        {
                            b1.Property<Guid>("CustomerPaymentMethodId")
                                .HasColumnType("uuid");

                            b1.Property<int>("Brand")
                                .HasColumnType("integer");

                            b1.Property<string>("Last4")
                                .HasColumnType("text");

                            b1.HasKey("CustomerPaymentMethodId");

                            b1.ToTable("CustomerPaymentMethod", "collection");

                            b1.ToJson("Data");

                            b1.WithOwner()
                                .HasForeignKey("CustomerPaymentMethodId");
                        });

                    b.Navigation("Config");

                    b.Navigation("Data");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerProfile", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "AppUser")
                        .WithOne()
                        .HasForeignKey("LendQube.Entities.Collection.Customers.CustomerProfile", "Id")
                        .HasPrincipalKey("LendQube.Entities.Core.BaseUser.ApplicationUser", "UserName")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("LendQube.Entities.Core.Constants.PhoneNumber", "MobileNumber", b1 =>
                        {
                            b1.Property<Guid>("CustomerProfileId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Code")
                                .HasColumnType("text");

                            b1.Property<string>("Number")
                                .HasColumnType("text");

                            b1.HasKey("CustomerProfileId");

                            b1.ToTable("CustomerProfile", "collection");

                            b1.ToJson("MobileNumber");

                            b1.WithOwner()
                                .HasForeignKey("CustomerProfileId");
                        });

                    b.OwnsOne("LendQube.Entities.Core.Constants.PhoneNumber", "PhoneNumber", b1 =>
                        {
                            b1.Property<Guid>("CustomerProfileId")
                                .HasColumnType("uuid");

                            b1.Property<string>("Code")
                                .HasColumnType("text");

                            b1.Property<string>("Number")
                                .HasColumnType("text");

                            b1.HasKey("CustomerProfileId");

                            b1.ToTable("CustomerProfile", "collection");

                            b1.ToJson("PhoneNumber");

                            b1.WithOwner()
                                .HasForeignKey("CustomerProfileId");
                        });

                    b.Navigation("AppUser");

                    b.Navigation("MobileNumber");

                    b.Navigation("PhoneNumber");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerSchedule", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("Schedules")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerTransaction", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("Transactions")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.Transaction", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("AllTransactions")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("LendQube.Entities.Core.Constants.KeyValueHelperVM", "Fields", b1 =>
                        {
                            b1.Property<string>("TransactionId")
                                .HasColumnType("character varying(24)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Key")
                                .HasColumnType("text");

                            b1.Property<string>("Value")
                                .HasColumnType("text");

                            b1.HasKey("TransactionId", "Id");

                            b1.ToTable("Transaction", "collection");

                            b1.ToJson("Fields");

                            b1.WithOwner()
                                .HasForeignKey("TransactionId");
                        });

                    b.OwnsMany("LendQube.Entities.Core.Constants.KeyValueHelperVM", "UserData", b1 =>
                        {
                            b1.Property<string>("TransactionId")
                                .HasColumnType("character varying(24)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Key")
                                .HasColumnType("text");

                            b1.Property<string>("Value")
                                .HasColumnType("text");

                            b1.HasKey("TransactionId", "Id");

                            b1.ToTable("Transaction", "collection");

                            b1.ToJson("UserData");

                            b1.WithOwner()
                                .HasForeignKey("TransactionId");
                        });

                    b.Navigation("Fields");

                    b.Navigation("Profile");

                    b.Navigation("UserData");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.TransactionHistory", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.Transaction", "Transaction")
                        .WithMany("History")
                        .HasForeignKey("TransactionId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Transaction");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.Placement", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany("Placements")
                        .HasForeignKey("ProfileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Profile");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementActivity", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", null)
                        .WithMany("Activities")
                        .HasForeignKey("PlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementNotes", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", null)
                        .WithMany("Notes")
                        .HasForeignKey("PlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementStatusChange", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", "Placement")
                        .WithMany("StatusChanges")
                        .HasForeignKey("PlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "Profile")
                        .WithMany()
                        .HasForeignKey("ProfileId");

                    b.HasOne("LendQube.Entities.Collection.Setup.PlacementStatusChangeReasonConfig", "Reason")
                        .WithMany()
                        .HasForeignKey("ReasonId");

                    b.Navigation("Placement");

                    b.Navigation("Profile");

                    b.Navigation("Reason");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementStatusChangeLog", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", "Placement")
                        .WithMany("StatusChangeLogs")
                        .HasForeignKey("PlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Placement");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementTag", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", null)
                        .WithMany("Tags")
                        .HasForeignKey("PlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.PlacementTransaction", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Placements.Placement", "Placement")
                        .WithMany("Transactions")
                        .HasForeignKey("PlacementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Placement");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.DiscountConfig", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationRole", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Setup.HoldConfig", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Setup.HoldDurationConfig", "DefaultDuration")
                        .WithMany()
                        .HasForeignKey("DefaultDurationId");

                    b.Navigation("DefaultDuration");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentToDebtSegmentMapping", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Workflows.Debt.DebtSegment", "DebtSegment")
                        .WithMany()
                        .HasForeignKey("DebtSegmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DebtSegment");

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAnalytics", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAvailability", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAvailabilityStatusChangeLog", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAvailability", "Availability")
                        .WithMany()
                        .HasForeignKey("AvailabilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Availability");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowTask", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowAvailability", "Availability")
                        .WithMany()
                        .HasForeignKey("AvailabilityId");

                    b.HasOne("LendQube.Entities.Collection.Customers.CustomerProfile", "CustomerProfile")
                        .WithMany()
                        .HasForeignKey("CustomerProfileId");

                    b.HasOne("LendQube.Entities.Collection.Workflows.Debt.DebtSegment", "DebtSegment")
                        .WithMany()
                        .HasForeignKey("DebtSegmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "EscalatedByUser")
                        .WithMany()
                        .HasForeignKey("EscalatedByUserId");

                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "EscalatedToUser")
                        .WithMany()
                        .HasForeignKey("EscalatedToUserId");

                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("LendQube.Entities.Collection.Workflows.Debt.WorkflowTask", "Tasks", b1 =>
                        {
                            b1.Property<long>("AgentWorkflowTaskId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<int?>("Action")
                                .HasColumnType("integer");

                            b1.Property<decimal?>("Amount")
                                .HasColumnType("numeric");

                            b1.Property<long?>("NoteId")
                                .HasColumnType("bigint");

                            b1.Property<string>("PaymentId")
                                .HasColumnType("text");

                            b1.Property<Instant>("When")
                                .HasColumnType("timestamp with time zone");

                            b1.HasKey("AgentWorkflowTaskId", "Id");

                            b1.ToTable("AgentWorkflowTask", "collection");

                            b1.ToJson("Tasks");

                            b1.WithOwner()
                                .HasForeignKey("AgentWorkflowTaskId");
                        });

                    b.Navigation("Availability");

                    b.Navigation("CustomerProfile");

                    b.Navigation("DebtSegment");

                    b.Navigation("EscalatedByUser");

                    b.Navigation("EscalatedToUser");

                    b.Navigation("Tasks");

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.AgentWorkflowTimeAnalytics", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtSegmentRule", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Setup.CustomerFlagTemplate", "Flag")
                        .WithMany()
                        .HasForeignKey("FlagId");

                    b.Navigation("Flag");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtSegmentRules", b =>
                {
                    b.HasOne("LendQube.Entities.Collection.Workflows.Debt.DebtSegment", "DebtSegment")
                        .WithMany("Rules")
                        .HasForeignKey("DebtSegmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Collection.Workflows.Debt.DebtSegmentRule", "Rule")
                        .WithMany()
                        .HasForeignKey("RuleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DebtSegment");

                    b.Navigation("Rule");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationRoleClaim", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationRole", "Role")
                        .WithMany("RoleClaims")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserClaim", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserLogin", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany("Logins")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserRole", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationRole", "Role")
                        .WithMany("UserRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany("UserRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUserToken", b =>
                {
                    b.HasOne("LendQube.Entities.Core.BaseUser.ApplicationUser", "User")
                        .WithMany("Tokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Location.Currency", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Location.Country", "Country")
                        .WithMany()
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Location.State", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Location.Country", "Country")
                        .WithMany("States")
                        .HasForeignKey("CountryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Country");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageConfiguration", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessagingTemplate", "BodyTemplate")
                        .WithMany()
                        .HasForeignKey("BodyTemplateId");

                    b.HasOne("LendQube.Entities.Core.Messaging.MessagingTemplate", "ContainerTemplate")
                        .WithMany()
                        .HasForeignKey("ContainerTemplateId");

                    b.Navigation("BodyTemplate");

                    b.Navigation("ContainerTemplate");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageConfigurationMessagingGroup", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessageConfiguration", null)
                        .WithMany()
                        .HasForeignKey("MessageConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Core.Messaging.MessagingGroup", null)
                        .WithMany("ConfigGroups")
                        .HasForeignKey("MessagingGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageLogActivity", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessageLog", null)
                        .WithMany("Activity")
                        .HasForeignKey("MessageLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageLogEntry", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessageConfiguration", "Config")
                        .WithMany()
                        .HasForeignKey("MessageConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Core.Messaging.MessageLog", "Log")
                        .WithMany("MessageLogEntries")
                        .HasForeignKey("MessageLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("LendQube.Entities.Core.Messaging.MessageCopiedIn", "CopiedIn", b1 =>
                        {
                            b1.Property<long>("MessageLogEntryId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<bool>("BlindCopy")
                                .HasColumnType("boolean");

                            b1.Property<string>("Email")
                                .HasColumnType("text");

                            b1.Property<string>("Name")
                                .HasColumnType("text");

                            b1.Property<string>("PhoneNumber")
                                .HasColumnType("text");

                            b1.HasKey("MessageLogEntryId", "Id");

                            b1.ToTable("MessageLogEntry", "core");

                            b1.ToJson("CopiedIn");

                            b1.WithOwner()
                                .HasForeignKey("MessageLogEntryId");
                        });

                    b.OwnsMany("LendQube.Entities.Core.Messaging.MessageRecipient", "Recipients", b1 =>
                        {
                            b1.Property<long>("MessageLogEntryId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<long?>("MessagingGroupId")
                                .HasColumnType("bigint");

                            b1.Property<string>("UserId")
                                .HasColumnType("text");

                            b1.HasKey("MessageLogEntryId", "Id");

                            b1.ToTable("MessageLogEntry", "core");

                            b1.ToJson("Recipients");

                            b1.WithOwner()
                                .HasForeignKey("MessageLogEntryId");

                            b1.OwnsOne("LendQube.Entities.Core.Messaging.AdHocRecipient", "AdHoc", b2 =>
                                {
                                    b2.Property<long>("MessageRecipientMessageLogEntryId")
                                        .HasColumnType("bigint");

                                    b2.Property<int>("MessageRecipientId")
                                        .HasColumnType("integer");

                                    b2.Property<string>("Email")
                                        .HasColumnType("text");

                                    b2.Property<string>("Key")
                                        .HasColumnType("text");

                                    b2.Property<string>("Name")
                                        .HasColumnType("text");

                                    b2.HasKey("MessageRecipientMessageLogEntryId", "MessageRecipientId");

                                    b2.ToTable("MessageLogEntry", "core");

                                    b2.WithOwner()
                                        .HasForeignKey("MessageRecipientMessageLogEntryId", "MessageRecipientId");

                                    b2.OwnsOne("LendQube.Entities.Core.Constants.PhoneNumber", "PhoneNumber", b3 =>
                                        {
                                            b3.Property<long>("AdHocRecipientMessageRecipientMessageLogEntryId")
                                                .HasColumnType("bigint");

                                            b3.Property<int>("AdHocRecipientMessageRecipientId")
                                                .HasColumnType("integer");

                                            b3.Property<string>("Code")
                                                .HasColumnType("text");

                                            b3.Property<string>("Number")
                                                .HasColumnType("text");

                                            b3.HasKey("AdHocRecipientMessageRecipientMessageLogEntryId", "AdHocRecipientMessageRecipientId");

                                            b3.ToTable("MessageLogEntry", "core");

                                            b3.WithOwner()
                                                .HasForeignKey("AdHocRecipientMessageRecipientMessageLogEntryId", "AdHocRecipientMessageRecipientId");
                                        });

                                    b2.Navigation("PhoneNumber");
                                });

                            b1.OwnsMany("LendQube.Entities.Core.Messaging.MessageAttachment", "Attachments", b2 =>
                                {
                                    b2.Property<long>("MessageRecipientMessageLogEntryId")
                                        .HasColumnType("bigint");

                                    b2.Property<int>("MessageRecipientId")
                                        .HasColumnType("integer");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer");

                                    b2.Property<string>("FileName")
                                        .HasColumnType("text");

                                    b2.Property<string>("Url")
                                        .HasColumnType("text");

                                    b2.HasKey("MessageRecipientMessageLogEntryId", "MessageRecipientId", "Id");

                                    b2.ToTable("MessageLogEntry", "core");

                                    b2.WithOwner()
                                        .HasForeignKey("MessageRecipientMessageLogEntryId", "MessageRecipientId");
                                });

                            b1.OwnsMany("LendQube.Entities.Core.Messaging.MessageCopiedIn", "CopiedIn", b2 =>
                                {
                                    b2.Property<long>("MessageRecipientMessageLogEntryId")
                                        .HasColumnType("bigint");

                                    b2.Property<int>("MessageRecipientId")
                                        .HasColumnType("integer");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer");

                                    b2.Property<bool>("BlindCopy")
                                        .HasColumnType("boolean");

                                    b2.Property<string>("Email")
                                        .HasColumnType("text");

                                    b2.Property<string>("Name")
                                        .HasColumnType("text");

                                    b2.Property<string>("PhoneNumber")
                                        .HasColumnType("text");

                                    b2.HasKey("MessageRecipientMessageLogEntryId", "MessageRecipientId", "Id");

                                    b2.ToTable("MessageLogEntry", "core");

                                    b2.WithOwner()
                                        .HasForeignKey("MessageRecipientMessageLogEntryId", "MessageRecipientId");
                                });

                            b1.OwnsMany("LendQube.Entities.Core.Messaging.TemplateKeyValue", "TemplateValues", b2 =>
                                {
                                    b2.Property<long>("MessageRecipientMessageLogEntryId")
                                        .HasColumnType("bigint");

                                    b2.Property<int>("MessageRecipientId")
                                        .HasColumnType("integer");

                                    b2.Property<int>("Id")
                                        .ValueGeneratedOnAdd()
                                        .HasColumnType("integer");

                                    b2.Property<string>("Key")
                                        .HasColumnType("text");

                                    b2.Property<string>("Value")
                                        .HasColumnType("text");

                                    b2.HasKey("MessageRecipientMessageLogEntryId", "MessageRecipientId", "Id");

                                    b2.ToTable("MessageLogEntry", "core");

                                    b2.WithOwner()
                                        .HasForeignKey("MessageRecipientMessageLogEntryId", "MessageRecipientId");
                                });

                            b1.Navigation("AdHoc");

                            b1.Navigation("Attachments");

                            b1.Navigation("CopiedIn");

                            b1.Navigation("TemplateValues");
                        });

                    b.Navigation("Config");

                    b.Navigation("CopiedIn");

                    b.Navigation("Log");

                    b.Navigation("Recipients");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageSchedule", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessageConfiguration", "Config")
                        .WithMany()
                        .HasForeignKey("ConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("LendQube.Entities.Core.Messaging.TemplateKeyValue", "TemplateValues", b1 =>
                        {
                            b1.Property<long>("MessageScheduleId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Key")
                                .HasColumnType("text");

                            b1.Property<string>("Value")
                                .HasColumnType("text");

                            b1.HasKey("MessageScheduleId", "Id");

                            b1.ToTable("MessageSchedule", "core");

                            b1.ToJson("TemplateValues");

                            b1.WithOwner()
                                .HasForeignKey("MessageScheduleId");
                        });

                    b.Navigation("Config");

                    b.Navigation("TemplateValues");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingConfigurationActivity", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessageConfiguration", null)
                        .WithMany("Activity")
                        .HasForeignKey("MessageConfigurationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingGroupEntry", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessagingGroup", "Group")
                        .WithMany("Directory")
                        .HasForeignKey("MessagingGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("LendQube.Entities.Core.Constants.PhoneNumber", "PhoneNumbers", b1 =>
                        {
                            b1.Property<long>("MessagingGroupEntryId")
                                .HasColumnType("bigint");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("integer");

                            b1.Property<string>("Code")
                                .HasColumnType("text");

                            b1.Property<string>("Number")
                                .HasColumnType("text");

                            b1.HasKey("MessagingGroupEntryId", "Id");

                            b1.ToTable("MessagingGroupEntry", "core");

                            b1.ToJson("PhoneNumbers");

                            b1.WithOwner()
                                .HasForeignKey("MessagingGroupEntryId");
                        });

                    b.Navigation("Group");

                    b.Navigation("PhoneNumbers");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingGroupQuery", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessagingGroup", "Group")
                        .WithOne("QueryDirectory")
                        .HasForeignKey("LendQube.Entities.Core.Messaging.MessagingGroupQuery", "MessagingGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Reporting.ReportSchedule", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Messaging.MessageConfiguration", "Config")
                        .WithMany()
                        .HasForeignKey("ConfigId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("LendQube.Entities.Core.Messaging.MessagingGroup", "MessagingGroup")
                        .WithMany()
                        .HasForeignKey("MessagingGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Config");

                    b.Navigation("MessagingGroup");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Reporting.SystemReport", b =>
                {
                    b.HasOne("LendQube.Entities.Core.Reporting.ReportSchedule", "ReportSchedule")
                        .WithMany()
                        .HasForeignKey("ReportScheduleId");

                    b.Navigation("ReportSchedule");
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreAuthorization<System.Guid>", b =>
                {
                    b.HasOne("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreApplication<System.Guid>", "Application")
                        .WithMany("Authorizations")
                        .HasForeignKey("ApplicationId");

                    b.Navigation("Application");
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreToken<System.Guid>", b =>
                {
                    b.HasOne("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreApplication<System.Guid>", "Application")
                        .WithMany("Tokens")
                        .HasForeignKey("ApplicationId");

                    b.HasOne("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreAuthorization<System.Guid>", "Authorization")
                        .WithMany("Tokens")
                        .HasForeignKey("AuthorizationId");

                    b.Navigation("Application");

                    b.Navigation("Authorization");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.CustomerProfile", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("Addresses");

                    b.Navigation("AllTransactions");

                    b.Navigation("Discounts");

                    b.Navigation("Flags");

                    b.Navigation("Holds");

                    b.Navigation("IncomeAndExpenditure");

                    b.Navigation("Notes");

                    b.Navigation("OneTimeCodes");

                    b.Navigation("Placements");

                    b.Navigation("Schedules");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Customers.Transaction", b =>
                {
                    b.Navigation("History");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Placements.Placement", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("Notes");

                    b.Navigation("StatusChangeLogs");

                    b.Navigation("StatusChanges");

                    b.Navigation("Tags");

                    b.Navigation("Transactions");
                });

            modelBuilder.Entity("LendQube.Entities.Collection.Workflows.Debt.DebtSegment", b =>
                {
                    b.Navigation("Rules");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationRole", b =>
                {
                    b.Navigation("RoleClaims");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("LendQube.Entities.Core.BaseUser.ApplicationUser", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Logins");

                    b.Navigation("Tokens");

                    b.Navigation("UserRoles");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Location.Country", b =>
                {
                    b.Navigation("States");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageConfiguration", b =>
                {
                    b.Navigation("Activity");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessageLog", b =>
                {
                    b.Navigation("Activity");

                    b.Navigation("MessageLogEntries");
                });

            modelBuilder.Entity("LendQube.Entities.Core.Messaging.MessagingGroup", b =>
                {
                    b.Navigation("ConfigGroups");

                    b.Navigation("Directory");

                    b.Navigation("QueryDirectory");
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreApplication<System.Guid>", b =>
                {
                    b.Navigation("Authorizations");

                    b.Navigation("Tokens");
                });

            modelBuilder.Entity("OpenIddict.EntityFrameworkCore.Models.OpenIddictEntityFrameworkCoreAuthorization<System.Guid>", b =>
                {
                    b.Navigation("Tokens");
                });
#pragma warning restore 612, 618
        }
    }
}

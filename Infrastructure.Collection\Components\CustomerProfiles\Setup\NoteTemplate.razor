﻿@page "/customers/notetemplates"
@using LendQube.Entities.Collection.Customers
@using LendQube.Entities.Collection.Setup
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using Radzen.Blazor

@inherits GenericCrudTable<CustomerNoteTemplate>

@attribute [Authorize(Policy = ManageCustomersNavigation.CustomerNoteTemplateIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<CustomerNoteTemplate> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Type">Type</label>
            <RadzenDropDown @bind-Value=@context.Type Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<CustomerNoteType>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Type" AllowClear=true Placeholder="Select note type" class="form-input" />
            <ValidationMessage For="() => context.Type" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="ContactType">Contact Type</label>
            <RadzenDropDown @bind-Value=@context.ContactType Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<CustomerContactType>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="ContactType" AllowClear=true Placeholder="Select contact type" class="form-input" />
            <ValidationMessage For="() => context.ContactType" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Template">Template</label>
            <InputTextArea @bind-Value="context.Template" />
            <ValidationMessage For="() => context.Template" class="text-danger" />
        </div>
    </div>
    ;

    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "Setup Note Templates";
        FormBaseTitle = "Note Template";
        CreatePermission = ManageCustomersNavigation.CustomerNoteTemplateCreatePermission;
        EditPermission = ManageCustomersNavigation.CustomerNoteTemplateEditPermission;
        DeletePermission = ManageCustomersNavigation.CustomerNoteTemplateDeletePermission;
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter) || EF.Functions.ILike(x.Template, filterAndPage.TextFilter);
    }
}


﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class I_M_AddNextCallBackDateToWorkflowAnalytics : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.AddColumn<DateOnly>(
                name: "NextCallbackDate",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics",
                type: "DATE",
                nullable: true);

            migrationBuilder.AddColumn<DateOnly>(
                name: "NextCallbackDate",
                schema: "collection",
                table: "AgentWorkflowTask",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<DateOnly>(
                name: "NextCallbackDate",
                schema: "collection",
                table: "AgentWorkflowAnalytics",
                type: "DATE",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NextCallbackDate",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics");

            migrationBuilder.DropColumn(
                name: "NextCallbackDate",
                schema: "collection",
                table: "AgentWorkflowTask");

            migrationBuilder.DropColumn(
                name: "NextCallbackDate",
                schema: "collection",
                table: "AgentWorkflowAnalytics");

        }
    }
}

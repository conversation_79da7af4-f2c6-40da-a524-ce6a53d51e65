using System.Text.Json;
using LendQube.Infrastructure.Collection.DependencyInjection;
using LendQube.Infrastructure.Core.DependencyInjection;
using LendQube.Infrastructure.Core.Helpers.Utils;
using NodaTime;
using NodaTime.Serialization.SystemTextJson;

namespace LendQube.Web.Api;

public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        builder.WebHost.ConfigureKestrel(serverOptions =>
        {
            serverOptions.Limits.KeepAliveTimeout = TimeSpan.FromMinutes(3);
        });


        builder.Services.AddControllers()
            .AddJsonOptions(options =>
            {
                options.JsonSerializerOptions.PropertyNamingPolicy = JsonNamingPolicy.CamelCase;
                options.JsonSerializerOptions.ConfigureForNodaTime(DateTimeZoneProviders.Tzdb);
            });


        builder.AddBaseApiDI()
            .AddDbContext()
            .AddApiIdentity()
            .ConfigureSwagger("Customer")
            .AddCollectionApiServices();

        builder.Services.AddBackgroundServices();

        var app = builder.Build();

        if (builder.Configuration.GetValue<bool>(InfrastructureCoreDI.IsDemoAppSettingsKey))
        {
            app.UseSwagger();
            app.UseSwaggerUI(c => c.DocumentTitle = StringConstants.ApiName);
        }

        app.AddApiApp(builder)
            .UseBackgroundServices();

        app.Run();
    }
}

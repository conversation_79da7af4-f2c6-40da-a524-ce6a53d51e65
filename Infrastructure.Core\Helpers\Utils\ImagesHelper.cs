﻿using SkiaSharp;

namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class ImagesHelper
{
    public static SKBitmap CombineBitmaps(this IEnumerable<SKBitmap> bitmaps, int spacing = 10)
    {
        // Calculate the width and height for the new bitmap
        int totalWidth = 0;
        int maxHeight = 0;

        foreach (var bitmap in bitmaps)
        {
            totalWidth += bitmap.Width + spacing; // Add spacing for each bitmap
            maxHeight = Math.Max(maxHeight, bitmap.Height);
        }

        // Create a new bitmap to hold the combined image
        SKBitmap combinedBitmap = new(totalWidth - spacing, maxHeight); // Subtract the last spacing

        using var canvas = new SKCanvas(combinedBitmap);

        canvas.Clear(SKColors.White);
        int currentX = 0;

        foreach (var bitmap in bitmaps)
        {
            // Draw each bitmap onto the combined bitmap
            canvas.DrawBitmap(bitmap, new SKPoint(currentX, 0));
            currentX += bitmap.Width + spacing; // Move x position for the next bitmap
        }

        return combinedBitmap;
    }

    public static string ToBase64Png(this SKBitmap bitmap, int quality = 60)
    {
        using var image = SKImage.FromBitmap(bitmap);
        using var stream = new MemoryStream();
        // Encode the image to PNG format
        image.Encode(SKEncodedImageFormat.Png, quality).SaveTo(stream);
        byte[] byteArray = stream.ToArray();
        return Convert.ToBase64String(byteArray);
    }
}

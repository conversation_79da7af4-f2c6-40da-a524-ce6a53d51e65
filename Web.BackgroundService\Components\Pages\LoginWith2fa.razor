﻿@page "/account/login/2fa"

@using System.ComponentModel.DataAnnotations
@using LendQube.Infrastructure.Core.Authentication
@using LendQube.Infrastructure.Core.Components
@using LendQube.Infrastructure.Core.Extensions
@using LendQube.Web.BackgroundService.Components.Layout
@layout LoginLayout

@inject IdentityRedirectManager redirectManager
@inject AdminAuthService authService
@inherits AsyncComponentBase

<PageTitle>2FA Authentication</PageTitle>


<div id="wrapper">
    <div id="footer-control" class="index-wrapper">
        <div class="login-wrapper mx-auto">
            <a href="#" class="brand-img mx-auto">
                <img src="@Assets["images/logo/logo.svg"]" alt="Ethica Resolve">
            </a>
            <div class="form-wrapper">
                <EditForm class="form form__login" method="post" Model="Input" OnValidSubmit="OnValidSubmitAsync" FormName="login">
                    <div class="title-wrapper flex __justify-between __align-center" style="padding: 0">
                        <span class="text_xl_medium">Enter Your Authenticator Code</span>
                    </div>
                    <StatusMessage @ref="message" />
                    <DataAnnotationsValidator />
                    <input type="hidden" name="ReturnUrl" value="@ReturnUrl" />
                    <input type="hidden" name="RememberMe" value="@RememberMe" />

                    <div class="form-row">
                        <label for="two-factor-code" class="form-label">Authenticator Code</label>
                        <InputText id="two-factor-code" @bind-Value="Input.TwoFactorCode" class="form-input" autocomplete="off" />
                        <ValidationMessage For="() => Input.TwoFactorCode" class="text-danger" />
                    </div>
                    <div class="form-row">
                        <div class="check-group">
                            <label class="check-label">
                                Remember This Browser For 30 Days
                                <InputCheckbox class="check-input" @bind-Value="Input.RememberMachine" />
                                <span class="checkmark"></span>
                            </label>
                        </div>
                    </div>
                    <div class="button-row">
                        <button id="login" type="submit" class="btn btn--primary __full">Continue</button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>


@code {
    private StatusMessage message;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string ReturnUrl { get; set; }

    [SupplyParameterFromQuery]
    private bool RememberMe { get; set; }

    [SupplyParameterFromQuery]
    private string Key { get; set; }

    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    protected override void OnInitialized()
    {
        if (!redirectManager.IsValidMFARouteKey(Key, HttpContext))
            redirectManager.RedirectTo("account/login");
    }

    private async Task OnValidSubmitAsync()
    {
        if (!redirectManager.IsValidMFARouteKey(Key, HttpContext))
        {
            await authService.Logout();
            redirectManager.RedirectTo("account/login");
            return;
        }

        var state = await authService.LoginWith2fa(new LoginWith2faVM(Input.TwoFactorCode, RememberMe, Input.RememberMachine), HttpContext.GetIpAddress());

        if (state == UserLoginState.RedirectToLogin)
            redirectManager.RedirectTo("account/login");
        else if (state == UserLoginState.Succeeded)
        {
            redirectManager.RemoveMFARouteKey(HttpContext);
            redirectManager.RedirectTo(ReturnUrl);
        }
        else if (state == UserLoginState.LockedOut)
        {
            redirectManager.RedirectTo("account/lockout");
        }
        else if (state == UserLoginState.ChangePasswordRequired)
        {
            message.Error("Login to admin to maintain account");
        }
        else
        {
            message.Error("Code is invalid");
        }
    }

    private sealed class InputModel
    {
        [Required]
        [StringLength(7, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Text)]
        [Display(Name = "Authenticator code")]
        public string TwoFactorCode { get; set; }

        [Display(Name = "Remember this machine")]
        public bool RememberMachine { get; set; }
    }
}

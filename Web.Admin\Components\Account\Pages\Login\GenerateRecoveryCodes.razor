﻿@page "/account/login/recoverycodes"

@using LendQube.Entities.Core.BaseUser

@inject AdminAuthService authService
@inject IdentityRedirectManager redirectManager

<PageTitle>Generate 2FA Recovery Codes</PageTitle>


<div id="wrapper">
    <div id="footer-control" class="index-wrapper">
        <div class="login-wrapper mx-auto">
            <a href="#" class="brand-img mx-auto" style="margin: 0 auto 40px">
                <img src="@Assets["images/logo/logo.svg"]" alt="Silicon">
            </a>
            <div class="card">

                <div class="title-wrapper flex __justify-between __align-center">
                    <span class="text_xl_medium">@(recoveryCodes == null ? "Generate Recovery Codes" : "Recovery Codes")</span>
                </div>

                <div class="form" style="padding: 5px">
                    @if (recoveryCodes is not null)
                    {
                        <ShowRecoveryCodes RecoveryCodes="recoveryCodes.ToArray()" StatusMessage="@message" />

                        <div class="button-row">
                            <a href="@ReturnUrl" id="FinishButton" class="btn btn--primary __full">Finish</a>
                        </div>
                    }
                    else
                    {
                        <div class="form-row">
                            <div class="alert warning hasFlex">
                                <div class="al-flex">
                                    <div class="al-content">
                                        <span class="al__title" role="alert">
                                            <p>
                                                <strong>Put these codes in a safe place.</strong>
                                            </p>
                                            <p>
                                                If you lose your device and don't have the recovery codes please contact admin.
                                            </p>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <form @formname="generate-recovery-codes" @onsubmit="OnSubmitAsync" method="post" style="padding: 0px">
                            <AntiforgeryToken />

                            <div class="button-row">
                                <button type="submit" class="btn btn--primary __full">Generate Recovery Codes</button>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private StatusMessageBuilder message = new();
    private IEnumerable<string> recoveryCodes;

    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    [SupplyParameterFromQuery]
    private string ReturnUrl { get; set; } = "/";

    [SupplyParameterFromQuery]
    private string Key { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (!redirectManager.IsValidMFARouteKey(Key, HttpContext))
        {
            await authService.Logout();
            redirectManager.RedirectTo("account/login");
        }
    }

    private async Task OnSubmitAsync()
    {
        (var state, recoveryCodes, var responseMessage) = await authService.GetRecoveryCodes(HttpContext.User);
        if (state == UserLoginState.RedirectToLogin)
            redirectManager.RedirectTo("account/login");
        else
            message.Set(state == UserLoginState.Succeeded, responseMessage);
    }
}

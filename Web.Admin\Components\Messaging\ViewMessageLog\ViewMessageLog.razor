﻿@page "/messaging/logs/{LogId:long}"
@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.AppSettings
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Components.Timeline
@using LendQube.Infrastructure.Core.Database.GenericCrud
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.Database.Specification
@using LendQube.Infrastructure.Core.Helpers.Utils
@using LendQube.Infrastructure.Core.Messaging
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using LendQube.Infrastructure.Core.Messaging.Configuration
@using Radzen.Blazor

@attribute [Authorize(Policy = MessagingNavigation.MessageLogsIndexPermission)]

@inherits AuthComponentBase

<PageTitle>@Title</PageTitle>
<StatusMessage @ref="message" />
<div class="pg-actions">
    <ul class="breadcrumbs">
        <li class="__item">
            <a href="/messaging/logs">
                <i data-feather="arrow-left"></i>
            </a>
        </li>
        <li class="__item">
            <a>@Title: Originated from @Data?.OriginatedFrom</a>
        </li>
    </ul>
    <div class="actions-item ml-auto">
        @if (Data.Status == MessageStatus.Sent || Data.Status == MessageStatus.Failed)
        {
            <LoadButton Label="Resend" OnClick="ResendMessage" Css="btn--success" />
        }
        <LoadButton Label="Clear Activity" OnClick="ClearActivity" Css="btn--danger" />
        @if (Config.DeployState.IsDemo)
        {
            <LoadButton Label="Send Message" OnClick="SendMessage" />
        }
    </div>
</div>
<div class="pg-row grid loan-grid grid-tab-1">
    <div class="info-col">
        <div class="card info-item">
            <div class="__header flex __justify-between">
                <div class="title">
                    <span class="__title">From: @Data?.OriginatedFrom</span>
                    <div class="status">
                        <span class="lz __@(Data.Status == MessageStatus.Sent ? "blue" :
                        Data.Status == MessageStatus.Failed ? "red" :
                        Data.Status == MessageStatus.Delivered ? "green" :
                        "default")">@Data.Status.GetDisplayName()</span>
                    </div>
                </div>
            </div>
            <div class="detail-wrapper grid-2">
                <div class="detail-item">
                    <span class="label">Total Attempts</span>
                    <span class="value">@Data?.AttemptCount</span>
                </div>
                <div class="detail-item">
                    <span class="label">Total Recipients</span>
                    <span class="value">@Data?.TotalRecipients</span>
                </div>
                <div class="detail-item">
                    <span class="label">Total Entries</span>
                    <span class="value">@Data?.TotalEntries</span>
                </div>
                <div class="detail-item">
                    <span class="label">Created By</span>
                    <span class="value">@Data?.CreatedByUser</span>
                </div>
                <div class="detail-item">
                    <span class="label">Created On</span>
                    <span class="value">
                        <LocalTime Value="Data?.CreatedDate" />
                    </span>
                </div>
                <div class="detail-item">
                    <span class="label">Last Modified On</span>
                    <span class="value">
                        <LocalTime Value="Data?.LastModifiedDate" />
                    </span>
                </div>
            </div>
        </div>
        <div class="card activity-item">
            <div class="accordion" id="activityAccordion">
                <div class="accordion-item">
                    <span class="accordion-header" id="activityHeading">
                        <button class="accordion-button" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapseActivity" aria-expanded="true"
                                aria-controls="collapseActivity">
                            Activity
                        </button>
                    </span>
                    <div id="collapseActivity"
                         class="accordion-collapse collapse show"
                         aria-labelledby="activityHeading"
                         data-bs-parent="#activityAccordion">
                        <div class="accordion-body">
                            @if (Data.Id != 0)
                            {
                                <ActivityTimeline T="MessageLogActivity" LoadData="LoadActivity" @ref=activityTimeline />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="details-col">
        <div class="card">
            <DataTable T="MessageLogEntryVM" DeferLoading="true" TableDefinition="GroupTableDefinition" LoadData="LoadEntries" @ref="entryTable" />
        </div>
    </div>
</div>

<ModalComponentWithoutPolicy ModalId="@ViewRecipientsModal" ModalCss="modal-dialog-large width-70vw" Title=@($"View Recipients: {SelectedEntry?.Subject}")>
    <BodyContent>
        <StatusMessage @ref="modalMessage" />

        <DataTable T="ViewMessageRecipientVM" DeferLoading="true" TableDefinition="RecipientTableDefinition" LoadData="LoadRecipients" DefaultPageSize="5" @ref="recipientsTable" />
    </BodyContent>
</ModalComponentWithoutPolicy>

<ModalComponentWithoutPolicy ModalId="@PreviewMessageModal" ModalCss="modal-dialog-large width-70vw" Title=@($"Preview Message For {previewMessageModel.Name}")>
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Description">Subject</label>
            <input type="text" class="form-input" value="@previewMessageModel.Subject" aria-required="true" readonly />
        </div>
        @if (previewMessageModel.TextRequired)
        {
            <div class="form-row">
                <label class="form-label" for="Description">Text Template</label>
                <textarea aria-required="true" readonly>
                    @previewMessageModel.TextTemplate
                                </textarea>
            </div>
        }

        @if (previewMessageModel.EmailRequired)
        {
            <div class="form-row">
                <RadzenHtmlEditor @bind-Value=@previewMessageModel.HtmlTemplate style="height: 50vh" ShowToolbar="false" Disabled="true" />
            </div>
        }
    </BodyContent>
</ModalComponentWithoutPolicy>
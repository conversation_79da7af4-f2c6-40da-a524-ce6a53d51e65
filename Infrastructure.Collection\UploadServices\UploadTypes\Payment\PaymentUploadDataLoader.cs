﻿using System.Collections.Concurrent;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;
using LendQube.Infrastructure.Core.ViewModels.Upload;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.Payment;

internal static class PaymentUploadDataLoader
{
    public static void ValidateData(this PaymentUploadVM data, long row, List<PaymentCustomerDataVM> placements, ConcurrentBag<UploadResult> resultList)
    {
        if (string.IsNullOrEmpty(data.AccountNumber))
        {
            resultList.Add(new(row, data.AccountNumber, "No account number, item will be skipped"));
        }
        else
        {
            var query = placements.Where(x => x.PlacementAccountNumber == data.AccountNumber);
            if (!query.Any())
            {
                resultList.Add(new(row, data.AccountNumber, "Placement for account number not found, item will be skipped"));
            }
            else if (query.Any(x => x.TransactionExists))
            {
                resultList.Add(new(row, data.AccountNumber, $"Transaction reference {data.Reference} already exists. Please cross check"));
            }
        }
    }

}

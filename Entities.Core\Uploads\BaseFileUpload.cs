﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;

namespace LendQube.Entities.Core.Uploads;

public abstract class BaseFileUpload<T> : BaseEntityWithIdentityId<T> where T : IBaseEntityWithNumberId
{
    [Required, StringLength(EntityConstants.DEFAULT_DESCRIPTION_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Description { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public UploadAction Action { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public UploadStatus Status { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string FileUrl { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string AnalysisFileUrl { get; set; }
    public string Message { get; set; }
}

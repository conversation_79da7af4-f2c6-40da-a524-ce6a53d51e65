﻿using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_PTPAmountRecording : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "DebtWorkflowTimeAnalytics",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "DebtWorkflowAnalytics",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "AgentWorkflowAnalytics",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.CreateTable(
                name: "CustomerPromiseToPay",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    DueDate = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    Redeemed = table.Column<bool>(type: "boolean", nullable: false),
                    NoteId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerPromiseToPay", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerPromiseToPay_CustomerNote_NoteId",
                        column: x => x.NoteId,
                        principalSchema: "collection",
                        principalTable: "CustomerNote",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerPromiseToPay_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPromiseToPay_NoteId",
                schema: "collection",
                table: "CustomerPromiseToPay",
                column: "NoteId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPromiseToPay_ProfileId",
                schema: "collection",
                table: "CustomerPromiseToPay",
                column: "ProfileId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CustomerPromiseToPay",
                schema: "collection");

            migrationBuilder.DropColumn(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "DebtWorkflowTimeAnalytics");

            migrationBuilder.DropColumn(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "DebtWorkflowAnalytics");

            migrationBuilder.DropColumn(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics");

            migrationBuilder.DropColumn(
                name: "TotalPTPAmount",
                schema: "collection",
                table: "AgentWorkflowAnalytics");
        }
    }
}

﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Core.Uploads;

public class SystemFileUpload : BaseFileUpload<SystemFileUpload>, IEntityHasNotifyTrigger
{
    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public SystemUploadType Type { get; set; }

    public string Schema => CoreEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => $@"NEW.""{nameof(Status)}"" = {(int)UploadStatus.Queued}";
}

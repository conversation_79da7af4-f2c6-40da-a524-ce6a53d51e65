﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;

namespace LendQube.Entities.Core.Constants;

public sealed record PhoneNumber([MaxLength(4), ValidString(ValidStringRule.NumberWithPlusOnly)] string Code, [DataType(DataType.PhoneNumber), ValidString(ValidStringRule.OnlyNumber)] string Number)
{
    public override string ToString() => $"{Code} {Number}";
    public bool IsValid() => !string.IsNullOrEmpty(Code) && !string.IsNullOrEmpty(Number);
}

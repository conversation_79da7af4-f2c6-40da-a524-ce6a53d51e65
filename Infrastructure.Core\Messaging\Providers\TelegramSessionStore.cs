﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class TelegramSessionStore : Stream
{
    private const int SESSION_ID = 1;
    private readonly IUnitofWork uow;
    private bool hasSession;
    private byte[] data;
    private int dataLen;
    private DateTime lastWrite;
    private Task delayedWrite;

    public TelegramSessionStore(IUnitofWork uow)
    {
        this.uow = uow;
        data = this.uow.Db.OneSelect(Query<TelegramSession, byte[]>.Where(x => x.Id == SESSION_ID).Select(x => x.Data));
        dataLen = data?.Length ?? 0;
        hasSession = dataLen > 0;
    }

    protected override void Dispose(bool disposing)
    {
        delayedWrite?.Wait();
    }

    public override int Read(byte[] buffer, int offset, int count)
    {
        Array.Copy(data, 0, buffer, offset, count);
        return count;
    }

    public override void Write(byte[] buffer, int offset, int count)
    {
        data = buffer;
        dataLen = count;

        if (delayedWrite != null) return;

        var delay = 1000 - (int)(DateTime.UtcNow - lastWrite).TotalMilliseconds;

        if (delay < 0)
        {
            var newData = count == buffer.Length ? buffer : buffer[offset..(offset + count)];
            if (!hasSession)
            {
                uow.Db.Insert(new TelegramSession { Id = SESSION_ID, Data = newData });
                uow.Save();
                hasSession = true;
            }
            else
            {
                uow.Db.UpdateAndSaveWithFilter<TelegramSession>(x => x.Id == SESSION_ID, x => x.SetProperty(y => y.Data, newData));
            }

            lastWrite = DateTime.UtcNow;
        }
        else
        {
            delayedWrite = Task.Delay(delay).ContinueWith(t =>
            {
                lock (this)
                {
                    delayedWrite = null;
                    Write(data, 0, dataLen);
                }
            });
        }
    }

    public override long Length => dataLen;
    public override long Position { get => 0; set { } }
    public override bool CanSeek => false;
    public override bool CanRead => true;
    public override bool CanWrite => true;
    public override long Seek(long offset, SeekOrigin origin) => 0;
    public override void SetLength(long value) { }
    public override void Flush() { }
}

﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Hosting;

namespace LendQube.Infrastructure.Core.Components;

public abstract class AsyncComponentBase : ComponentBase, IDisposable
{
    [Inject]
    private IHostApplicationLifetime Lifetime { get; set; }
    private CancellationTokenSource cts;
    public CancellationToken Cancel => cts?.Token ?? CancellationToken.None;

    protected override void OnInitialized()
    {
        cts = Lifetime != null ? CancellationTokenSource.CreateLinkedTokenSource(Lifetime.ApplicationStopping) : new();
    }

    public virtual void Dispose()
    {
        GC.SuppressFinalize(this);
        if (cts != null)
        {
            cts.Cancel();
            cts.Dispose();
            cts = null;
        }
    }
}
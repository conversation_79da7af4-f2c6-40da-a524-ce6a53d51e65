﻿using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Core.Database.GenericSpecification;

public sealed class GenericSpecificationService<T>(GenericEntityCrudService<T> crudService) : BaseSpecification<T> where T : class, IBaseEntityForRelationalDb
{
    public GenericEntityCrudService<T> CrudService { get; } = crudService;
}


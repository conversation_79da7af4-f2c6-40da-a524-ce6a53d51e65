﻿using System.Runtime.CompilerServices;

namespace LendQube.Infrastructure.Core.Telemetry;

public interface ILogManager<T> where T : class
{
    void LogError(EventSource eventSource, EventAction action, Exception exception, string message, [CallerMemberName] string callerMemberName = "", params object[] data);
    void LogInformation(EventSource eventSource, EventAction action, string message, [CallerMemberName] string callerMemberName = "", params object[] data);
    void LogWarning(EventSource eventSource, EventAction action, string message, [CallerMemberName] string callerMemberName = "", params object[] data);
}
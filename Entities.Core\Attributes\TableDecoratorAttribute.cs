﻿namespace LendQube.Entities.Core.Attributes;

[AttributeUsage(AttributeTargets.Property)]
public class TableDecoratorAttribute(params TableDecoratorType[] type) : Attribute
{
    public virtual TableDecoratorType[] Type => type;
}


public enum TableDecoratorType
{
    Id, //mark field to be treated as id
    Image, //show image in table row
    Percentage, //show as percentage bar
    UserId, //make user id clickable to link back to user profile
    ShowInDelete, //show data in delete modal of table
    ShowInInfo, //show data in info view of table
    GroupActionCheckbox, //use to allow user select multiple
    SkipFilter, //remove from table filter
    HideColumn, //hides column in view, but keeps data to be available for use later
    NoSort, //Disable sort on this column
}
public static class TableDecoratorHelper
{
    public static bool HasDecoratorType(this TableDecoratorType[] typeList, TableDecoratorType type) => typeList != null && typeList.Contains(type);
}

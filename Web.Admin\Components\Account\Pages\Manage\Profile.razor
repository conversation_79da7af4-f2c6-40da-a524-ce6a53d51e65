﻿@page "/account/manage/profile"
@using LendQube.Infrastructure.Core.AdminUserManagement
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inject AdminUserManagerService service

@inherits SingleFormComponentBase<AdminUserEditVM>

<FormComponent Title="Update Your Profile" Model="Model" OnValidSubmit="@SubmitEdit" FormName="@FormName" FormMessage="Message">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Email">Email</label>
            <InputText @bind-Value="context.Email" class="form-input" aria-required="true" placeholder="Email" />
            <ValidationMessage For="() => context.Email" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="FirstName">First Name</label>
            <InputText @bind-Value="context.FirstName" class="form-input" aria-required="true" placeholder="First Name" />
            <ValidationMessage For="() => context.FirstName" class="text-danger" />
        </div>


        <div class="form-row">
            <label class="form-label" for="LastName">Last Name</label>
            <InputText @bind-Value="context.LastName" class="form-input" aria-required="true" placeholder="Last Name" />
            <ValidationMessage For="() => context.LastName" class="text-danger" />
        </div>

        <div class="form-row">
            <label class="form-label" for="OtherNames">Other Names</label>
            <InputText @bind-Value="context.OtherNames" class="form-input" aria-required="true" placeholder="Other Names" />
            <ValidationMessage For="() => context.OtherNames" class="text-danger" />
        </div>
        <div class="grid-col-2">

            <div class="form-row">
                <label class="form-label" for="PhoneCode">Phone Code</label>
                <InputText @bind-Value="context.PhoneCode" class="form-input" aria-required="true" placeholder="Phone Code" />
                <ValidationMessage For="() => context.PhoneCode" class="text-danger" />
            </div>

            <div class="form-row">
                <label class="form-label" for="PhoneNumber">Phone Number</label>
                <InputText @bind-Value="context.PhoneNumber" class="form-input" aria-required="true" placeholder="Phone Number" />
                <ValidationMessage For="() => context.PhoneNumber" class="text-danger" />
            </div>

        </div>
    </BodyContent>
</FormComponent>
@code
{

    protected override async ValueTask Init()
    {
        Model = await service.GetByUsername(UserName, Cancel);
    }

    public Task SubmitEdit() => Submit(() => service.Update(Model, UserName));
}
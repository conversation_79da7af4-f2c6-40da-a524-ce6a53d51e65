﻿using System.ComponentModel.DataAnnotations;
using System.Security.Claims;

namespace LendQube.Infrastructure.Core.Authentication;

public sealed class AdminLoginVM
{
    [Required(ErrorMessage = "Username is required")]
    [EmailAddress(ErrorMessage = "Please enter a valid username")]
    public string Username { get; set; } = "";

    [Required(ErrorMessage = "Password is required")]
    [DataType(DataType.Password)]
    public string Password { get; set; } = "";

    [Display(Name = "Remember me")]
    public bool RememberMe { get; set; }

    [Display(Name = "Skip 2FA")]
    public bool Skip2fa { get; set; }
}

public enum UserLoginState
{
    Failed,
    LockedOut,
    TwoFactorSignInRequired,
    TwoFactorSetupRequired,
    ChangePasswordRequired,
    RedirectToLogin,
    Succeeded
}

public sealed record ValidateAuthenticatorSetupCommand(string AuthenticatorCode, ClaimsPrincipal User);

public sealed record AuthenticatorKeyModel(string SharedKey, string AuthenticatorUri, string ErrorMessage);

public sealed record ValidateAuthenticatorSetup(string AuthenticatorCode, ClaimsPrincipal User, string Ip, bool RememberMe);

public sealed record LoginWith2faVM(string AuthenticatorCode, bool RememberMe, bool RememberMachine);

public sealed class AdminChangePasswordVM
{
    [MinLength(8, ErrorMessage = "The {0} must be at least {1} characters long.")]
    [DataType(DataType.Password)]
    [Display(Name = "Password")]
    public required string Password { get; set; }

    [DataType(DataType.Password)]
    [Display(Name = "Confirm password")]
    [Compare("Password", ErrorMessage =
        "The password and confirmation password do not match.")]
    public required string ConfirmPassword { get; set; }
}
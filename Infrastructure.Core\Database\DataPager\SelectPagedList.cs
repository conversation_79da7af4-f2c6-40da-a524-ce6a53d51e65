﻿using System.Linq.Expressions;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Core.Database.DataPager;

internal sealed class SelectPagedList<T, TVM> : AbstractPagedList<T, TVM>
    where T : class
{
    public SelectPagedList(IQueryable<T> source, DataFilterAndPage filterAndPage, ISpecification<T> spec, Expression<Func<T, TVM>> selector) : base(source, filterAndPage, spec, selector)
    {
    }
    public SelectPagedList(IQueryable<T> source, DataFilterAndPage filterAndPage, ISpecification<TVM> spec, Expression<Func<T, TVM>> selector) : base(source, filterAndPage, spec, selector)
    {
    }
}
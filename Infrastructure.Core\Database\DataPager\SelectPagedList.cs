﻿using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Core.Database.DataPager;

internal sealed class SelectPagedList<T, TVM> : AbstractPagedList<T, TVM>
    where T : class
{
    public SelectPagedList(IQueryable<T> source, DataFilterAndPage filterAndPage, ISpecification<T> spec, Expression<Func<T, TVM>> selector, CancellationToken ct) : base(source, filterAndPage, spec, selector)
    {
        var localSource = LocalSource
                       .Skip(SkipBy)
                       .Take(PageSize);

        if (ct.IsCancellationRequested)
            return;

        if (filterAndPage.AsyncNotSupportedDataSource)
        {
            UnboxedData = [.. localSource];
        }
        else
        {
            Data = localSource.QCache(filterAndPage.IsCacheable).ToListAsync(ct);
        }
    }
    public SelectPagedList(IQueryable<T> source, DataFilterAndPage filterAndPage, ISpecification<TVM> spec, Expression<Func<T, TVM>> selector, CancellationToken ct) : base(source, filterAndPage, spec, selector)
    {
        var localSource = LocalSource
                       .Skip(SkipBy)
                       .Take(PageSize);

        if (ct.IsCancellationRequested)
            return;

        if (filterAndPage.AsyncNotSupportedDataSource)
        {
            UnboxedData = [.. localSource];
        }
        else
        {
            Data = localSource.QCache(filterAndPage.IsCacheable).ToListAsync(ct);
        }
    }
}
﻿using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using System.Reflection;

namespace LendQube.Infrastructure.Core.AppSettings;

public static class AppConfigLoader
{
    public static void Load<T>(this WebApplicationBuilder builder) where T : class
    {
        string c = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "AppSettings");
        var configuration = new ConfigurationBuilder().SetBasePath(c)
            .AddJsonFile("appsettings.json")
            .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json")
            .Build();

        IConfigurationBuilder configBuilder = builder.Configuration;
        configBuilder.AddConfiguration(configuration);

        builder.Services.Configure((T options) => configuration.GetSection("Providers").Bind(options));
    }

    public static void Load<T>(this WebApplicationBuilder builder, params string[] appsettings) where T : class
    {
        string c = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "AppSettings");
        var baseConfig = new ConfigurationBuilder().SetBasePath(c);
        foreach (var settings in appsettings)
        {
            baseConfig = baseConfig
                .AddJsonFile($"{settings}.json")
                .AddJsonFile($"{settings}.{builder.Environment.EnvironmentName}.json", true);
        }

        var configuration = baseConfig.Build();

        IConfigurationBuilder configBuilder = builder.Configuration;
        configBuilder.AddConfiguration(configuration);

        builder.Services.Configure((T options) => configuration.Bind(options));
    }
}

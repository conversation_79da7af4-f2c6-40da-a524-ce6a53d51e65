﻿using System.Collections.Concurrent;
using System.Globalization;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using OfficeOpenXml.Export.ToCollection;
using SecurityDriven;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.PlacementUpload;

internal static class PlacementUploadDataLoader
{
    public static readonly string[] Headers = ["account_number","balance_total","first_name","last_name","dob","email","mobile","phone","address1","address2","city","county","postcode",
        "country","company","balance_principal","balance_interest","balance_fees","monthly_recurring_bills","last_invoice_date","last_payment_date","agreement_date",
        "default_date","placement_count","tags","notes","extra_field1","extra_field2","closed"];

    public static UploadData LoadCustomerData(this UploadData data, CollectionFileUpload upload, ToCollectionRow row, List<CountryWithCurrencyVM> countries)
    {
        var countryText = row.GetText("country")?.Trim();
        var country = countries.FirstOrDefault(x => x.CountryName.Equals(countryText, StringComparison.OrdinalIgnoreCase));

        var mobile = row.GetValue<string>("mobile")?.Trim();
        var phone = row.GetValue<string>("phone")?.Trim();

        var profile = new CustomerProfile
        {
            Id = FastGuid.NewPostgreSqlGuid().ToString(),
            AccountId = $"{DateTime.UtcNow:yyyy-MMddHHmmssffffff}",
            Email = row.GetText("email")?.Trim()?.ToLowerInvariant(),
            FirstName = row.GetText("first_name")?.Trim()?.FirstCharToUpper(),
            LastName = row.GetText("last_name")?.Trim()?.FirstCharToUpper(),
            DateOfBirth = row.GetValue<string>("dob")?.Trim().ToDate(),
            CountryCode = country?.CountryCode,
            CurrencySymbol = country?.CurrencySymbol,
            CurrencyCode = country?.CurrencyCode,
            PhoneNumber = country?.PhoneCode == null || string.IsNullOrEmpty(phone) ? null : new PhoneNumber(country.PhoneCode, phone.CleanPhoneNumber(country.PhoneCode)),
            MobileNumber = country?.PhoneCode == null || string.IsNullOrEmpty(mobile) ? null : new PhoneNumber(country.PhoneCode, mobile.CleanPhoneNumber(country.PhoneCode)),
            CreatedByUserId = upload.CreatedByUserId,
            FileUploadId = upload.Id,
        };


        var address = new CustomerAddress
        {
            Country = country?.CountryName,
            AddressLine1 = row.GetText("address1")?.Trim(),
            AddressLine2 = row.GetText("address2")?.Trim(),
            City = row.GetText("city")?.Trim(),
            Locality = row.GetText("county")?.Trim(),
            PostCode = row.GetText("postcode")?.Trim(),
            CountryCode = country?.CountryCode,
            CreatedByUserId = upload.CreatedByUserId,
        };

        data.Profile = profile;
        data.Address = address;
        data.CurrencySymbol = country?.CurrencySymbol;

        return data;
    }

    public static UploadData LoadPlacementData(this UploadData data, CollectionFileUpload upload, ToCollectionRow row)
    {

        var placement = new Placement
        {
            SourceAccountNumber = row.GetText("account_number")?.Trim(),
            BalanceTotal = row.GetValue<decimal?>("balance_total") ?? 0,
            Company = row.GetText("company")?.Trim(),
            BalancePrincipal = row.GetValue<decimal?>("balance_principal") ?? 0,
            BalanceInterest = row.GetValue<decimal?>("balance_interest") ?? 0,
            BalanceFees = row.GetValue<decimal?>("balance_fees") ?? 0,
            MonthlyRecurringBills = row.GetValue<decimal?>("monthly_recurring_bills") ?? 0,
            LastInvoiceDate = row.GetValue<string>("last_invoice_date")?.Trim().ToDate(),
            LastPaymentDate = row.GetValue<string>("last_payment_date")?.Trim().ToDate(),
            AgreementDate = row.GetValue<string>("agreement_date")?.Trim().ToDate(),
            DefaultDate = row.GetValue<string>("default_date")?.Trim().ToDate(),
            PlacementCount = row.GetValue<int?>("placement_count") ?? 0,
            CreatedByUserId = upload.CreatedByUserId,
            FileUploadId = upload.Id,
            Activities = []
        };


        var note = row.GetText("notes")?.Trim();
        var tags = row.GetText("tags")?.Trim().Split(',', StringSplitOptions.TrimEntries);
        if (!tags.IsNullOrEmpty())
        {
            placement.Tags = tags.Select(x => new PlacementTag
            {
                Tag = x,
                CreatedByUserId = upload.CreatedByUserId,
            }).ToList();

        }

        if (!string.IsNullOrEmpty(note))
        {
            placement.Notes = [new PlacementNotes
            {
                PlacementId = placement.Id,
                Note = note,
                CreatedByUserId = upload.CreatedByUserId,
            }];
        }

        var closed = !string.IsNullOrEmpty(row.GetText("closed")?.Trim());
        placement.Status = closed ? PlacementStatus.Closed : PlacementStatus.New;
        data.Placement = placement;

        return data;
    }

    public static void ValidateData(this UploadData data, long row, List<string> placementSourceAccounts, ConcurrentBag<UploadResult> resultList)
    {

        if (string.IsNullOrEmpty(data.Profile.Email))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No email"));
        }

        if (data.Profile.MobileNumber == null)
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No mobile number"));
        }

        if (string.IsNullOrEmpty(data.Profile.CountryCode))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "Country not found"));
        }

        if (string.IsNullOrEmpty(data.Profile.FirstName))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No first name"));
        }

        if (string.IsNullOrEmpty(data.Profile.LastName))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No last name"));
        }

        if (string.IsNullOrEmpty(data.Address.AddressLine1))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No AddressLine1"));
        }

        if (string.IsNullOrEmpty(data.Address.PostCode))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No PostCode"));
        }

        if (string.IsNullOrEmpty(data.Placement.SourceAccountNumber))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No account id"));
        }
        else if (placementSourceAccounts.Contains(data.Placement.SourceAccountNumber))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "Placement with the same id exists for customer"));
        }

        if (string.IsNullOrEmpty(data.Placement.Company))
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No company name"));
        }

        if (data.Placement.BalanceTotal == 0)
        {
            resultList.Add(new(row, data.Placement.SourceAccountNumber, "No balance total"));
        }
    }

    public static DateOnly? ToDate(this string dateString)
    {
        if (!string.IsNullOrEmpty(dateString))
            dateString = dateString.Replace("00:00:00", string.Empty).Trim();

        if (!string.IsNullOrEmpty(dateString) && DateOnly.TryParseExact(dateString, ["MM/dd/yyyy", "MM/dd/yyyy HH:mm:ss", "MM/dd/yyyy hh:mm:ss tt", "dd/MM/yyyy hh:mm:ss tt", "dd/MM/yyyy"], CultureInfo.InvariantCulture, DateTimeStyles.None, out var date))
            return date;

        return null;
    }
}

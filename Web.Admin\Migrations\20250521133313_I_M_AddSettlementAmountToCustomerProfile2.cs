﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class I_M_AddSettlementAmountToCustomerProfile2 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "BalanceRemaining",
                schema: "collection",
                table: "CustomerProfile",
                type: "numeric(18,2)",
                nullable: false,
                computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\" - \"SettlementAmount\"",
                stored: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)",
                oldComputedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"",
                oldStored: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterColumn<decimal>(
                name: "BalanceRemaining",
                schema: "collection",
                table: "CustomerProfile",
                type: "numeric(18,2)",
                nullable: false,
                computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"",
                stored: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)",
                oldComputedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\" - \"SettlementAmount\"",
                oldStored: true);
        }
    }
}

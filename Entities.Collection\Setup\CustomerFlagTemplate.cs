﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Setup;

public class CustomerFlagTemplate : BaseEntityWithIdentityId<CustomerFlagTemplate>
{
    [Required, MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH)]
    public string Flag { get; set; }

    public override void Configure(EntityTypeBuilder<CustomerFlagTemplate> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.Flag).IsUnique();
    }
}

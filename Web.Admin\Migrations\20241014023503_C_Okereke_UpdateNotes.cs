﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_Okereke_UpdateNotes : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "ContactType",
                schema: "collection",
                table: "CustomerNote",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "PlacementId",
                schema: "collection",
                table: "CustomerNote",
                type: "bigint",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "CustomerContactDetail",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    Email = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber_Code = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber_Number = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerContactDetail", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerContactDetail_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerDiscount",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Reason = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerDiscount", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerDiscount_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerFlagTemplate",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Flag = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerFlagTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerNoteTemplate",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    ContactType = table.Column<int>(type: "integer", nullable: true),
                    Template = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerNoteTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerFlag",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    FlagId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerFlag", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerFlag_CustomerFlagTemplate_FlagId",
                        column: x => x.FlagId,
                        principalSchema: "collection",
                        principalTable: "CustomerFlagTemplate",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerFlag_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNote_PlacementId",
                schema: "collection",
                table: "CustomerNote",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerContactDetail_ProfileId",
                schema: "collection",
                table: "CustomerContactDetail",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerDiscount_ProfileId",
                schema: "collection",
                table: "CustomerDiscount",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFlag_FlagId",
                schema: "collection",
                table: "CustomerFlag",
                column: "FlagId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFlag_ProfileId",
                schema: "collection",
                table: "CustomerFlag",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFlagTemplate_Flag",
                schema: "collection",
                table: "CustomerFlagTemplate",
                column: "Flag",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNoteTemplate_Name",
                schema: "collection",
                table: "CustomerNoteTemplate",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "Type", "ContactType" });

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerNote_Placement_PlacementId",
                schema: "collection",
                table: "CustomerNote",
                column: "PlacementId",
                principalSchema: "collection",
                principalTable: "Placement",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomerNote_Placement_PlacementId",
                schema: "collection",
                table: "CustomerNote");

            migrationBuilder.DropTable(
                name: "CustomerContactDetail",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerDiscount",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerFlag",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerNoteTemplate",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerFlagTemplate",
                schema: "collection");

            migrationBuilder.DropIndex(
                name: "IX_CustomerNote_PlacementId",
                schema: "collection",
                table: "CustomerNote");

            migrationBuilder.DropColumn(
                name: "ContactType",
                schema: "collection",
                table: "CustomerNote");

            migrationBuilder.DropColumn(
                name: "PlacementId",
                schema: "collection",
                table: "CustomerNote");
        }
    }
}

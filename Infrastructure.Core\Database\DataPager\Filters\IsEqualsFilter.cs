﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

internal sealed class IsEqualsFilter() : ObjectFilter(ColumnFilterRule.IsEquals)
{
    public override Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value)
    {
        if (propertyType.IsDateTime())
            return ExpressionsExtension.DateTimeEqual<T>(propertyType, value.ToString(), vm.ColumnName);

        if (propertyType.IsDateOnly())
            return ExpressionsExtension.DateOnlyEqual<T>(propertyType, value.ToString(), vm.ColumnName);

        if (propertyType.IsInstant())
            return ExpressionsExtension.InstantEqual<T>(propertyType, vm.UserTimeZone, value.ToString(), vm.ColumnName);

        if (propertyType.IsPhoneNumber())
            return ExpressionsExtension.PhoneNumberEqual<T>(vm.ColumnName, value);

        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(vm.ColumnName);

        UnaryExpression unaryExpression;

        if (propertyType.IsEnum())
        {
            unaryExpression = Expression.ConvertChecked(Expression.Constant(Convert.ToInt32(Enum.Parse(propertyType, value.ToString()))), propertyType);
        }
        else if (propertyType.IsBooleanType())
        {
            unaryExpression = Expression.ConvertChecked(Expression.Constant(value), propertyType);
        }
        else if (propertyType != typeof(string))
        {
            var converter = TypeDescriptor.GetConverter(propertyType);
            if (converter != null && converter.IsValid(value.ToString()))
            {
                value = converter.ConvertFrom(value.ToString());
                unaryExpression = Expression.ConvertChecked(Expression.Constant(value), propertyType);
            }
            else
            {
                return null;
            }
        }
        else
        {
            if (vm.ColumnName != nameof(IBaseEntityWithNumberId.Id))
            {
                var stringToLowerMethod = typeof(string).GetMethod(nameof(string.ToLower), Type.EmptyTypes);
                expression = Expression.Call(expression, stringToLowerMethod);
                unaryExpression = Expression.ConvertChecked(Expression.Constant(value.ToString().ToLower()), propertyType);
            }
            else
            {
                unaryExpression = Expression.ConvertChecked(Expression.Constant(value), propertyType);
            }
        }


        return Expression.Lambda<Func<T, bool>>(Expression.Equal(expression, unaryExpression), pe);
    }

    public override bool IsTypeSupported(ObjectFilterRule rule) => !rule.Type.IsGenericList() && (rule.Type.IsPhoneNumber() || rule.Type.IsDate() || rule.Type.IsInstant() || Type.GetTypeCode(rule.Type) != TypeCode.Object);
}

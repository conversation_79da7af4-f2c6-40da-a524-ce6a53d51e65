﻿using System.Net.Http.Json;
using System.Text;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.SerializersAndConverters;
using LendQube.Infrastructure.Core.SerializersAndConverters.SnakeCaseLowerSerializer;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.ExternalApi.AppSettings;
using LendQube.Infrastructure.ExternalApi.Base;
using LendQube.Infrastructure.ExternalApi.ViewModels;
using Microsoft.Extensions.Caching.Distributed;

namespace LendQube.Infrastructure.ExternalApi.Acquired;

internal sealed class AcquiredExternalApiService(HttpClient httpClient, ExternalApiConfig config, ILogManager<AcquiredExternalApiService> logger, IDistributedCache cache) : BaseExternalApi<AcquiredExternalApiService>(httpClient, logger), IAcquiredExternalApiService
{
    private const string AccessToken = "acquiredaccesstoken";
    private readonly HttpClient httpClient = httpClient;

    public Task<ExternalApiResult<AcquiredCreateCustomerResponseVM>> CreateCustomer(AcquiredCustomerRequestVM requestBody, CancellationToken ct)
        => ProcessRequest<AcquiredCreateCustomerResponseVM>(HttpMethod.Post, "customers", ct, requestBody);

    public Task<ExternalApiResult<AcquiredGeneratePaymentLinkResponseVM>> GeneratePaymentLinkId(AcquiredGenerateLinkRequestVM requestBody, CancellationToken ct)
        => ProcessRequest<AcquiredGeneratePaymentLinkResponseVM>(HttpMethod.Post, "payment-links", ct, requestBody);

    public Task<ExternalApiResult<AcquiredTransactionResponseVM>> GetTransaction(string orderId, CancellationToken ct)
        => ProcessRequest<AcquiredTransactionResponseVM>(HttpMethod.Get, $"transactions?order_id={orderId}", ct);

    public Task<ExternalApiResult<AcquiredChargeCardResponseVM>> ChargeCard(AcquiredChargeCardRequestVM requestBody, CancellationToken ct)
        => ProcessRequest<AcquiredChargeCardResponseVM>(HttpMethod.Post, "payments/recurring", ct, requestBody);

    private async Task<string> Authenticate(CancellationToken ct)
    {
        var token = await cache.GetStringAsync(AccessToken, ct);
        if (!string.IsNullOrEmpty(token))
        {
            return token;
        }

        var requestBody = new AcquiredLoginRequestVM
        {
            AppId = config.Acquired.AppId,
            AppKey = config.Acquired.AppKey,
        };

        using var request = new HttpRequestMessage(HttpMethod.Post, "login")
        {
            Content = new StringContent(JsonSerializer.ToJsonString(requestBody), Encoding.UTF8, HttpHeaderHelper.JsonHeader)
        };

        using var responseMessage = await httpClient.SendAsync(request, ct);
        var response = await responseMessage.Content.ReadFromJsonAsync<AcquiredLoginResponseVM>(SerializerOptions, ct);

        await cache.SetStringAsync(AccessToken, $"{response.TokenType} {response.AccessToken}", new DistributedCacheEntryOptions { AbsoluteExpirationRelativeToNow = TimeSpan.FromSeconds(response.ExpiresIn) }, ct);
        return $"{response.TokenType} {response.AccessToken}";
    }

    protected async override Task AddRequestAuthentication(HttpRequestMessage request, CancellationToken ct)
    {
        var token = await Authenticate(ct);
        request.Headers.Add("Authorization", token);
    }

    protected override Task ResetAccessToken(CancellationToken ct) => cache.RemoveAsync(AccessToken, ct);

    protected override System.Text.Json.JsonSerializerOptions SerializerOptions => JsonOptions.SnakeCaseLowerAndEnumAsStringOptions;
}

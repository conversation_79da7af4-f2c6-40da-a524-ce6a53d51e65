﻿using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Specification;

namespace LendQube.Infrastructure.Core.Database.GenericSpecification;

public sealed class GenericVMSpecificationService<T, TVM>(GenericEntityCrudVMService<T, TVM> crudService) : BaseSpecification<T> where T : class, IBaseEntityForRelationalDb where TVM : class
{
    public GenericEntityCrudVMService<T, TVM> CrudService { get; } = crudService;
}

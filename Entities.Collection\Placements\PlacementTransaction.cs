﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Placements;

public class PlacementTransaction : BaseEntityWithIdentityId<PlacementTransaction>
{
    [DbGuid, Required, RemoveColumn]
    public string ProfileId { get; set; }
    [RemoveColumn]
    public long PlacementId { get; set; }
    public virtual Placement Placement { get; set; }
    [StringLength(EntityConstants.DEFAULT_ID_FIELD_LENGTH)]
    public string TransactionId { get; set; }
    public string PaymentMethod { get; set; }
    public PaymentProvider PaymentProvider { get; set; }
    public decimal AmountPaid { get; set; }
    public string PaymentType { get; set; }

    public override void Configure(EntityTypeBuilder<PlacementTransaction> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.TransactionId);
    }
}

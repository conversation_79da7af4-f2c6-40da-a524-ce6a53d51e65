﻿using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Core.FileManagement;

public interface IFileManagementService
{
    string GenerateUrlFileName(string name);
    string GenerateFileName(string name, string uploadedFileName);
    string GenerateFileNameWithExtension(string name, string extension = "jpg");

    Task<List<string>> GetAllFilesInBlob(string containerName, string name, CancellationToken ct);

    Task DeleteBlobData(string containerName, string fileName, string blobName = null, CancellationToken ct = default);
    Task DeleteFile(string name, CancellationToken ct);

    Task<Result<string>> SaveBase64(string containerName, string storagePath, string file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false, bool validateNotEdited = false, long maxFileSize = 0);
    Task<string> SaveBase64WithTimeStampValidation(string containerName, string storagePath, string file, TimeSpan validityPeriod, string[] validExtensions, CancellationToken ct);

    Task<Result<string>> SaveRadzenFile(string containerName, string storagePath, Radzen.FileInfo file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false, bool validateNotEdited = false, long maxFileSize = 0);
    Task<Result<string>> SaveBrowserFile(string containerName, string storagePath, IBrowserFile file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false, bool validateNotEdited = false, long maxFileSize = 0);

    Task<Result<string>> SaveBytes(string containerName, string storagePath, byte[] file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false);
    Task<string> SaveBytes(string containerName, string fileName, byte[] file, CancellationToken ct, bool skipCreateCheck = false);

    Task<string> SaveStream(string containerName, string fileName, Stream file, CancellationToken ct, bool skipCreateCheck = false);

    Task<Result<string>> SaveFormFile(string service, string name, IFormFile file, CancellationToken ct);

    Task<string> SaveExcel(ExcelPackage package, string containerName, string fileName, CancellationToken ct);
}
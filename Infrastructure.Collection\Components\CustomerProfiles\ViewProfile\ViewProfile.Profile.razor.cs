﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Components.Timeline;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericSpecification;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private ActivityTimeline<CustomerActivity> activityTimeline;
    private DataTable<CustomerTransaction> transactionsTable;

    private ValueTask<TypedBasePageList<CustomerActivity>> LoadActivity(DataFilterAndPage filterAndPage, GenericSpecificationService<CustomerActivity> service, CancellationToken ct)
    {
        service.PrimaryCriteria = x => x.ProfileId == Data.Id;
        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            service.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Title, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Activity, filterAndPage.TextFilter) || EF.Functions.ILike(x.CreatedByUser, filterAndPage.TextFilter));
        }

        return service.CrudService.GetTypeBasedPagedData(service, filterAndPage, ct: ct);
    }

    private ValueTask<TypedBasePageList<CustomerTransaction>> LoadTxns(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerTransaction>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Profile.AccountId, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.FirstName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.LastName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.Email, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.PhoneNumber.Number, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Profile.MobileNumber.Number, filterAndPage.TextFilter));
        }

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, ct);
    }

    private async Task ResetOtp()
    {
        CloseMessage();
        _ = await CrudService.Db.DeleteAndSaveWithFilterAsync<CustomerOneTimeCode>(x => x.ProfileId == Data.Id, Cancel);
        TableMessage.Success("Customer OTP reset successfully");
        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "OTP Reset", Activity = "OTP reset successfully" });
        await uow.SaveAsync(Cancel);
        await activityTimeline.Refresh();
    }

    private async Task ClearIAndE()
    {
        CloseMessage();
        _ = await CrudService.Db.DeleteAndSaveWithFilterAsync<CustomerIncomeAndExpenditure>(x => x.ProfileId == Data.Id, Cancel);
        TableMessage.Success("Customer income and expenditure cleared successfully");
        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Income And Expenditure", Activity = "I&E reset successfully" });
        await uow.SaveAsync(Cancel);
        await activityTimeline.Refresh();
    }
}

﻿@page "/messaging/templates"
@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.FileManagement
@using LendQube.Infrastructure.Core.Helpers.Utils
@using LendQube.Infrastructure.Core.Messaging.Configuration
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using Radzen.Blazor
@using System.Text

@inherits GenericCrudVMTable<MessagingTemplate, MessagingTemplateVM>
@inject IFileManagementService fileService
@inject HttpClient httpClient

@attribute [Authorize(Policy = MessagingNavigation.MessagingTemplateIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddLocalModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="modal-dialog-large width-70vw">
    <BodyContent>
        @FormContent(context)
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditLocalModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="modal-dialog-large width-70vw">
    <BodyContent>
        @FormContent(context)
  </BodyContent>
</ModalEditComponent>

<ModalComponent Policy="@MessagingNavigation.MessagingTemplateIndexPermission" ModalId="@ViewTemplateModalName" ModalCss="modal-dialog-large width-70vw"
                 Title=@($"View {FormBaseTitle}: {SelectedTemplate?.Name}")>
    <BodyContent>
        <div class="form-row">
            <div class="grid-col-3">
                <span>
                    <strong>Description: </strong>
                    @SelectedTemplate?.Description
                </span>
                <span>
                    <strong>Types: </strong>
                    @string.Join(", ", SelectedTemplate?.Types)
                </span>

                <span>
                    <strong>Template Keys: </strong>
                    @string.Join(", ", SelectedTemplate?.Keys)
                </span>
            </div>
        </div>

        @if (SelectedTemplate != null)
        {
            if (SelectedTemplate.Types.HasFlag(MessageConfigTemplateType.Text))
            {
                <div class="form-row">
                    <span>
                        <strong>Text Template: </strong>
                        <code>@SelectedTemplate.TextTemplate</code>
                    </span>
                </div>
            }

            if (SelectedTemplate.Types.HasFlag(MessageConfigTemplateType.Html))
            {
                <RadzenHtmlEditor @bind-Value=@SelectedTemplate.HtmlTemplate style="height: 50vh" ShowToolbar="false" Disabled="true" />
            }
        }
    </BodyContent>
    <FooterContent>
        <button class="btn btn--default" type="button" data-bs-dismiss="modal">Close</button>
    </FooterContent>

</ModalComponent>

<RadzenDialog />

@code
{

    [SupplyParameterFromForm]
    protected MessagingTemplateVM AddLocalModel { get; set; }

    [SupplyParameterFromForm]
    protected MessagingTemplateVM EditLocalModel { get; set; }

    private string ViewTemplateModalName => "ViewTemplateModal";

    private string templateDirectory = "generaltemplates";
    private MessagingTemplateVM SelectedTemplate { get; set; } = new();

    private RenderFragment<MessagingTemplateVM> FormContent => context => @<div>
        <div class="form-row">
            <div class="alert info hasFlex">
                <div class="al-flex">
                    <div class="al-content">
                        <span class="al__title" role="alert">
                            Tags can be used to set parts that should be replaced by the message using the template. Create tags with names inside curly braces
                            eg. <code>{body}</code>.  The system <code>{body}</code> tag indicates this is a container template. Please use the same keys for both text and html templates.
                            System keys are <code>@string.Join(", ", LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumNames<MessageTemplateSystemTags>().Select(x => $"{{{x}}}"))</code>.
                            These keys will be substituted automatically. Please use lower case characters for keys
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid-col-3">
            <div class="form-row">
                <label class="form-label" for="Name">Name</label>
                <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
                <ValidationMessage For="() => context.Name" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="Description">Description</label>
                <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
                <ValidationMessage For="() => context.Description" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="Types">Types</label>
                <RadzenDropDown @bind-Value=@context.TypesList Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<MessageConfigTemplateType>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="TypesList" Multiple=true AllowClear=true Placeholder="Select template types" Chips=true class="form-input" />
                <ValidationMessage For="() => context.TypesList" class="text-danger" />
            </div>
        </div>
        @if (context.TypesList != null)
        {
            if (context.TypesList.Contains(MessageConfigTemplateType.Text))
            {
                <div class="form-row">
                    <label class="form-label" for="TextTemplate">Text Template</label>
                    <PagedTextArea Model="context" Property="context => context.TextTemplate" />
                    <ValidationMessage For="() => context.TextTemplate" class="text-danger" />
                </div>
            }

            if (context.TypesList.Contains(MessageConfigTemplateType.Html))
            {
                <div class="form-row">
                    <label class="form-label" for="HtmlTemplate">Html Template</label>
                    <ValidationMessage For="() => context.HtmlTemplate" class="text-danger" />
                    <RadzenHtmlEditor @bind-Value=@context.HtmlTemplate style="height: 50vh; padding-top: 5px;" UploadUrl="upload/messagetemplates">
                        <RadzenHtmlEditorRedo />
                        <RadzenHtmlEditorSeparator />
                        <RadzenHtmlEditorBold />
                        <RadzenHtmlEditorItalic />
                        <RadzenHtmlEditorUnderline />
                        <RadzenHtmlEditorStrikeThrough />
                        <RadzenHtmlEditorSeparator />
                        <RadzenHtmlEditorAlignLeft />
                        <RadzenHtmlEditorAlignCenter />
                        <RadzenHtmlEditorAlignRight />
                        <RadzenHtmlEditorJustify />
                        <RadzenHtmlEditorSeparator />
                        <RadzenHtmlEditorIndent />
                        <RadzenHtmlEditorOutdent />
                        <RadzenHtmlEditorUnorderedList />
                        <RadzenHtmlEditorOrderedList />
                        <RadzenHtmlEditorSeparator />
                        <RadzenHtmlEditorColor />
                        <RadzenHtmlEditorBackground />
                        <RadzenHtmlEditorRemoveFormat />
                        <RadzenHtmlEditorSeparator />
                        <RadzenHtmlEditorSubscript />
                        <RadzenHtmlEditorSuperscript />
                        <RadzenHtmlEditorSeparator />
                        <RadzenHtmlEditorLink />
                        <RadzenHtmlEditorUnlink />
                        <RadzenHtmlEditorImage />
                        <RadzenHtmlEditorFontName>
                            <RadzenHtmlEditorFontNameItem Text="HK Grotesk" Value="'HK Grotesk'" />
                            <RadzenHtmlEditorFontNameItem Text="Segoe UI" Value="'Segoe UI'" />
                            <RadzenHtmlEditorFontNameItem Text="Roboto" Value="'Roboto'" />
                            <RadzenHtmlEditorFontNameItem Text="Oxygen-Sans" Value="'Oxygen-Sans'" />
                            <RadzenHtmlEditorFontNameItem Text="Ubuntu" Value="'Ubuntu'" />
                            <RadzenHtmlEditorFontNameItem Text="Cantarell" Value="'Cantarell'" />
                            <RadzenHtmlEditorFontNameItem Text="Helvetica Neue" Value="'Helvetica Neue'" />
                            <RadzenHtmlEditorFontNameItem Text="Sans Serif" Value="'sans-serif'" />
                        </RadzenHtmlEditorFontName>
                        <RadzenHtmlEditorFontSize />
                        <RadzenHtmlEditorFormatBlock />
                        <RadzenHtmlEditorSeparator />
                        <RadzenHtmlEditorSource />              
                    </RadzenHtmlEditor>
                </div>
            }
        }
    </div>
    ;

    protected override void OnInitialized()
    {
        Title = "Messaging";
        FormBaseTitle = "Template";
        SubTitle = "Messaging Templates";
        CreatePermission = MessagingNavigation.MessagingTemplateIndexPermission;
        EditPermission = MessagingNavigation.MessagingTemplateEditPermission;
        DeletePermission = MessagingNavigation.MessagingTemplateDeletePermission;
        QuerySelector = MessagingTemplateVM.Mapping;
    }


    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            AddLocalModel = new();
            EditLocalModel = new();

            await base.OnInitializedAsync();

            AddRowButton(MessagingNavigation.MessagingTemplateIndexPermission, new RowActionButton("View", Icon: "eye", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                SelectedTemplate = await LoadTemplateRecord(row as MessagingTemplateVM, table.Cancel);
                table.Loading = false;
                await JSRuntime.OpenModal(ViewTemplateModalName, Cancel);
                StateHasChanged();
            }));


            AddRowButton(EditPermission, new RowActionButton("Disable", ButtonClass: "btn--danger", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                var template = row as MessagingTemplateVM;
                var result = await Service.CrudService.UpdateWithFilter(x => x.Id == template.Id && !x.DisabledOn.HasValue, x => x.SetProperty(y => y.DisabledOn, clock.GetCurrentInstant()), Cancel);

                await table.Refresh();
                if (result)
                    TableMessage.Success($"Template with name {template.Name} disabled successfully");
                else
                    TableMessage.Error($"Disabling template with name {template.Name} failed");
                StateHasChanged();
            },
            ShowCondition: (object row) => !(row as MessagingTemplateVM).IsDisabled));


            AddRowButton(EditPermission, new RowActionButton("Enable", ButtonClass: "btn--success", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                var template = row as MessagingTemplateVM;
                var result = await Service.CrudService.UpdateWithFilter(x => x.Id == template.Id && x.DisabledOn.HasValue, x => x.SetProperty(y => y.DisabledOn, (Instant?) null), Cancel);

                await table.Refresh();
                if (result)
                    TableMessage.Success($"Template with name {template.Name} enabled successfully");
                else
                    TableMessage.Error($"Enabling template with name {template.Name} failed");
                StateHasChanged();
            },
            ShowCondition: (object row) => (row as MessagingTemplateVM).IsDisabled));
        }
    }

    protected override void StartAdd() => AddLocalModel = new();

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Description, filterAndPage.TextFilter);
    }

    protected override ValueTask SubmitAdd() => BaseSaveAdd(async () =>
    {
        var data = AddLocalModel.Get();
        if (data.Types.HasFlag(MessageConfigTemplateType.Text))
        {
            if (string.IsNullOrWhiteSpace(AddLocalModel.TextTemplate))
            {
                CustomMessage = "Text template is required";
                return false;
            }

            data.Keys = MessageConfigHelper.GetTemplateKeys(AddLocalModel.TextTemplate);
            data.IsContainer = data.Keys.Contains("body");
        }

        if (data.Types.HasFlag(MessageConfigTemplateType.Html))
        {
            if (string.IsNullOrWhiteSpace(AddLocalModel.HtmlTemplate))
            {
                CustomMessage = "Html template is required";
                return false;
            }

            var htmlKeys = MessageConfigHelper.GetTemplateKeys(AddLocalModel.HtmlTemplate);

            if (!CheckKeyEquality(data.Keys, htmlKeys))
                return false;

            data.Keys = htmlKeys;

            data.IsContainer = data.Keys.Contains("body");

            data.HtmlTemplate = await fileService.SaveBytes(MessageConfigHelper.FileService, $"{templateDirectory}/{SecurityDriven.FastGuid.NewGuid()}.html", Encoding.UTF8.GetBytes(AddLocalModel.HtmlTemplate), Cancel);
        }

        return await Service.CrudService.New(data, Cancel);
    }, 
    () =>
    {
        AddLocalModel = new();
        return table.Refresh();

    });

    protected override ValueTask StartEdit(MessagingTemplateVM editData, CancellationToken ct) => BaseEdit(async () =>
    {
        table.Loading = true;
        EditLocalModel = await LoadTemplateRecord(editData, ct);
        table.Loading = false;

    }, ct);

    protected override ValueTask SubmitEdit() => BaseSaveEdit(async () =>
    {
        var data = EditLocalModel.Get();
        if (data.Types.HasFlag(MessageConfigTemplateType.Text))
        {
            if (string.IsNullOrWhiteSpace(EditLocalModel.TextTemplate))
            {
                CustomMessage = "Text template is required";
                return false;
            }

            data.Keys = MessageConfigHelper.GetTemplateKeys(EditLocalModel.TextTemplate);
            data.IsContainer = data.Keys.Contains("body");
        }

        if (data.Types.HasFlag(MessageConfigTemplateType.Html))
        {
            if (string.IsNullOrWhiteSpace(EditLocalModel.HtmlTemplate))
            {
                CustomMessage = "Html template is required";
                return false;
            }

            var htmlKeys = MessageConfigHelper.GetTemplateKeys(EditLocalModel.HtmlTemplate);

            if (!CheckKeyEquality(data.Keys, htmlKeys))
                return false;

            data.Keys = htmlKeys;
            data.IsContainer = data.Keys.Contains("body");

            data.HtmlTemplate = await fileService.SaveBytes(MessageConfigHelper.FileService, $"{templateDirectory}/{SecurityDriven.FastGuid.NewGuid()}.html", Encoding.UTF8.GetBytes(EditLocalModel.HtmlTemplate), Cancel);
        }

        return await Service.CrudService.Update(data, Cancel);

    }, 
    () =>
    {
        EditLocalModel = new();
        return table.Refresh();

    });

    protected override ValueTask<bool> SubmitDelete(MessagingTemplateVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);

    private async Task<MessagingTemplateVM> LoadTemplateRecord(MessagingTemplateVM editData, CancellationToken ct)
    {
        var data = await Service.CrudService.GetVM(Query<MessagingTemplate, MessagingTemplateVM>.Select(MessagingTemplateVM.EditMapping).Where(x => x.Id == editData.Id), ct);
        data.TypesList = data.Types.FlagsToList<MessageConfigTemplateType>();
        if (data.Types.HasFlag(MessageConfigTemplateType.Html))
        {
            data.HtmlTemplate = await httpClient.ReadPhysicalFileAsString(data.HtmlTemplate, Cancel);
        }

        return data;
    }

    private bool CheckKeyEquality(List<string> textKeys, List<string> htmlKeys)
    {
        if (!MessageConfigHelper.CheckKeyEquality(textKeys, htmlKeys))
        {
            CustomMessage = $"Your text and html template do not have the same template keys. Text Keys: {string.Join(", ", textKeys)}. Html Keys: {string.Join(", ", htmlKeys)}";
            return false;
        }
        return true;
    }
}

/* LendQube Customer Portal Design System */
/* This resolves Blocker #5: CSS Styling and Design System Issues */
/* Font definitions are loaded from main.css via Assets service */

/* Design Tokens */
:root {
    /* Core Brand Colors */
    --core-primary: #350f45;        /* Dark purple */
    --core-light-accent: #7f4896;   /* Light purple */
    --core-dark-accent: #2d0b3b;    /* Darker purple */
    
    /* Neutral Colors */
    --neutral-white: #ffffff;
    --neutral-light-gray: #f8f9fa;
    --neutral-gray: #6c757d;
    --neutral-dark-gray: #343a40;
    --neutral-black: #000000;
    
    /* Status Colors */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    
    /* Typography */
    --font-stack: "MatterSQ", -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, Oxygen-San<PERSON>, Ubu<PERSON><PERSON>, Cantarell, "Helvetica Neue", sans-serif;
    --font-size-xs: 1rem;
    --font-size-sm: 1.5rem;
    --font-size-base: 1.5rem;
    --font-size-lg: 1.75rem;
    --font-size-xl: 2rem;
    --font-size-2xl: 2.25rem;
    --font-size-3xl: 2.5rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2.9rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --border-radius-sm: 0.25rem;
    --border-radius-md: 0.5rem;
    --border-radius-lg: 0.75rem;
    --border-radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Base Styles */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-stack);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--neutral-dark-gray);
    background-color: var(--neutral-light-gray);
    margin: 0;
    padding: 0;
}

/* Headings */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-stack);
    color: var(--core-primary);
    font-weight: 600;
    line-height: 1.2;
}

/* Paragraphs */
p {
    font-family: var(--font-stack);
    font-weight: 400;
    line-height: 1.43;
    color: var(--neutral-dark-gray);
}

/* Login Page Styles */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--core-primary) 0%, var(--core-light-accent) 100%);
    padding: var(--spacing-md);
}

.login-card {
    background: var(--neutral-white);
    border-radius: var(--border-radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-2xl);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-header {
    margin-bottom: var(--spacing-xl);
}

.login-header .logo {
    width: 120px;
    height: auto;
    margin-bottom: var(--spacing-md);
}

.login-header h1 {
    color: var(--core-primary);
    font-size: 2.8rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-sm) 0;
}

.login-header p {
    color: var(--neutral-gray);
    font-size: var(--font-size-base);
    margin: 0;
}

/* Form Styles */
.form-group {
    margin-bottom: var(--spacing-lg);
    text-align: left;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--neutral-dark-gray);
    margin-bottom: var(--spacing-xs);
    font-size: var(--font-size-base);
}

.form-control {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid #e9ecef;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-base);
    font-family: var(--font-stack);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    outline: none;
    border-color: var(--core-light-accent);
    box-shadow: 0 0 0 0.2rem rgba(127, 72, 150, 0.25);
}

.form-control:disabled {
    background-color: #f8f9fa;
    opacity: 0.6;
}

/* Button Styles - This resolves Blocker #8: Button Text Color Styling Issue */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-xl);
    border: none;
    border-radius: var(--border-radius-md);
    font-size: var(--font-size-lg);
    font-family: var(--font-stack);
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    min-height: 44px;
    gap: var(--spacing-xs);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background-color: var(--core-primary);
    color: var(--neutral-white) !important; /* Explicit white text - Resolves Blocker #8 */
}

.btn-primary:hover:not(:disabled) {
    background-color: var(--core-dark-accent);
    color: var(--neutral-white) !important; /* Maintain white text on hover */
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-primary span {
    color: var(--neutral-white) !important; /* Ensure span elements are white */
}

.btn-secondary {
    background-color: var(--neutral-gray);
    color: var(--neutral-white) !important;
}

.btn-secondary:hover:not(:disabled) {
    background-color: var(--neutral-dark-gray);
    color: var(--neutral-white) !important;
}

/* Make Payment Button - Special styling as requested */
.make-payment-btn {
    background-color: var(--core-primary);
    color: var(--neutral-white) !important;
    font-weight: 700;
    width: 100%;
}

.make-payment-btn span {
    color: var(--neutral-white) !important;
    font-size: 0.8em;
}

.make-payment-btn:hover:not(:disabled) {
    background-color: var(--core-dark-accent);
    color: var(--neutral-white) !important;
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

/* Manage Cards Button - Same styling pattern as Make Payment */
.manage-cards-btn span {
    color: var(--neutral-white) !important;
    font-size: 0.8em;
}

/* Logout Button - Header styling */
.logout-btn {
    background-color: transparent;
    border: 2px solid var(--neutral-gray);
    color: var(--neutral-dark-gray);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    transition: all 0.15s ease-in-out;
}

.logout-btn:hover:not(:disabled) {
    background-color: var(--neutral-gray);
    color: var(--neutral-white);
    border-color: var(--neutral-gray);
}

.logout-btn span {
    font-size: 0.9em;
}

/* Alert Styles */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--border-radius-md);
    margin-bottom: var(--spacing-md);
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}



/* Dashboard Styles */
.dashboard-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.dashboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2xl);
    background: var(--neutral-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.customer-section {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
}

.customer-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--core-primary), var(--core-light-accent));
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--neutral-white);
    font-size: var(--font-size-xl);
    font-weight: 700;
    margin-right: var(--spacing-lg);
}

.customer-info h1 {
    color: var(--core-primary);
    font-size: var(--font-size-3xl);
    margin: 0 0 var(--spacing-xs) 0;
}

.customer-id {
    color: var(--neutral-gray);
    font-size: var(--font-size-base);
    margin: 0;
}

.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
}

.info-card {
    background: var(--neutral-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.info-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.info-card h3 {
    color: var(--core-primary);
    font-size: var(--font-size-lg);
    margin: 0 0 var(--spacing-md) 0;
    font-weight: 600;
}

.balance-amount {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--core-primary);
    margin-bottom: var(--spacing-xs);
}

.balance-status {
    color: var(--neutral-gray);
    font-size: var(--font-size-base);
    margin: 0;
}

.contact-details p {
    margin: 0 0 var(--spacing-sm) 0;
    font-size: var(--font-size-sm);
}

.payment-methods-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.payment-method-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--neutral-light-gray);
    border-radius: var(--border-radius-md);
}

.card-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.card-brand {
    font-weight: 600;
    color: var(--core-primary);
    font-size: var(--font-size-sm);
}

.card-number {
    color: var(--neutral-gray);
    font-size: var(--font-size-sm);
}

.card-status {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
}

.card-status.active {
    background-color: var(--success-color);
    color: var(--neutral-white);
}

.card-status.inactive {
    background-color: var(--neutral-gray);
    color: var(--neutral-white);
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}



/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-container {
        padding: var(--spacing-md);
    }

    .dashboard-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .customer-section {
        flex-direction: column;
    }

    .customer-avatar {
        margin-right: 0;
        margin-bottom: var(--spacing-md);
    }

    .header-actions {
        margin-top: var(--spacing-md);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }

    .login-card {
        margin: var(--spacing-md);
        padding: var(--spacing-xl);
    }

    /* Payment History Responsive */
    .payment-history-container {
        padding: var(--spacing-md);
    }

    .payment-history-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .summary-grid {
        grid-template-columns: 1fr;
    }

    .table-responsive {
        font-size: var(--font-size-sm);
    }

    .date-cell {
        min-width: 100px;
    }

    /* Manage Profile Responsive */
    .manage-profile-container {
        padding: var(--spacing-md);
    }

    .manage-profile-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .info-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
    }
}

/* Payment History Styles */
.payment-history-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.payment-history-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2xl);
    background: var(--neutral-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.header-content h1 {
    color: var(--core-primary);
    font-size: var(--font-size-3xl);
    margin: 0 0 var(--spacing-xs) 0;
}

.header-subtitle {
    color: var(--neutral-gray);
    font-size: var(--font-size-base);
    margin: 0;
}

.back-btn {
    background-color: transparent;
    border: 2px solid var(--neutral-gray);
    color: var(--neutral-dark-gray);
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-sm);
    transition: all 0.15s ease-in-out;
}

.back-btn:hover:not(:disabled) {
    background-color: var(--neutral-gray);
    color: var(--neutral-white);
    border-color: var(--neutral-gray);
}

.payment-history-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.summary-card {
    background: var(--neutral-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--spacing-lg);
    margin-top: var(--spacing-md);
}

.summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: var(--spacing-md);
    background: var(--neutral-light-gray);
    border-radius: var(--border-radius-md);
}

.summary-label {
    color: var(--neutral-gray);
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-xs);
}

.summary-value {
    color: var(--core-primary);
    font-size: var(--font-size-xl);
    font-weight: 700;
}

.table-card {
    background: var(--neutral-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.date-cell {
    display: flex;
    flex-direction: column;
}

.date-main {
    font-weight: 600;
    color: var(--neutral-dark-gray);
}

.date-time {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

.provider-badge {
    background: var(--neutral-light-gray);
    color: var(--neutral-dark-gray);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 500;
}

.no-payments {
    text-align: center;
    padding: var(--spacing-3xl) var(--spacing-xl);
}

.no-payments-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
}

.no-payments h4 {
    color: var(--core-primary);
    margin-bottom: var(--spacing-md);
}

.no-payments p {
    color: var(--neutral-gray);
    margin-bottom: var(--spacing-lg);
}

/* Manage Profile Styles */
.manage-profile-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.manage-profile-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-2xl);
    background: var(--neutral-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.manage-profile-content {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

.coming-soon-card {
    background: var(--neutral-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    text-align: center;
}

.coming-soon-content {
    max-width: 600px;
    margin: 0 auto;
}

.coming-soon-icon {
    font-size: 4rem;
    margin-bottom: var(--spacing-lg);
}

.coming-soon-card h3 {
    color: var(--core-primary);
    margin-bottom: var(--spacing-md);
}

.coming-soon-card p {
    color: var(--neutral-gray);
    margin-bottom: var(--spacing-xl);
}

.current-info {
    background: var(--neutral-light-gray);
    border-radius: var(--border-radius-md);
    padding: var(--spacing-lg);
    margin: var(--spacing-xl) 0;
    text-align: left;
}

.current-info h4 {
    color: var(--core-primary);
    margin-bottom: var(--spacing-md);
    text-align: center;
}

.info-grid {
    display: grid;
    gap: var(--spacing-md);
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-sm) 0;
    border-bottom: 1px solid var(--neutral-gray);
}

.info-item:last-child {
    border-bottom: none;
}

.info-label {
    font-weight: 600;
    color: var(--neutral-dark-gray);
}

.info-value {
    color: var(--neutral-gray);
}

.contact-support {
    margin-top: var(--spacing-xl);
}

.contact-support h4 {
    color: var(--core-primary);
    margin-bottom: var(--spacing-md);
}

.contact-support p {
    margin-bottom: var(--spacing-lg);
}

.support-actions {
    display: flex;
    justify-content: center;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.form-control:focus {
    outline: 2px solid var(--core-light-accent);
    outline-offset: 2px;
}

/* Payment Page Styles */
.payment-container {
    max-width: 800px;
    margin: 0 auto;
    padding: var(--spacing-xl);
}

.payment-header {
    text-align: center;
    margin-bottom: var(--spacing-2xl);
    background: var(--neutral-white);
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
}

.payment-header h1 {
    color: var(--core-primary);
    font-size: var(--font-size-2xl);
    margin: 0 0 var(--spacing-sm) 0;
}

.payment-header p {
    color: var(--neutral-gray);
    margin: 0;
}

.payment-form-container {
    background: var(--neutral-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
    margin-bottom: var(--spacing-xl);
}

.form-section {
    margin-bottom: var(--spacing-xl);
}

.form-section h3 {
    color: var(--core-primary);
    font-size: var(--font-size-lg);
    margin: 0 0 var(--spacing-lg) 0;
    padding-bottom: var(--spacing-sm);
}

.form-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-start;
    flex-wrap: wrap;
}

.schedule-summary {
    background: var(--neutral-white);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-sm);
}

.schedule-summary h3 {
    color: var(--core-primary);
    font-size: var(--font-size-lg);
    margin: 0 0 var(--spacing-lg) 0;
}

.schedule-list {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.schedule-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md);
    background: var(--neutral-light-gray);
    border-radius: var(--border-radius-md);
}

.schedule-info strong {
    color: var(--core-primary);
    font-size: var(--font-size-base);
}

.schedule-info p {
    margin: var(--spacing-xs) 0 0 0;
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

.schedule-status {
    flex-shrink: 0;
}

.status-badge {
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    font-size: var(--font-size-sm);
    font-weight: 600;
    text-transform: uppercase;
}

.status-badge.active {
    background-color: var(--success-color);
    color: var(--neutral-white);
}

.status-badge.inactive {
    background-color: var(--neutral-gray);
    color: var(--neutral-white);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .btn-primary {
        border: 2px solid var(--neutral-white);
    }

    .form-control {
        border-width: 2px;
    }
}

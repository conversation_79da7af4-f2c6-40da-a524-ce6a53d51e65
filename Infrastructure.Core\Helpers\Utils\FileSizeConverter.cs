﻿namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class FileSizeConverter
{
    private const long KB = 1024;
    private const long MB = KB * 1024;
    private const long GB = MB * 1024;

    public static long ToKB<T>(this T size) where T : IComparable => Convert.ToInt64(size) * KB;
    public static long ToMB<T>(this T size) where T : IComparable => Convert.ToInt64(size) * MB;
    public static long ToGB<T>(this T size) where T : IComparable => Convert.ToInt64(size) * GB;

    public static string ToFileSize<T>(this T fileLength) where T : IComparable
    {
        long fileLengthInBytes = Convert.ToInt64(fileLength);

        if (fileLengthInBytes < 0)
        {
            throw new ArgumentException("File size must be a non-negative number.");
        }


        if (fileLengthInBytes >= GB)
        {
            double sizeInGB = (double)fileLengthInBytes / GB;
            return $"{sizeInGB:F2} GB";
        }
        else if (fileLengthInBytes >= MB)
        {
            double sizeInMB = (double)fileLengthInBytes / MB;
            return $"{sizeInMB:F2} MB";
        }
        else if (fileLengthInBytes >= KB)
        {
            double sizeInKB = (double)fileLengthInBytes / KB;
            return $"{sizeInKB:F2} KB";
        }
        else
        {
            return $"{fileLengthInBytes} Bytes";
        }
    }
}

﻿using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Telemetry;

namespace LendQube.Infrastructure.Collection.Discounts;

internal sealed class AutoDiscountHandler(IUnitofWork uow, ILogManager<AutoDiscountHandler> logger) : IHandleTriggerNotification<AutoDiscountConfig>
{
    public Task OnStartup(CancellationToken ct) => Task.CompletedTask;

    public ValueTask OnChanged(string id, AutoDiscountConfig oldData, AutoDiscountConfig newData, TriggerChange change, CancellationToken ct) => AutoDiscountService.ApplyDiscount(uow, logger, newData, ct);
}


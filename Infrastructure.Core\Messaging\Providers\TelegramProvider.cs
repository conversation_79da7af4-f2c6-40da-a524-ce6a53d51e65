﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Telemetry;
using TL;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class TelegramProvider : AbstractMessageProvider
{
    public const string Name = "Telegram";
    private readonly DefaultAppConfig config;
    private readonly HttpClient httpClient;
    private readonly ILogManager<TelegramProvider> logger;
    private readonly WTelegram.Client client;

    protected override MessageChannel SupportedChannel => MessageChannel.Telegram;
    public override ProviderConfigVM Config { get; }

    public TelegramProvider(IUnitofWork uow, DefaultAppConfig config, TelegramSessionStore sessionStore, HttpClient httpClient, ILogManager<TelegramProvider> logger) : base(uow)
    {
        this.config = config;
        this.httpClient = httpClient;
        this.logger = logger;

        if (config.Telegram != null)
        {
            client = new WTelegram.Client(what => TelegramConfig(what, this.config), sessionStore);
            Config = MessagingCompiledQueries.GetProviderConfig(uow, Name) ?? new ProviderConfigVM { Disabled = true }; 
        }
        else
        {
            Config = new ProviderConfigVM { Disabled = true };
        }
    }

    internal static string TelegramConfig(string what, DefaultAppConfig config) => what switch
    {
        "api_id" => config.Telegram.ApiId,
        "api_hash" => config.Telegram.ApiHash,
        "phone_number" => config.Telegram.PhoneNumber,
        _ => null,
    };

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        Dictionary<long, MessageStatus> result = [];
        var leadingMessage = messages[0];
        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        try
        {
            _ = await client.LoginUserIfNeeded();
            await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count, CancellationToken = ct }, async (message, ct) =>
            {
                result[message.MessageId] = await SendMessagesToRecipients(message, ct);
            });
        }
        catch (Exception ex)
        {
            var messageIds = string.Join(", ", messages.Select(x => x.MessageId));
            logger.LogError(EventSource.Messaging, EventAction.SendSms, ex, $"Could not ProcessMessage: {messageIds}");
            LogActivity(leadingMessage, MessageStatus.Failed, Name, "Provider failure");
        }

        var status = result.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, result, null, null, ct);
        return status;
    }

    private async Task<MessageStatus> SendMessagesToRecipients(PreparedMessageVM message, CancellationToken ct)
    {
        Dictionary<long, MessageStatus> results = [];
        await Parallel.ForEachAsync(message.Data.Select((x, i) => (Value: x, Index: i)), new ParallelOptions { MaxDegreeOfParallelism = message.Data.Count, CancellationToken = ct }, async (recipient, ct) =>
        {
            var uploadedFiles = await UploadFiles(recipient.Value.Attachments, ct);
            var phoneNumbers = recipient.Value.PhoneNumbers.Select(phone => new InputPhoneContact { phone = phone.ToString() }).ToArray();
            var importedContacts = await client.Contacts_ImportContacts(phoneNumbers);

            foreach (var contact in importedContacts.imported)
            {
                var peer = importedContacts.users[contact.user_id];
                if (uploadedFiles.IsNullOrEmpty())
                {
                    var result = await client.SendMessageAsync(peer, recipient.Value.TextTemplate ?? message.TextTemplate);
                    results[message.MessageId + recipient.Index] = (result?.peer_id != null).ToMessageStatus();
                }
                else
                {
                    var inputMedia = uploadedFiles
                        .AsParallel()
                        .Select(file => new InputMediaUploadedPhoto { file = file })
                        .Cast<InputMedia>()
                        .ToList();

                    var result = await client.SendAlbumAsync(peer, inputMedia, recipient.Value.TextTemplate ?? message.TextTemplate);
                    results[message.MessageId + recipient.Index] = results[message.MessageId + recipient.Index] == MessageStatus.Sent ? MessageStatus.Sent : (result.Length != 0).ToMessageStatus();
                }
            }
        });

        return results.ToMessageStatus();
    }

    private async Task<List<InputFileBase>> UploadFiles(IEnumerable<MessageAttachment> attachments, CancellationToken ct)
    {
        var uploadedFiles = new List<InputFileBase>();

        if (attachments != null)
        {
            foreach (var item in attachments)
            {
                using var stream = await httpClient.ReadPhysicalFileAsStream(item.Url, ct);
                var uploadedFile = await client.UploadFileAsync(stream, item.FileName);
                uploadedFiles.Add(uploadedFile);
            }
        }

        return uploadedFiles;
    }
}

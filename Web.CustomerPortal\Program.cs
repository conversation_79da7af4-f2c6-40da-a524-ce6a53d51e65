using System.IO.Compression;
using LendQube.Infrastructure.Core.DependencyInjection;
using LendQube.Infrastructure.Collection.DependencyInjection;
using Microsoft.AspNetCore.ResponseCompression;
using Web.CustomerPortal.Components;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Use the proper database setup from the main application
// This includes all required services and configurations
builder.AddBaseAdminService()
    .AddDbContext();

// Add Collection services to register Collection entities (including CustomerProfile)
builder.AddCollectionAdminServices();

// Add essential data access services required by ManageScheduleService
builder.Services.AddBaseDIServices()
    .AddAdminServices()
    .AddBackgroundServices();

// Add HttpContextAccessor for HTTP redirect functionality
// This resolves Blocker #1: NavigationManager Exception During Form Submission
builder.Services.AddHttpContextAccessor();

// Add SSL/HTTPS configuration similar to web admin
if (builder.Configuration.GetValue<bool>("EnableSSL"))
{
    builder.Services.AddResponseCompression(options =>
    {
        options.Providers.Add<BrotliCompressionProvider>();
        options.Providers.Add<GzipCompressionProvider>();
        options.EnableForHttps = true;
        options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(["image/svg+xml", "image/png", "image/jpg", "image/jpeg"]);
    });

    builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
    {
        options.Level = CompressionLevel.Optimal;
    });

    builder.Services.Configure<GzipCompressionProviderOptions>(options =>
    {
        options.Level = CompressionLevel.Optimal;
    });

    builder.Services.AddHsts(options =>
    {
        options.Preload = true;
        options.IncludeSubDomains = true;
        options.MaxAge = TimeSpan.FromDays(366);
    });
}

// Add logging for debugging
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.SetMinimumLevel(LogLevel.Debug);
});

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseAntiforgery();

// Add admin app configuration to enable Assets service
app.AddAdminApp(builder);

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.UseBackgroundServices();

app.Run();

﻿# ASP.NET Core
# Build and test ASP.NET Core projects targeting .NET Core.
# Add steps that run tests, create a NuGet package, deploy, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

trigger:
  branches:
    exclude:
    - '*'

pool:
  vmImage: ubuntu-latest

variables:
  buildConfiguration: 'Release'

steps:

- task: UseDotNet@2
  displayName: Use .NET 9 SDK
  inputs:
    packageType: sdk
    version: 9.0.x
    installationPath: $(Agent.ToolsDirectory)/dotnet

- task: DownloadSecureFile@1
  displayName: 'Download appsettings.Production.json'
  inputs:
    secureFile: 'appsettings.Production.json'
    
- task: DownloadSecureFile@1
  displayName: 'Download externalapisettings.Production.json'
  inputs:
    secureFile: 'externalapisettings.Production.json'

- task: CopyFiles@2
  inputs:
    sourceFolder: "$(Agent.TempDirectory)"
    contents: "appsettings.Production.json"
    targetFolder: "$(Agent.BuildDirectory)/s/Infrastructure.Core/AppSettings"
  displayName: "Import appsettings.production.json"

- task: CopyFiles@2
  inputs:
    sourceFolder: "$(Agent.TempDirectory)"
    contents: "externalapisettings.Production.json"
    targetFolder: "$(Agent.BuildDirectory)/s/Infrastructure.ExternalApi/AppSettings"
  displayName: "Import externalapisettings.production.json"

- task: DotNetCoreCLI@2
  displayName: Build Background Service
  inputs:
    command: 'build'
    projects: '**/Web.BackgroundService.csproj'
    arguments: '--configuration $(buildConfiguration)'

- task: DotNetCoreCLI@2
  displayName: Create Web App Package (.zip)
  inputs:
    command: publish
    publishWebProjects: false
    projects: '**/Web.BackgroundService.csproj'
    arguments: '--configuration $(buildConfiguration) --output $(build.artifactstagingdirectory)'
    zipAfterPublish: True

- task: PublishBuildArtifacts@1
  displayName: Publish Artifact
  inputs:
    PathtoPublish: '$(build.artifactstagingdirectory)'

- task: AzureRmWebAppDeployment@4
  inputs:
    ConnectionType: 'AzureRM'
    azureSubscription: 'EthicaProd'
    appType: 'webAppLinux'
    WebAppName: 'lendqubeprodbackground'
    packageForLinux: '$(build.artifactstagingdirectory)/**/*.zip'
    RuntimeStack: 'DOTNETCORE|9.0'
﻿using LendQube.Infrastructure.Core.Database.DataPager;
using Microsoft.AspNetCore.Components;

namespace LendQube.Infrastructure.Core.Components.Table;

public abstract class TableComponentBase<T> : BaseTableComponentBase where T : new()
{
	[SupplyParameterFromForm]
	protected T AddModel { get; set; }

	[SupplyParameterFromForm]
	protected T EditModel { get; set; }

	protected override async Task OnInitializedAsync()
	{
		if (TableDefinition == null)
		{
			AddModel = new();
			EditModel = new();

			await base.OnInitializedAsync();

			AddTopButton(CreatePermission, new TopActionButton(ModalName: AddModalName, Action: () =>
			{
				StartAdd();
				return CloseMessage(ModalMessage);
			}));
		}
		else
		{
			await base.OnInitializedAsync();
		}
	}

	protected virtual void StartAdd() => AddModel = new();

	protected abstract void DefinePrimaryCriteria(DataFilterAndPage filterAndPage);

	protected virtual void GeneralSearchQuery(DataFilterAndPage filterAndPage) //define general search in base class
	{
		if (NoGeneralSearch)
			return;

		throw new NotImplementedException($"Please provide a query for the general search functionality in method {nameof(GeneralSearchQuery)}");
	}

	protected ValueTask SaveAdd(Func<T, CancellationToken, Task<bool>> save, Func<Task> refresh) =>
		BaseSaveAdd(() => save(AddModel, Cancel), refresh);

	protected ValueTask SaveAddWithRedirect(Func<T, Task<bool>> save, Action<T> redirect) =>
		BaseSaveAddWithRedirect(() => save(AddModel), () => redirect(AddModel));

	protected ValueTask Edit(Func<Task<T>> getData, CancellationToken ct) => BaseEdit(async () => { EditModel = await getData(); }, ct);

	protected ValueTask SaveEdit(Func<T, CancellationToken, Task<bool>> save, Func<Task> refresh) =>
		BaseSaveEdit(() => save(EditModel, Cancel), refresh);
}

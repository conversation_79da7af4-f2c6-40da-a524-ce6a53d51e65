﻿using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Collection.Reporting.ViewModel;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.Reporting;

partial class BusinessReportService
{
    private void BuildAllMessagesReport(SystemReport data)
    {
        var startDate = data.StartDate ?? "28-09-2024".GetInstantFromString();
        var endDate = data.EndDate ?? clock.GetCurrentInstant();

        var reportQuery = GetChannelRecipientsSummary(startDate, endDate);

        queries.Add(BusinessReportTypesEnum.AllMessages.ToString().SplitOnUpper(), reportQuery);
    }

    public IQueryable<AllMessagesReportVM> GetChannelRecipientsSummary(Instant startDate, Instant endDate)
    {
        var result = uow.Db.Queryable<MessageLogEntry>()
         .Where(x => (x.Log.Status == MessageStatus.Sent || x.Log.Status == MessageStatus.SentPartially) && x.CreatedDate >= startDate && x.CreatedDate <= endDate)
         .GroupBy(x => x.Channels)
         .Select(x => new AllMessagesReportVM
         {
             Channel = x.Key,
             TotalRecipients = x.Sum(y => y.Recipients.Count)
         });

        return result;
    }


    private static async Task SaveAllMessagesReport(ExcelWorksheet worksheet, IQueryable<object> reportData, CancellationToken ct)
    {
        var queryData = reportData.Cast<AllMessagesReportVM>();
        var data = await queryData.ToListAsync(ct);
        worksheet.Cells["A1"].LoadFromCollection([.. data.Select(x => new { Channels = string.Join(", ", x.Channel.FlagsToDisplayList<MessageChannel>()), x.TotalRecipients })], true);
    }
}

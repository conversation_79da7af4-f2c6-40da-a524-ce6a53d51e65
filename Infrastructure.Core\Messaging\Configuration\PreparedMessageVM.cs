﻿using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Extensions;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.Messaging.Configuration;

internal sealed class PreparedMessageVM
{
    public long MessageId { get; set; }
    public MessageChannel Channel { get; set; }
    public bool HasSeparateTemplateValues { get; set; }
    public string Subject { get; set; }
    public string TextTemplate { get; set; }
    public bool HasText => !string.IsNullOrEmpty(TextTemplate);
    public string HtmlTemplate { get; set; }
    public bool HasHtml => !string.IsNullOrEmpty(HtmlTemplate);
    public string SenderEmail { get; set; }
    public string SenderName { get; set; }
    public List<TemplateKeyValue> TemplateValues { get; set; }
    public List<SinglePreparedMessageVM> Data { get; set; }
    public List<MessageCopiedIn> CopiedIn { get; set; }
    public string CreatedByUser { get; set; }
    public string CreatedByUserId { get; set; }

}

internal sealed class SinglePreparedMessageVM
{
    public string UserId { get; set; }
    public bool ForUser => !string.IsNullOrEmpty(UserId);
    public List<PhoneNumber> PhoneNumbers { get; set; } = [];
    public List<string> Emails { get; set; } = [];
    public string Email { get; set; }
    public bool HasEmail => !Emails.IsNullOrEmpty();
    public string Name { get; set; }
    public string Subject { get; set; }
    public string TextTemplate { get; set; }
    public bool HasText => !string.IsNullOrEmpty(TextTemplate);
    public string HtmlTemplate { get; set; }
    public bool HasHtml => !string.IsNullOrEmpty(HtmlTemplate);
    public List<TemplateKeyValue> TemplateValues { get; set; } = [];
    public List<MessageAttachment> Attachments { get; set; }
    public List<MessageCopiedIn> CopiedIn { get; set; }
}

internal sealed class MessageRecipientUserDataVM
{
    public static readonly Expression<Func<ApplicationUser, MessageRecipientUserDataVM>> UserMapping = data =>
    new MessageRecipientUserDataVM
    {
        UserId = data.UserName,
        FirstName = data.FirstName,
        LastName = data.LastName,
        FullName = data.FullName,
        PhoneNumber = new PhoneNumber(data.PhoneCode, data.PhoneNumber),
        Email = data.Email,
    };

    public static readonly Expression<Func<MessagingGroupEntry, MessageRecipientUserDataVM>> MessagingGroupMapping = data =>
    new MessageRecipientUserDataVM
    {
        MessagingGroupId = data.MessagingGroupId,
        MessagingGroupEntryId = data.Id,
        FullName = data.Name,
        PhoneNumbers = data.PhoneNumbers,
        Emails = data.Emails,
        GroupName = data.Group.Name
    };

    public static readonly Expression<Func<MessagingGroupQuery, MessageRecipientUserDataVM>> MessagingGroupQueryMapping = data =>
   new MessageRecipientUserDataVM
   {
       MessagingGroupId = data.MessagingGroupId,
       Query = data.SenderQuery,
       GroupName = data.Group.Name
   };


    public string UserId { get; set; }
    public long? MessagingGroupId { get; set; }
    public long? MessagingGroupEntryId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string FullName { get; set; }
    public PhoneNumber PhoneNumber { get; set; }
    public string Email { get; set; }
    public string Query { get; set; }
    public string GroupName { get; set; }
    public List<PhoneNumber> PhoneNumbers { get; set; } = [];
    public List<string> Emails { get; set; } = [];
}
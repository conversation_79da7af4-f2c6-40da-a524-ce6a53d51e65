# Silicon Management Company - LendQube Deployment Pipeline

## Overview
The `silicon-pipeline.yml` file is the main deployment pipeline for Silicon Management Company's LendQube application. It builds and deploys both the Admin Website and Background Service to the production server.

## What Changed
- **Renamed**: `dev-deploy-azure-pipelines.yml` → `silicon-pipeline.yml`
- **Enhanced**: Added Background Service deployment alongside the existing Admin deployment
- **Improved**: Better logging and status reporting
- **Maintained**: All existing Admin deployment functionality preserved

## Pipeline Structure

### Build Stage
1. **Install .NET 9.0 SDK**
2. **Build Admin Website** (`Web.Admin.csproj`)
3. **Build Background Service** (`Web.BackgroundService.csproj`)
4. **Package both applications** as ZIP files for deployment

### Deploy Stage
1. **Verify .NET version** on production server
2. **Deploy Admin Website**:
   - Stop `lendqube-admin` service
   - Backup current deployment
   - Preserve production settings
   - Deploy new files
   - Restore settings
   - Start service and verify
3. **Deploy Background Service**:
   - Stop `lendqube-background` service
   - Backup current deployment
   - Preserve production settings
   - Deploy new files
   - Restore settings
   - Start service and verify
4. **Clean up old backups** (keep last 5)
5. **Display deployment summary**

## Server Requirements

### Services
The pipeline expects these systemd services to exist on the production server:
- `lendqube-admin` - Admin website service
- `lendqube-background` - Background service

### Directory Structure
```
/var/www/html/
├── admin/                    # Admin website files
│   └── AppSettings/
│       └── appsettings.Production.json
└── background/               # Background service files
    └── AppSettings/
        └── appsettings.Production.json
```

### User Permissions
- The `silicon` user must own both deployment directories
- The pipeline user must have sudo access for systemctl commands

## Azure DevOps Configuration

### Required Service Connection
- **Name**: `LendQube-Production-Server`
- **Type**: SSH
- **Target**: Production server with appropriate credentials

### Environment
- **Name**: `Production`
- **Approvals**: Recommended for production deployments

## Usage

### Manual Trigger
The pipeline is configured with `trigger: none`, so it must be manually triggered in Azure DevOps.

### Monitoring
The pipeline provides detailed logging for:
- Build status for both applications
- Deployment progress
- Service status verification
- Error details if deployment fails

## Rollback Strategy
- Automatic backups are created before each deployment
- Backups are timestamped: `admin_backup_YYYYMMDD_HHMMSS`
- Last 5 backups are retained automatically
- Manual rollback can be performed by restoring from backup

## Troubleshooting

### Common Issues
1. **Service won't start**: Check journalctl logs displayed in pipeline output
2. **Permission errors**: Verify `silicon` user ownership
3. **Missing settings**: Pipeline will create from Development settings if Production settings are missing

### Log Locations
- Pipeline logs: Azure DevOps pipeline run details
- Service logs: `journalctl -u lendqube-admin` or `journalctl -u lendqube-background`

## Security Notes
- Production settings are preserved during deployment
- Sensitive configuration is not logged
- Services are stopped/started gracefully to prevent data corruption

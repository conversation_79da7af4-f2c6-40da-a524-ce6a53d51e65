﻿using NodaTime;

namespace LendQube.Infrastructure.Core.DateAndTime;

public sealed class UserTimeProvider 
{
    // Notify when the local time zone changes
    public event EventHandler? LocalTimeZoneChanged;

    public DateTimeZone? LocalTimeZone { get; private set; }


    // Set the local time zone
    public void SetUserTimeZone(string timeZone)
    {
        var timeZoneInfo = DateTimeZoneProviders.Tzdb.GetZoneOrNull(timeZone);

        if (timeZoneInfo != LocalTimeZone)
        {
            LocalTimeZone = timeZoneInfo;
            LocalTimeZoneChanged?.Invoke(this, EventArgs.Empty);
        }
    }
}

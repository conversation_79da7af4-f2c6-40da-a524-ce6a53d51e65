﻿@using Radzen.Blazor
<RadzenDatePicker style="display: block; width: 100%"
@bind-Value="@dateTime"
TValue="DateTime?"
Change="UpdateTime"
Name="@Name"
Placeholder="@Placeholder"
DateFormat="dd MMM, yyyy"/>

@code {
    [Parameter]
    public DateOnly? DateValue { get; set; }

    [Parameter]
    public EventCallback<DateOnly?> DateValueChanged { get; set; }

    [Parameter]
    public string Name { get; set; }

    [Parameter]
    public string Placeholder { get; set; }

    private DateTime? dateTime { get; set; }

    protected override void OnInitialized()
    {
        if (DateValue.HasValue)
        {
            dateTime = DateValue.Value.ToDateTime(new TimeOnly(0, 0));
        }
        base.OnInitialized();
    }

    void UpdateTime(DateTime? dateTime)
    {
        if (dateTime is null) return;
        DateValue = DateOnly.FromDateTime(dateTime.Value);
        DateValueChanged.InvokeAsync(DateValue);
    }
}
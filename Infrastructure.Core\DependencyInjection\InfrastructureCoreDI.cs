﻿using System.Globalization;
using System.Net.Http.Headers;
using System.Reflection;
using System.Text;
using System.Threading.RateLimiting;
using Azure.Storage.Blobs;
using Community.Microsoft.Extensions.Caching.PostgreSql;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.GenericSpecification;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.FileManagement;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using LendQube.Infrastructure.Core.Helpers.Encryption;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Messaging.Providers;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.ApplicationInsights.DependencyCollector;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Net.Http.Headers;
using NodaTime;
using Radzen;
using SendGrid.Extensions.DependencyInjection;
using Serilog;
using Serilog.Events;
using ILogger = Serilog.ILogger;

namespace LendQube.Infrastructure.Core.DependencyInjection;

public static class InfrastructureCoreDI
{
    public const string IsDemoAppSettingsKey = "Providers:DeployState:IsDemo";
    private static IServiceCollection AddAppInsights(this IServiceCollection services)
    {
        services.AddApplicationInsightsTelemetry();
        services.AddSingleton<ITelemetryInitializer, CustomTelemetryInitializer>();
        services.ConfigureTelemetryModule<DependencyTrackingTelemetryModule>((module, o) => { module.EnableSqlCommandTextInstrumentation = true; });
        services.AddScoped<TrackCommandTimeInterceptor>();
        return services;
    }

    internal static IServiceCollection AddGlobalRateLimiting(this IServiceCollection services)
    {
        return services.AddRateLimiter(_ =>
        {
            _.OnRejected = async (context, ct) =>
            {
                if (context.Lease.TryGetMetadata(MetadataName.RetryAfter, out var retryAfter))
                {
                    context.HttpContext.Response.Headers.RetryAfter =
                        ((int)retryAfter.TotalSeconds).ToString(NumberFormatInfo.InvariantInfo);
                }

                context.HttpContext.Response.StatusCode = StatusCodes.Status429TooManyRequests;
                await context.HttpContext.Response.WriteAsync("Too many requests. Please try again later.", ct);
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogManager<RateLimiter>>();
                logger?.LogWarning(EventSource.Infrastructure, EventAction.RateLimiting, "Rate limiting active");
            };

            _.AddPolicy(ApiConstants.ConcurrentPolicy, httpContext =>
                RateLimitPartition.GetConcurrencyLimiter(httpContext.User?.Identity?.Name ?? httpContext.GetIpAddress(), _ => new ConcurrencyLimiterOptions
                {
                    PermitLimit = 1,
                    QueueLimit = 1,
                    QueueProcessingOrder = QueueProcessingOrder.OldestFirst
                }));

            _.AddPolicy(ApiConstants.SingleRequestPerUserEveryFiveSecondsLimitPolicy, httpContext =>
                RateLimitPartition.GetFixedWindowLimiter(httpContext.User.Identity.Name, _ => new FixedWindowRateLimiterOptions
                {
                    AutoReplenishment = true,
                    PermitLimit = 1,
                    QueueLimit = 0,
                    Window = TimeSpan.FromSeconds(5)
                }));

            _.GlobalLimiter = PartitionedRateLimiter.CreateChained(
                PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
                {
                    var key = httpContext.User?.Identity?.Name ?? httpContext.GetIpAddress();

                    return RateLimitPartition.GetFixedWindowLimiter
                    (key, _ =>
                        new FixedWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = 4,
                            QueueLimit = 2,
                            Window = TimeSpan.FromSeconds(2)
                        });
                }),
                PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
                {
                    var key = httpContext.User?.Identity?.Name ?? httpContext.GetIpAddress();

                    return RateLimitPartition.GetFixedWindowLimiter
                    (key, _ =>
                        new FixedWindowRateLimiterOptions
                        {
                            AutoReplenishment = true,
                            PermitLimit = 20,
                            QueueLimit = 2,
                            Window = TimeSpan.FromSeconds(30)
                        });
                }),
                PartitionedRateLimiter.Create<HttpContext, string>(httpContext =>
                {
                    var key = httpContext.User?.Identity?.Name ?? httpContext.GetIpAddress();

                    return RateLimitPartition.GetConcurrencyLimiter
                    (key, _ =>
                        new ConcurrencyLimiterOptions
                        {
                            PermitLimit = 5,
                            QueueLimit = 5,
                            QueueProcessingOrder = QueueProcessingOrder.OldestFirst
                        });
                }));
        });

    }

    public static WebApplicationBuilder AddAppSettings(this WebApplicationBuilder builder)
    {
        builder.Load<DefaultAppConfigProvidersVM>();
        builder.Services.AddAppInsights();
        builder.AddApplicationLogging();
        builder.Services.AddSingleton<IClock>(SystemClock.Instance);

        builder.Services.AddSingleton<DefaultAppConfig>();

        builder.Services.AddHttpContextAccessor();
        builder.Services.AddHttpClient();


        builder.Services.AddDistributedPostgreSqlCache(options =>
        {
            options.ConnectionString = builder.Configuration.GetValue<string>("ConnectionStrings:ConnectionString");
            options.SchemaName = "AppCache";
            options.TableName = "DistributedCache";
            options.DisableRemoveExpired = AppDomain.CurrentDomain.FriendlyName != DbContextDI.AdminProjectName;
        });

        return builder;
    }

    public static IServiceCollection AddDataAccessHelperServices(this IServiceCollection services)
    {
        services.AddTransient<ICurrentUserInfoProvider, CurrentUserInfoProvider>();
        services.AddTransient<IUnitofWork, UnitofWork>();

        services.AddTransient<GeneralGenericCrudService>();
        services.AddTransient<GeneralGenericCrudVMService>();

        services.AddTransient(typeof(GenericEntityCrudService<>));
        services.AddTransient(typeof(GenericEntityCrudVMService<,>));

        services.AddTransient(typeof(GenericSpecificationService<>));
        services.AddTransient(typeof(GenericVMSpecificationService<,>));

        return services;
    }

    public static IServiceCollection AddBaseDIServices(this IServiceCollection services)
    {
        services.AddDataAccessHelperServices();

        services.AddTransient<UserAccessService>();
        services.AddTransient<AesEncryptionService>();

        services.AddHttpClient();
        services.AddScoped<HttpClient>();
        services.AddFileManagement();

        return services;
    }

    public static IServiceCollection AddFileManagement(this IServiceCollection services)
    {
        services.AddSingleton<UriComposer>();
        services.AddScoped((sp) =>
        {
            var config = sp.GetRequiredService<DefaultAppConfig>();
            return new BlobServiceClient(config.AzureStorage.ConnectionString);
        })
        .AddTransient<IFileManagementService, FileManagementService>();

        return services;
    }

    public static IServiceCollection AddRadzenServices(this IServiceCollection services)
    {
        services.AddRadzenComponents();
        return services;
    }

    public static IServiceCollection AddRazorRoute<T>(this IServiceCollection service)
    {
        if (AppDomain.CurrentDomain.FriendlyName == DbContextDI.AdminProjectName)
            AdminDI.RoutableAssemblies.Add(typeof(T).Assembly);

        return service;
    }

    public static void AddApplicationLogging(this WebApplicationBuilder builder)
    {
        var demoMode = builder.Configuration.GetValue<bool>(IsDemoAppSettingsKey);
        ILogger logger = new LoggerConfiguration()
            .WriteTo.ApplicationInsights(new TelemetryConfiguration
            {
                ConnectionString = builder.Configuration.GetValue<string>("ApplicationInsights:ConnectionString")
            }, new IncludeRenderedMessageConverter())
            .MinimumLevel.Override("Default", demoMode ? LogEventLevel.Debug : LogEventLevel.Warning)
            .MinimumLevel.Override("System", demoMode ? LogEventLevel.Debug : LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft", demoMode ? LogEventLevel.Debug : LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.AspNetCore", demoMode ? LogEventLevel.Debug : LogEventLevel.Warning)
            .MinimumLevel.Override("Microsoft.EntityFrameworkCore.Database.Command", demoMode ? LogEventLevel.Debug : LogEventLevel.Warning)
            .Enrich.WithMachineName()
            .Enrich.WithEnvironmentName()
            .Enrich.With<LoggedInUserEnricher>()
            .CreateLogger();
        builder.Logging.AddSerilog(logger);
        builder.Services.AddScoped(typeof(ILogManager<>), typeof(LogManager<>));
    }

    public static IServiceCollection AddMessaging(this IServiceCollection services, IConfiguration config)
    {
        var sendGridApiKey = config.GetValue<string>("Providers:SendGrid:ApiKey");
        if (!string.IsNullOrEmpty(sendGridApiKey))
        {
            services.AddSendGrid(x =>
            {
                x.ApiKey = sendGridApiKey;
            });
            services.AddTransient<IEmailProvider, SendGridProvider>();
        }

        services.AddTransient<MessagingFactory>();
        services.AddTransient<MessageRouter>();
        services.AddTransient<MessagingGroupBuilder>();
        services.AddHttpClient<MessagingGroupUploadService>();

        services.AddHttpClient<IEmailProvider, MailgunProvider>((sp, httpClient) =>
        {
            var config = sp.GetRequiredService<DefaultAppConfig>();
            if (config.Mailgun == null)
                return;

            var authString = $"api:{config.Mailgun.Key}".Base64Encode(Encoding.ASCII);
            httpClient.BaseAddress = new Uri($"{config.Mailgun.Url}/{config.Mailgun.Domain}/");
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authString);

        });

        services.AddTransient<TextBasedMessageRouter>();
        services.AddHttpClient<ITextMessageProvider, TermiiProvider>((sp, httpClient) =>
        {
            var config = sp.GetRequiredService<DefaultAppConfig>();
            if (config.Termii == null)
                return;

            httpClient.BaseAddress = new Uri(config.Termii.ApiUrl);
            httpClient.DefaultRequestHeaders.Add(HeaderNames.Accept, HttpHeaderHelper.JsonHeader);

        });

        services.AddHttpClient<ITextMessageProvider, TerragonProvider>((httpClient) =>
        {
            httpClient.DefaultRequestHeaders.Add(HeaderNames.Accept, HttpHeaderHelper.JsonHeader);
        });

        services.AddHttpClient<ITextMessageProvider, ClickSendProvider>((sp, httpClient) =>
        {
            var config = sp.GetRequiredService<DefaultAppConfig>();
            if (config.ClickSend == null)
                return;

            var token = $"{config.ClickSend.UserName}:{config.ClickSend.ApiKey}".Base64Encode();
            httpClient.BaseAddress = new Uri(config.ClickSend.BaseUrl);
            httpClient.DefaultRequestHeaders.Add(HeaderNames.Accept, HttpHeaderHelper.JsonHeader);
            httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", token);

        });

        services.AddHttpClient<ITextMessageProvider, TextLocalSmsProvider>((sp, httpClient) =>
        {
            var config = sp.GetRequiredService<DefaultAppConfig>();
            if (config.TextLocal == null)
                return;

            httpClient.BaseAddress = new Uri(config.TextLocal.BaseUrl);
            httpClient.DefaultRequestHeaders.Add(HeaderNames.Accept, HttpHeaderHelper.JsonHeader);
        });

        services.AddTransient<ITextMessageProvider, TwilioSmsProvider>();

        services.AddTransient<TwilioWhatsappProvider>();
        services.AddTransient<CustomerInboxProvider>();
        services.AddHttpClient<TelegramProvider>();
        services.AddTransient<TelegramSessionStore>();

        services.AddTransient<FirebaseCloudMessagingProvider>();

        services.AddHttpClient<IEmailProvider, SmtpEmailProvider>();

        services.AddScoped<Func<MessageChannel, List<IMessageProvider>>>(serviceProvider => key =>
        {
            return key switch
            {
                MessageChannel.Email => [.. serviceProvider.GetServices<IEmailProvider>()],
                MessageChannel.Sms => [.. serviceProvider.GetServices<ITextMessageProvider>()],
                MessageChannel.Text => [serviceProvider.GetService<TextBasedMessageRouter>()],
                MessageChannel.PushNotification => [serviceProvider.GetService<FirebaseCloudMessagingProvider>()],
                MessageChannel.WhatsApp => [serviceProvider.GetService<TwilioWhatsappProvider>()],
                MessageChannel.CustomerInbox => [serviceProvider.GetService<CustomerInboxProvider>()],
                MessageChannel.Telegram => [serviceProvider.GetService<TelegramProvider>()],
                _ => throw new KeyNotFoundException($"Setup -> Message Provider for Channel: {key} not found"),
            };
        });

        return services;
    }

    public static IServiceCollection AddTriggerNotification(this IServiceCollection services, List<Type> triggerTypes)
    {
        if (triggerTypes.Count == 0) return services;

        //trigger types should only be registered once for the entire application
        //registration should be done in the project that will respond to the trigger only to prevent multiple hosted projects responding to the same notification

        var interfaceType = typeof(IEntityHasNotifyTrigger);
        Assembly.GetEntryAssembly()
            .GetReferencedAssemblies()
            .AsParallel()
            .Where(x => x.Name != null && x.Name.StartsWith(CoreEntityConfig.InfrastructurePrefix))
            .Select(Assembly.Load)
            .SelectMany(x => x.GetTypes())
            .Where(item => item.GetInterfaces().AsParallel()
            .Where(i => i.IsGenericType).Any(i => i.GetGenericTypeDefinition() == typeof(IHandleTriggerNotification<>) && triggerTypes.Any(t => interfaceType.IsAssignableFrom(t) && i.GenericTypeArguments.Contains(t))) && !item.IsAbstract && !item.IsInterface)
            .ForAll(assignedType =>
            {
                var serviceType = assignedType.GetInterfaces().FirstOrDefault(i => i.GetGenericTypeDefinition() == typeof(IHandleTriggerNotification<>));
                if (serviceType != null)
                    services.AddTransient(serviceType, assignedType);
            });

        services.AddSingleton<ILogManager<TriggerNotificationService>, LogManager<TriggerNotificationService>>();
        services.AddSingleton(provider => new TriggerConfiguration(triggerTypes));
        services.AddSingleton<TriggerNotificationService>();

        services.AddHostedService<TriggerHostedService>();

        return services;
    }
}

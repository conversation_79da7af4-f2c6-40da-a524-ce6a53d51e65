﻿using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;

namespace LendQube.Entities.Core.Messaging;

public class MessageProviderConfig : BaseEntityWithIdentityId<MessageProviderConfig>
{
    public string Name { get; set; }
    public List<string> SupportedCountries { get; set; }
    public Instant? DisabledOn { get; set; }
    public Instant? LogActivityOn { get; set; }
    [DbDecimal(18,8)]
    public decimal UnitCost { get; set; }
    public long TotalCount { get; set; }
    public long FailureCount { get; set; }
    [DbDecimal(18, 8)]
    public decimal TotalFunding { get; set; }
    [DbDecimal(18, 8)]
    public decimal TotalDebit { get; set; }
    public decimal ExpectedTotalDebit { get; set; }
    [DbComputed(@$"""{nameof(TotalFunding)}"" - ""{nameof(TotalDebit)}"""), DbDecimal(18, 8)]
    public decimal Balance { get; set; }

    [NotMapped]
    public decimal FundingAmount { get; set; }

    [NotMapped]
    public bool Disabled { get; set; }

    [NotMapped]
    public bool LogActivity { get; set; }

    public override void Configure(EntityTypeBuilder<MessageProviderConfig> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.Name).IsUnique(); 
    }
}

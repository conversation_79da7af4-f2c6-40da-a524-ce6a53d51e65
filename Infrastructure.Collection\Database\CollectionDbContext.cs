﻿using LendQube.Entities.Collection.Base;
using LendQube.Infrastructure.Core.Database.DbContexts;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Database;

public sealed class CollectionDbContext : IAppDbContextBase
{
    public string Schema => CollectionEntityConfig.DefaultSchema;
    public bool IsActive => true;

    public void Create(ModelBuilder builder) => //for appdbcontext
       this.OnModelCreate(builder, typeof(CollectionDbContext).Assembly.GetReferencedAssemblies().AsParallel().Where(x => x.Name != null && x.Name.StartsWith(CollectionEntityConfig.EntitiesProject)));
}

﻿using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Messaging;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Reporting;

namespace LendQube.Infrastructure.Core.BackgroundTasks;

internal sealed class DefaultBackgroundTaskManager(BackgroundTaskControlService controlService) : AbstractBackgroundTaskManager(controlService)
{
    private readonly BackgroundTaskControlService controlService = controlService;

    public override BackgroundEventSource AllowedSource => BackgroundEventSource.System;
    public override IReadOnlyList<BackgroundTask> AllowedTasks => [BackgroundTask.SendMessages, BackgroundTask.ScheduledMessages, BackgroundTask.MessagingGroupUpload, BackgroundTask.ScheduledReports];


    public override async Task StartTasks(BackgroundTask key, CancellationToken ct)
    {
        switch (key)
        {
            case BackgroundTask.SendMessages:
                _ = await controlService.Uow.Db.UpdateAndSaveWithFilterAsync<MessageLog>(x => x.Status == MessageStatus.Queued, x => x.SetProperty(y => y.Status, MessageStatus.Queued), ct);
                break;
            case BackgroundTask.MessagingGroupUpload:
                _ = await controlService.Uow.Db.UpdateAndSaveWithFilterAsync<SystemFileUpload>(x => x.Type == SystemUploadType.MessageGroup && x.Status == UploadStatus.Queued, x => x.SetProperty(y => y.Status, UploadStatus.Queued), ct);
                break;
            case BackgroundTask.ScheduledMessages:
                _ = await MessageScheduler.StartupScheduledMessages(controlService.Uow, ct);
                break;
            case BackgroundTask.ScheduledReports:
                _ = await ReportScheduler.StartupScheduledReports(controlService.Uow, ct);
                break;
            default:
                break;
        }
    }
}
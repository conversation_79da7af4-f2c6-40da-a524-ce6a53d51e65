﻿using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;

namespace LendQube.Infrastructure.Collection.Workflow.Debt;

internal sealed class AgentWorkflowTaskHandler : IHandleTriggerNotification<AgentWorkflowTask>
{
	public Task OnStartup(CancellationToken ct) => Task.CompletedTask;

	public ValueTask OnChanged(string id, AgentWorkflowTask oldData, AgentWorkflowTask newData, TriggerChange change, CancellationToken ct)
	{
		WorkflowManagementService.RaiseEvent(this, new SystemBackgroundTaskEventArgs(true, "New task assigned", newData.UserId.ToString()));

		return ValueTask.CompletedTask;
	}
}

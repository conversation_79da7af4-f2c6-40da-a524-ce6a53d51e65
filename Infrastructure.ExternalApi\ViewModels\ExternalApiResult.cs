﻿namespace LendQube.Infrastructure.ExternalApi.ViewModels;

public sealed class ExternalApiResult<T>
{
    public string RequestData { get; set; }
    public string ResponseData { get; set; }
    public bool Successful { get; set; }
    public bool Failed => !Successful;
    public T Result { get; set; }
    public ExternalApiResult<TVM> To<TVM>(TVM data) => new() { Result = data, RequestData = RequestData, ResponseData = ResponseData, Successful = Successful };
}

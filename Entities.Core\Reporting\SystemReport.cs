﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using NodaTime;

namespace LendQube.Entities.Core.Reporting;

public class SystemReport : BaseEntityWithIdentityId<SystemReport>, IEntityHasNotifyTrigger
{
    [Required]
    public string TypeName { get; set; }
    [Required, MinLength(1)]
    public List<string> Names { get; set; }
    public ReportStatus Status { get; set; }
    public ReportType FileType { get; set; }
    [Required, StringLength(EntityConstants.DEFAULT_DESCRIPTION_FIELD_LENGTH)]
    public string Description { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string FileUrl { get; set; }
    public string Message { get; set; }
    public Instant? StartDate { get; set; }
    public Instant? EndDate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public int RunCount { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public bool AutoRun { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long? ReportScheduleId { get; set; }
    public virtual ReportSchedule? ReportSchedule { get; set; }


    public string Schema => CoreEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => string.Empty;
}

﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Collection.Helpers;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<CustomerHoldVM> holdTable;
    private ColumnList holdTableDefinition;
    private string AddHoldModal => "AddHoldModal";

    private CustomerHold AddHoldModel { get; set; } = new();

    private List<HoldConfig> holdConfig = [];

    private void SetupHold()
    {
        holdTableDefinition = CrudService.GetTableDefinition<CustomerHoldVM>(new()
        {
            ShowUserInfo = true,
            HasInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileDeleteHoldPermission)
        });

        holdTableDefinition.TopActionButtons.Add(new TopActionButton("Add Hold", ModalName: AddHoldModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileAddHoldPermission)));

        holdTableDefinition.RowActionButtons.Add(new RowActionButton("Disable", Icon: "shield-off", IconClass: "btn--danger", Action: async (object row) =>
        {
            TableMessage.Close();
            var data = row as CustomerHoldVM;
            var result = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerHold>(x => x.Id == data.Id, x => x.SetProperty(y => y.Disabled, true), Cancel);
            TableMessage.Set(result > 0, "Hold successfully disabled", "Disabling hold failed");
            await holdTable.Refresh();
            if (result > 0)
                await LoadProfile();
            StateHasChanged();

        }, ShowCondition: (row) => !(row as CustomerHoldVM).Disabled && HasClaim(ManageCustomersNavigation.CustomerProfileModifyHoldPermission)));

        holdTableDefinition.RowActionButtons.Add(new RowActionButton("Enable", Icon: "shield", IconClass: "btn--success" , Action: async (object row) =>
        {
            TableMessage.Close();
            var data = row as CustomerHoldVM;
            var result = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerHold>(x => x.Id == data.Id, x => x.SetProperty(y => y.Disabled, false), Cancel);
            TableMessage.Set(result > 0, "Hold successfully enabled", "Enabling hold failed");
            await holdTable.Refresh();
            if (result > 0)
                await LoadProfile();
            StateHasChanged();

        }, ShowCondition: (row) => (row as CustomerHoldVM).Disabled && HasClaim(ManageCustomersNavigation.CustomerProfileModifyHoldPermission)));

        holdTable.SetTableDefinition(holdTableDefinition);
    }

    private async ValueTask<TypedBasePageList<CustomerHoldVM>> LoadHold(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        if (holdConfig.IsNullOrEmpty())
            holdConfig = await uow.Db.ManyAsync(Query<HoldConfig>.All().Include(x => x.Include(y => y.DefaultDuration)), Cancel);

        var spec = new BaseSpecification<CustomerHold>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Hold.Description, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Comment, filterAndPage.TextFilter));
        }

        return await CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerHoldVM.Mapping, ct);
    }

    private ValueTask SubmitNewHold() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileAddHoldPermission, AddHoldModal, async () =>
    {
        AddHoldModel.ProfileId = Data.Id;
        
        if(!AddHoldModel.HoldDefaultsOverriden)
        {
            var config = holdConfig.FirstOrDefault(x => x.Id == AddHoldModel.HoldConfigId);
            AddHoldModel.ExpiresOn = config.DefaultDuration.GetExpiryFromHoldDuration(timeZone);
            AddHoldModel.Action = config.Action;
        }
        else
        {
            AddHoldModel.Action = AddHoldModel.ActionList.CombineFlags();
            if(!string.IsNullOrEmpty(AddHoldModel.Expiry))
                AddHoldModel.ExpiresOn = AddHoldModel.Expiry.GetInstantFromStringGivenTimeZone(timeZone);
        }

        uow.Db.Insert(AddHoldModel);
        await uow.SaveAsync(Cancel);
        Data.HasActiveHold = true;
        return true;
    }, () =>
    {
        AddHoldModel = new();
        StateHasChanged();
        return holdTable.Refresh();
    });

    private ValueTask<bool> DeleteHold(CustomerHoldVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileDeleteHoldPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerHold>(x => x.Id == data.Id, ct);
        if(result > 0)
        {
            await LoadProfile();
            StateHasChanged();
        }

        return result > 0;
    }, refresh);
}

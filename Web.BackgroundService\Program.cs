using LendQube.Infrastructure.Collection.DependencyInjection;
using LendQube.Infrastructure.Core.DependencyInjection;
using LendQube.Web.BackgroundService.Components;

namespace LendQube.Web.BackgroundService;

public class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);

        builder.Services.AddRazorPages()
            .AddNewtonsoftJson();

        // Add services to the container.
        builder.Services.AddRazorComponents(options =>
                options.DetailedErrors = builder.Environment.IsDevelopment())
            .AddInteractiveServerComponents();

        builder.AddBaseAdminService()
            .AddDbContext();

        builder.AddCollectionBackgroundServices();

        builder.Services
            .AddBaseDIServices()
            .AddAdminServices()
            .AddCoreBackgroundServices()
            .AddBackgroundTriggers();


        var app = builder.Build();

        app.AddAdminApp(builder);

        app.MapRazorComponents<App>()
            .AddInteractiveServerRenderMode();

        app.MapRazorPages();

        app.UseCoreBackgroundService();

        app.Run();
    }
}
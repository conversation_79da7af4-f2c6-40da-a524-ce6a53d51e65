﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Enums;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Extensions;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public class CustomerProfileVM : IBaseEntityWithStringId
{
    public static readonly Expression<Func<CustomerProfile, CustomerProfileVM>> Mapping = data => new()
    {
        Id = data.Id,
        AccountId = data.AccountId,
        FullName = data.FullName,
        FirstName = data.FirstName,
        MiddleName = data.MiddleName,
        LastName = data.LastName,
        Email = data.Email,
        MobileNumber = data.MobileNumber,
        PhoneNumber = data.PhoneNumber,
        DateOfBirth = data.DateOfBirth,
        Gender = data.Gender,
        CountryCode = data.CountryCode,
        CurrencySymbol = data.CurrencySymbol,
        CurrencyCode = data.CurrencyCode,
        Blacklisted = data.Blacklisted,
        LastRescheduleDate = data.LastRescheduleDate,
        NextRescheduleDate = data.NextRescheduleDate,
        CanReschedule = data.CanReschedule,
        TotalRescheduleCount = data.TotalRescheduleCount,
        BalanceTotal = data.BalanceTotal,
        BalancePaid = data.BalancePaid,
        Discount = data.Discount,
        BalanceRemaining = data.BalanceRemaining,
        PaymentFrequency = data.PaymentFrequency,
        HasFlags = data.Flags.Any(),
        HasActiveHold = data.Holds.Any(x => !x.Disabled && (!x.ExpiresOn.HasValue || SystemClock.Instance.GetCurrentInstant() < x.ExpiresOn)),
        IncomeAndExpenditure = data.IncomeAndExpenditure,
        Addresses = data.Addresses.ToList(),
        Schedules = data.Schedules.ToList(),
        TotalPlacements = data.Placements.Count(),
        HasActivePlacements = data.Placements.Any(x => x.Status < PlacementStatus.Settled),

        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public string Id { get; set; }
    public string AccountId { get; set; }
    public string FullName { get; set; }
    public string FirstName { get; set; }
    public string MiddleName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public PhoneNumber MobileNumber { get; set; }
    public PhoneNumber? PhoneNumber { get; set; }
    public DateOnly? DateOfBirth { get; set; }
    public Gender Gender { get; set; }
    public string CountryCode { get; set; }
    public string CurrencySymbol { get; set; }
    public string CurrencyCode { get; set; }
    public bool Blacklisted { get; set; }
    public Instant? LastRescheduleDate { get; set; }
    public Instant? NextRescheduleDate { get; set; }
    public bool CanReschedule { get; set; }
    public int TotalRescheduleCount { get; set; }
    public decimal BalanceTotal { get; set; }
    public decimal BalancePaid { get; set; }
    public decimal Discount { get; set; }
    public decimal BalanceRemaining { get; set; }
    public int TotalPlacements { get; set; }
    public bool HasFlags { get; set; }
    public bool HasActiveHold { get; set; }
    public bool HasActivePlacements { get; set; }
    public SchedulePaymentFrequency? PaymentFrequency { get; set; }
    public CustomerIncomeAndExpenditure IncomeAndExpenditure { get; set; }
    public List<CustomerAddress> Addresses { get; set; } = [];
    public List<CustomerSchedule> Schedules { get; set; } = [];

}
public sealed class CustomerMessageEntryVM : IBaseEntityWithNumberId
{
    public static readonly Func<string, Expression<Func<MessageLogEntry, CustomerMessageEntryVM>>> Mapping = (profileId) => data => new CustomerMessageEntryVM
    {
        Id = data.Id,
        Name = data.Name,
        Subject = data.Subject,
        Status = data.Log.Status,
        Channels = data.Channels,
        NeedsConfiguration = data.Config.NeedsConfiguration,
        TextTemplate = data.TextTemplate,
        HtmlTemplate = data.HtmlTemplate,
        BodyTextTemplate = data.BodyTextTemplate,
        BodyHtmlTemplate = data.BodyHtmlTemplate,
        ContainerTextTemplate = data.ContainerTextTemplate,
        ContainerHtmlTemplate = data.ContainerHtmlTemplate,
        Keys = data.Keys,

        CopiedIn = data.Recipients.Where(x => x.UserId == profileId).SelectMany(x => x.CopiedIn),
        TemplateValues = data.Recipients.Where(x => x.UserId == profileId).SelectMany(x => x.TemplateValues),
        Attachments = data.Recipients.Where(x => x.UserId == profileId).SelectMany(x => x.Attachments),
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public string Name { get; set; }
    public string Subject { get; set; }
    public MessageStatus Status { get; set; }
    public MessageChannel Channels { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public bool NeedsConfiguration { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string TextTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string HtmlTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string BodyTextTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string BodyHtmlTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ContainerTextTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string ContainerHtmlTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public List<string> Keys { get; set; }
    [DisplayName("Keys")]
    public string KeysFormatted => Keys.IsNullOrEmpty() ? string.Empty : string.Join(", ", Keys);
    [DisplayName("Copied In")]
    public string CopiedInFormatted => CopiedIn.IsNullOrEmpty() ? string.Empty : string.Join(", ", CopiedIn.Select(x => $"{x.Name}: {x.Email} {(x.BlindCopy ? "(Bcc)" : "")}"));
    [DisplayName("Template Values")]
    public string TemplateValuesFormatted => TemplateValues.IsNullOrEmpty() ? "" : string.Join("<br> ", TemplateValues.Select(x => $"{x.Key}: {x.Value}"));
    [DisplayName("Attachments")]
    public string AttachmentsFormatted => Attachments.IsNullOrEmpty() ? "" : string.Join("<br> ", Attachments);

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public IEnumerable<TemplateKeyValue> TemplateValues { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public IEnumerable<MessageAttachment> Attachments { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public IEnumerable<MessageCopiedIn> CopiedIn { get; set; }

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public bool HasText => Channels.IsFlagSet(MessageChannel.Text);
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public bool HasHtml => Channels.IsFlagSet(MessageChannel.Email | MessageChannel.SmsOrEmail | MessageChannel.PushNotificationAndEmail | MessageChannel.PushNotificationOrEmail | MessageChannel.SmsAndEmail | MessageChannel.EmailOrSms);
}

public sealed class CustomerNoteVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerNote, CustomerNoteVM>> Mapping = data => new()
    {
        Id = data.Id,
        Type = data.Type,
        ContactType = data.ContactType,
        Note = data.Note,
        PlacementId = data.PlacementId,
        Placement = data.Placement == null ? "All" : data.Placement.Company + ": " + data.Placement.SourceAccountNumber,
        Files = data.Files,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId
    };

    public CustomerNote Get(string profileId) => new()
    {
        Id = Id,
        ProfileId = profileId,
        PlacementId = PlacementId,
        Type = Type,
        ContactType = ContactType,
        Note = Note,
        Files = Files,
    };

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long? PlacementId { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Placement { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public CustomerNoteType Type { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public CustomerContactType? ContactType { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Note { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public List<CustomerNoteFile> Files { get; set; }
}

public sealed class CustomerDropDownPlacementVM
{
    public static readonly Expression<Func<Placement, CustomerDropDownPlacementVM>> Mapping = data => new()
    {
        Id = data.Id,
        Info = data.Company + ": " + data.SourceAccountNumber,
        BalanceRemaining = data.BalanceRemaining,
        Status = data.Status
    };

    public long? Id { get; set; }
    public string Info { get; set; }
    public decimal BalanceRemaining { get; set; }
    public PlacementStatus Status { get; set; }
}

public sealed class CustomerFlagVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerFlag, CustomerFlagVM>> Mapping = data => new()
    {
        Id = data.Id,
        Flag = data.Flag.Flag,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId
    };
    public string Flag { get; set; }
}

public sealed class CustomerDiscountVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerDiscount, CustomerDiscountVM>> Mapping = data => new()
    {
        Placement = data.Placement.Company + ": " + data.Placement.SourceAccountNumber,
        Discount = data.Discount,
        Reason = data.Reason,
        Id = data.Id,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId
    };

    public string Placement { get; set; }
    public decimal Discount { get; set; }
    public string Reason { get; set; }
}

public sealed class CustomerHoldVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerHold, CustomerHoldVM>> Mapping = data => new()
    {
        Id = data.Id,
        PlacementId = data.PlacementId,
        HoldConfigId = data.HoldConfigId,
        Placement = data.Placement == null ? "All" : data.Placement.Company + ": " + data.Placement.SourceAccountNumber,
        Reason = data.Hold.Reason,
        Comment = data.Comment,
        Length = data.Hold.DefaultDuration.Length,
        Duration = data.Hold.DefaultDuration.Duration,
        Action = data.HoldDefaultsOverriden ? data.Action : data.Hold.Action,
        HoldDefaultsOverriden = data.HoldDefaultsOverriden,
        ExpiresOn = data.ExpiresOn,
        Disabled = data.Disabled,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId
    };

    [RemoveColumn]
    public long? PlacementId { get; set; }
    [RemoveColumn]
    public long HoldConfigId { get; set; }

    public string Placement { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.ShowInInfo)]
    public string Reason { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.ShowInInfo)]
    public string Comment { get; set; }

    [RemoveColumn]
    public int Length { get; set; }
    [RemoveColumn]
    public HoldDuration Duration { get; set; }

    [DisplayName("Duration")]
    public string HoldDuration => HoldDefaultsOverriden ? string.Empty : $"{Length} {Duration}";

    [TableDecorator(TableDecoratorType.HideColumn)]
    public HoldAction Action { get; set; }

    [DisplayName("Action")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ActionFormatted => string.Join(", ", Action.FlagsToDisplayList<HoldAction>());
    public bool HoldDefaultsOverriden { get; set; }
    public Instant? ExpiresOn { get; set; }
    public bool Disabled { get; set; }

}

public sealed class PlacementStatusChangeVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<PlacementStatusChange, PlacementStatusChangeVM>> Mapping = data => new()
    {
        PlacementId = data.PlacementId,
        ReasonId = data.ReasonId,
        Placement = data.Placement.Company + ": " + data.Placement.SourceAccountNumber,
        FromStatus = data.FromStatus,
        ToStatus = data.ToStatus,
        Reason = data.Reason != null ? data.Reason.Code + ": " + data.Reason.Description : "",
        Comment = data.Comment,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId
    };

    [RemoveColumn]
    public long PlacementId { get; set; }
    [RemoveColumn]
    public long? ReasonId { get; set; }

    public string Placement { get; set; }
    public PlacementStatus FromStatus { get; set; }
    public PlacementStatus ToStatus { get; set; }
    public string Reason { get; set; }
    public string Comment { get; set; }
}


public sealed class CustomerPromiseToPayVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<CustomerPromiseToPay, CustomerPromiseToPayVM>> Mapping = data => new()
    {
        Id = data.Id,
        Amount = data.Amount,
        AmountPaid = data.AmountPaid,
        DueDate = data.DueDate,
        Redeemed = data.Redeemed,
        Note = data.Note.Note,
        CreatedByIp = data.CreatedByIp,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByUserId = data.CreatedByUserId,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByIp = data.ModifiedByIp,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByUserId = data.ModifiedByUserId
    };

    public CustomerPromiseToPay Get(string profileId, long noteId) => new()
    {
        Id = Id,
        ProfileId = profileId,
        Amount = Amount,
        NoteId = noteId
    };

    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public decimal Amount { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public Instant? DueDate { get; set; }
    public decimal AmountPaid { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public bool Redeemed { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Note { get; set; }
}

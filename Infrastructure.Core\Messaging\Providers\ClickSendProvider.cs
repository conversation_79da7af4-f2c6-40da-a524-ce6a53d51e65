﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Messaging.Providers.ViewModels;
using LendQube.Infrastructure.Core.Telemetry;
using System.Net.Http.Json;
using System.Text.Json;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class ClickSendProvider : AbstractMessageProvider, ITextMessageProvider
{
    public const string Name = "ClickSend";
    protected override MessageChannel SupportedChannel => MessageChannel.Sms;
    private readonly int bulkLimit = 1000;

    private readonly DefaultAppConfig config;
    private readonly HttpClient httpClient;
    private readonly ILogManager<ClickSendProvider> logger;

    public ClickSendProvider(IUnitofWork uow, DefaultAppConfig config, HttpClient httpClient, ILogManager<ClickSendProvider> logger) : base(uow)
    {
        this.config = config;
        this.httpClient = httpClient;
        this.logger = logger;

        if (config.ClickSend != null)
        {
            SupportedCountryCodes = MessagingCompiledQueries.GetMessageProviderSupportedCountriesAndConfig(uow, Name);
            Config = SupportedCountryCodes.IsNullOrEmpty() ? new ProviderConfigVM { Disabled = true } : SupportedCountryCodes[0];
        }
        else
        {
            Config = new ProviderConfigVM { Disabled = true };
        }
    }

    public override ProviderConfigVM Config { get; }
    public IReadOnlyList<ProviderConfigVM> SupportedCountryCodes { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        var leadingMessage = messages[0];
        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        string serverResponse = null;
        Dictionary<long, MessageStatus> result = [];
        List<ClickSendMessageResponseVM> typedResponse = [];

        var data = messages.SelectMany(m => m.HasSeparateTemplateValues ?
            m.Data.SelectMany(d => d.PhoneNumbers.Select(p => new ClickSendMessageVM { From = config.ClickSend.From, Source = config.ClickSend.Source, To = $"{p.Code}{p.Number}", Body = d.TextTemplate })) :
            m.Data.SelectMany(d => d.PhoneNumbers.Select(p => new ClickSendMessageVM { From = config.ClickSend.From, Source = config.ClickSend.Source, To = $"{p.Code}{p.Number}", Body = m.TextTemplate }))
            ).ToList();


        var total = data.Count;
        var skip = 0;
        while (total > 0)
        {
            try
            {
                var dataToSend = new ClickSendMesageRequestVM { Messages = [.. data.Skip(skip * bulkLimit).Take(bulkLimit)] };
                var response = await httpClient.PostAsJsonAsync("sms/send", dataToSend, ct);
                result[skip] = response.IsSuccessStatusCode.ToMessageStatus();

                serverResponse = await response.Content.ReadAsStringAsync(ct);
                typedResponse.Add(JsonSerializer.Deserialize<ClickSendMessageResponseVM>(serverResponse));
            }
            catch (Exception ex)
            {
                result[skip] = MessageStatus.Failed;
                var messageIds = string.Join(", ", messages.Select(x => x.MessageId));

                logger.LogError(EventSource.BackgroundTask, EventAction.SendMail, ex, $"Could not ProcessMessage: {messageIds} {skip}, {serverResponse}");
                LogActivity(leadingMessage, MessageStatus.Failed, Name, "Provider failure");
            }

            skip++;
            total -= bulkLimit;
        }

        var status = result.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, result, null, typedResponse.AsParallel().Sum(x => x?.Data?.TotalPrice ?? 0), ct);
        return status;
    }
}
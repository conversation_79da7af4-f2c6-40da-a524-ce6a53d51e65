﻿namespace LendQube.Infrastructure.Core.ViewModels.Base;

public sealed class Result<T>
{
    public T Data { get; set; }
    public string Message { get; set; }
    public bool IsSuccessful { get; set; }
    public bool IsFailed => !IsSuccessful && !IsPending;
    public bool IsPending { get; set; }
    public string[] Errors { get; set; }

    public static Result<T> Failed(string message = null, T data = default, string[] errors = null) => new() { Data = data, Message = message, Errors = errors };

    public static Result<T> Successful(string message = null, T data = default) => new() { Data = data, Message = message, IsSuccessful = true };

    public static Result<T> Pending(string message = null, T data = default) => new() { Data = data, Message = message, IsPending = true };

    public Result<TVM> To<TVM>(TVM data = default) => new() { Data = data, Message = Message, Errors = Errors, IsPending = IsPending, IsSuccessful = IsSuccessful };

    public void Deconstruct(out bool isSuccessful, out T data)
    {
        isSuccessful = IsSuccessful;
        data = Data;
    }

    public static implicit operator Result<T>(T data) => Successful(data: data);
    public static implicit operator Result<T>(string message) => Failed(message);
    public static implicit operator Result<T>(string[] errors) => Failed(errors: errors);
}

public sealed class ValidatorResult<T>
{
    public bool Failed { get; internal set; }
    public bool Succeeded => !Failed;
    public Result<T> Result { get; private set; }
    public T Data { get; set; }
    public static ValidatorResult<T> Success(T data = default) => new() { Data = data };
    public static ValidatorResult<T> Fail(string message, ValidatorResultType type = ValidatorResultType.Failed, T data = default) => new()
    {
        Failed = true,
        Data = data,
        Result = type switch
        {
            ValidatorResultType.Pending => Result<T>.Pending(message, data),
            ValidatorResultType.Successful => Result<T>.Successful(message, data),
            _ => Result<T>.Failed(message, data),
        }
    };


    public static implicit operator ValidatorResult<T>(T data) => Success(data);
    public static implicit operator ValidatorResult<T>(string message) => Fail(message);
}

public enum ValidatorResultType
{
    Pending,
    Successful,
    Failed
}
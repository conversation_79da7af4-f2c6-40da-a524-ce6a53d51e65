﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Collection.Reporting.ViewModel;
using Microsoft.EntityFrameworkCore;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.Reporting;

partial class BusinessReportService
{
    private void BuildPlacementReport(SystemReport data)
    {
        var endDate = data.EndDate ?? clock.GetCurrentInstant();

        var reportQuery = PreparePlacement(endDate);

        queries.Add(BusinessReportTypesEnum.Placement.ToString().SplitOnUpper(), reportQuery);
    }

    private IQueryable<PlacementReportVM> PreparePlacement(Instant endDate)
    {
        var zonedEndDate = endDate.InZone(timeZone).Date;
        var zeroTimespan = TimeOnly.FromTimeSpan(TimeSpan.Zero);
        var query = uow.Db.Queryable<Placement>().Where(x => x.CreatedDate <= endDate)
            .GroupBy(x => x.CreatedDate.Value.InZone(timeZone).Date)
            .Select(data => new PlacementReportVM()
            {
                PlacementDate = data.Key,
                CustomersEscalated = data.GroupBy(x => x.ProfileId).Count(),
                DebtEscalated = data.Sum(x => x.BalanceTotal),
                AccountAgeInDays = (zonedEndDate - data.Key).Days,
                ActiveCustomers = data.Where(x => x.Status < PlacementStatus.Settled).GroupBy(x => x.ProfileId).Count(),
                ActiveOutstandingBalance = data.Where(x => x.Status < PlacementStatus.Settled).Sum(x => x.BalanceRemaining),
                TotalPayments = data.Sum(x => x.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Sum(x => x.AmountPaid)),
                OutstandingBalanceOnPlan = data.Where(x => x.Status < PlacementStatus.Settled).Sum(x => x.Profile.Schedules.Where(y => y.CreatedDate <= endDate
                && y.PaymentStatus != SchedulePaymentStatus.Paid).Sum(y => y.Balance)),

                AccountsWithSchedule = data.Where(x => x.Status < PlacementStatus.Settled && x.Profile.Schedules.Any(y => y.CreatedDate <= endDate))
                .GroupBy(x => x.ProfileId).Count(),

                AccountsWithVulnerableHold = data.Where(x =>
                x.Status < PlacementStatus.Settled &&
                x.Profile.Holds.Any(x =>
                x.CreatedDate <= endDate &&
                (EF.Functions.ILike(x.Hold.Reason, "%Breathing%") ||
                EF.Functions.ILike(x.Hold.Reason, "%Holiday%") ||
                EF.Functions.ILike(x.Hold.Reason, "%Vulnerable%"))))
                .GroupBy(x => x.ProfileId).Count(),

                AccountsWithLogin = data.Where(x => x.Status < PlacementStatus.Settled && x.Profile.AppUser.LastLoginDate.HasValue).GroupBy(x => x.ProfileId).Count()
            });

        return query;
    }

    private static void SavePlacementReport(ExcelWorksheet worksheet, IQueryable<object> reportData)
    {
        var queryData = reportData.Cast<PlacementReportVM>();
        worksheet.Cells["A1"].LoadFromCollection(queryData);
    }
}

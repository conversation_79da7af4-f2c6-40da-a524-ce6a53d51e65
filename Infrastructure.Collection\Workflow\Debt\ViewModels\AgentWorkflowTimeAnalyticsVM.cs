﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Attributes;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Workflow.Debt.ViewModels;

public class AgentWorkflowTimeAnalyticsVM
{
    public static readonly Expression<Func<AgentWorkflowTimeAnalytics, AgentWorkflowTimeAnalyticsVM>> Mapping = data => new()
    {
        Id = data.Id,
        Date = data.Date,
        UserId = data.UserId,
        AgentName = data.User.FullName,
        Start = data.CreatedDate,
        LastModified = data.LastModifiedDate,
        TotalAssigned = data.TotalAssigned,
        TotalOpened = data.TotalOpened,
        TotalClosed = data.TotalClosed,
        TotalEscalated = data.TotalEscalated,
        TotalAmountCollected = data.TotalAmountCollected,
        TotalPTP = data.TotalPTP,
        TotalPTPAmount = data.TotalPTPAmount,
        TotalSchedulesSetup = data.TotalSchedulesSetup,
        TotalContacted = data.TotalContacted,
        TotalResolved = data.TotalResolved
    };
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long Id { get; set; }
    public LocalDate Date { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public Guid UserId { get; set; }
    public string AgentName { get; set; }
    public Instant? Start { get; set; }
    public Instant? LastModified { get; set; }
    public int TotalAssigned { get; set; }
    public int TotalOpened { get; set; }
    public int TotalClosed { get; set; }
    public int TotalEscalated { get; set; }
    public decimal TotalAmountCollected { get; set; }
    public int TotalPTP { get; set; }
    public decimal TotalPTPAmount { get; set; }
    public int TotalSchedulesSetup { get; set; }
    public int TotalContacted { get; set; }
    public int TotalResolved { get; set; }
}


﻿@page "/customers/autodiscountsetup"
@using LendQube.Entities.Collection.Collections
@using LendQube.Entities.Collection.Setup
@using LendQube.Infrastructure.Collection.ViewModels.CollectionUpload
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using Radzen
@using Radzen.Blazor

@inherits GenericCrudTable<AutoDiscountConfig>

@attribute [Authorize(Policy = ManageCustomersNavigation.AutoDiscountConfigIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-xlg">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-xlg">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<AutoDiscountConfig> FormContent => context =>@<div>

        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="grid-col-2">

            <div class="form-row">
                <label class="form-label" for="Rule">Rule</label>
                <RadzenDropDown @bind-Value=@context.Rule Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<AutoDiscountRule>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="Rule" Placeholder="Select discount rule" class="form-input"
                                FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" FilterOperator="StringFilterOperator.StartsWith" AllowFiltering="true" />
                <ValidationMessage For="() => context.Rule" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="FileUploadId">Apply to Upload</label>
                <RadzenDropDown @bind-Value=@context.FileUploadId Data=@uploads
                                TextProperty="@nameof(FileUploadVM.Name)" ValueProperty="@nameof(FileUploadVM.Id)"
                                Name="FileUploadId" Placeholder="Select upload" class="form-input"
                                FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
                <ValidationMessage For="() => context.FileUploadId" class="text-danger" />
                <small class="text_sm_medium text_small">Leave empty for general discount with other criteria</small>
            </div>
        </div>
        @if (context.Rule == AutoDiscountRule.SetupWithinDays)
        {
            <div class="grid-col-2">
                <div class="form-row">
                    <label class="form-label" for="MinDays">Min Days</label>
                    <InputNumber @bind-Value="context.MinDays" class="form-input" aria-required="true" placeholder="Min Days" />
                    <ValidationMessage For="() => context.MinDays" class="text-danger" />
                </div>
                <div class="form-row">
                    <label class="form-label" for="MaxDays">Max Days</label>
                    <InputNumber @bind-Value="context.MaxDays" class="form-input" aria-required="true" placeholder="Max Days" />
                    <ValidationMessage For="() => context.MaxDays" class="text-danger" />
                </div>
            </div>
        }
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="Amount">Discount Amount</label>
                <InputNumber @bind-Value="context.Amount" class="form-input" aria-required="true" placeholder="Amount" />
                <ValidationMessage For="() => context.Amount" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="Percentage">% Discount on Balance</label>
                <InputNumber @bind-Value="context.Percentage" class="form-input" aria-required="true" placeholder="Percentage" />
                <ValidationMessage For="() => context.Percentage" class="text-danger" />
            </div>
        </div>
        <div class="form-row">
            <label class="form-label" for="UpperLimit">Max Discount Applicable</label>
            <InputNumber @bind-Value="context.UpperLimit" class="form-input" aria-required="true" placeholder="Upper Limit" />
            <ValidationMessage For="() => context.UpperLimit" class="text-danger" />
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <div class="check-group">
                    <label class="check-label">
                        Do not apply if discount amount settles account
                        <InputCheckbox class="check-input" @bind-Value="context.DoNotApplyIfDiscountSettlesAccount" />
                        <span class="checkmark"></span>
                    </label>
                </div>
            </div>
            <div class="form-row">
                <div class="check-group">
                    <label class="check-label">
                        Apply discount to new accounts only
                        <InputCheckbox class="check-input" @bind-Value="context.ApplyToNewAccountsOnly" />
                        <span class="checkmark"></span>
                    </label>
                </div>
            </div>
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <div class="check-group">
                    <label class="check-label">
                        Apply discounts to only accounts with no discount
                        <InputCheckbox class="check-input" @bind-Value="context.ApplyToOnlyAccountsWithNoDiscount" />
                        <span class="checkmark"></span>
                    </label>
                </div>
            </div>
            <div class="form-row">
                <div class="check-group">
                    <label class="check-label">
                        Apply discount now
                        <InputCheckbox class="check-input" @bind-Value="context.ApplyDiscountNow" />
                        <span class="checkmark"></span>
                    </label>
                </div>
            </div>
        </div>
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Auto apply discount to customers that meet criteria
                    <InputCheckbox class="check-input" @bind-Value="context.AutoApplyDiscount" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </div>
    ;


    private List<FileUploadVM> uploads = [];
    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "Auto Discounts";
        FormBaseTitle = "Auto Discount Config";
        CreatePermission = ManageCustomersNavigation.AutoDiscountConfigCreatePermission;
        EditPermission = ManageCustomersNavigation.AutoDiscountConfigEditPermission;
        DeletePermission = ManageCustomersNavigation.AutoDiscountConfigDeletePermission;
        uploads = Service.CrudService.Db.ManySelect(Query<CollectionFileUpload, FileUploadVM>.Where(x => x.Type == CollectionFileUploadType.Placement).Select(x => new FileUploadVM(x.Id, x.Description)));
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter);
    }
}
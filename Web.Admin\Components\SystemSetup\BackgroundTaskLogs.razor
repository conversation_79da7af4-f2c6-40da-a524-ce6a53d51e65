﻿@page "/backgroundtasklogs"

@using LendQube.Entities.Core.BackgroundTasks
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inject GenericSpecificationService<BackgroundTaskEventLog> Service

@inherits BaseTableComponentBase

@attribute [Authorize(Policy = SetupNavigation.BackgroundTaskLogsPermission)]

<PageTitle>@Title</PageTitle>
<div class="pg-row grid grid-col-1 grid-tab-1">
	<div class="card">

        <div class="title-wrapper flex __justify-between __align-center">
            <span class="text_xl_medium">@SubTitle</span>
        </div>
        <StatusMessage @ref="TableMessage" />

		<DataTable T="BackgroundTaskEventLog" TableDefinition="TableDefinition" LoadData="Load" EditRow="StartEdit" DeleteRow="SubmitDelete" CheckboxSelectionEvent="RegisterSelection" @ref="table" />

    </div>
</div>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
					ModalCss="width-md">
	<BodyContent>
		<div class="form-row">
			<label class="form-label" for="Status">Status</label>
			<InputSelect @bind-Value="context.Status">
				<option label="Update Status" selected></option>
				@foreach (var option in Enum.GetValues<BackgroundEventStatus>())
				{
					<option value="@option">@option.GetDisplayName()</option>
				}
			</InputSelect>
			<ValidationMessage For="() => context.Status" class="text-danger" />
		</div>
	</BodyContent>
</ModalEditComponent>

<ModalComponent Policy="@DeletePermission" ModalId="@ClearLogsModalName" ModalCss="width-xlg" Title=@($"Clear {FormBaseTitle}") >
	<BodyContent>
		Are you sure you want to clear all logs?
	</BodyContent>
	<FooterContent>
		<button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
		<LoadButton Label="Yes" OnClick="ClearLogs" />
	</FooterContent>
</ModalComponent>


@code
{
	private DataTable<BackgroundTaskEventLog> table;
	private BackgroundTaskEventLog SelectedLog { get; set; }
	private string ClearLogsModalName => "ClearLogsModal";
	private int selectedLogsCount = 0;

	[SupplyParameterFromForm]
	protected BackgroundTaskEventLog EditModel { get; set; }

	protected override void OnInitialized()
	{
		Title = "Background Task Logs";
		SubTitle = "All Tasks";
		FormBaseTitle = "Background Task Event Logs";
		EditPermission = SetupNavigation.BackgroundTaskLogsEditPermission;
		DeletePermission = SetupNavigation.BackgroundTaskLogsClearLogsPermission;
	}

	protected override async Task OnInitializedAsync()
	{
		if (TableDefinition == null)
		{
			EditModel = new();
			await base.OnInitializedAsync();

			AddTopButton(EditPermission, new TopActionButton("Multiple Requeue", Icon: "codepen", Action: async () =>
			{
				CloseMessage();
				table.Loading = true;
				await RequeueMany();
				table.Loading = false;

				StateHasChanged();
			}));

			AddTopButton(DeletePermission, new TopActionButton("Clear Logs", "btn--danger", Icon: "trash-2", ModalName: ClearLogsModalName, Action: () => CloseMessage(ModalMessage)));

			AddRowButton(SetupNavigation.BackgroundTaskLogsEditPermission, new RowActionButton("Requeue", Icon: "refresh-cw", Action: async (object row) =>
			{
				CloseMessage();
				table.Loading = true;
				SelectedLog = row as BackgroundTaskEventLog;
				await RequeueSingle(SelectedLog.Id);
				table.Loading = false;

				StateHasChanged();
			}));
		}
	}

	protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition();

	protected ValueTask<bool> SubmitDelete(BackgroundTaskEventLog data, Func<Task> refresh, CancellationToken ct) => SaveDelete(async () =>
	{
		var result = await Service.CrudService.Delete(x => x.Id == data.Id, ct);
		if (!result)
		{
			CustomMessage = $"Unable to delete log with Id {data.Id}";
		}
		return result;
	}, refresh);

	private ValueTask<TypedBasePageList<BackgroundTaskEventLog>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
	{
		CloseMessage();
		return Service.CrudService.GetTypeBasedPagedData(Service, filterAndPage, ct: ct);
	}

	private async Task RequeueSingle(long id)
	{
		var success = await Service.CrudService.UpdateWithFilter(x => x.Id == id, x => x.SetProperty(y => y.Status, BackgroundEventStatus.Queued).SetProperty(y => y.Tries, 0), Cancel);

		await table.Refresh();

		TableMessage.Set(success, $"Job {id} requeued", $"Unable to requeue Job {id}");
	}

	private async Task RequeueMany()
	{
		var logsToQueue = table.CheckboxChanges.Values.SelectMany(x => x.Select(y => y)).Where(x => x.NewValue).Select(y => y.Row as BackgroundTaskEventLog);

		if(logsToQueue.Count() == 0)
		{
			TableMessage.Info("No logs have been queued up to be processed");
			return;
		}

		var logsToQueueIds = logsToQueue.Select(x => x.Id);

		var success = await Service.CrudService.UpdateWithFilter(x => logsToQueueIds.Contains(x.Id), x => x.SetProperty(y => y.Status, BackgroundEventStatus.Queued).SetProperty(y => y.Tries, 0), Cancel);

		await table.Refresh();


		TableMessage.Set(success, "Selected logs have been queued up to be processed", $"Unable to queue up selected logs");
	}

	private void RegisterSelection(int count)
	{
		selectedLogsCount = count;
		StateHasChanged();
	}

	protected ValueTask StartEdit(BackgroundTaskEventLog data, CancellationToken ct) => BaseEdit(() =>
	{
		EditModel = data;
		return Task.CompletedTask;
	}, ct);

	public ValueTask SubmitEdit() => BaseSaveEdit(async () =>
	{
		var result = await Service.CrudService.Update(EditModel, Cancel);
		if (!result)
			CustomMessage = $"Unable to update job with Id {EditModel.Id}";
		return result;
	},
	() =>
	{
		EditModel = new();
		return table.Refresh();
	});

	private async Task ClearLogs()
	{
		var success = await Service.CrudService.Db.TruncateTable<BackgroundTaskEventLog>(Cancel);
		await JSRuntime.CloseModal(ClearLogsModalName, Cancel);
		await table.Refresh();

		TableMessage.Set(success, "All logs cleared", $"Unable clear all logs");
	}
}

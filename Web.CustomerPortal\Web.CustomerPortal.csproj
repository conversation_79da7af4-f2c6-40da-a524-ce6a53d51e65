<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Components.QuickGrid" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.0.4" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.NodaTime" Version="9.0.4" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Infrastructure.Collection\Infrastructure.Collection.csproj" />
    <ProjectReference Include="..\Infrastructure.Core\Infrastructure.Core.csproj" />
    <ProjectReference Include="..\Entities.Collection\Entities.Collection.csproj" />
    <ProjectReference Include="..\Entities.Core\Entities.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Include="AppSettings\**\*" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>

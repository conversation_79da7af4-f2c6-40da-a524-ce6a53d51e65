﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using NodaTime;

namespace LendQube.Infrastructure.Collection.ViewModels.PlacementData;

public class PlacementApiVM
{
    public static readonly Expression<Func<Placement, PlacementApiVM>> Mapping = data => new PlacementApiVM
    {
        Id = data.SourceAccountNumber,
        Company = data.Company,
        BalancePaid = data.BalancePaid,
        BalanceTotal = data.BalanceTotal,
        BalanceRemaining = data.BalanceRemaining,
        ProfileCurrency = data.Profile.CurrencySymbol,
        Status = data.Status,
        CreatedDate = data.CreatedDate,
        LastModifiedDate = data.LastModifiedDate,

    };
    public string Id { get; set; }
    public string Company { get; set; }
    public decimal BalanceTotal { get; set; }
    public decimal BalancePaid { get; set; }
    public decimal BalanceRemaining { get; set; }
    public string ProfileCurrency { get; set; }
    public PlacementStatus Status { get; set; }
    public Instant? CreatedDate { get; set; }
    public Instant? LastModifiedDate { get; set; }

}

public class PlacementTransactionApiVM
{
    public static readonly Expression<Func<PlacementTransaction, PlacementTransactionApiVM>> Mapping = data => new PlacementTransactionApiVM
    {
        AccountId = data.Placement.SourceAccountNumber,
        Company = data.Placement.Company,
        AmountPaid = $"{data.Placement.Profile.CurrencySymbol}{data.AmountPaid:n2}",
        Date = data.CreatedDate,
        PaymentMethod = data.PaymentMethod,
        TransactionId = data.TransactionId,
        PaymentType = data.PaymentType
    };

    public string Company { get; set; }
    public string AccountId { get; set; }
    public string TransactionId { get; set; }
    public string PaymentMethod { get; set; }
    public string AmountPaid { get; set; }
    public string PaymentType { get; set; }
    public Instant? Date { get; set; }
}

public class CustomerTransactionApiVM
{
    public static readonly Expression<Func<CustomerTransaction, CustomerTransactionApiVM>> Mapping = data => new CustomerTransactionApiVM
    {
        TransactionId = data.TransactionId,
        AmountPaid = $"{data.Profile.CurrencySymbol}{data.AmountPaid:n2}",
        PaymentType = data.PaymentType,
        PaymentMethod = data.PaymentMethod,
        Date = data.CreatedDate
    };
    public string TransactionId { get; set; }
    public string PaymentMethod { get; set; }
    public string AmountPaid { get; set; }
    public string PaymentType { get; set; }
    public Instant? Date { get; set; }
}

public class InitiateTransactionRequest
{
    [Required]
    public decimal Amount { get; set; }
    [Required]
    public RepaymentTransactionType Type { get; set; }
    public bool NotifyCustomer { get; set; }
}

public enum RepaymentTransactionType
{
    ReduceBalance,
    RecalculateRepayment,
    AmendSchedule
}
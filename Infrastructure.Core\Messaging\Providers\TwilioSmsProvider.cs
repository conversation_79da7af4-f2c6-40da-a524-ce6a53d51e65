﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using Twilio;
using Twilio.Rest.Api.V2010.Account;
using Twilio.Types;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class TwilioSmsProvider : AbstractMessageProvider, ITextMessageProvider
{
    public const string Name = "Twilio Sms";
    protected override MessageChannel SupportedChannel => MessageChannel.Sms;
    private readonly DefaultAppConfig config;

    public TwilioSmsProvider(IUnitofWork uow, DefaultAppConfig config) : base(uow)
    {
        this.config = config;

        if(config.Twilio != null)
        {
            TwilioClient.Init(config.Twilio.Sid, config.Twilio.Token);
            SupportedCountryCodes = MessagingCompiledQueries.GetMessageProviderSupportedCountriesAndConfig(uow, Name);
            Config = SupportedCountryCodes.IsNullOrEmpty() ? new ProviderConfigVM { Disabled = true } : SupportedCountryCodes[0];
        }
        else
        {
            Config = new ProviderConfigVM { Disabled = true };
        }
    }

    public override ProviderConfigVM Config { get; }
    public IReadOnlyList<ProviderConfigVM> SupportedCountryCodes { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        Dictionary<long, MessageStatus> results = [];
        var leadingMessage = messages[0];

        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        var from = new PhoneNumber(config.Twilio.Call);

        await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count, CancellationToken = ct }, async (message, ct) =>
        {
            await Parallel.ForEachAsync(message.Data.Select((x, i) => (Value: x, Index: i)), new ParallelOptions { MaxDegreeOfParallelism = message.Data.Count, CancellationToken = ct }, async (recipient, ct) =>
            {
                await Parallel.ForEachAsync(recipient.Value.PhoneNumbers, new ParallelOptions { MaxDegreeOfParallelism = recipient.Value.PhoneNumbers.Count, CancellationToken = ct }, async (phoneNumber, ct) =>
                {

                    var result = await MessageResource.CreateAsync(new CreateMessageOptions(new PhoneNumber($"{phoneNumber.Code}{phoneNumber.Number}"))
                    {
                        From = from,
                        Body = recipient.Value.TextTemplate ?? message.TextTemplate,
                        StatusCallback = new Uri(string.Format(config.Url.WebHook, "twilo/receivemessagestatus"))
                    });

                    results[message.MessageId + recipient.Index] = results.GetValueOrDefault(message.MessageId + recipient.Index) == MessageStatus.Sent ? MessageStatus.Sent : (result.Status != MessageResource.StatusEnum.Failed).ToMessageStatus();
                });
            });
        });

        var status = results.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, results, null, null, ct);
        return status;
    }
}

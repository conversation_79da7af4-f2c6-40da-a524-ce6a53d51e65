﻿@using LendQube.Infrastructure.Core.DependencyInjection
@using LendQube.Web.Admin.Components.Account.Shared
<CascadingAuthenticationState>
    <Router AppAssembly="typeof(Program).Assembly" AdditionalAssemblies="AdminDI.RoutableAssemblies">
        <Found Context="routeData">
            <AuthorizeRouteView RouteData="routeData" DefaultLayout="typeof(Layout.MainLayout)">
                <NotAuthorized>
                    <RedirectToLogin />
                </NotAuthorized>
            </AuthorizeRouteView>
        </Found>
    </Router>
</CascadingAuthenticationState>

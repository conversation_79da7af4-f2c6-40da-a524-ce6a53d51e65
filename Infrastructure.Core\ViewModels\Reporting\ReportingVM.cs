﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Entities.Core.Reporting;
using NodaTime;

namespace LendQube.Infrastructure.Core.ViewModels.Reporting;

public class ReportScheduleVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<ReportSchedule, ReportScheduleVM>> Mapping = data => new()
    {
        Id = data.Id,
        Description = data.Description,
        ReportTypeName = data.ReportTypeName,
        ReportNames = data.ReportNames,
        FileType = data.FileType,
        Frequency = data.Frequency,
        FrequencyNumber = data.FrequencyNumber,
        Action = data.Action,
        StartDate = data.StartDate,
        EndDate = data.EndDate,
        LastRunDate = data.LastRunDate,
        RunCount = data.RunCount,
        Starting = data.Starting,
        Disabled = data.Disabled,
        TimeZone = data.TimeZone,
        Days = data.Days,
        MessageGroup = data.MessagingGroup.Name,
        MessageConfig = data.Config.Name,
        MessagingGroupId = data.MessagingGroupId,
        ConfigId = data.ConfigId,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public ReportSchedule Get() => new()
    {
        Id = Id,
        Description = Description,
        ReportTypeName = ReportTypeName,
        ReportNames = ReportNames,
        FileType = FileType,
        Frequency = Frequency,
        FrequencyNumber = FrequencyNumber,
        Action = Action,
        Days = DaysList.CombineFlags(),
        StartDate = StartDate,
        EndDate = EndDate,
        TimeZone = TimeZone,
        Disabled = Disabled,
        Starting = !Disabled,
        RunCount = RunCount,
        ConfigId = ConfigId,
        MessagingGroupId = MessagingGroupId,
    };


    [Required, MaxLength(EntityConstants.DEFAULT_DESCRIPTION_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Description { get; set; }

    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ReportTypeName { get; set; }
    [Required, MinLength(1), TableDecorator(TableDecoratorType.ShowInDelete)]
    public List<string> ReportNames { get; set; } = [];
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public ReportType FileType { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public ReportFrequency Frequency { get; set; }
    public int FrequencyNumber { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public ReportScheduleAction Action { get; set; }
    public Instant? StartDate { get; set; }
    public Instant? EndDate { get; set; }
    public Instant? LastRunDate { get; set; }
    public int RunCount { get; set; }
    public bool Starting { get; set; }
    public bool Disabled { get; set; }
    public string TimeZone { get; set; }
    public string MessageGroup { get; set; }
    public string MessageConfig { get; set; }

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public ScheduleDay Days { get; set; }

    [Required, TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long MessagingGroupId { get; set; }

    [Required, TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long ConfigId { get; set; }

    [DisplayName("Days")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string DaysFormatted => string.Join(", ", Days.FlagsToDisplayList<ScheduleDay>());

    [RemoveColumn]
    public List<ScheduleDay> DaysList { get; set; } = [];
}

public class SystemReportLogVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<SystemReport, SystemReportLogVM>> Mapping = data => new()
    {
        Id = data.Id,
        TypeName = data.TypeName,
        Names = data.Names,
        Status = data.Status,
        FileType = data.FileType,
        Description = data.Description,
        FileUrl = data.FileUrl,
        Message = data.Message,
        StartDate = data.StartDate,
        EndDate = data.EndDate,
        RunCount = data.RunCount,
        AutoRun = data.AutoRun,
        Schedule = data.ReportSchedule.Description,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public string TypeName { get; set; }
    public List<string> Names { get; set; }
    public ReportStatus Status { get; set; }
    public ReportType FileType { get; set; }
    public string Description { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public string FileUrl { get; set; }
    public string Message { get; set; }
    public Instant? StartDate { get; set; }
    public Instant? EndDate { get; set; }
    public int RunCount { get; set; }
    public bool AutoRun { get; set; }
    public string Schedule { get; set; }
}
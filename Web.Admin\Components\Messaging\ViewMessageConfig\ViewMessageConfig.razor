﻿@page "/messaging/configuration/{MessageId:long}"
@using LendQube.Entities.Core.Messaging
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Components.Timeline
@using LendQube.Infrastructure.Core.Database.Repository
@using LendQube.Infrastructure.Core.FileManagement
@using LendQube.Infrastructure.Core.Helpers.Utils
@using LendQube.Infrastructure.Core.Messaging
@using LendQube.Infrastructure.Core.Messaging.Configuration
@using LendQube.Infrastructure.Core.ViewModels.Messaging
@using Ra<PERSON>zen
@using Radzen.Blazor
@using System.Text

@attribute [Authorize(Policy = MessagingNavigation.MessageConfigurationEditPermission + "," + MessagingNavigation.MessageConfigurationCreatePermission)]
 
@inherits AuthComponentBase

<PageTitle>@Title</PageTitle>
<StatusMessage @ref="message" />
<div class="pg-actions">
    <ul class="breadcrumbs">
        <li class="__item">
            <a href="/messaging/configuration">
                <i data-feather="arrow-left"></i>
            </a>
        </li>
        <li class="__item">
            <a>@Title: @Data?.Name</a>
        </li>
    </ul>
    <div class="actions-item ml-auto">
        @if(hasPendingChanges)
        {
            <LoadButton Label="Discard Changes" OnClick="Reload" Css="btn--danger" />
            <LoadButton Label="Save Changes" OnClick="SaveData" Css="btn--success" />
        }

        @if(!Data.NeedsConfiguration)
        {
            if (HasGroupConfigured)
            {
                <LoadButton Label="Send Message" OnClick="StartSendMessage" Css="btn--primary" />
            }
            <a class="btn btn--gray" href="/messaging/scheduler">
                Schedule Message
            </a>
        }
    </div>
</div>
<div class="pg-row grid loan-grid grid-tab-1">
    <div class="info-col">
        <div class="card info-item">
            <div class="__header flex __justify-between">
                <div class="title">
                    <span class="__title">@Data?.Name</span>
                </div>
            </div>
            <div class="detail-wrapper grid-2">
                <div class="detail-item">
                    <span class="label">Description</span>
                    <span class="value">@Data?.Description</span>
                </div>
                <div class="detail-item">
                    <span class="label">Subject</span>
                    <span class="value">@Data?.Subject</span>
                </div>
                <div class="detail-item">
                    <span class="label">Channels</span>
                    <span class="value">@(Data?.Channels == null ? "None" : string.Join(", ", Data?.Channels))</span>
                </div>
                <div class="detail-item">
                    <span class="label">Keys</span>
                    <span class="value">@(Data?.Keys == null ? "None" : string.Join(", ", Data?.Keys))</span>
                </div>
                <div class="detail-item">
                    <span class="label">Text Configured</span>
                    <span class="value @(Data?.TextConfigured ?? false ? "__verified" : "")">@(!Data?.TextRequired ?? false ? "Not Required" : Data?.TextConfigured ?? false ? "Yes" : "No")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Email Configured</span>
                    <span class="value @(Data?.EmailConfigured ?? false ? "__verified" : "")">@(!Data?.EmailRequired ?? false ? "Not Required" : Data?.EmailConfigured ?? false ? "Yes" : "No")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Total Directories</span>
                    <span class="value">@(Data?.SendDirectory?.Count ?? 0)</span>
                </div>
            </div>
        </div>
        <div class="card activity-item">
            <div class="accordion" id="activityAccordion">
                <div class="accordion-item">
                    <span class="accordion-header" id="activityHeading">
                        <button class="accordion-button" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapseActivity" aria-expanded="true"
                                aria-controls="collapseActivity">
                            Activity
                        </button>
                    </span>
                    <div id="collapseActivity"
                         class="accordion-collapse collapse show"
                         aria-labelledby="activityHeading"
                         data-bs-parent="#activityAccordion">
                        <div class="accordion-body">
                            @if(Data.Id != 0)
                            {
                                <ActivityTimeline T="MessagingConfigurationActivity" LoadData="LoadActivity" @ref=activityTimeline />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="details-col">
        <div class="card">
            <div class="accordion loan-details" id="loanAccordion">

                <div class="accordion-item">
                    <h2 class="accordion-header" id="messageTemplateHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#messageTemplateCollapse" aria-expanded="true"
                                aria-controls="messageTemplateCollapse">
                            <i data-feather="mail"></i>
                            Message Template
                        </button>
                    </h2>
                    <div id="messageTemplateCollapse" class="accordion-collapse show"
                         aria-labelledby="messageTemplateHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            @if (hasTextCustomChanges || hasHtmlCustomChanges)
                            {
                                <div class="form-row">
                                    <div class="alert info hasFlex">
                                        <div class="al-flex">
                                            <div class="al-content">
                                                <span class="al__title" role="alert">
                                                    Customizations will be used for composing messages
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            @if (Data.TextRequired)
                            {
                                <div class="form-row">
                                    <PagedTextArea Model="pagedTextTemplate" Property="pagedTextTemplate => pagedTextTemplate.Text" OnChange="() => hasTextCustomChanges = hasPendingChanges = true" />
                                </div>
                            }

                            @if (Data.EmailRequired)
                            {
                                <div class="form-row">
                                    <RadzenHtmlEditor @bind-Value=@htmlTemplate style="height: 50vh" UploadUrl="upload/messagetemplates" Change="() => hasHtmlCustomChanges = hasPendingChanges = true">
                                        <RadzenHtmlEditorRedo />
                                        <RadzenHtmlEditorSeparator />
                                        <RadzenHtmlEditorBold />
                                        <RadzenHtmlEditorItalic />
                                        <RadzenHtmlEditorUnderline />
                                        <RadzenHtmlEditorStrikeThrough />
                                        <RadzenHtmlEditorSeparator />
                                        <RadzenHtmlEditorAlignLeft />
                                        <RadzenHtmlEditorAlignCenter />
                                        <RadzenHtmlEditorAlignRight />
                                        <RadzenHtmlEditorJustify />
                                        <RadzenHtmlEditorSeparator />
                                        <RadzenHtmlEditorIndent />
                                        <RadzenHtmlEditorOutdent />
                                        <RadzenHtmlEditorUnorderedList />
                                        <RadzenHtmlEditorOrderedList />
                                        <RadzenHtmlEditorSeparator />
                                        <RadzenHtmlEditorColor />
                                        <RadzenHtmlEditorBackground />
                                        <RadzenHtmlEditorRemoveFormat />
                                        <RadzenHtmlEditorSeparator />
                                        <RadzenHtmlEditorSubscript />
                                        <RadzenHtmlEditorSuperscript />
                                        <RadzenHtmlEditorSeparator />
                                        <RadzenHtmlEditorLink />
                                        <RadzenHtmlEditorUnlink />
                                        <RadzenHtmlEditorImage />
                                        <RadzenHtmlEditorFontName>
                                            <RadzenHtmlEditorFontNameItem Text="HK Grotesk" Value="'HK Grotesk'" />
                                            <RadzenHtmlEditorFontNameItem Text="Segoe UI" Value="'Segoe UI'" />
                                            <RadzenHtmlEditorFontNameItem Text="Roboto" Value="'Roboto'" />
                                            <RadzenHtmlEditorFontNameItem Text="Oxygen-Sans" Value="'Oxygen-Sans'" />
                                            <RadzenHtmlEditorFontNameItem Text="Ubuntu" Value="'Ubuntu'" />
                                            <RadzenHtmlEditorFontNameItem Text="Cantarell" Value="'Cantarell'" />
                                            <RadzenHtmlEditorFontNameItem Text="Helvetica Neue" Value="'Helvetica Neue'" />
                                            <RadzenHtmlEditorFontNameItem Text="Sans Serif" Value="'sans-serif'" />
                                        </RadzenHtmlEditorFontName>
                                        <RadzenHtmlEditorFontSize />
                                        <RadzenHtmlEditorFormatBlock />
                                        <RadzenHtmlEditorSeparator />
                                        <RadzenHtmlEditorSource />
                                    </RadzenHtmlEditor>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="containerTemplateHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#containerTemplateCollapse" aria-expanded="false"
                                aria-controls="containerTemplateCollapse">
                            <i data-feather="at-sign"></i>
                            Container Template
                        </button>
                    </h2>
                    <div id="containerTemplateCollapse" class="accordion-collapse collapse"
                         aria-labelledby="containerTemplateHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">

                            @if (hasPendingChanges)
                            {
                                <div class="form-row">
                                    <div class="alert info hasFlex">
                                        <div class="al-flex">
                                            <div class="al-content">
                                                <span class="al__title" role="alert">
                                                    Selecting a template will wipe out your custom changes. Please save first if you have made any changes to the message template and wish to keep them
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            <div class="form-row">

                                <RadzenDropDown TValue="long?" Value=@Data.ContainerTemplateId Data=@ConfiguredTemplates.Where(x => x.IsContainer) Name="Container" TextProperty="@nameof(MessagingTemplate.Name)" ValueProperty="@nameof(MessagingTemplate.Id)"
                                                AllowClear=true Placeholder="Select container template" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" FilterOperator="StringFilterOperator.StartsWith" AllowFiltering="true"
                                                Change="(e) => OnSelectContainerTemplate(e as long?)" class="form-input">
                                    <Template>
                                        @((context as MessagingTemplate).Name) (@((context as MessagingTemplate).Description))
                                    </Template>
                                </RadzenDropDown>
                            </div>

                            @if (Data?.ContainerTemplate != null && Data.TextRequired)
                            {
                                <div class="form-row">
                                    <label class="form-label" for="Description">Text Template</label>
                                    <PagedTextArea Model="Data" Property="Data => Data.ContainerTemplate.TextTemplate" IsReadOnly="true" TextAreaId="Description"/>
                                </div>
                            }

                            @if (Data?.ContainerTemplate != null && Data.EmailRequired)
                            {
                                <div class="form-row">
                                    <RadzenHtmlEditor @bind-Value=@Data.ContainerTemplate.HtmlTemplate style="height: 50vh" ShowToolbar="false"Disabled="true"/>
                                </div>
                            }
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="bodyTemplateHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#bodyTemplateCollapse" aria-expanded="false"
                                aria-controls="bodyTemplateCollapse">
                            <i data-feather="at-sign"></i>
                            Body Template
                        </button>
                    </h2>
                    <div id="bodyTemplateCollapse" class="accordion-collapse collapse"
                         aria-labelledby="bodyTemplateHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            @if(hasPendingChanges)
                            {
                                <div class="form-row">
                                    <div class="alert info hasFlex">
                                        <div class="al-flex">
                                            <div class="al-content">
                                                <span class="al__title" role="alert">
                                                    Selecting a template will wipe out your custom changes. Please save first if you have made any changes to the message template and wish to keep them
                                                </span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }

                            <div class="form-row">

                                <RadzenDropDown TValue="long?" Value=@Data.BodyTemplateId Data=@ConfiguredTemplates.Where(x => !x.IsContainer) Name="Container" TextProperty="@nameof(MessagingTemplate.Name)" ValueProperty="@nameof(MessagingTemplate.Id)"
                                                AllowClear=true Placeholder="Select body template" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" FilterOperator="StringFilterOperator.StartsWith" AllowFiltering="true"
                                                Change="(e) => OnSelectBodyTemplate(e as long?)" class="form-input">
                                    <Template>
                                        @((context as MessagingTemplate).Name) (@((context as MessagingTemplate).Description))
                                    </Template>
                                </RadzenDropDown>
                            </div>

                            @if (Data?.BodyTemplate != null && Data.TextRequired)
                            {
                                <div class="form-row">
                                    <label class="form-label" for="Description">Text Template</label>
                                    <PagedTextArea Model="Data" Property="Data => Data.BodyTemplate.TextTemplate" IsReadOnly="true" TextAreaId="Description" />
                                </div>
                            }

                            @if (Data?.BodyTemplate != null && Data.EmailRequired)
                            {
                                <div class="form-row">
                                    <RadzenHtmlEditor @bind-Value=@Data.BodyTemplate.HtmlTemplate style="height: 50vh" ShowToolbar="false" Disabled="true"/>
                                </div>
                            }

                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="sendDirectoryHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#sendDirectoryCollapse" aria-expanded="false"
                                aria-controls="sendDirectoryCollapse">
                            <i data-feather="align-center"></i>
                            Directory
                        </button>
                    </h2>
                    <div id="sendDirectoryCollapse" class="accordion-collapse collapse"
                         aria-labelledby="sendDirectoryHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">

                            <DataTable T="MessagingGroupVM" DeferLoading="true" TableDefinition="GroupTableDefinition" DeleteRow="SubmitGroupDelete" LoadData="LoadGroups" @ref="groupTable" />
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<ModalEditComponentWithoutPolicy Model="@DirectoryModel" OnValidSubmit="@SubmitNewDirectory" FormName="NewDirectoryForm" Title="New Directory"
                                 ModalId="@addGroupModal" ModalMessage="modalMessage" ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="AllGroups">Groups</label>

            <RadzenDropDown @bind-Value=@context.Groups Data=@AllGroups TextProperty="@nameof(MessagingGroup.Name)" ValueProperty="@nameof(MessagingGroup.Id)"
                            Name="AllGroups" Multiple=true AllowClear=true Placeholder="Select groups" Chips=true FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" 
                            FilterOperator="StringFilterOperator.StartsWith" AllowFiltering="true" class="form-input" />

            <ValidationMessage For="() => context.Groups" class="text-danger" />
        </div>
    </BodyContent>

</ModalEditComponentWithoutPolicy>

<ModalEditComponentWithoutPolicy Model="@SendMessageModel" ModalId="@sendMessageModal" ModalCss="width-md" ModalMessage="modalMessage" FormName="SendMessageForm" Title="@sendMessageModal" OnValidSubmit="@SendMessage"
                                 SubmitButtonText="Queue">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Subject">Subject</label>
            <InputText @bind-Value="context.Subject" class="form-input" aria-required="true" placeholder="Subject" />
            <ValidationMessage For="() => context.Subject" class="text-danger" />
        </div>

        @if (Data.Keys != null)
        {
            foreach (var item in Data.Keys)
            {
                SendMessageModel.KeysWithValues[item] = null;
                <div class="form-row">
                    <label class="form-label" for="@item">Template Key: @item</label>
                    <input type="text" class="form-input" aria-required="true" placeholder="@item" @onchange="(e) => SetTemplateValue(e, item)" />
                    @if (systemKeys.Contains(item))
                    {
                        <small class="text_sm_medium text_small">This is a system key, leave empty for automatic substitution</small>
                    }
                </div>
            }
        }
    </BodyContent>
</ModalEditComponentWithoutPolicy>

<RadzenDialog />
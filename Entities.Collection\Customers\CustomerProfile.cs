﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Enums;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;
using SecurityDriven;

namespace LendQube.Entities.Collection.Customers;

public class CustomerProfile : BaseEntityWithGuid, IEntityHasEnum, IEntityTypeConfiguration<CustomerProfile>
{
    [Required, TableDecorator(TableDecoratorType.ShowInDelete)]
    public string AccountId { get; set; }

    [DbComputed($@"""{nameof(FirstName)}""  || ' ' || ""{nameof(LastName)}""")]
    public string FullName { get; set; }
    [StringLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.HideColumn)]
    public string FirstName { get; set; }
    [StringLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.HideColumn)]
    public string MiddleName { get; set; }
    [StringLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.HideColumn)]
    public string LastName { get; set; }
    [StringLength(EntityConstants.DEFAULT_EMAIL_FIELD_LENGTH)]
    public string Email { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public PhoneNumber MobileNumber { get; set; }
    public PhoneNumber? PhoneNumber { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public DateOnly? DateOfBirth { get; set; }
    public Gender Gender { get; set; }
    public string CountryCode { get; set; }
    [RemoveColumn]
    public string CurrencySymbol { get; set; }
    [StringLength(EntityConstants.DEFAULT_CURRENCY_FIELD_LENGTH), Required]
    public string CurrencyCode { get; set; }
    public bool Blacklisted { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public Instant? LastRescheduleDate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public Instant? NextRescheduleDate => LastRescheduleDate == null ? SystemClock.Instance.GetCurrentInstant() : LastRescheduleDate.Value.Plus(Duration.FromDays(28));
    public bool CanReschedule => TotalRescheduleCount == 0 || LastRescheduleDate == null || (SystemClock.Instance.GetCurrentInstant() - LastRescheduleDate.Value) >= Duration.FromDays(28);
    [TableDecorator(TableDecoratorType.HideColumn)]
    public int TotalRescheduleCount { get; set; }
    public decimal BalanceTotal { get; set; }
    public decimal BalancePaid { get; set; }
    public decimal Discount { get; set; }
    [DbComputed($@"""{nameof(BalanceTotal)}"" - ""{nameof(BalancePaid)}"" - ""{nameof(Discount)}"" - ""{nameof(SettlementAmount)}""")]
    public decimal BalanceRemaining { get; set; }
    public decimal SettlementAmount { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public SchedulePaymentFrequency? PaymentFrequency { get; set; }
    public Instant? LastAssignedDate { get; set; }
    public bool CurrentlyAssigned { get; set; }
    public DateOnly? NextCallbackDate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long? FileUploadId { get; set; }
    public virtual CollectionFileUpload FileUpload { get; set; }
    public virtual CustomerIncomeAndExpenditure IncomeAndExpenditure { get; set; }
    public virtual ICollection<CustomerAddress> Addresses { get; set; } = [];
    public virtual ICollection<Placement> Placements { get; set; }
    public virtual ICollection<CustomerSchedule> Schedules { get; set; } = [];
    public virtual ICollection<CustomerTransaction> Transactions { get; set; }
    public virtual ICollection<Transaction> AllTransactions { get; set; }
    public virtual ICollection<CustomerOneTimeCode> OneTimeCodes { get; set; }
    public virtual ICollection<CustomerActivity> Activities { get; set; }
    public virtual ICollection<CustomerNote> Notes { get; set; }
    public virtual ICollection<CustomerFlag> Flags { get; set; }
    public virtual ICollection<CustomerHold> Holds { get; set; }
    public virtual ICollection<CustomerDiscount> Discounts { get; set; }
    public virtual ICollection<CustomerPromiseToPay> PTP { get; set; }
    public virtual ICollection<CustomerPaymentMethod> PaymentMethods { get; set; }
    public virtual ApplicationUser AppUser { get; set; }

    public void Configure(EntityTypeBuilder<CustomerProfile> builder)
    {
        builder.HasOne(x => x.AppUser)
           .WithOne()
           .HasForeignKey<CustomerProfile>(x => x.Id)
           .HasPrincipalKey<ApplicationUser>(x => x.UserName);

        builder.OwnsOne(c => c.MobileNumber, c => c.ToJson());
        builder.OwnsOne(c => c.PhoneNumber, c => c.ToJson());

        builder.HasIndex(c => c.AccountId).IsUnique();
    }

    public void RegisterEnumInDataSource(NpgsqlDataSourceBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<SchedulePaymentFrequency>($"{CollectionEntityConfig.DefaultSchema}.{nameof(SchedulePaymentFrequency)}", nameTranslator);
    }

    public void RegisterEnumInDataSource(NpgsqlDbContextOptionsBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<SchedulePaymentFrequency>(nameof(SchedulePaymentFrequency), CollectionEntityConfig.DefaultSchema, nameTranslator);
    }

    public ApplicationUser GetUser(string domain)
    {
        var user = new ApplicationUser()
        {
            Id = FastGuid.NewPostgreSqlGuid(),
            UserName = Id,
            NormalizedUserName = Id.ToUpperInvariant(),
            FirstName = FirstName,
            LastName = LastName,
            OtherNames = MiddleName,
            Email = string.IsNullOrEmpty(Email) ? $"{Id}@{domain}" : Email,
            NormalizedEmail = Email.ToUpperInvariant(),
            PhoneCode = MobileNumber?.Code,
            PhoneNumber = MobileNumber?.Number,
            RegistrationDate = SystemClock.Instance.GetCurrentInstant(),
            MustChangePasswordOn = null,
            Role = SystemRoleConfig.CustomerRole
        };

        return user;
    }
}


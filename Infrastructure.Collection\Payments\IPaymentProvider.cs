﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.ViewModels.Base;

namespace LendQube.Infrastructure.Collection.Payments;

public interface IPaymentProvider
{

}

internal interface ICreatesCustomerPaymentProvider
{
    Task<CustomerPaymentMethodConfiguration> CreateCustomer(CustomerScheduleVM profile, CancellationToken ct);
}

internal interface ICreatesPaymentMethodProvider
{
    Task<Result<bool>> CreateSetupCharge(CustomerPaymentMethodConfiguration config, Transaction txn, CancellationToken ct);
    Task<(TransactionStatus, CustomerPaymentMethod, bool)> ConfirmSetupCharge(IUnitofWork uow, Transaction txn, CancellationToken ct);
}

internal interface IChargeCardPaymentProvider
{
    Task<Result<bool>> CreateCharge(CustomerPaymentMethodConfiguration config, Transaction txn, CancellationToken ct);
    Task<(TransactionStatus, CustomerPaymentMethod)> ConfirmCharge(IUnitofWork uow, Transaction txn, CustomerPaymentMethod paymentMethod, CancellationToken ct);
}

internal interface ICreateAndConfirmCardChargePaymentProvider
{
    Task<TransactionStatus?> CreateAndConfirmSingleCharge(IUnitofWork uow, CreateChargeAndConfirmPaymentMethodVM config, Transaction txn, CancellationToken ct);
}

internal interface ISavesPaymentMethodPaymentProvider
{

}

internal interface IProvidesKeyPaymentProvider
{
    string GetKey();
}
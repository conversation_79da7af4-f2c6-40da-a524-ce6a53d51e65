﻿using System.IO.Compression;
using System.Reflection;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Reporting;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Core.AdminUserManagement;
using LendQube.Infrastructure.Core.Authentication;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.AnalyticsTriggers;
using LendQube.Infrastructure.Core.DateAndTime;
using LendQube.Infrastructure.Core.DeviceManagement;
using LendQube.Infrastructure.Core.Middleware;
using LendQube.Infrastructure.Core.PermissionsAndRoles;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Components.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging.AzureAppServices;
using Microsoft.Net.Http.Headers;

namespace LendQube.Infrastructure.Core.DependencyInjection;

public static class AdminDI
{
    public static readonly List<Assembly> RoutableAssemblies = [];
    public static readonly List<Type> AdminTriggerTypes = [typeof(SystemFileUpload), typeof(SystemReport)];
    public static readonly List<AnalyticsTriggerModel> AnalyticsTriggers = [];
    public static IServiceCollection AddAdminUserManagement(this IServiceCollection services)
    {
        services.AddAntiforgeryToken()
            .AddTransient<AdminUserManagerService>()
            .AddTransient<AdminRolesManagerService>()
            .AddTransient<AdminRoleClaimsManagerService>()
            .AddTransient<AdminUserRolesManagerService>()
            .AddTransient<SuperAdminAccountSeeder>();

        services.AddTransient<BackgroundTaskManageService>()
            .AddKeyedTransient<AbstractBackgroundTaskManager, DefaultBackgroundTaskManager>(BackgroundEventSource.System)
            .AddTransient<Func<BackgroundEventSource, AbstractBackgroundTaskManager>>(serviceProvider => key => serviceProvider.GetKeyedService<AbstractBackgroundTaskManager>(key));

        services.AddScoped<UserTimeProvider>();

        return services;
    }

    public static IServiceCollection AddAdminServices(this IServiceCollection services)
    {
        services.AddCascadingAuthenticationState()
            .AddTransient<AdminAuthService>()
            .AddScoped<IdentityRedirectManager>()
            .AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

        services.AddTransient<BackgroundTaskControlService>();

        return services;
    }

    public static WebApplicationBuilder AddBaseAdminService(this WebApplicationBuilder builder)
    {
        builder.AddAppSettings();
        builder.Services.Configure<AzureFileLoggerOptions>(builder.Configuration.GetSection("AzureLogging"));

        builder.Services.AddAdminIdentity()
            .AddCustomHasher();


        builder.Services.AddTransient<CustomerDeviceService>();

        if (builder.Configuration.GetValue<bool>("EnableSSL"))
        {
            builder.Services.AddResponseCompression(options =>
            {
                options.Providers.Add<BrotliCompressionProvider>();
                options.Providers.Add<GzipCompressionProvider>();
                options.EnableForHttps = true;
                options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(["image/svg+xml", "image/png", "image/jpg", "image/jpeg"]);
            });

            builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Optimal;
            });

            builder.Services.Configure<GzipCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Optimal;
            });

            builder.Services.AddHsts(options =>
            {
                options.Preload = true;
                options.IncludeSubDomains = true;
                options.MaxAge = TimeSpan.FromDays(366);
            });

            builder.Services.AddHttpsRedirection(options =>
            {
                options.RedirectStatusCode = StatusCodes.Status301MovedPermanently;
            });
        }

        builder.Services.AddGlobalRateLimiting()
            .AddMessaging(builder.Configuration);
        return builder;
    }

    public static WebApplication AddAdminApp(this WebApplication app, WebApplicationBuilder builder)
    {
        if (app.Environment.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }
        else
        {
            app.UseExceptionHandler("/error");
        }


        if (builder.Configuration.GetValue<bool>("EnableSSL"))
        {
            app.UseHsts();
            app.UseHttpsRedirection();
            app.UseResponseCompression();
        }

        app.UseMiddleware<SecureHeadersMiddleware>();

        app.UseStaticFiles(new StaticFileOptions
        {
            OnPrepareResponse = ctx =>
            {
                const int durationInSeconds = 60 * 60 * 24 * 365;
                ctx.Context.Response.Headers[HeaderNames.CacheControl] =
                    "public,max-age=" + durationInSeconds;
            }
        });

        app.UseAntiforgery();
        app.UseRateLimiter();

        return app;
    }

    public static IServiceCollection AddAdminTriggers(this IServiceCollection services)
    {
        services.AddTriggerNotification(AdminTriggerTypes);
        if (AnalyticsTriggers.Count > 0)
        {
            services.AddSingleton<ILogManager<AnalyticsTriggerHostedService>, LogManager<AnalyticsTriggerHostedService>>();
            services.AddHostedService<AnalyticsTriggerHostedService>();
        }
        return services;
    }

}
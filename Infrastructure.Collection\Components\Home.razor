﻿@page "/"
@using LendQube.Entities.Collection.Analytics
@using LendQube.Infrastructure.Core.Database.Repository
@using Radzen.Blazor

@inject IUnitofWork uow
@attribute [Authorize]

@inherits AsyncComponentBase

<PageTitle>Dashboard</PageTitle>


@if(analytics != null)
{
    <div class="pg-row grid grid-col-4 grid-tab-2">
        <div class="card">
            <div class="dropdown">
                <span class="dropdown-toggle" role="button" id="dropdownMenuLink"
                      data-bs-toggle="dropdown" aria-expanded="false">
                    @selectedCustomerAnalytics
                </span>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                    <li><a class="dropdown-item" @onclick='() => ChangeCustomerAnalytics("Total")'>Total</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeCustomerAnalytics("Signed In")'>Signed In</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeCustomerAnalytics("Created Schedule")'>Created Schedule</a></li>
                </ul>
            </div>
            <span class="card-title">Customers</span>
            <span class="card-value">@customerCount</span>
            <span class="card-label">@selectedCustomerAnalytics</span>
        </div>
        <div class="card">
            <div class="dropdown">
                <span class="dropdown-toggle" role="button" id="dropdownMenuLink"
                      data-bs-toggle="dropdown" aria-expanded="false">
                    @selectedPlacementAnalytics
                </span>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                    <li><a class="dropdown-item" @onclick='() => ChangePlacementAnalytics("Total")'>Total</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangePlacementAnalytics("Settled")'>Settled</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangePlacementAnalytics("Closed")'>Closed</a></li>
                </ul>
            </div>
            <span class="card-title">Placements</span>
            <span class="card-value">@placementCount</span>
            <span class="card-label">@selectedPlacementAnalytics</span>
        </div>
        <div class="card">
            <span class="card-title">Placements</span>
            <span class="card-value">£@analytics.TotalPlacementValue.ToString("n2")</span>
            <span class="card-label">Total Value</span>
        </div>
        <div class="card">
            <span class="card-title">Placements</span>
            <span class="card-value">£@analytics.TotalPlacementValuePaid.ToString("n2")</span>
            <span class="card-label">Total Paid</span>
        </div>
    </div>
}

<div class="pg-row grid grid-col-1 grid-tab-1">
    <div class="card" style="height: 70vh">
        <RadzenChart>
            <RadzenChartTooltipOptions Shared="true" />
            <RadzenLineSeries Smooth="true" Data="@timeSeries" CategoryProperty="@nameof(DashboardTimeAnalytics.Date)" Title="Schedules Created" LineType="LineType.Dashed" ValueProperty="@nameof(DashboardTimeAnalytics.TotalCustomersThatCreatedSchedule)">
                <RadzenMarkers Visible="true" MarkerType="MarkerType.Square" />
                <RadzenSeriesDataLabels Visible="false" />
            </RadzenLineSeries>
            <RadzenLineSeries Smooth="true" Data="@timeSeries" CategoryProperty="@nameof(DashboardTimeAnalytics.Date)" Title="Total Amount Paid" LineType="LineType.Solid" ValueProperty="@nameof(DashboardTimeAnalytics.TotalAmountPaid)">
                <RadzenMarkers Visible="true" MarkerType="MarkerType.Circle" />
                <RadzenSeriesDataLabels Visible="false" />
            </RadzenLineSeries>
            <RadzenCategoryAxis Padding="20" FormatString="{0:dd MMM}" />
            <RadzenValueAxis FormatString="{0:n2}">
                <RadzenGridLines Visible="true" />
                <RadzenAxisTitle Text="Value" />
            </RadzenValueAxis>
        </RadzenChart>
    </div>
</div>

@code 
{
    private string selectedCustomerAnalytics { get; set; } = "Total";
    private string customerCount = "0";


    private string selectedPlacementAnalytics { get; set; } = "Total";
    private string placementCount = "0";

    private DashboardAnalytics analytics;
    private List<DashboardTimeAnalytics> timeSeries = [];

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender)
        {
            analytics = await uow.Db.OneAsync(Query<DashboardAnalytics>.All(), Cancel);
            ChangeCustomerAnalytics(selectedCustomerAnalytics);
            ChangePlacementAnalytics(selectedPlacementAnalytics);

            var now = DateOnly.FromDateTime(DateTime.UtcNow);
            var thirtyDaysPast = DateOnly.FromDateTime(DateTime.UtcNow.AddDays(-30));

            timeSeries = await uow.Db.ManyAsync(Query<DashboardTimeAnalytics>.Where(x => x.Date <= now && x.Date >= thirtyDaysPast).OrderBy(x => x.OrderBy(y => y.Date)), Cancel);

            StateHasChanged();
        }
    }

    private void ChangeCustomerAnalytics(string type)
    {
        selectedCustomerAnalytics = type;
        customerCount = type switch
        {
            "Signed In" => analytics.TotalSignedInCustomers.ToString("n0"),
            "Created Schedule" => analytics.TotalCustomersWithSchedules.ToString("n0"),
            _ => analytics.TotalCustomers.ToString("n0"),
        };

        StateHasChanged();
    }


    private void ChangePlacementAnalytics(string type)
    {
        selectedPlacementAnalytics = type;
        placementCount = type switch
        {
            "Settled" => analytics.TotalSettledPlacements.ToString("n0"),
            "Closed" => analytics.TotalClosedPlacements.ToString("n0"),
            _ => analytics.TotalPlacements.ToString("n0"),
        };

        StateHasChanged();
    }
}
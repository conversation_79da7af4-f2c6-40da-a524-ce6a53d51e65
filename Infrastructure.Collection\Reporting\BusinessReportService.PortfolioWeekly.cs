﻿using System.Globalization;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Collection.Helpers;
using LendQube.Infrastructure.Collection.Reporting.ViewModel;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.Reporting;

partial class BusinessReportService
{
    private void BuildPortfolioWeeklyReport(SystemReport data)
    {
        var startDate = data.StartDate ?? "28-09-2024".GetInstantFromString(); //start date for first uploaded placements
        var endDate = data.EndDate ?? clock.GetCurrentInstant();

        var localStartDate = startDate.InZone(timeZone);

        var beginDate = localStartDate.Date;

        int dayOfWeek = (int)beginDate.DayOfWeek;

        var startOfWeek = beginDate.PlusDays(-dayOfWeek);
        var startofNextWeek = startOfWeek.PlusDays(7);

        IQueryable<PortfolioWeeklyReportVM> weeklyQuery = null;

        var duration = endDate - startDate;

        // Convert the duration to weeks
        var weeksBetween = Math.Ceiling(duration.TotalDays / 7);


        while (weeksBetween >= 0)
        {
            var startOfWeekZoned = startOfWeek.AtStartOfDayInZone(localStartDate.Zone).Date;
            var startOfNextWeekZoned = startofNextWeek.AtStartOfDayInZone(localStartDate.Zone).Date;

            var queryable = BuildWeeklyAcountsTotal(startOfWeekZoned, startOfNextWeekZoned);

            if (weeklyQuery == null)
                weeklyQuery = queryable;
            else
                weeklyQuery = weeklyQuery.Union(queryable);

            startOfWeek = startofNextWeek;
            startofNextWeek = startOfWeek.PlusDays(7);

            weeksBetween--;
        }

        if (weeklyQuery != null)
            queries.Add(BusinessReportTypesEnum.PortfolioWeekly.ToString().SplitOnUpper(), weeklyQuery);
    }

    private IQueryable<PortfolioWeeklyReportVM> BuildWeeklyAcountsTotal(LocalDate startOfWeek, LocalDate startOfNextWeek)
    {
        var query = uow.Db.Queryable<CustomerProfile>().Select(data => new PortfolioWeeklyReportVM()
        {
            Date = startOfWeek,

            TotalAccounts = data.Placements.Where(x => x.StatusChangeLogs.Any(y =>
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            (PlacementHelper.ReportActiveStatus.HasFlag(y.NewStatus) || PlacementHelper.ReportActiveStatus.HasFlag(y.OldStatus))
            ) ||
            (PlacementHelper.ReportActiveStatus.HasFlag(x.Status) &&
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),

            TotalCustomers = data.Placements.Where(x => x.StatusChangeLogs.Any(y =>
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            (PlacementHelper.ReportActiveStatus.HasFlag(y.NewStatus) || PlacementHelper.ReportActiveStatus.HasFlag(y.OldStatus))
            ) ||
            (PlacementHelper.ReportActiveStatus.HasFlag(x.Status) &&
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Any() ? 1 : 0,

            ReturnedToClient = data.Placements.Where(x => x.StatusChanges.Any(y => y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek && y.ToStatus == PlacementStatus.Closed && (y.Reason.Code == "RTC" || y.Comment == "RTC"))).Count(),

            StatusChange = data.Placements.Where(x => x.StatusChangeLogs.Any(y => y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek && (PlacementHelper.ReportActiveStatus.HasFlag(y.NewStatus) || PlacementHelper.ReportActiveStatus.HasFlag(y.OldStatus)) && y.OldStatus != y.NewStatus)).Count(),

            AmountCollectedToDate = data.Transactions.Where(x => x.Successful &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.PaymentProvider != PaymentProvider.Discount &&
            x.AmountPaid > 0).Sum(x => x.AmountPaid),

            AmountCollectedThisWeek = data.Transactions.Where(x => x.Successful &&
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.PaymentProvider != PaymentProvider.Discount &&
            x.AmountPaid > 0).Sum(x => x.AmountPaid),

            TotalArrangements = data.Schedules.Any(x =>
            x.CPADate.Year >= startOfWeek.Year && x.CPADate.Month >= startOfWeek.Month &&
            x.CPADate.Year <= startOfNextWeek.Year && x.CPADate.Month <= startOfNextWeek.Month &&
            (x.CPADate.Day >= startOfWeek.Day || x.CPADate.Day < startOfNextWeek.Day)) ? 1 : 0,

            TotalActiveAccounts = data.Schedules.Any(x => x.PeriodStatus < SchedulePeriodStatus.PastDue &&
            x.CPADate.Year >= startOfWeek.Year && x.CPADate.Month >= startOfWeek.Month &&
            x.CPADate.Year <= startOfNextWeek.Year && x.CPADate.Month <= startOfNextWeek.Month &&
            (x.CPADate.Day >= startOfWeek.Day || x.CPADate.Day < startOfNextWeek.Day)) ? 1 : 0,

            ActivePPMonthlyValue = data.Schedules.Where(x => x.PeriodStatus < SchedulePeriodStatus.PastDue &&
            x.CPADate.Month == startOfWeek.Month && x.CPADate.Year == startOfWeek.Year
            ).Sum(x => x.Amount),

            ActiveInvoiceBalance = data.Schedules.Where(x => x.PeriodStatus < SchedulePeriodStatus.PastDue &&
            x.CPADate.Month == startOfWeek.Month && x.CPADate.Year == startOfWeek.Year
            ).Sum(x => x.Balance),

            TotalBrokenAccounts = data.Schedules.Any(x => x.PeriodStatus == SchedulePeriodStatus.PastDue &&
            x.CPADate.Year >= startOfWeek.Year && x.CPADate.Month >= startOfWeek.Month &&
            x.CPADate.Year <= startOfNextWeek.Year && x.CPADate.Month <= startOfNextWeek.Month &&
            (x.CPADate.Day >= startOfWeek.Day || x.CPADate.Day < startOfNextWeek.Day)) ? 1 : 0,

            BrokenPPMonthlyValue = data.Schedules.Where(x => x.PeriodStatus == SchedulePeriodStatus.PastDue &&
            x.CPADate.Month == startOfWeek.Month && x.CPADate.Year == startOfWeek.Year
            ).Sum(x => x.Amount),

            BrokenInvoiceBalance = data.Schedules.Where(x => x.PeriodStatus == SchedulePeriodStatus.PastDue &&
            x.CPADate.Month == startOfWeek.Month && x.CPADate.Year == startOfWeek.Year
            ).Sum(x => x.Balance),

            TotalReEngagedAccounts = data.Placements.Where(x => x.StatusChangeLogs.Any(y =>
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            y.OldStatus == PlacementStatus.Broken && y.NewStatus == PlacementStatus.Active)).Count(),

            TotalSettledAccounts = data.Placements.Where(x => x.Status == PlacementStatus.Settled &&
            x.LastModifiedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.LastModifiedDate.Value.InZone(timeZone).Date < startOfNextWeek).Count(),

            SettledAmountCollected = data.Placements.Where(x => x.Status == PlacementStatus.Settled &&
            x.LastModifiedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.LastModifiedDate.Value.InZone(timeZone).Date < startOfNextWeek).Sum(x => x.BalancePaid),

            PredictedCurrentMonth = data.Schedules.Where(x =>
            x.CPADate.Month == startOfWeek.Month && x.CPADate.Year == startOfWeek.Year
            ).Sum(x => x.Amount),

            ActualCurrentMonth = data.Schedules.Where(x => x.PaymentStatus == SchedulePaymentStatus.Paid &&
            x.CPADate.Month == startOfWeek.Month && x.CPADate.Year == startOfWeek.Year
            ).Sum(x => x.AmountPaid),

            PredictedUpcomingMonth = data.Schedules.Where(x => x.CPADate.Month == startOfWeek.PlusMonths(1).Month && x.CPADate.Year == startOfWeek.PlusMonths(1).Year
            ).Sum(x => x.Amount),

            NonPerformingTotal = data.Placements.Where(x => x.Transactions.Where(y =>
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek
            && y.AmountPaid > 0).Count() == 0).Count(),

            NonPerformingBalance = data.Placements.Where(x => x.Transactions.Where(y =>
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek
            && y.AmountPaid > 0).Count() == 0).Sum(x => x.BalanceTotal - x.Transactions.Where(y =>
            y.CreatedDate.Value.InZone(timeZone).Date < startOfWeek && y.AmountPaid > 0).Sum(y => y.AmountPaid)),

            TotalClosedAccounts = data.Placements.Where(x => x.StatusChangeLogs.Any(y =>
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            y.NewStatus >= PlacementStatus.Closed) ||
            (x.Status >= PlacementStatus.Closed &&
            x.LastModifiedDate.Value.InZone(timeZone).Date >= startOfWeek && x.LastModifiedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),


            ClosedAccountDebtReliefOrder = data.Placements.Where(x =>
            x.StatusChanges.Any(y => y.ToStatus == PlacementStatus.Closed && (y.Reason.Code == "DRO" || y.Comment == "DRO") &&
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),

            ClosedAccountDeceased = data.Placements.Where(x =>
            x.StatusChanges.Any(y => y.ToStatus == PlacementStatus.Closed && (y.Reason.Code == "DEC" || y.Comment == "DEC") &&
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),

            ClosedAccountsBankruptcyOrder = data.Placements.Where(x =>
            x.StatusChanges.Any(y => y.ToStatus == PlacementStatus.Closed && (y.Reason.Code == "BK" || y.Comment == "BK") &&
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),

            ClosedAccountsReturnedToClient = data.Placements.Where(x =>
            x.StatusChanges.Any(y => y.ToStatus == PlacementStatus.Closed && (y.Reason.Code == "RTC" || y.Comment == "RTC") &&
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),

            CloseAccountIndividualVoluntaryArrangement = data.Placements.Where(x =>
            x.StatusChanges.Any(y => y.ToStatus == PlacementStatus.Closed && (y.Reason.Code == "IVA" || y.Comment == "IVA") &&
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),

            ClosedAccountCampaignEnded = data.Placements.Where(x =>
            x.StatusChanges.Any(y => y.ToStatus == PlacementStatus.Closed && (y.Reason.Code == "PE" || y.Comment == "PE") &&
            y.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            y.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek)).Count(),

            ClosedAccountTrustDeed = 0,


            TotalHeldAccounts = data.Holds.Any(x =>
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek) ? 1 : 0,

            TotalHeld14Days = data.Holds.Any(x =>
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.DefaultDuration.Duration == HoldDuration.Days &&
            x.Hold.DefaultDuration.Length == 14) ? 1 : 0,

            TotalHeld7Days = data.Holds.Any(x =>
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.DefaultDuration.Duration == HoldDuration.Days &&
            x.Hold.DefaultDuration.Length == 7) ? 1 : 0,

            TotalHeldComplaint = data.Holds.Any(x =>
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.Reason.Contains("Complaint")) ? 1 : 0,

            TotalHeldDebtRespite = data.Holds.Any(x =>
            x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.Reason == "DRO") ? 1 : 0,

            TotalDebtMgtInvolvement = 0,
            DebtManagementPlan = 0,
            DebtMgtAuthorizationNoPaymentDate = 0,
            DebtRespiteShemeHold = 0,


            SettledAccountsPaidInFull = data.Placements.Where(x => x.Discount == 0 && x.Status == PlacementStatus.Settled &&
            x.LastModifiedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.LastModifiedDate.Value.InZone(timeZone).Date < startOfNextWeek).Count(),

            SettledAccountsSettledInFull = data.Placements.Where(x => x.Discount > 0 && x.Status == PlacementStatus.Settled &&
            x.LastModifiedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.LastModifiedDate.Value.InZone(timeZone).Date < startOfNextWeek).Count(),

            TotalComplaints = data.Holds.Where(x => x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.Reason.Contains("Complaint")).Count(),

            ClosedComplaints = data.Holds.Where(x => x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.Reason.Contains("Complaint") && (x.ExpiresOn.Value.InZone(timeZone).Date < startOfNextWeek || x.Disabled)).Count(),

            OpenComplaints = data.Holds.Where(x => x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.Reason.Contains("Complaint") && (!x.ExpiresOn.HasValue || x.ExpiresOn.Value.InZone(timeZone).Date > startOfNextWeek) && !x.Disabled).Count(),

            FOSComplaints = data.Holds.Where(x => x.CreatedDate.Value.InZone(timeZone).Date >= startOfWeek &&
            x.CreatedDate.Value.InZone(timeZone).Date < startOfNextWeek &&
            x.Hold.Reason.StartsWith("FOS")).Count(),
        });


        var result = query.GroupBy(x => x.Date).Select(x => new PortfolioWeeklyReportVM()
        {
            Date = x.Key,
            TotalAccounts = x.Sum(y => y.TotalAccounts),
            TotalCustomers = x.Sum(y => y.TotalCustomers),
            ReturnedToClient = x.Sum(y => y.ReturnedToClient),
            StatusChange = x.Sum(y => y.StatusChange),

            AmountCollectedToDate = x.Sum(y => y.AmountCollectedToDate),
            AmountCollectedThisWeek = x.Sum(y => y.AmountCollectedThisWeek),
            NonPerformingTotal = x.Sum(y => y.NonPerformingTotal),
            NonPerformingBalance = x.Sum(y => y.NonPerformingBalance),

            TotalArrangements = x.Sum(y => y.TotalArrangements),

            TotalActiveAccounts = x.Sum(y => y.TotalActiveAccounts),
            ActivePPMonthlyValue = x.Sum(y => y.ActivePPMonthlyValue),
            ActiveInvoiceBalance = x.Sum(y => y.ActiveInvoiceBalance),
            TotalBrokenAccounts = x.Sum(y => y.TotalBrokenAccounts),
            BrokenPPMonthlyValue = x.Sum(y => y.BrokenPPMonthlyValue),
            BrokenInvoiceBalance = x.Sum(y => y.BrokenInvoiceBalance),
            TotalReEngagedAccounts = x.Sum(y => y.TotalReEngagedAccounts),
            TotalSettledAccounts = x.Sum(y => y.TotalSettledAccounts),
            SettledAmountCollected = x.Sum(y => y.SettledAmountCollected),
            PredictedCurrentMonth = x.Sum(y => y.PredictedCurrentMonth),
            ActualCurrentMonth = x.Sum(y => y.ActualCurrentMonth),
            PredictedUpcomingMonth = x.Sum(y => y.PredictedUpcomingMonth),

            TotalClosedAccounts = x.Sum(y => y.TotalClosedAccounts),
            ClosedAccountsReturnedToClient = x.Sum(y => y.ClosedAccountsReturnedToClient),
            ClosedAccountsBankruptcyOrder = x.Sum(y => y.ClosedAccountsBankruptcyOrder),
            CloseAccountIndividualVoluntaryArrangement = x.Sum(y => y.CloseAccountIndividualVoluntaryArrangement),
            ClosedAccountDeceased = x.Sum(y => y.ClosedAccountDeceased),
            ClosedAccountTrustDeed = x.Sum(y => y.ClosedAccountTrustDeed),
            ClosedAccountDebtReliefOrder = x.Sum(y => y.ClosedAccountDebtReliefOrder),
            ClosedAccountCampaignEnded = x.Sum(y => y.ClosedAccountCampaignEnded),

            TotalHeldAccounts = x.Sum(y => y.TotalHeldAccounts),
            TotalHeld14Days = x.Sum(y => y.TotalHeld14Days),
            TotalHeld7Days = x.Sum(y => y.TotalHeld7Days),
            TotalHeldComplaint = x.Sum(y => y.TotalHeldComplaint),
            TotalHeldDebtRespite = x.Sum(y => y.TotalHeldDebtRespite),

            SettledAccountsPaidInFull = x.Sum(y => y.SettledAccountsPaidInFull),
            SettledAccountsSettledInFull = x.Sum(y => y.SettledAccountsSettledInFull),

            TotalDebtMgtInvolvement = x.Sum(y => y.TotalDebtMgtInvolvement),
            DebtMgtAuthorizationNoPaymentDate = x.Sum(y => y.DebtMgtAuthorizationNoPaymentDate),
            DebtRespiteShemeHold = x.Sum(y => y.DebtRespiteShemeHold),
            DebtManagementPlan = x.Sum(y => y.DebtManagementPlan),

            TotalComplaints = x.Sum(y => y.TotalComplaints),
            ClosedComplaints = x.Sum(y => y.ClosedComplaints),
            OpenComplaints = x.Sum(y => y.OpenComplaints),
            FOSComplaints = x.Sum(y => y.FOSComplaints),
        });

        return result;
    }

    private static async Task SavePortfolioWeeklyReport(ExcelWorksheet worksheet, IQueryable<object> reportData, CancellationToken ct)
    {
        var queryData = reportData.Cast<PortfolioWeeklyReportVM>();

        var data = await queryData.OrderBy(x => x.Date).ToListAsync(ct);

        worksheet.Cells[1, 2].Value = "Weekly Stats";
        worksheet.Cells[1, 2].Style.Font.Bold = true;

        worksheet.Cells[3, 1].Value = "WEEK COMMENCING";
        worksheet.Cells[3, 1].Style.Font.Bold = true;

        worksheet.Cells[5, 1].Value = "ACCOUNTS - TOTAL";
        worksheet.Row(5).Style.Font.Bold = true;

        worksheet.Cells[7, 1].Value = "Unique customers";
        worksheet.Cells[8, 1].Value = "Returned to client";
        worksheet.Cells[9, 1].Value = "Status change";
        worksheet.Cells[10, 1].Value = "Percentage (of accounts with status changes)";

        worksheet.Cells[12, 1].Value = "COLLECTED";
        worksheet.Row(12).Style.Font.Bold = true;

        worksheet.Cells[13, 1].Value = "Collected to Date";
        worksheet.Cells[14, 1].Value = "Collected this week";
        worksheet.Cells[15, 1].Value = "Non Performing Total";
        worksheet.Cells[16, 1].Value = "Non Performing Balance";

        worksheet.Cells[19, 1].Value = "ARRANGEMENTS - TOTAL";
        worksheet.Row(19).Style.Font.Bold = true;

        worksheet.Cells[21, 1].Value = "Percentage (Status Change vs Active Arrangements)";
        worksheet.Row(21).Style.Font.Bold = true;

        worksheet.Cells[22, 1].Value = "Active";
        worksheet.Cells[23, 1].Value = "  PP Monthly Value";
        worksheet.Cells[24, 1].Value = "  Invoice Balance";
        worksheet.Cells[25, 1].Value = "Broken";
        worksheet.Cells[26, 1].Value = "  PP Monthly Value";
        worksheet.Cells[27, 1].Value = "  Invoice Balance";
        worksheet.Cells[28, 1].Value = "  Re-engaged";
        worksheet.Cells[29, 1].Value = "Settled";
        worksheet.Cells[30, 1].Value = "  Collected";
        worksheet.Cells[31, 1].Value = "Predicted current month";
        worksheet.Cells[32, 1].Value = "Predicted vs Actual current month";
        worksheet.Cells[33, 1].Value = "Predicted following month";

        worksheet.Cells[35, 1].Value = "CLOSED - TOTAL";
        worksheet.Row(35).Style.Font.Bold = true;

        worksheet.Cells[37, 1].Value = "Return to Client";
        worksheet.Cells[38, 1].Value = "Bankruptcy Order";
        worksheet.Cells[39, 1].Value = "Individual Voluntary Arrangement";
        worksheet.Cells[40, 1].Value = "Deceased";
        worksheet.Cells[41, 1].Value = "Trust Deed";
        worksheet.Cells[42, 1].Value = "Debt Relief Order ";
        worksheet.Cells[43, 1].Value = "Campaign Ended";

        worksheet.Cells[45, 1].Value = "HELD ACCOUNTS - TOTAL";
        worksheet.Row(45).Style.Font.Bold = true;

        worksheet.Cells[47, 1].Value = "Hold Procedure 14 days";
        worksheet.Cells[48, 1].Value = "Hold Procedure 7 days";
        worksheet.Cells[49, 1].Value = "Complaint";
        worksheet.Cells[50, 1].Value = "Debt Respite Scheme";

        worksheet.Cells[52, 1].Value = "SETTLED ACCOUNTS - TOTAL";
        worksheet.Row(52).Style.Font.Bold = true;

        worksheet.Cells[54, 1].Value = "Paid in full";
        worksheet.Cells[55, 1].Value = "Settled in full";


        worksheet.Cells[57, 1].Value = "DEBT MANAGEMENT INVOLVEMENT - TOTAL";
        worksheet.Row(57).Style.Font.Bold = true;

        worksheet.Cells[59, 1].Value = "Debt Management Authorisation (No payment plan)";
        worksheet.Cells[60, 1].Value = "Debt Respite Scheme hold";
        worksheet.Cells[61, 1].Value = "Debt Management plan";


        worksheet.Cells[63, 1].Value = "COMPLAINTS - TOTAL";
        worksheet.Row(63).Style.Font.Bold = true;

        worksheet.Cells[65, 1].Value = "Open";
        worksheet.Cells[66, 1].Value = "Closed";
        worksheet.Cells[67, 1].Value = "FOS";

        foreach (var (item, index) in data.Select((x, i) => (x, i)))
        {
            var column = index + 3;
            worksheet.Cells[3, column].Value = item.Date.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);


            worksheet.Cells[5, column].Value = item.TotalAccounts;
            worksheet.Cells[7, column].Value = item.TotalCustomers;
            worksheet.Cells[8, column].Value = item.ReturnedToClient;
            worksheet.Cells[9, column].Value = item.StatusChange;
            worksheet.Cells[10, column].Value = item.PercentageStatusChange;


            worksheet.Cells[13, column].Value = item.AmountCollectedToDate;
            worksheet.Cells[13, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[14, column].Value = item.AmountCollectedThisWeek;
            worksheet.Cells[14, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[15, column].Value = item.NonPerformingTotal;

            worksheet.Cells[16, column].Value = item.NonPerformingBalance;
            worksheet.Cells[16, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[19, column].Value = item.TotalArrangements;
            worksheet.Cells[22, column].Value = item.TotalActiveAccounts;

            worksheet.Cells[23, column].Value = item.ActivePPMonthlyValue;
            worksheet.Cells[23, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[24, column].Value = item.ActiveInvoiceBalance;
            worksheet.Cells[24, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[25, column].Value = item.TotalBrokenAccounts;

            worksheet.Cells[26, column].Value = item.BrokenPPMonthlyValue;
            worksheet.Cells[26, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[27, column].Value = item.BrokenInvoiceBalance;
            worksheet.Cells[27, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[28, column].Value = item.TotalReEngagedAccounts;
            worksheet.Cells[29, column].Value = item.TotalSettledAccounts;

            worksheet.Cells[30, column].Value = item.SettledAmountCollected;
            worksheet.Cells[30, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[31, column].Value = item.PredictedCurrentMonth;
            worksheet.Cells[31, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[32, column].Value = item.DifferenceBetweenPredictedAndActualCurrentMonth;
            worksheet.Cells[32, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[33, column].Value = item.PredictedUpcomingMonth;
            worksheet.Cells[33, column].Style.Numberformat.Format = "£#,##0.00";

            worksheet.Cells[35, column].Value = item.TotalClosedAccounts;
            worksheet.Cells[37, column].Value = item.ClosedAccountsReturnedToClient;
            worksheet.Cells[38, column].Value = item.ClosedAccountsBankruptcyOrder;
            worksheet.Cells[39, column].Value = item.CloseAccountIndividualVoluntaryArrangement;
            worksheet.Cells[40, column].Value = item.ClosedAccountDeceased;
            worksheet.Cells[41, column].Value = item.ClosedAccountTrustDeed;
            worksheet.Cells[42, column].Value = item.ClosedAccountDebtReliefOrder;
            worksheet.Cells[43, column].Value = item.ClosedAccountCampaignEnded;

            worksheet.Cells[45, column].Value = item.TotalHeldAccounts;
            worksheet.Cells[47, column].Value = item.TotalHeld14Days;
            worksheet.Cells[48, column].Value = item.TotalHeld7Days;
            worksheet.Cells[49, column].Value = item.TotalHeldComplaint;
            worksheet.Cells[50, column].Value = item.TotalHeldDebtRespite;

            worksheet.Cells[52, column].Value = item.TotalSettledAccounts;

            worksheet.Cells[54, column].Value = item.SettledAccountsPaidInFull;

            worksheet.Cells[55, column].Value = item.SettledAccountsSettledInFull;

            worksheet.Cells[57, column].Value = item.TotalDebtMgtInvolvement;
            worksheet.Cells[59, column].Value = item.DebtMgtAuthorizationNoPaymentDate;
            worksheet.Cells[60, column].Value = item.DebtRespiteShemeHold;
            worksheet.Cells[61, column].Value = item.DebtManagementPlan;


            worksheet.Cells[63, column].Value = item.TotalComplaints;
            worksheet.Cells[65, column].Value = item.OpenComplaints;
            worksheet.Cells[66, column].Value = item.ClosedComplaints;
            worksheet.Cells[67, column].Value = item.FOSComplaints;

        }
    }
}

﻿using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Infrastructure.Collection.Analytics;
using LendQube.Infrastructure.Collection.Authentication;
using LendQube.Infrastructure.Collection.BackgroundServices;
using LendQube.Infrastructure.Collection.Database;
using LendQube.Infrastructure.Collection.Messaging;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Collection.Payments.Providers.Acquired;
using LendQube.Infrastructure.Collection.Reporting;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Collection.UploadServices;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.Payment;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.PlacementAmendUpload;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.PlacementUpload;
using LendQube.Infrastructure.Collection.Workflow.Debt;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.DependencyInjection;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.PermissionsAndRoles;
using LendQube.Infrastructure.Core.Reporting;
using LendQube.Infrastructure.ExternalApi.DependencyInjection;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace LendQube.Infrastructure.Collection.DependencyInjection;

public static class CollectionDI
{
    private const string CollectionsSetting = "Providers:DeployState:EnabledApplications:Collections";

    public static async Task<WebApplication> CreateCollectionPermissions(this WebApplication app, AsyncServiceScope scope)
    {
        var isEnabled = scope.ServiceProvider.GetRequiredService<DefaultAppConfig>().DeployState.EnabledApplications.Collections;
        if (!isEnabled)
            return app;

        var uow = scope.ServiceProvider.GetRequiredService<IUnitofWork>();
        await ManageAnalyticsService.Seed(uow);
        return app.AddPermissions(typeof(CollectionDbContext));
    }

    public static WebApplicationBuilder AddCollectionAdminServices(this WebApplicationBuilder builder)
    {
        var isEnabled = builder.Configuration.GetValue<bool>(CollectionsSetting);
        if (!isEnabled)
            return builder;

        builder.Services.AddRazorRoute<CollectionDbContext>();

        builder.AddExternalApis();

        builder.Services.AddCollectionUploadServices()
            .AddPayments()
            .AddKeyedTransient<AbstractBackgroundTaskManager, CollectionBackgroundTaskManager>(BackgroundEventSource.Collection)
            .AddTransient<ManageScheduleService>()
            .AddTransient<WorkflowManagementService>()
            .AddCollectionTriggers()
            .AddMessaging()
            .AddReporting();

        AnalyticsScript.Prepare();

        return builder;
    }

    public static WebApplicationBuilder AddCollectionApiServices(this WebApplicationBuilder builder)
    {
        var isEnabled = builder.Configuration.GetValue<bool>(CollectionsSetting);
        if (!isEnabled)
            return builder;

        builder.AddExternalApis();


        builder.Services.AddTransient<CustomerOneTimeCodeManager>()
            .AddTransient<CustomerAuthService>()
            .AddTransient<ManageScheduleService>()
            .AddPayments()
            .AddMessaging();

        return builder;
    }

    public static WebApplicationBuilder AddCollectionBackgroundServices(this WebApplicationBuilder builder)
    {
        var isEnabled = builder.Configuration.GetValue<bool>(CollectionsSetting);
        if (!isEnabled)
            return builder;

        BackgroundTaskDI.BackgroundTriggerTypes.AddRange([typeof(AgentWorkflowAvailability), typeof(AutoDiscountConfig)]);

        builder.AddExternalApis();

        builder.Services
            .AddPayments()
            .AddMessaging()
            .AddReporting()
            .AddTransient<WorkflowAssignmentService>();

        return builder;
    }

    private static IServiceCollection AddCollectionUploadServices(this IServiceCollection services)
    {
        services.AddHttpClient<CollectionUploadService>();
        services.AddTransient<PlacementUploadService>()
            .AddTransient<PlacementAmendUploadService>()
            .AddTransient<PaymentUploadService>();

        services.AddTransient<Func<CollectionFileUploadType, IUploadTypeService>>(serviceProvider => key =>
        {
            return key switch
            {
                CollectionFileUploadType.Placement => serviceProvider.GetService<PlacementUploadService>(),
                CollectionFileUploadType.PlacementAmend => serviceProvider.GetService<PlacementAmendUploadService>(),
                CollectionFileUploadType.Transaction => serviceProvider.GetService<PaymentUploadService>(),
                _ => default,
            };
        });

        return services;
    }

    private static IServiceCollection AddCollectionTriggers(this IServiceCollection services)
    {
        AdminDI.AdminTriggerTypes.AddRange([typeof(CollectionFileUpload), typeof(AgentWorkflowTask)]);
        return services;
    }

    public static WebApplicationBuilder AddExternalApis(this WebApplicationBuilder builder)
    {
        builder.AddExternalApiSettings()
            .AddAcquiredExternalApi();

        return builder;
    }

    private static IServiceCollection AddPayments(this IServiceCollection services)
    {

        services.AddTransient<PaymentFactory>();
        services.AddTransient<AcquiredPaymentProvider>();

        services.AddTransient<Func<PaymentProvider, IPaymentProvider>>(serviceProvider => key =>
        {
            return key switch
            {
                PaymentProvider.Acquired => serviceProvider.GetService<AcquiredPaymentProvider>(),
                _ => throw new KeyNotFoundException($"Setup -> Payment provider: {key} not found"),
            };
        });
        return services;
    }

    private static IServiceCollection AddMessaging(this IServiceCollection services)
    {
        services.AddTransient<IMessageTemplateKeyProvider, CollectionMessageTemplateKeyProvider>();
        return services;
    }

    private static IServiceCollection AddReporting(this IServiceCollection services)
    {
        services.AddKeyedTransient<IReportingService, BusinessReportService>(BusinessReportService.ReportTypeName);

        return services;
    }
}
﻿using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Placements;

public class PlacementActivity : BaseActivityTimeline<PlacementActivity>
{
    public long PlacementId { get; set; }

    public override void Configure(EntityTypeBuilder<PlacementActivity> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.PlacementId);
    }
}

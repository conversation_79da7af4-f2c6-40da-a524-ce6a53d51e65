﻿@page "/customers/flagsetup"

@using LendQube.Entities.Collection.Setup
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inherits GenericCrudTable<CustomerFlagTemplate>

@attribute [Authorize(Policy = ManageCustomersNavigation.CustomerFlagSetupIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<CustomerFlagTemplate> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Flag">Flag</label>
            <InputText @bind-Value="context.Flag" class="form-input" aria-required="true" placeholder="Flag" />
            <ValidationMessage For="() => context.Flag" class="text-danger" />
        </div>
    </div>
    ;

    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "Setup Flags";
        FormBaseTitle = "Flag";
        CreatePermission = ManageCustomersNavigation.CustomerFlagSetupCreatePermission;
        EditPermission = ManageCustomersNavigation.CustomerFlagSetupEditPermission;
        DeletePermission = ManageCustomersNavigation.CustomerFlagSetupDeletePermission;
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new());

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Flag, filterAndPage.TextFilter);
    }
}


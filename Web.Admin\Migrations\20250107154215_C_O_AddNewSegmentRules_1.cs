﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_AddNewSegmentRules_1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<int>(
                name: "CurrentlyAssignedCount",
                schema: "collection",
                table: "AgentWorkflowAvailability",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AddColumn<int>(
                name: "TotalAssigned",
                schema: "collection",
                table: "AgentWorkflowAvailability",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "CurrentlyAssignedCount",
                schema: "collection",
                table: "AgentWorkflowAvailability");

            migrationBuilder.DropColumn(
                name: "TotalAssigned",
                schema: "collection",
                table: "AgentWorkflowAvailability");
        }
    }
}

﻿using System.Reflection;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.FileManagement;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Core.Reporting;

internal sealed class BaseReportingService(ILogManager<BaseReportingService> logger, IFileManagementService fileService, IServiceProvider serviceProvider, BackgroundTaskControlService backgroundService) : IHandleTriggerNotification<SystemReport>
{
    private readonly BackgroundEventSource source = BackgroundEventSource.System;
    private readonly BackgroundTask key = BackgroundTask.ScheduledReports;

    private readonly IUnitofWork uow = backgroundService.Uow;

    private readonly string appName = AppDomain.CurrentDomain.FriendlyName;

    public static event EventHandler<ReportEventArgs>? StatusNotificationEvent;

    #region Process Report

    private async Task Generate(SystemReport data, CancellationToken ct)
    {
        try
        {
            var reportingService = serviceProvider.GetKeyedService<IReportingService>(data.TypeName);
            var reportData = reportingService.Build(data);

            if (reportData.IsNullOrEmpty() || reportData.All(x => !x.Value.Any()))
            {
                data.Message = "No data in report";
                data.Status = ReportStatus.Error;
                await uow.SaveAsync(ct);
                return;
            }

            data.FileUrl = data.FileType switch
            {
                _ => await SaveReportAsExcelOrCsv(reportingService, data, reportData, ct),
            };
            data.Status = ReportStatus.Done;

        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.BackgroundTask, EventAction.Reports, ex, "Generating report failed", data: data);
            data.Message = "Generating report failed";
            data.Status = ReportStatus.Error;
        }

        await uow.SaveAsync(ct);

        if (data is { ReportSchedule.Action: ReportScheduleAction.SendAsMessage, Status: ReportStatus.Done })
        {
            _ = await MessageBuilder.New(MessageConfigNames.ScheduledReport.GetDisplayName(), data.CreatedByUserId)
                .Message(data.ReportSchedule.Config.Name, data.ReportSchedule.ConfigId)
                .WithRecipient(data.ReportSchedule.MessagingGroupId,
                [
                    new($"{MessageTemplateKeys.ReportName}", string.Join(',', data.Names)),
                    new($"{MessageTemplateKeys.FileUrl}", data.FileUrl)
                ])
                .Send(uow, logger, ct);
        }
    }

    private async Task<string> SaveReportAsExcelOrCsv(IReportingService reportingService, SystemReport data, Dictionary<string, IQueryable<object>> reportData, CancellationToken ct)
    {
        ExcelPackage.License.SetNonCommercialPersonal(StringConstants.ExcelLicenseName);
        using var package = new ExcelPackage();
        if (reportingService is IReportDescribesExcelFormat excelReportingService)
        {
            await excelReportingService.SaveReportAsCustomExcelOrCsv(package, data, reportData, ct);
        }
        else
        {
            foreach (var item in reportData)
            {
                var worksheet = package.Workbook.Worksheets.Add(item.Key);
                worksheet.Cells["A1"].LoadFromCollection(item.Value);
                worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
            }
        }

        return await fileService.SaveExcel(package, reportingService.UPLOADKEY, fileService.GenerateFileNameWithExtension(data.Description, data.FileType == ReportType.Excel ? "xlsx" : "csv"), ct);
    }

    #endregion

    #region Change Notification
    public async ValueTask OnChanged(string id, SystemReport oldData, SystemReport newData, TriggerChange change, CancellationToken ct)
    {
        var stopped = await backgroundService.CheckStoppedOrStopping(source, key, ct);

        switch (appName)
        {
            case "Web.BackgroundService":
                await BackgroundServiceReportAction(newData, stopped, ct);
                break;
            case "Web.Admin":
                WebAdminReportAction(newData, stopped);
                break;
            default:
                break;
        }
    }


    private async Task BackgroundServiceReportAction(SystemReport newData, bool stopped, CancellationToken ct)
    {
        if (stopped || newData.Status != ReportStatus.Queued) return;

        await backgroundService.SetStatusToRunning(source, key, ct);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<SystemReport>(x => x.Id == newData.Id && x.Status == ReportStatus.Queued,
             x => x.SetProperty(y => y.Status, ReportStatus.Running)
             .SetProperty(y => y.RunCount, y => y.RunCount + 1), ct);

        var reportData = await uow.Db.OneAsync(Query<SystemReport>.Where(x => x.Id == newData.Id)
            .Include(x =>
            x.Include(y => y.ReportSchedule)
                .ThenInclude(y => y.MessagingGroup)
                )
            .Track(), ct);

        await Generate(reportData, ct);

        await backgroundService.SetStatusToIdle(source, key, ct);
    }

    private void WebAdminReportAction(SystemReport newData, bool stopped)
    {
        var message = "Report background service is stopped. Contact admin";
        if (stopped)
        {
            StatusNotificationEvent?.Invoke(this, new(false, message, newData.CreatedByUserId));
            return;
        }

        switch (newData.Status)
        {
            case ReportStatus.Queued:
                message = $"{newData.Description} report has been queued";
                break;
            case ReportStatus.Running:
                message = $"{newData.Description} report is being generated";
                break;
            case ReportStatus.Done:
                message = $"{newData.Description} report has been generated";
                break;
            case ReportStatus.Error:
                message = $"Generating {newData.Description} report failed. See report message for error";
                break;
            default:
                break;
        }

        StatusNotificationEvent?.Invoke(this, new(newData.Status != ReportStatus.Error, message, newData.CreatedByUserId, newData.Status));
    }

    public Task OnStartup(CancellationToken ct) => uow.Db.UpdateAndSaveWithFilterAsync<SystemReport>(x => x.Status == ReportStatus.Queued,
             x => x.SetProperty(y => y.Status, ReportStatus.Queued), ct);
    #endregion
}

public record ReportEventArgs(bool Successful, string Message, string Owner, ReportStatus? Status = null);

public static class ReportingHelper
{
    static ReportingHelper()
    {
        var type = typeof(IReportingService);

        ReportNames = Assembly.GetEntryAssembly()
            .GetReferencedAssemblies()
            .AsParallel()
            .Where(x => x.Name != null && x.Name.StartsWith(CoreEntityConfig.InfrastructurePrefix))
            .Select(Assembly.Load)
            .SelectMany(x => x.GetTypes())
            .Where(x => type.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
            .Select(type => type.GetFields(BindingFlags.Public | BindingFlags.Static))
            .Where(x => !x.IsNullOrEmpty())
            .Select(x =>
            {
                var reportTypeName = x.FirstOrDefault(m => m.Name == "ReportTypeName");
                var reportTypesField = x.FirstOrDefault(m => m.Name == "ReportTypes");
                var permissionField = x.FirstOrDefault(m => m.Name == "Permission");
                return reportTypesField == null ? null : new ReportTypes
                {
                    TypeName = reportTypeName.GetValue(reportTypeName)?.ToString(),
                    Names = reportTypesField.GetValue(reportTypesField) as HashSet<string>,
                    RequiredPermission = permissionField.GetValue(permissionField)?.ToString()
                };
            })
            .Where(x => x != null)
            .ToList();
    }

    public static IReadOnlyList<ReportTypes> ReportNames { get; set; }
}

public class ReportTypes
{
    public string TypeName { get; set; }
    public HashSet<string> Names { get; set; }
    public string RequiredPermission { get; set; }
}
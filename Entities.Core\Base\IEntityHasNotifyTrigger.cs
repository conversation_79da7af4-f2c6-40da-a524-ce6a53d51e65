﻿namespace LendQube.Entities.Core.Base;

public interface IEntityHasNotifyTrigger //decorator for configuring notify triggers for postgre
{
    public string Schema { get; }
    public TriggerChange[] ChangesToObserve { get; }
    public TriggerType[] Types { get; }
    public bool TrackOldData { get; }
    public bool ReturnOnlyId { get; }

    public string ConditionScript { get; }
}

public enum TriggerType
{
    After,
    Before,
}

public enum TriggerChange
{
    Insert,
    Update,
    Delete
}
﻿@using LendQube.Infrastructure.Core.DateAndTime
@using NodaTime
@using Radzen.Blazor
<RadzenDatePicker style="display: block; width: 100%"
                  @bind-Value="@dateTime"
                  TValue="DateTime?"
                  Change="UpdateTime"
                  Name="@Name"
                  Placeholder="@Placeholder"
                  DateFormat="hh:mm tt"
                  ShowTime="true"
                  HourFormat="12"
                  TimeOnly="true" />

@code {

    [Inject]
    public UserTimeProvider TimeProvider { get; set; }

    [Parameter, EditorRequired]
    public Instant? TimeValue { get; set; }

    [Parameter]
    public EventCallback<Instant?> TimeValueChanged { get; set; }

    [Parameter]
    public string Name { get; set; }

    [Parameter]
    public string Placeholder { get; set; }

    private DateTime? dateTime { get; set; }

    protected override void OnInitialized()
    {
        if (TimeValue.HasValue)
        {
            var timespan = TimeValue.Value.InZone(TimeProvider.LocalTimeZone ?? DateTimeZone.Utc).TimeOfDay.ToTimeOnly().ToTimeSpan();
            if (timespan == TimeSpan.Zero)
            {
                var now = DateTime.UtcNow;
                dateTime = new DateTime(now.Year, now.Month, now.Day, 0, 0, 0);
            }
            else
            {
                dateTime = (new DateTime()).Add(timespan);
            }
        }
        base.OnInitialized();
    }

    void UpdateTime(DateTime? dateTime)
    {
        if (dateTime is null) return;
        TimeValue = Instant.FromDateTimeUtc(dateTime.Value.ToUniversalTime());
        TimeValueChanged.InvokeAsync(TimeValue);
    }
}
﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_Okereke_DiscountAndClosure : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "Amount",
                schema: "collection",
                table: "CustomerDiscount",
                newName: "MaxPercentage");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .Annotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .Annotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure")
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Stripe")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.AddColumn<decimal>(
                name: "Discount",
                schema: "collection",
                table: "Placement",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Discount",
                schema: "collection",
                table: "CustomerProfile",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "BalanceAfterDiscount",
                schema: "collection",
                table: "CustomerDiscount",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "BalanceBeforeDiscount",
                schema: "collection",
                table: "CustomerDiscount",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "Discount",
                schema: "collection",
                table: "CustomerDiscount",
                type: "numeric(18,2)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<long>(
                name: "PlacementId",
                schema: "collection",
                table: "CustomerDiscount",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);

            migrationBuilder.AlterColumn<decimal>(
                name: "BalanceRemaining",
                schema: "collection",
                table: "Placement",
                type: "numeric(18,2)",
                nullable: false,
                computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"",
                stored: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)",
                oldComputedColumnSql: "\"BalanceTotal\" - \"BalancePaid\"",
                oldStored: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "BalanceRemaining",
                schema: "collection",
                table: "CustomerProfile",
                type: "numeric(18,2)",
                nullable: false,
                computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"",
                stored: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)",
                oldComputedColumnSql: "\"BalanceTotal\" - \"BalancePaid\"",
                oldStored: true);

            migrationBuilder.CreateTable(
                name: "DiscountConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Role = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    PercentageLimit = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CanOverride = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DiscountConfig", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "HoldDurationConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Length = table.Column<int>(type: "integer", nullable: false),
                    Duration = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HoldDurationConfig", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PlacementStatusChangeReasonConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    Code = table.Column<string>(type: "character varying(4)", maxLength: 4, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementStatusChangeReasonConfig", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "HoldConfig",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Reason = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    DefaultDurationId = table.Column<long>(type: "bigint", nullable: true),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    AllowDefaultOverride = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_HoldConfig", x => x.Id);
                    table.ForeignKey(
                        name: "FK_HoldConfig_HoldDurationConfig_DefaultDurationId",
                        column: x => x.DefaultDurationId,
                        principalSchema: "collection",
                        principalTable: "HoldDurationConfig",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PlacementStatusChange",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    FromStatus = table.Column<int>(type: "integer", nullable: false),
                    ToStatus = table.Column<int>(type: "integer", nullable: false),
                    ReasonId = table.Column<long>(type: "bigint", nullable: true),
                    Comment = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementStatusChange", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementStatusChange_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PlacementStatusChange_PlacementStatusChangeReasonConfig_Rea~",
                        column: x => x.ReasonId,
                        principalSchema: "collection",
                        principalTable: "PlacementStatusChangeReasonConfig",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_PlacementStatusChange_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerHold",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    PlacementId = table.Column<long>(type: "bigint", nullable: true),
                    HoldConfigId = table.Column<long>(type: "bigint", nullable: false),
                    Comment = table.Column<string>(type: "text", nullable: true),
                    ExpiresOn = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    HoldDefaultsOverriden = table.Column<bool>(type: "boolean", nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerHold", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerHold_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_CustomerHold_HoldConfig_HoldConfigId",
                        column: x => x.HoldConfigId,
                        principalSchema: "collection",
                        principalTable: "HoldConfig",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_CustomerHold_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerDiscount_PlacementId",
                schema: "collection",
                table: "CustomerDiscount",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_ExpiresOn",
                schema: "collection",
                table: "CustomerHold",
                column: "ExpiresOn")
                .Annotation("Npgsql:IndexInclude", new[] { "HoldDefaultsOverriden" });

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_HoldConfigId",
                schema: "collection",
                table: "CustomerHold",
                column: "HoldConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_PlacementId",
                schema: "collection",
                table: "CustomerHold",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerHold_ProfileId",
                schema: "collection",
                table: "CustomerHold",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_DiscountConfig_Role",
                schema: "collection",
                table: "DiscountConfig",
                column: "Role",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HoldConfig_DefaultDurationId",
                schema: "collection",
                table: "HoldConfig",
                column: "DefaultDurationId");

            migrationBuilder.CreateIndex(
                name: "IX_HoldConfig_Reason",
                schema: "collection",
                table: "HoldConfig",
                column: "Reason",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_HoldDurationConfig_Length_Duration",
                schema: "collection",
                table: "HoldDurationConfig",
                columns: new[] { "Length", "Duration" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChange_PlacementId",
                schema: "collection",
                table: "PlacementStatusChange",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChange_ProfileId",
                schema: "collection",
                table: "PlacementStatusChange",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChange_ReasonId",
                schema: "collection",
                table: "PlacementStatusChange",
                column: "ReasonId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementStatusChangeReasonConfig_Code",
                schema: "collection",
                table: "PlacementStatusChangeReasonConfig",
                column: "Code",
                unique: true);

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerDiscount_Placement_PlacementId",
                schema: "collection",
                table: "CustomerDiscount",
                column: "PlacementId",
                principalSchema: "collection",
                principalTable: "Placement",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CustomerDiscount_Placement_PlacementId",
                schema: "collection",
                table: "CustomerDiscount");

            migrationBuilder.DropTable(
                name: "CustomerHold",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DiscountConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementStatusChange",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "HoldConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementStatusChangeReasonConfig",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "HoldDurationConfig",
                schema: "collection");

            migrationBuilder.DropIndex(
                name: "IX_CustomerDiscount_PlacementId",
                schema: "collection",
                table: "CustomerDiscount");

            migrationBuilder.DropColumn(
                name: "Discount",
                schema: "collection",
                table: "Placement");

            migrationBuilder.DropColumn(
                name: "Discount",
                schema: "collection",
                table: "CustomerProfile");

            migrationBuilder.DropColumn(
                name: "BalanceAfterDiscount",
                schema: "collection",
                table: "CustomerDiscount");

            migrationBuilder.DropColumn(
                name: "BalanceBeforeDiscount",
                schema: "collection",
                table: "CustomerDiscount");

            migrationBuilder.DropColumn(
                name: "Discount",
                schema: "collection",
                table: "CustomerDiscount");

            migrationBuilder.DropColumn(
                name: "PlacementId",
                schema: "collection",
                table: "CustomerDiscount");

            migrationBuilder.RenameColumn(
                name: "MaxPercentage",
                schema: "collection",
                table: "CustomerDiscount",
                newName: "Amount");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .Annotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported")
                .Annotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure")
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Stripe")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.AlterColumn<decimal>(
                name: "BalanceRemaining",
                schema: "collection",
                table: "Placement",
                type: "numeric(18,2)",
                nullable: false,
                computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\"",
                stored: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)",
                oldComputedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"",
                oldStored: true);

            migrationBuilder.AlterColumn<decimal>(
                name: "BalanceRemaining",
                schema: "collection",
                table: "CustomerProfile",
                type: "numeric(18,2)",
                nullable: false,
                computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\"",
                stored: true,
                oldClrType: typeof(decimal),
                oldType: "numeric(18,2)",
                oldComputedColumnSql: "\"BalanceTotal\" - \"BalancePaid\" - \"Discount\"",
                oldStored: true);
        }
    }
}

using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Customers;

public class CustomerIncomeAndExpenditure : BaseEntityWithIdentityId<CustomerIncomeAndExpenditure>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public string FullName { get; set; }
    public string Address { get; set; }
    public PhoneNumber PhoneNumber { get; set; }
    public decimal NetSalary { get; set; }
    public decimal BenefitsIncome { get; set; }
    public decimal OtherIncome { get; set; }
    public decimal Rent { get; set; }
    public decimal Utilities { get; set; }
    public decimal CouncilTax { get; set; }
    public decimal Food { get; set; }
    public decimal Transport { get; set; }
    public decimal Insurance { get; set; }
    public decimal Loan { get; set; }
    public decimal OtherExpenditure { get; set; }
    public decimal TotalIncome => NetSalary + BenefitsIncome + OtherIncome;
    public decimal TotalExpenditure => Rent + Utilities + CouncilTax + Food + Transport + Insurance + Loan + OtherExpenditure;
    public decimal NetDisposableIncome => TotalIncome - TotalExpenditure;

    public override void Configure(EntityTypeBuilder<CustomerIncomeAndExpenditure> builder)
    {
        base.Configure(builder);
        builder.OwnsOne(x => x.PhoneNumber, x => x.ToJson());
    }
}
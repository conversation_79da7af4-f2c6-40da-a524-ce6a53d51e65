﻿using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using System.Reflection;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

internal sealed class EndsWithFilter() : ObjectFilter(ColumnFilterRule.EndsWith)
{
    public override Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value)
    {
        if (EmbeddedQuery )
            return ExpressionsExtension.EndsWithLowerCase<T>(value.ToString().ToLower(), vm.ColumnName);

        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(vm.ColumnName);

        ConstantExpression valueExpression = Expression.Constant(string.Format("%{0}", value));

        MethodInfo likeMethod = typeof(NpgsqlDbFunctionsExtensions).GetMethod(
            nameof(NpgsqlDbFunctionsExtensions.ILike), BindingFlags.Public | BindingFlags.Static,
            null,
            [typeof(DbFunctions), typeof(string), typeof(string)], null);

        Expression body = Expression.Call(null,
            likeMethod, Expression.Constant(EF.Functions),
            expression, valueExpression);

        return Expression.Lambda<Func<T, bool>>(body, pe);
    }

    public override bool IsTypeSupported(ObjectFilterRule rule) => Type.GetTypeCode(rule.Type) switch
    {
        TypeCode.String => true,
        _ => false,
    } && !rule.IsId && rule.FilterType != ColumnFilterDataType.DBList;
}

﻿using LendQube.Infrastructure.Core.Database.DataPager;

namespace LendQube.Infrastructure.Core.Components.Table;

public abstract class BaseTableComponentBase : AbstractBaseTableComponentBase
{
    protected string AddModalName { get; set; } = "newData";
    protected string EditModalName { get; set; } = "editData";
    protected string ViewModalName { get; set; } = "viewData";

    protected string Title { get; set; }
    protected string SubTitle { get; set; }
    public string FormBaseTitle { get; set; }

    protected string CreatePermission { get; set; } = "TableDummy.Create.Permission";
    protected string EditPermission { get; set; } = "TableDummy.Edit.Permission";
    protected string DeletePermission { get; set; } = "TableDummy.Delete.Permission";

    protected bool HasInfo { get; set; }
    protected bool NoGeneralSearch { get; set; }

    protected ColumnList TableDefinition { get; private set; }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        TableDefinition = GetLocalTableDefinition(GetTableDefinition(), EditPermission, DeletePermission);
        TableDefinition.HasInfo = HasInfo;
        TableDefinition.NoGeneralSearch = NoGeneralSearch;
    }

    protected abstract ColumnList GetTableDefinition();

    protected ValueTask BaseSaveAdd(Func<Task<bool>> save, Func<Task> refresh) => BaseSaveAdd(CreatePermission, AddModalName, save, refresh);

    protected ValueTask BaseSaveAddWithRedirect(Func<Task<bool>> save, Action redirect) => BaseSaveAddWithRedirect(CreatePermission, AddModalName, save, redirect);

    protected ValueTask BaseEdit(Func<Task> loadData, CancellationToken ct) => BaseEdit(EditPermission, EditModalName, loadData, ct);

    protected ValueTask BaseSaveEdit(Func<Task<bool>> save, Func<Task> refresh) => BaseSaveEdit(EditPermission, EditModalName, save, refresh);

    protected ValueTask<bool> SaveDelete(Func<Task<bool>> save, Func<Task> refresh) => SaveDelete(DeletePermission, save, refresh);
}

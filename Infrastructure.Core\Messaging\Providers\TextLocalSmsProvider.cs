﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class TextLocalSmsProvider : AbstractMessageProvider, ITextMessageProvider
{
    public const string Name = "TextLocal Sms";
    protected override MessageChannel SupportedChannel => MessageChannel.Sms;
    private readonly DefaultAppConfig config;
    private readonly HttpClient client;

    public TextLocalSmsProvider(IUnitofWork uow, DefaultAppConfig config, HttpClient client) : base(uow)
    {
        this.config = config;
        this.client = client;
        if (config.TextLocal != null && !string.IsNullOrEmpty(config.TextLocal.ApiKey))
        {
            SupportedCountryCodes = MessagingCompiledQueries.GetMessageProviderSupportedCountriesAndConfig(uow, Name);
            Config = SupportedCountryCodes.IsNullOrEmpty() ? new ProviderConfigVM { Disabled = true } : SupportedCountryCodes[0];
        }
        else
        {
            Config = new ProviderConfigVM { Disabled = true };
        }
    }

    public override ProviderConfigVM Config { get; }
    public IReadOnlyList<ProviderConfigVM> SupportedCountryCodes { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        Dictionary<long, MessageStatus> results = [];
        var leadingMessage = messages[0];

        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count, CancellationToken = ct }, async (message, ct) =>
        {
            await Parallel.ForEachAsync(message.Data.Select((x, i) => (Value: x, Index: i)), new ParallelOptions { MaxDegreeOfParallelism = message.Data.Count, CancellationToken = ct }, async (recipient, ct) =>
            {
                await Parallel.ForEachAsync(recipient.Value.PhoneNumbers, new ParallelOptions { MaxDegreeOfParallelism = recipient.Value.PhoneNumbers.Count, CancellationToken = ct }, async (phoneNumber, ct) =>
                {
                    var values = new Dictionary<string, string>
                    {
                        { "apikey", config.TextLocal.ApiKey },
                        { "numbers", $"{phoneNumber.Code}{phoneNumber.Number}" },
                        { "message", recipient.Value.TextTemplate ?? message.TextTemplate },
                        { "sender", config.TextLocal.From }
                    };

                    var content = new FormUrlEncodedContent(values);

                    try
                    {
                        var response = await client.PostAsync(config.TextLocal.BaseUrl, content, ct);
                        var responseContent = await response.Content.ReadAsStringAsync(ct);

                        if (response.IsSuccessStatusCode && responseContent.Contains("\"status\":\"success\""))
                        {
                            results[message.MessageId + recipient.Index] = MessageStatus.Sent;
                        }
                        else
                        {
                            results[message.MessageId + recipient.Index] = MessageStatus.Failed;
                        }
                    }
                    catch
                    {
                        results[message.MessageId + recipient.Index] = MessageStatus.Failed;
                    }
                });
            });
        });

        var status = results.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);
        await UpdateProviderWithResult(messages, results, null, null, ct);

        return status;
    }
}
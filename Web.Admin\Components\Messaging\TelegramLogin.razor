﻿@page "/messaging/providers/telegram-login"
@using LendQube.Infrastructure.Core.AppSettings
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Messaging.Providers

@inject DefaultAppConfig config
@inject ILogManager<TelegramLogin> logger
@inject TelegramSessionStore sessionStore

@inherits AuthComponentBase

@attribute [Authorize(Policy = MessagingNavigation.MessageProviderConfigEditPermission)]

<PageTitle>@Title</PageTitle>

@if (isLoading)
{
    <div>
        <p>
            Loading
            <span> &nbsp;</span>
            <i class="fa fa-spinner fa-spin"></i>
        </p>
    </div>
}
else
{
    <div class="pg-actions">
        <ul class="breadcrumbs">
            <li class="__item">
                <a href="/messaging/providers">
                    <i class="fa fa-arrow-left"></i>
                </a>
            </li>
            <li class="__item">
                <a>Setup Provider: @Title</a>
            </li>
        </ul>
    </div>

    @if (!isLoggedIn)
    {
        <FormComponent Title="Telegram Verify" Model="LoginModel" OnValidSubmit="Submit" FormName="Telegram_Verify" FormMessage="Message">
            <BodyContent>
                @if (requiresVerificationCode)
                {
                    <div class="form-row">
                        <label class="form-label" for="VerificationCode">Verification Code</label>
                        <InputText type="text" @bind-Value="context.VerificationCode" class="form-input" aria-required="true" placeholder="Verification Code" />
                        <ValidationMessage For="() => context.VerificationCode" class="text-danger" />
                    </div>
                }
                @if (requiresPassword)
                {
                    <div class="form-row">
                        <label class="form-label" for="Password">Password</label>
                        <InputText type="password" @bind-Value="context.Password" class="form-input" aria-required="true" placeholder="Password" />
                        <ValidationMessage For="() => context.Password" class="text-danger" />
                    </div>
                }
            </BodyContent>
        </FormComponent>
    }
    else
    {
        <StatusMessage Message=Message />
    }
}
@code {
    private bool isLoading = true;
    private bool isLoggedIn;
    private bool requiresVerificationCode;
    private bool requiresPassword;
    private TelegramLoginVM LoginModel = new();
    private StatusMessageBuilder Message = new();
    private WTelegram.Client client;
    private string Title => "Telegram Login";

    protected override async Task OnInitializedAsync()
    {
        try
        {
            InitializeClient();

            var loginResult = await AttemptTelegramLogin(config.Telegram?.PhoneNumber);
            isLoggedIn = string.IsNullOrWhiteSpace(loginResult);

            if (isLoggedIn)
            {
                Message.Success("Telegram is all set");
            }
            else
            {
                requiresVerificationCode = true;
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.AdminWeb, EventAction.Api, ex, "Could not set up Telegram during initialization.");
        }
        finally
        {
            isLoading = false;
        }
    }

    private void InitializeClient()
    {
        if (config.Telegram == null)
            return;
        client = new WTelegram.Client(what => TelegramProvider.TelegramConfig(what, config), sessionStore);
    }

    private async Task<string> AttemptTelegramLogin(string loginInfo)
    {
        try
        {
            return client.User == null ? await client.Login(loginInfo) : string.Empty;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.AdminWeb, EventAction.Api, ex, "Could not log in to Telegram.");
            return "Error";
        }
    }

    private async Task Submit()
    {
        string loginInfo = GetLoginInfo();
        var result = await AttemptTelegramLogin(loginInfo);

        if (string.IsNullOrWhiteSpace(result))
        {
            requiresVerificationCode = false;
            requiresPassword = false;
            isLoggedIn = true;
            Message.Success("You have been logged in successfully.");
        }
        else
        {
            HandleLoginResult(result);
        }

        StateHasChanged();
    }

    private string GetLoginInfo() => requiresVerificationCode ? LoginModel.VerificationCode : requiresPassword ? LoginModel.Password : config.Telegram?.PhoneNumber;

    private void HandleLoginResult(string result)
    {
        switch (result)
        {
            case "verification_code":
                requiresVerificationCode = true;
                requiresPassword = false;
                Message.Info("Please input the verification code sent to your number and submit.");
                break;

            case "password":
                requiresVerificationCode = false;
                requiresPassword = true;
                Message.Info("Please input your password and submit.");
                break;

            default:
                Message.Error("Unable to log you in because a Telegram configuration is missing.");
                break;
        }
    }

    private sealed class TelegramLoginVM
    {
        public string VerificationCode { get; set; }
        public string Password { get; set; }
    }
}


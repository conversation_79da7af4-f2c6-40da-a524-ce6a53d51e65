﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Infrastructure.Core.Database.AnalyticsTriggers;

namespace LendQube.Infrastructure.Collection.Analytics;

static partial class AnalyticsScript
{
    private static void PrepareAgentWorkflowDashboardAnalyticsTrigger(AnalyticsTriggerModel trigger)
    {
        PrepareAgentTotalAssignedTrigger(trigger);
        PrepareAgentTotalOpenedTrigger(trigger);
        PrepareAgentTotalClosedTrigger(trigger);
    }

    private static void PrepareAgentTotalAssignedTrigger(AnalyticsTriggerModel trigger)
    {
        //add dashboard trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(AgentWorkflowAnalytics),
            Condition = $@"{AnalyticsTriggerHelpers.NewRecordCondition} AND NEW.""{nameof(AgentWorkflowTask.Assigned)}"" IS NOT NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(AgentWorkflowAnalytics.TotalAssigned),
                    RightExpression = $@" ""{nameof(AgentWorkflowAnalytics.TotalAssigned)}"" + 1"
                },
                new()
                {
                    LeftExpression = nameof(AgentWorkflowAnalytics.LastModifiedDate),
                    RightExpression = AnalyticsTriggerHelpers.CurrentDateTime
                }
            ],
            UpdateScriptWhere = $@"""{nameof(AgentWorkflowAnalytics.UserId)}"" = NEW.""{nameof(AgentWorkflowTask.UserId)}""",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(AgentWorkflowAnalytics.UserId), nameof(AgentWorkflowAnalytics.TotalAssigned), nameof(AgentWorkflowAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Column, Value = $@"""{nameof(AgentWorkflowTask.UserId)}""" }, new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDateTime }]
            }
        });

        //add dashboard time trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(AgentWorkflowTimeAnalytics),
            Condition = $@"{AnalyticsTriggerHelpers.NewRecordCondition} AND NEW.""{nameof(AgentWorkflowTask.Assigned)}"" IS NOT NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(AgentWorkflowTimeAnalytics.TotalAssigned),
                    RightExpression = $@" ""{nameof(AgentWorkflowTimeAnalytics.TotalAssigned)}"" + 1"
                },
                new()
                {
                    LeftExpression = nameof(AgentWorkflowTimeAnalytics.LastModifiedDate),
                    RightExpression = AnalyticsTriggerHelpers.CurrentDateTime
                }
            ],
            UpdateScriptWhere = $@"""{nameof(AgentWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate} AND ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = NEW.""{nameof(AgentWorkflowTask.UserId)}""",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(AgentWorkflowTimeAnalytics.Date), nameof(AgentWorkflowTimeAnalytics.UserId), nameof(AgentWorkflowTimeAnalytics.TotalAssigned), nameof(AgentWorkflowTimeAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDate }, new() { Type = InsertValueType.Column, Value = $@"""{nameof(AgentWorkflowTask.UserId)}""" },
                    new() { Type = InsertValueType.Value, Value = "1" }, new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDateTime }]
            }
        });
    }

    private static void PrepareAgentTotalOpenedTrigger(AnalyticsTriggerModel trigger)
    {
        //add dashboard trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(AgentWorkflowAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Opened)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Opened)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(AgentWorkflowAnalytics.TotalOpened),
                    RightExpression = $@" ""{nameof(AgentWorkflowAnalytics.TotalOpened)}"" + 1"
                },
                new()
                {
                    LeftExpression = nameof(AgentWorkflowAnalytics.LastModifiedDate),
                    RightExpression = AnalyticsTriggerHelpers.CurrentDateTime
                }
            ],
            UpdateScriptWhere = $@"""{nameof(AgentWorkflowAnalytics.UserId)}"" = NEW.""{nameof(AgentWorkflowTask.UserId)}""",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(AgentWorkflowAnalytics.UserId), nameof(AgentWorkflowAnalytics.TotalOpened), nameof(AgentWorkflowAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Column, Value = $@"""{nameof(AgentWorkflowTask.UserId)}""" }, new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDateTime }]
            }
        });

        //add dashboard time trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(AgentWorkflowTimeAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Opened)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Opened)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(AgentWorkflowTimeAnalytics.TotalOpened),
                    RightExpression = $@" ""{nameof(AgentWorkflowTimeAnalytics.TotalOpened)}"" + 1"
                },
                new()
                {
                    LeftExpression = nameof(AgentWorkflowTimeAnalytics.LastModifiedDate),
                    RightExpression = AnalyticsTriggerHelpers.CurrentDateTime
                }
            ],
            UpdateScriptWhere = $@"""{nameof(AgentWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate} AND ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = NEW.""{nameof(AgentWorkflowTask.UserId)}""",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(AgentWorkflowTimeAnalytics.Date), nameof(AgentWorkflowTimeAnalytics.UserId), nameof(AgentWorkflowTimeAnalytics.TotalOpened), nameof(AgentWorkflowTimeAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDate }, new() { Type = InsertValueType.Column, Value = $@"""{nameof(AgentWorkflowTask.UserId)}""" },
                    new() { Type = InsertValueType.Value, Value = "1" }, new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDateTime }]
            }
        });
    }

    private static void PrepareAgentTotalClosedTrigger(AnalyticsTriggerModel trigger)
    {
        //add dashboard trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(AgentWorkflowAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Removed)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Removed)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(AgentWorkflowAnalytics.TotalClosed),
                    RightExpression = $@" ""{nameof(AgentWorkflowAnalytics.TotalClosed)}"" + 1"
                },
                new()
                {
                    LeftExpression = nameof(AgentWorkflowAnalytics.LastModifiedDate),
                    RightExpression = AnalyticsTriggerHelpers.CurrentDateTime
                }
            ],
            UpdateScriptWhere = $@"""{nameof(AgentWorkflowAnalytics.UserId)}"" = NEW.""{nameof(AgentWorkflowTask.UserId)}""",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(AgentWorkflowAnalytics.UserId), nameof(AgentWorkflowAnalytics.TotalClosed), nameof(AgentWorkflowAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Column, Value = $@"""{nameof(AgentWorkflowTask.UserId)}""" }, new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDateTime}]
            }
        });

        //add dashboard time trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(AgentWorkflowTimeAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Removed)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Removed)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(AgentWorkflowTimeAnalytics.TotalClosed),
                    RightExpression = $@" ""{nameof(AgentWorkflowTimeAnalytics.TotalClosed)}"" + 1"
                },
                new()
                {
                    LeftExpression = nameof(AgentWorkflowTimeAnalytics.LastModifiedDate),
                    RightExpression = AnalyticsTriggerHelpers.CurrentDateTime
                }
            ],
            UpdateScriptWhere = $@"""{nameof(AgentWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate} AND ""{nameof(AgentWorkflowTimeAnalytics.UserId)}"" = NEW.""{nameof(AgentWorkflowTask.UserId)}""",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(AgentWorkflowTimeAnalytics.Date), nameof(AgentWorkflowTimeAnalytics.UserId), nameof(AgentWorkflowTimeAnalytics.TotalClosed), nameof(AgentWorkflowTimeAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDate }, new() { Type = InsertValueType.Column, Value = $@"""{nameof(AgentWorkflowTask.UserId)}""" },
                    new() { Type = InsertValueType.Value, Value = "1" }, new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDateTime }]
            }
        });
    }
}

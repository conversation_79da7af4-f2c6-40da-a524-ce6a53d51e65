﻿using iText.Kernel.Pdf;
using MetadataExtractor;
using MetadataExtractor.Formats.Exif;

namespace LendQube.Infrastructure.Core.FileManagement;

public static class FileTamperValidationHelper
{
    public static bool ValidateFileTamper(this byte[] file, string extension)
    {
        using var stream = new MemoryStream(file);
        if ("pdf".Equals(extension, StringComparison.OrdinalIgnoreCase))
            return ValidatePdfFileTamper(stream);

        return ValidateImageFileTamper(stream);
    }

    public static bool ValidateFileTamper(this MemoryStream stream, string extension)
    {
        stream.Position = 0;
        bool result;

        if ("pdf".Equals(extension, StringComparison.OrdinalIgnoreCase))
            result = ValidatePdfFileTamper(stream);
        else
            result = ValidateImageFileTamper(stream);

        stream.Position = 0;
        return result;
    }

    private static bool ValidatePdfFileTamper(MemoryStream stream)
    {
        using var reader = new PdfDocument(new PdfReader(stream));
        var info = reader.GetDocumentInfo();

        string creationDate = info.GetMoreInfo("CreationDate");
        string modificationDate = info.GetMoreInfo("ModDate");


        // If the creation date is not equal to the modification date, the document has likely been edited.
        if (!string.IsNullOrEmpty(creationDate) && !string.IsNullOrEmpty(modificationDate) && creationDate != modificationDate)
        {
            return true;  // The PDF has likely been edited.
        }

        return false;
    }

    private static bool ValidateImageFileTamper(MemoryStream stream)
    {
        var directories = ImageMetadataReader.ReadMetadata(stream);

        // Look for EXIF metadata related to creation and modification dates
        var exifDirectory = directories.OfType<ExifSubIfdDirectory>().FirstOrDefault();
        if (exifDirectory != null)
        {
            var creationDate = exifDirectory.GetDescription(ExifDirectoryBase.TagDateTimeOriginal);
            var modificationDate = exifDirectory.GetDescription(ExifDirectoryBase.TagDateTime);

            // Compare the creation date with the modification date
            if (!string.IsNullOrEmpty(creationDate) && !string.IsNullOrEmpty(modificationDate) && creationDate != modificationDate)
            {
                return true;  // The image has likely been edited.
            }
        }

        return false;
    }
}

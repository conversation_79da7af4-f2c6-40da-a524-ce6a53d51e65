﻿using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.DataPager;

internal sealed class PagedList<T> : AbstractPagedList<T>
    where T : class
{
    public PagedList(IQueryable<T> source, DataFilterAndPage filterAndPage, CancellationToken ct) : base(source, filterAndPage)
    {
        var localSource = Source
                       .Skip(SkipBy)
                       .Take(PageSize);
        
        if (ct.IsCancellationRequested)
            return;

        if (filterAndPage.AsyncNotSupportedDataSource)
        {
            UnboxedData = [.. localSource];
        }
        else
        {
            Data = localSource.QCache(filterAndPage.IsCacheable).ToListAsync(ct);
        }
    }
}

PETER OKACHIE
Senior Full Stack Developer & Technical Lead
📍 <PERSON><PERSON>. Marie, ON, Canada | 📞 +1-************ | 📧 <EMAIL>
🔗 LinkedIn | 🇨🇦 Canadian Work Authorization | 🇺🇸 TN Visa Eligible
________________________________________

PROFESSIONAL SUMMARY
Senior Full Stack Developer and Technical Lead with 8+ years of expertise architecting
enterprise-grade applications for highly regulated industries including healthcare, fintech,
and debt collection. Proven track record delivering mission-critical systems with 99.9%
uptime serving 35K+ users while maintaining strict security and compliance standards.
Expert in modern tech stack including .NET Core, React, Node.js, Azure Cloud, and AI
integration with demonstrated success leading cross-functional teams and mentoring
developers in fast-paced, remote environments.

Key Strengths: Enterprise debt collection platforms • AI-powered matching algorithms •
Multi-tenant SaaS architecture • Payment gateway integrations • Real-time analytics •
Microservices design • Regulatory compliance (HIPAA) • Team leadership
________________________________________

TECHNICAL SKILLS
Languages & Frameworks: C#, JavaScript, TypeScript, Python, .NET Core 9, React 18, Node.js, Blazor Server/WebAssembly
Frontend Technologies: Next.js, Tailwind CSS, Zustand, Socket.IO Client, Radzen UI Components
Backend & APIs: ASP.NET MVC, Entity Framework Core, RESTful APIs, Express.js, OpenIddict, OAuth2, JWT
Databases: PostgreSQL, MongoDB, MS SQL Server, Redis, Entity Framework with NodaTime
Cloud Platforms: Microsoft Azure (primary), AWS (EC2, Lambda, S3), Docker containerization
DevOps & Tools: CI/CD pipelines, GitHub Actions, Git, Jira, Postman, Swagger, Jest, Cypress
AI & Integrations: OpenAI API, AI-powered analytics, automated decision systems, content moderation
Payment Systems: Stripe, Paystack, Acquired Payment Gateway, multi-currency processing

________________________________________

PROFESSIONAL EXPERIENCE

Senior Full Stack Developer & Technical Lead – Verifiable Through LinkedIn
Silicon Management Company | Remote (United Kingdom) | April 2025 - Present

LendQube - Enterprise Debt Collection & Financial Management Platform (Present)
Mission-critical .NET Core/Blazor platform serving debt collection agencies and financial institutions
• Architected enterprise-grade debt collection platform with multi-tenant SaaS architecture serving multiple financial institutions
• Designed and implemented scalable microservices using .NET Core 9, PostgreSQL, and Entity Framework Core with background job processing
• Built secure authentication system with OpenIddict, role-based permissions, and comprehensive audit logging for regulatory compliance
• Developed automated payment processing engine supporting multiple gateways (Stripe, Acquired) with real-time transaction monitoring
• Created advanced reporting dashboard with real-time analytics, automated Excel exports, and business intelligence capabilities
• Engineered customer self-service portal featuring payment scheduling, promise-to-pay workflows, and mobile-responsive design
• Implemented configurable debt workflow automation with business rule engine reducing manual processing by 60%
• Maintained 99.9% system uptime through proactive monitoring, automated alerts, and disaster recovery protocols
• Tech Stack: .NET Core 9, Blazor Server, PostgreSQL, Entity Framework Core, Radzen UI, OpenIddict, NodaTime, Coravel

Senior Full Stack Developer & Team Lead
Tripple Time Technologies Ltd (Employed by Agency since 2017 – 2024)

HoneApp - AI-Powered Dating Platform (www.honeconnections.com) (2024-Present)
Full-stack React/Node.js social platform with advanced AI matching and real-time communication
• Scaled to 35,000+ registered users across North America achieving 4.6/5 app rating and 65% user retention rate
• Engineered real-time messaging infrastructure using Socket.IO supporting instant delivery, read receipts, and typing indicators
• Developed proprietary AI-powered personality matching algorithm with 9-category compatibility scoring system
• Implemented dual payment processing architecture supporting USD (Stripe) and NGN (Paystack) for international markets
• Built advanced content moderation system with AI-powered financial content detection and automated risk assessment
• Created comprehensive admin dashboard with real-time user analytics, monitoring tools, and revenue tracking
• Tech Stack: React 18, Vite, Node.js, MongoDB, Socket.IO, Stripe/Paystack, Cloudinary, AI/ML APIs

E-Commerce Marketplace (https://www.zionbusinesses.com/) (2025)
Ecommerce platform for LEGO US/Ethiopian retail client
• $172K+ annual transaction volume processed with 99.99% uptime
• 45% mobile conversion rate increase through responsive design optimization
• 80% improvement in page load speeds via Redis caching and CDN implementation
• Tech Stack: React, Redux, Node.js, MongoDB, Redis, Stripe API

HealthConnect Portal - HIPAA Healthcare Platform (https://exmstaffing.web.app/#/setter) (2023)
Patient management system for healthcare provider
• HIPAA-compliant architecture with end-to-end encryption and audit logging
• 70% reduction in administrative workload through automated appointment scheduling
• 35% increase in patient satisfaction scores via streamlined user experience
• Tech Stack: Flutter, Firebase, One Signal

FinTech Dashboard (2021)
Multi-tenant financial management system for Toronto startup
• Event-sourcing architecture with RabbitMQ for real-time transaction processing
• 25% client revenue increase through automated financial reporting
• 45% faster month-end closing via automated reconciliation processes
• Tech Stack: .NET Core, C#, React, RabbitMQ, PostgreSQL

Leadership & Technical Achievements:
• Led architectural transformation from monolithic to microservices for 20+ enterprise clients, improving scalability and maintainability
• Designed and implemented CI/CD pipelines using GitHub Actions, reducing deployment cycles from 1 week to 2 days
• Mentored and led team of 6+ junior developers, implementing code review processes that increased team productivity by 40%
• Optimized database performance through query optimization and indexing strategies, achieving 80% improvement in response times
• Pioneered AI integration initiatives including automated risk assessment, content moderation, and intelligent decision systems

Systems Engineer & Software Development Lead 
Nigeria Liquefied Natural Gas Limited | Sep 2013 - Dec 2016 
• Automated deployment for 3,000+ workstations using PowerShell and Windows Deployment Services 
• $500K annual savings by replacing Excel processes with custom C#/ASP.NET applications 
• Zero-downtime OS migration for enterprise systems across all departments 
• Led team of 4 developers designing scalable architecture with 75% latency reduction 
• 98% system uptime through proactive monitoring and maintenance 

EDUCATION & CERTIFICATIONS 
MBA, Corporate Finance | University of Abertay, Dundee, Scotland | 2010-2011 
B.Sc., Information Technology & Business Systems | Middlesex University, Dubai | 2007-2010 
Professional Diploma, Cybersecurity | Sault College, Ontario, Canada 

ADDITIONAL INFORMATION 
Languages: English (Fluent, IELTS: 8.0), French (Professional Working Proficiency) 
Work Authorization: Legally authorized to work in Canada, TN Visa eligible for US 
Remote Work: 8+ years remote collaboration with US/Canadian teams across EST/CST/PST 
Leadership: Proven track record mentoring developers and leading technical teams 
Security: Experience with highly regulated environments and security clearance eligibility

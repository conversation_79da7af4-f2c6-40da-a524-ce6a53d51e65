﻿@page "/debtworkflow/segment"
@using LendQube.Entities.Collection.Workflows.Debt
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using <PERSON><PERSON><PERSON>
@using Radzen.Blazor

@inherits GenericCrudVMTable<DebtSegment, DebtSegmentVM>

@attribute [Authorize(Policy = DebtWorkflowNavigation.DebtWorkflowSegmentIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<DebtSegment> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="StartTime">Start Time</label>
            <RadzenTimePicker @bind-TimeValue="@context.Start" Name="Start" Placeholder="Start Time eg 8:00 AM" />
            <ValidationMessage For="() => context.Start" class="text-danger" />
            <small class="text_sm_medium text_small">Time is in UTC</small>
        </div>
        <div class="form-row">
            <label class="form-label" for="EndTime">End Time</label>
            <RadzenTimePicker @bind-TimeValue="@context.End" Name="End" Placeholder="End Time eg 4:00 PM" />
            <ValidationMessage For="() => context.End" class="text-danger" />
            <small class="text_sm_medium text_small">Time is in UTC</small>
        </div>
        <div class="form-row">
            <label class="form-label" for="Priority">Priority</label>
            <RadzenDropDown @bind-Value=@context.Priority Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<DebtSegmentPriority>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Priority" AllowClear=true Placeholder="Select priority" class="form-input" />
            <ValidationMessage For="() => context.Priority" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="RuleIds">Rules</label>
            <RadzenDropDown @bind-Value=@context.RuleIds Data=@rules
                            TextProperty="@nameof(DebtSegmentRule.Name)" ValueProperty="@nameof(DebtSegmentRule.Id)"
                            Name="Rules" Multiple=true AllowClear=true Placeholder="Select applicable rules" class="form-input" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive" 
                            FilterOperator="StringFilterOperator.Contains" AllowFiltering="true"/>
            <ValidationMessage For="() => context.RuleIds" class="text-danger" />
        </div>
    </div>
    ;


    private List<DebtSegmentRule> rules = [];
    protected override void OnInitialized()
    {
        Title = "Debt Workflow";
        SubTitle = "Setup Debt Segments";
        FormBaseTitle = "Debt Segment";
        CreatePermission = DebtWorkflowNavigation.DebtWorkflowSegmentCreatePermission;
        EditPermission = DebtWorkflowNavigation.DebtWorkflowSegmentEditPermission;
        DeletePermission = DebtWorkflowNavigation.DebtWorkflowSegmentDeletePermission;
        QuerySelector = DebtSegmentVM.Mapping;
        rules = Service.CrudService.Db.Many(Query<DebtSegmentRule>.All());
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter);
    }

    protected override ValueTask SubmitAdd() => SaveAdd(async (data, ct) =>
    {
        var result = await Service.CrudService.New(data, ct);

        if(result)
        {
            Service.CrudService.Db.InsertBulk(data.RuleIds.Select(x => new DebtSegmentRules { DebtSegmentId = data.Id, RuleId = x }), ct);
            await Service.CrudService.Uow.SaveAsync(ct);
        }
        return result;

    }, table.Refresh);


    protected override ValueTask StartEdit(DebtSegmentVM data, CancellationToken ct) => BaseEdit(() =>
    {
        EditModel = data.Get();
        return Task.CompletedTask;
    }, ct);

    protected override ValueTask SubmitEdit() => SaveEdit(async (data, ct) =>
    {
        var result = await Service.CrudService.Update(data, ct);

        if (result)
        {
            _ = await Service.CrudService.Db.DeleteAndSaveWithFilterAsync<DebtSegmentRules>(x => x.DebtSegmentId == data.Id, ct);
            Service.CrudService.Db.InsertBulk(data.RuleIds.Select(x => new DebtSegmentRules { DebtSegmentId = data.Id, RuleId = x }), ct);
            await Service.CrudService.Uow.SaveAsync(ct);
        }

        return result;

    }, table.Refresh);


    protected override ValueTask<bool> SubmitDelete(DebtSegmentVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}


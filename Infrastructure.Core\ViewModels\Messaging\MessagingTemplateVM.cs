﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.ViewModels.Messaging;

public sealed class MessagingTemplateVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<MessagingTemplate, MessagingTemplateVM>> Mapping = data => new MessagingTemplateVM
    {
        Id = data.Id,
        Name = data.Name,
        Description = data.Description,
        Types = data.Types,
        Keys = data.Keys,
        IsContainer = data.IsContainer,
        IsDisabled = data.DisabledOn.HasValue,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public static readonly Expression<Func<MessagingTemplate, MessagingTemplateVM>> EditMapping = data => new MessagingTemplateVM
    {
        Id = data.Id,
        Name = data.Name,
        Description = data.Description,
        IsDisabled = data.DisabledOn.HasValue,
        Keys = data.Keys,
        Types = data.Types,
        TextTemplate = data.TextTemplate,
        HtmlTemplate = data.HtmlTemplate,
    };

    public MessagingTemplate Get() => new()
    {
        Id = Id,
        Name = Name,
        Description = Description,
        Types = TypesList.CombineFlags(),
        Keys = Keys,
        DisabledOn = IsDisabled ? NodaTime.SystemClock.Instance.GetCurrentInstant() : null,
        TextTemplate = TextTemplate,
        HtmlTemplate = HtmlTemplate,
    };

    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string Name { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyTextWithSpecialCharactersWithSpacing)]
    public string Description { get; set; }

    [DisplayName("Types")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string TypesFormatted => string.Join(", ", Types.FlagsToList<MessageConfigTemplateType>());

    [DisplayName("Keys")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string KeysFormatted => string.Join(", ", Keys);

    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public bool IsContainer { get; set; }

    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public bool IsDisabled { get; set; }

    [Required, RemoveColumn]
    public List<MessageConfigTemplateType> TypesList { get; set; } = [];

    [TableDecorator(TableDecoratorType.HideColumn)]
    public List<string> Keys { get; set; } = [];

    [TableDecorator(TableDecoratorType.HideColumn)]
    public MessageConfigTemplateType Types { get; set; }

    [RemoveColumn, ValidString(ValidStringRule.NoScriptTag)]
    public string TextTemplate { get; set; }

    public string HtmlTemplate { get; set; }
}

﻿using Coravel.Invocable;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Logs;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using NodaTime;
using OpenIddict.Abstractions;

namespace LendQube.Infrastructure.Core.BackgroundTasks;

internal sealed class MaintenanceBackgroundTaskService(IUnitofWork uow, IClock clock,
    IOpenIddictAuthorizationManager openIddictAuthorizationManager, IOpenIddictTokenManager openIddictTokenManager) : IInvocable, ICancellableInvocable
{
    public CancellationToken CancellationToken { get; set; }

    public async Task Invoke()
    {
        await uow.Db.DeleteAndSaveWithFilterAsync<BackgroundTaskEventLog>(x => x.Status == BackgroundEventStatus.Success && (clock.GetCurrentInstant() - x.CreatedDate) >= Duration.FromDays(30), CancellationToken);
        await uow.Db.DeleteAndSaveWithFilterAsync<BackgroundTaskEventLog>(x => x.Status == BackgroundEventStatus.Failed && (clock.GetCurrentInstant() - x.CreatedDate) >= Duration.FromDays(40), CancellationToken);
        await uow.Db.DeleteAndSaveWithFilterAsync<MessageLog>(x => x.Status > MessageStatus.Sent && (clock.GetCurrentInstant() - x.CreatedDate.Value) >= Duration.FromDays(120), CancellationToken);
        await uow.Db.DeleteAndSaveWithFilterAsync<CustomerInbox>(x => (clock.GetCurrentInstant() - x.CreatedDate.Value) >= Duration.FromDays(120), CancellationToken);
        await uow.Db.DeleteAndSaveWithFilterAsync<UserAccessLog>(x => (clock.GetCurrentInstant() - x.CreatedDate.Value) >= Duration.FromDays(30), CancellationToken);
        await openIddictTokenManager.PruneAsync(DateTimeOffset.Now.AddDays(-1), CancellationToken);
        await openIddictAuthorizationManager.PruneAsync(DateTimeOffset.Now.AddDays(-1), CancellationToken);
    }
}

﻿@using System.Net.Http
@using System.Net.Http.Json
@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Routing
@using Microsoft.AspNetCore.Components.Web
@using static Microsoft.AspNetCore.Components.Web.RenderMode
@using Microsoft.AspNetCore.Components.Web.Virtualization
@using Microsoft.JSInterop
@using Microsoft.AspNetCore.Authorization
@using Microsoft.EntityFrameworkCore
@using NodaTime
@using LendQube.Web.Admin
@using LendQube.Web.Admin.Components
@using LendQube.Entities.Core.Extensions
@using LendQube.Infrastructure.Core.Navigation
@using LendQube.Infrastructure.Core.Extensions
@using LendQube.Infrastructure.Core.Telemetry
@using LendQube.Infrastructure.Core.Database.GenericSpecification
@using LendQube.Web.Admin.Components.Layout
@using LendQube.Infrastructure.Core.Components.Table
@using LendQube.Infrastructure.Core.Database.DataPager
@using LendQube.Infrastructure.Core.Components
@using LendQube.Infrastructure.Core.Components.Helpers
@inject IClock clock
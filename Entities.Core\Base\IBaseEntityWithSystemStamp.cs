﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Constants;
using NodaTime;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Entities.Core.Base;

public abstract class IBaseEntityWithSystemStamp
{
    [MaxLength(EntityConstants.DEFAULT_IP_FIELD_LENGTH)]
    public string CreatedByIp { get; set; } 
    [MaxLength(EntityConstants.DEFAULT_EMAIL_FIELD_LENGTH), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.ShowInInfo)]
    public string CreatedByUser { get; set; }
    [DbGuid(true)]
    public string CreatedByUserId { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.ShowInInfo)]
    public Instant? CreatedDate { get; set; }
    [MaxLength(EntityConstants.DEFAULT_IP_FIELD_LENGTH)]
    public string ModifiedByIp { get; set; }
    [MaxLength(EntityConstants.DEFAULT_EMAIL_FIELD_LENGTH)]
    public string ModifiedByUser { get; set; }
    [DbGuid(true)]
    public string ModifiedByUserId { get; set; }
    public Instant? LastModifiedDate { get; set; }
}

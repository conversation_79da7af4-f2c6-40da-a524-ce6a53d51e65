﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Messaging.Providers.ViewModels;
using LendQube.Infrastructure.Core.Telemetry;
using System.Net.Http.Json;
using System.Text.Json;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class TermiiProvider : AbstractMessageProvider, ITextMessageProvider
{
    public const string Name = "Termii";
    protected override MessageChannel SupportedChannel => MessageChannel.Sms;
    private readonly int bulkLimit = 8000;

    private readonly DefaultAppConfig config;
    private readonly HttpClient httpClient;
    private readonly ILogManager<TermiiProvider> logger;

    public TermiiProvider(IUnitofWork uow, DefaultAppConfig config, HttpClient httpClient, ILogManager<TermiiProvider> logger) : base(uow)
    {
        this.config = config;
        this.httpClient = httpClient;
        this.logger = logger;

        if (config.Termii != null)
        {
            SupportedCountryCodes = MessagingCompiledQueries.GetMessageProviderSupportedCountriesAndConfig(uow, Name);
            Config = SupportedCountryCodes.IsNullOrEmpty() ? new ProviderConfigVM { Disabled = true } : SupportedCountryCodes[0];
        }
        else
        {
            Config = new ProviderConfigVM { Disabled = true };
        }
    }

    public override ProviderConfigVM Config { get; }
    public IReadOnlyList<ProviderConfigVM> SupportedCountryCodes { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        var leadingMessage = messages[0];
        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        Dictionary<long, MessageStatus> result = [];
        TermiiMessageResponseVM typedResponse = null;

        foreach (var message in messages)
        {
            if(message.HasSeparateTemplateValues)
            {
                foreach (var (recipient, index) in message.Data.Select((x, i) => (x, i)))
                {
                    (result[message.MessageId + index], typedResponse) = await ProcessSingleMessage(message, recipient, ct);
                }
            }
            else
            {
                var total = message.Data.Count;
                var skip = 0;
                while (total > 0)
                {
                    (result[message.MessageId + skip], typedResponse) = await ProcessBulkMessage(message, message.Data.Skip(skip * bulkLimit).Take(bulkLimit), ct);
                    skip++;
                    total -= bulkLimit;
                }
            }
        }

        var status = result.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, result, typedResponse?.Balance, null, ct);
        return status;
    }

    private async Task<(MessageStatus, TermiiMessageResponseVM response)> ProcessSingleMessage(PreparedMessageVM message, SinglePreparedMessageVM recipient, CancellationToken ct)
    {
        string serverResponse = null;
        bool successful = false;
        TermiiMessageResponseVM typedResponse = null;
        try
        {
            var data = new TermiiMessageVM
            {
                To = recipient.PhoneNumbers.Select(p => $"{p.Code[1..]}{p.Number}").ToList(),
                From = "N-Alert", //config.Termii.SenderId,
                Sms = recipient.TextTemplate,
                ApiKey = config.Termii.ApiKey
            };

            var response = await httpClient.PostAsJsonAsync("sms/send/bulk", data, ct);
            successful = response.IsSuccessStatusCode;
            serverResponse = await response.Content.ReadAsStringAsync(ct);
            typedResponse = JsonSerializer.Deserialize<TermiiMessageResponseVM>(serverResponse);

        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.BackgroundTask, EventAction.SendSms, ex, $"Error occured while sending sms {message.MessageId}");
        }

        return (successful.ToMessageStatus(), typedResponse);
    }

    private async Task<(MessageStatus, TermiiMessageResponseVM response)> ProcessBulkMessage(PreparedMessageVM message, IEnumerable<SinglePreparedMessageVM> reciepients,  CancellationToken ct)
    {
        string serverResponse = null;
        bool successful = false;
        TermiiMessageResponseVM typedResponse = null;

        try
        {
            var data = new TermiiMessageVM
            {
                To = reciepients.SelectMany(m => m.PhoneNumbers.Select(p => $"{p.Code[1..]}{p.Number}")).ToList(),
                From = "N-Alert", //config.Termii.SenderId,
                Sms = message.TextTemplate,
                ApiKey = config.Termii.ApiKey
            };

            var response = await httpClient.PostAsJsonAsync("sms/send/bulk", data, ct);
            successful = response.IsSuccessStatusCode;
            serverResponse = await response.Content.ReadAsStringAsync(ct);
            typedResponse = JsonSerializer.Deserialize<TermiiMessageResponseVM>(serverResponse);

        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.BackgroundTask, EventAction.SendSms, ex, $"Error occured while sending sms {message.MessageId}");
        }

        return (successful.ToMessageStatus(), typedResponse);
    }
}

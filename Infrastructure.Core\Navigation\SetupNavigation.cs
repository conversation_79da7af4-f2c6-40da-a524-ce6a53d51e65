﻿namespace LendQube.Infrastructure.Core.Navigation;

public sealed class SetupNavigation : INavigationDescriptor, INavigatorHasPermissions
{
    public bool IsDisabled { get; set; } = false;
    public const string GroupName = "System Setup";
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = GroupName,
            Icon = "settings",
            Permission = CountryIndexPermission,
            Controller = "Countries",
            SubNavigation =
            [
                new() { Name = "Countries", Icon = "globe", Permission = CountryIndexPermission, Controller = "Countries" },
                new() { Name = "States", Icon = "globe", Permission = StatesIndexPermission, Controller = "States" },
                new() { Name = "Currencies", Icon = "dollar-sign", Permission = CurrencyIndexPermission, Controller = "Currencies" },
                new() { Name = "Customer Device Version", Icon = "smartphone", Permission = CustomerDeviceVersionIndexPermission, Controller = "CustomerDeviceVersion" },
                new() { Name = "Background Task Logs", Icon = "list", Permission = BackgroundTaskLogsPermission, Controller = "BackgroundTaskLogs" },
                new() { Name = "System Configuration", Icon = "settings", Permission = SysConfigIndexPermission, Controller = "SysConfig" },

            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.SetupIndex, GroupName, navs);
    }

    public void PreparePermissionDescriptions()
    {
        Navigator.PermissionDescription[CountryIndexPermission] = $"Can view countries and access {GroupName}";
        Navigator.PermissionDescription[CountryCreatePermission] = "Can create countries";
        Navigator.PermissionDescription[CountryEditPermission] = "Can modify countries";
        Navigator.PermissionDescription[CountryDeletePermission] = "Can delete countries";

        Navigator.PermissionDescription[StatesIndexPermission] = "Can view states";
        Navigator.PermissionDescription[StatesCreatePermission] = "Can create states";
        Navigator.PermissionDescription[StatesEditPermission] = "Can modify states";
        Navigator.PermissionDescription[StatesDeletePermission] = "Can delete states";

        Navigator.PermissionDescription[CurrencyIndexPermission] = "Can view currencies";
        Navigator.PermissionDescription[CurrencyCreatePermission] = "Can create currencies";
        Navigator.PermissionDescription[CurrencyEditPermission] = "Can modify currencies";
        Navigator.PermissionDescription[CurrencyDeletePermission] = "Can delete currencies";

        Navigator.PermissionDescription[CustomerDeviceVersionIndexPermission] = "Can view customer device versions";
        Navigator.PermissionDescription[CustomerDeviceVersionCreatePermission] = "Can create customer device versions";
        Navigator.PermissionDescription[CustomerDeviceVersionEditPermission] = "Can modify customer device versions";
        Navigator.PermissionDescription[CustomerDeviceVersionDeletePermission] = "Can delete customer device versions";


        Navigator.PermissionDescription[BackgroundTaskLogsPermission] = "Can view background tasks";
		Navigator.PermissionDescription[BackgroundTaskLogsEditPermission] = "Can edit background tasks";
		Navigator.PermissionDescription[BackgroundTaskLogsClearLogsPermission] = "Can clear background task data";

        Navigator.PermissionDescription[SysConfigIndexPermission] = "Can view system configuration";
		Navigator.PermissionDescription[SysConfigCreatePermission] = "Can manage system configuration";

		Navigator.PermissionDescription[BackgroundTasksPermission] = "Can manage background services";
    }

    public const string CountryIndexPermission = "Permission.Countries.Index";
    public const string CountryCreatePermission = "Permission.Countries.Create";
    public const string CountryEditPermission = "Permission.Countries.Edit";
    public const string CountryDeletePermission = "Permission.Countries.Delete";

    public const string StatesIndexPermission = "Permission.States.Index";
    public const string StatesCreatePermission = "Permission.States.Create";
    public const string StatesEditPermission = "Permission.States.Edit";
    public const string StatesDeletePermission = "Permission.States.Delete";

    public const string CurrencyIndexPermission = "Permission.Currency.Index";
    public const string CurrencyCreatePermission = "Permission.Currency.Create";
    public const string CurrencyEditPermission = "Permission.Currency.Edit";
    public const string CurrencyDeletePermission = "Permission.Currency.Delete";

    public const string CustomerDeviceVersionIndexPermission = "Permission.CustomerDeviceVersion.Index";
    public const string CustomerDeviceVersionCreatePermission = "Permission.CustomerDeviceVersion.Create";
    public const string CustomerDeviceVersionEditPermission = "Permission.CustomerDeviceVersion.Edit";
    public const string CustomerDeviceVersionDeletePermission = "Permission.CustomerDeviceVersion.Delete";


    public const string BackgroundTaskLogsPermission = "Permission.BackgroundTaskLogs.Index";
    public const string BackgroundTaskLogsEditPermission = "Permission.BackgroundTaskLogs.Edit";
    public const string BackgroundTaskLogsClearLogsPermission = "Permission.BackgroundTaskLogs.ClearLogs";

    public const string SysConfigIndexPermission = "Permission.SysConfig.Index";
	public const string SysConfigCreatePermission = "Permission.SysConfig.Create";

	public const string BackgroundTasksPermission = "Permission.Admin.BackgroundTasks";
}
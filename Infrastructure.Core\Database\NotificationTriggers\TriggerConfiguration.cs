﻿using System.Reflection;
using System.Text.Json.Nodes;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Core.Database.NotificationTriggers;

public sealed class TriggerConfiguration
{
    internal readonly IReadOnlyList<NotifyTrigger> Triggers;

    public TriggerConfiguration()
    {
        var interfaceType = typeof(IEntityHasNotifyTrigger);
        var asm = Assembly.GetExecutingAssembly().GetReferencedAssemblies().AsParallel().Where(x => x.Name != null && x.Name.StartsWith(CoreEntityConfig.EntitiesPrefix))
            .Select(Assembly.Load)
            .SelectMany(x => x.GetTypes())
            .Where(x => interfaceType.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
            .DistinctBy(x => x.Name)
            .AsParallel();

        ArgumentNullException.ThrowIfNull(asm);

        Triggers = asm
          .Select(x => Activator.CreateInstance(x) as IEntityHasNotifyTrigger)
          .Select(item =>
          new NotifyTrigger
          {
              Table = item.GetType().Name,
              Schema = item.Schema,
              DataType = item.GetType(),
              On = item.ChangesToObserve,
              Type = item.Types,
              TrackOldData = item.TrackOldData,
              ReturnOnlyId = item.ReturnOnlyId,
              Condition = item.ConditionScript,
          }).ToList();
    }

    public TriggerConfiguration(List<Type> typesToRegister)
    {
        ArgumentNullException.ThrowIfNull(typesToRegister);

        var interfaceType = typeof(IEntityHasNotifyTrigger);
        var asm = AppDomain.CurrentDomain.GetAssemblies()
            .AsParallel()
            .SelectMany(x => x.GetReferencedAssemblies())
            .Where(x => x.Name != null && (x.Name.StartsWith(CoreEntityConfig.EntitiesPrefix)))
            .Select(Assembly.Load)
            .SelectMany(x => x.GetTypes())
            .Where(x => typesToRegister.Contains(x) && interfaceType.IsAssignableFrom(x) && !x.IsInterface && !x.IsAbstract)
            .DistinctBy(x => x.Name)
            .AsParallel();

        ArgumentNullException.ThrowIfNull(asm);

        Triggers = asm
          .Select(x => Activator.CreateInstance(x) as IEntityHasNotifyTrigger)
          .Select(item =>
          new NotifyTrigger
          {
              Table = item.GetType().Name,
              Schema = item.Schema,
              DataType = item.GetType(),
              On = item.ChangesToObserve,
              Type = item.Types,
              TrackOldData = item.TrackOldData,
              ReturnOnlyId = item.ReturnOnlyId,
              Condition = item.ConditionScript,
          }).ToList();
    }
}


internal static class TriggerHelpers
{
	public static string CreateTriggerName(this NotifyTrigger trigger)
		=> $"Notify_OnChange_{trigger.Schema}_{trigger.Table}_" + trigger.Type.Select(x => Enum.GetName(typeof(TriggerType), x)).Aggregate((p, n) => $"{p}_Or_{n}") + "_"
		+ trigger.On.Select(x => Enum.GetName(typeof(TriggerChange), x)).Aggregate((p, n) => $"{p}_Or_{n}");

	public static string CreateTriggerNotifyTypeName(this NotifyTrigger trigger) => trigger.On.Select(x => Enum.GetName(typeof(TriggerChange), x)?.ToUpper()).Aggregate((p, n) => $"{p} OR {n}");
}


internal sealed class NotifyTrigger
{
	public required string Table { get; init; }
	public required string Schema { get; init; } 
	public required Type DataType { get; init; }
	public required TriggerChange[] On { get; init; }
    public required TriggerType[] Type { get; init; }
    public required bool TrackOldData { get; set; }
    public required bool ReturnOnlyId { get; set; }
    public required string Condition { get; set; }
    public string Name => this.CreateTriggerName();
	public string NotifyType => this.CreateTriggerNotifyTypeName();
}


public sealed record NotifyTriggerPayload(object? Id, JsonObject? OldData, JsonObject? NewData, string Action, string Table);
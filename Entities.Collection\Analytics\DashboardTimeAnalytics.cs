﻿using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Microsoft.EntityFrameworkCore;
using LendQube.Entities.Core.Attributes;

namespace LendQube.Entities.Collection.Analytics;

[DbTableFillFactor(70)]
public class DashboardTimeAnalytics : BaseEntityWithIdentityId<DashboardTimeAnalytics>
{
    public DateOnly Date { get; set; }
    public int TotalCustomersThatCreatedSchedule { get; set; }
    public decimal TotalAmountPaid { get; set; }

    public override void Configure(EntityTypeBuilder<DashboardTimeAnalytics> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.Date).IsUnique()
            .IncludeProperties(x =>
            new
            {
                x.TotalCustomersThatCreatedSchedule,
                x.TotalAmountPaid
            });
    }
}
﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Attributes;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public class AgentWorkTaskVM
{
    public static readonly Expression<Func<AgentWorkflowTask, AgentWorkTaskVM>> Mapping = data => new()
    {
        AssignedToFullName = data.User.FullName,
        EscalatedByFullName = data.EscalatedByUser.FullName,
        DebtSegmentName = data.DebtSegment.Name,
        DebtSegmentPriority = data.DebtSegment.Priority,
        Rules = data.DebtSegment.Rules.Select(x => x.Rule).Where(x => !x.Disabled).ToList(),
    };

    public string AssignedToFullName { get; set; }
    public string EscalatedByFullName { get; set; }
    public string DebtSegmentName { get; set; }
    public DebtSegmentPriority DebtSegmentPriority { get; set; }
    public List<DebtSegmentRule> Rules { get; set; }
}


public class RecordAgentWorkflowAction
{
    [Required]
    public WorkflowTaskAction? Action { get; set; }
    public long? NoteId { get; set; }
    public long? PtpId { get; set; }
    public string? PaymentId { get; set; }
    public decimal? Amount { get; set; }
    public DateOnly? NextCallbackDate { get; set; }

    public WorkflowTask Get(Instant now, decimal? amount) => new()
    {
        Action = Action,
        NoteId = NoteId,
        PtpId = PtpId,
        PaymentId = PaymentId,
        When = now,
        Amount = amount,
        NextCallbackDate = NextCallbackDate
    };
}

public record RequiredNoteVM(long Id, string Note);
public record AgentWorkflowPaymentVM(string Id, decimal Amount)
{
    public string AmountFormatted => Amount.ToString("n2");
}

public record AgentWorkflowPtpVM(long Id, decimal Amount)
{
    public string AmountFormatted => Amount.ToString("n2");
}

public class WorkflowEscalationVM
{
    [Required]
    public Guid EscalateTo { get; set; }
    [Required, ValidString(ValidStringRule.NoScriptTag)]
    public string Reason { get; set; }
}
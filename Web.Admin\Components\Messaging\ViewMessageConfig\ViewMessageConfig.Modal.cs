﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageConfig;

public partial class ViewMessageConfig
{
    private StatusMessageBuilder modalMessage = new();
    private string sendMessageModal => "SendMessageModal";
    private string addGroupModal => "AddGroupModal";

    private async Task StartSendMessage()
    {
        modalMessage.Close();
        SendMessageModel = new() { Subject = Data.Subject };
        await jSRuntime.OpenModal(sendMessageModal, Cancel);
    }

    private async ValueTask SendMessage()
    {
        modalMessage.Close();
        List<TemplateKeyValue> templateValues = [];
        if (!Data.Keys.IsNullOrEmpty())
        {
            templateValues = SendMessageModel.KeysWithValues.Select(x => new TemplateKeyValue(x.Key, x.Value)).ToList();
            if (templateValues.IsNullOrEmpty() && !Data.Keys.All(y => systemKeys.Contains(y)))
            {
                modalMessage.Warning("Please provide all template values");
                return;
            }
        }

        var recipients = await uow.Db.Queryable<MessagingGroupEntry>().Where(x => x.Group.ConfigGroups.Any(y => y.MessageConfigurationId == Data.Id))
            .Select(x => x.MessagingGroupId)
            .Union(uow.Db.Queryable<MessagingGroupQuery>().Where(x => x.Group.ConfigGroups.Any(y => y.MessageConfigurationId == Data.Id))
            .Select(x => x.MessagingGroupId)).ToListAsync(Cancel);

        var result = await MessageBuilder.New("Message Configuration", UserName)
            .Message(Data.Name, Data.Id)
            .WithRecipients(recipients, templateValues)
            .Send(uow, Cancel);

        await jSRuntime.CloseModal(sendMessageModal, Cancel);
        message.Set(result.IsSuccessful, "Message queued successfully", $"Queuing message failed. {result.Message}");
    }
}

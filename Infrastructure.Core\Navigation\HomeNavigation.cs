﻿namespace LendQube.Infrastructure.Core.Navigation;

internal sealed class HomeNavigation : INavigationDescriptor
{
    public bool IsDisabled { get; set; } = false;
    public readonly string GroupName = "Home";
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = GroupName,
            Icon = "home"
        };

        Navigator.SetupModuleNavigation(NavigationOrder.HomeIndex, GroupName, navs);
    }

}

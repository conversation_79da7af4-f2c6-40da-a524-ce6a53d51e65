﻿using System.Linq.Expressions;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Core.Database.DataPager;

namespace LendQube.Infrastructure.Core.Database.GenericCrud;

internal static class GenericColumnAndFilterService<T, TVM>
{
    public static ColumnList GetTableDefinition(TableSettings<TVM> settings)
    {
        ColumnList data = TableHelper.GetColumnDefinition<T, TVM>(settings);
        data.HasInfo = data.ColumnHeaders.Any(x => x.Name != nameof(IBaseEntityWithNumberId.Id) && (x.DecoratorTypes.HasDecoratorType(TableDecoratorType.ShowInInfo) || (settings.ColumnsToAdd?.Any(x => x.ShowInInfo) ?? false)));
        data.HasEdit = settings.HasEdit;
        data.HasDelete = settings.HasDelete;

        if (settings.ColumnsToAdd != null)
        {
            var specialColumns = new List<string> { nameof(IBaseEntityWithSystemStamp.CreatedDate), nameof(IBaseEntityWithSystemStamp.LastModifiedDate) };
            foreach (var item in settings.ColumnsToAdd)
            {
                if (specialColumns.Contains(item.NameString))
                {
                    data.AddDateColumn(data.ColumnHeaders, item.NameString);
                }
                else
                {
                    var navigationProperty = item.Name.Body is MemberExpression body ? body.Expression is MemberExpression member ? member?.Member?.Name : null : null;

                    var name = item.Name switch
                    {
                        { Body: MemberExpression bodyMember } => bodyMember.Member.Name,
                        { Body: UnaryExpression bodyUnary } => (bodyUnary.Operand as MemberExpression).Member.Name,
                        _ => throw new NotSupportedException($"{item.Name.Body.GetType()} not supported")
                    };

                    name = string.IsNullOrEmpty(navigationProperty) ? name :
                        $"{navigationProperty}.{name}";

                    var property = typeof(T)?.GetProperty(name);
                    var decorators = property != null ? (TableDecoratorAttribute)Attribute.GetCustomAttribute(property, typeof(TableDecoratorAttribute)) : null;

                    var column = new ColumnHeader
                    {
                        Name = name,
                        Title = string.IsNullOrEmpty(navigationProperty) ? name.SplitOnUpper().ToTitleCase() : navigationProperty.SplitOnUpper().ToTitleCase(),
                        DecoratorTypes = decorators?.Type ?? item.DecoratorTypes,
                        IsSortable = string.IsNullOrEmpty(navigationProperty),
                    };

                    if (item.Index == -1 || data.ColumnHeaders.Count < item.Index)
                        data.ColumnHeaders.Add(column);
                    else
                        data.ColumnHeaders.Insert(item.Index, column);

                    if (item.ShowInFilter)
                        data.ColumnFilters.AddFilter(new(column.Name, column.Title, property.PropertyType, property.CanWrite), column.DecoratorTypes, item.Index);
                }
            }
        }

        if (settings.HasDateColumns)
            data.AddDateColumn(data.ColumnHeaders, nameof(IBaseEntityWithSystemStamp.CreatedDate))
                .AddDateColumn(data.ColumnHeaders, nameof(IBaseEntityWithSystemStamp.LastModifiedDate));

        if (settings.ShowUserInfo)
            data.AddUserInfoColumns(data.ColumnHeaders, nameof(IBaseEntityWithSystemStamp.CreatedByUser))
                .AddUserInfoColumns(data.ColumnHeaders, nameof(IBaseEntityWithSystemStamp.ModifiedByUser));

        return data;
    }
}

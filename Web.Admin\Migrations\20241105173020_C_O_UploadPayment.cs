﻿using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_UploadPayment : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .Annotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .Annotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure,PlacementAmend")
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure,PlacementAmend")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Stripe")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.AlterColumn<Instant>(
                name: "ExpiresOn",
                schema: "collection",
                table: "CustomerHold",
                type: "timestamp with time zone",
                nullable: true,
                oldClrType: typeof(Instant),
                oldType: "timestamp with time zone");

            migrationBuilder.AddForeignKey(
                name: "FK_DiscountConfig_AspNetRoles_RoleId",
                schema: "collection",
                table: "DiscountConfig",
                column: "RoleId",
                principalSchema: "public",
                principalTable: "AspNetRoles",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_DiscountConfig_AspNetRoles_RoleId",
                schema: "collection",
                table: "DiscountConfig");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .Annotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .Annotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure,PlacementAmend")
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure,PlacementAmend")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.AlterColumn<Instant>(
                name: "ExpiresOn",
                schema: "collection",
                table: "CustomerHold",
                type: "timestamp with time zone",
                nullable: false,
                defaultValue: NodaTime.Instant.FromUnixTimeTicks(0L),
                oldClrType: typeof(Instant),
                oldType: "timestamp with time zone",
                oldNullable: true);
        }
    }
}

﻿using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text.Json.Serialization;
using LendQube.Entities.Core.Attributes;

namespace LendQube.Entities.Core.Constants;

public record KeyValueHelperVM([Required, ValidString(ValidStringRule.NoScriptTag)] string Key, [ValidString(ValidStringRule.NoScriptTag)] string Value)
{
    [JsonIgnore, NotMapped]
    public bool SaveField { get; set; }
}

public static class KeyHelperExtension
{
    public static string Get(this List<KeyValueHelperVM> data, string key)
        => data?.Find(x => key.Equals(x.Key, StringComparison.OrdinalIgnoreCase))?.Value;
    public static bool Exists(this List<KeyValueHelperVM> data, string key)
        => data?.Any(x => key.Equals(x.Key, StringComparison.OrdinalIgnoreCase)) ?? false;

    public static void Add(this List<KeyValueHelperVM> data, string key, string value)
    {
        if (data.Exists(key))
            return;
        else data.Add(new(key, value));
    }

    public static void Add(this List<KeyValueHelperVM> data, string key, string value, bool saveField)
    {
        if (data.Exists(key))
            return;
        else data.Add(new(key, value) { SaveField = saveField });
    }
}


public class KeyValueObjectHelperVM
{
    [Required, ValidString(ValidStringRule.NoScriptTag)]
    public string Key { get; set; }
    public object Value { get; set; }
}

public static class KeyHelperObjectExtension
{
    public static object Get(this List<KeyValueObjectHelperVM> data, string key)
        => data.Find(x => key.Equals(x.Key, StringComparison.OrdinalIgnoreCase))?.Value;
    public static string GetAsString(this List<KeyValueObjectHelperVM> data, string key)
        => data.Find(x => key.Equals(x.Key, StringComparison.OrdinalIgnoreCase))?.Value?.ToString()?.Trim();
    public static bool Exists(this List<KeyValueObjectHelperVM> data, string key)
        => data.Any(x => key.Equals(x.Key, StringComparison.OrdinalIgnoreCase));

    public static void Add(this List<KeyValueObjectHelperVM> data, string key, object value)
    {
        if (data.Exists(key))
            return;
        else data.Add(new KeyValueObjectHelperVM { Key = key, Value = value });
    }

    public static List<KeyValueHelperVM> ToKeyValueVM(this List<KeyValueObjectHelperVM> data)
        =>  data.Select(x => new KeyValueHelperVM(x.Key, x.Value.ToString())).ToList();
}
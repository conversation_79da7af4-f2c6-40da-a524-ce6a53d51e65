﻿@using Radzen.Blazor
<RadzenDatePicker style="display: block; width: 100%"
@bind-Value="@dateTime"
TValue="DateTime?"
Change="UpdateTime"
Name="@Name"
Placeholder="@Placeholder"
DateFormat="dd MMM, yyyy" />

@code {
    [Parameter]
    public DateTime DateValue { get; set; }

    [Parameter]
    public EventCallback<DateTime> DateValueChanged { get; set; }

    [Parameter]
    public string Name { get; set; }

    [Parameter]
    public string Placeholder { get; set; }

    private DateTime? dateTime { get; set; }

    protected override void OnInitialized()
    {
        dateTime = DateValue;
        base.OnInitialized();
    }

    void UpdateTime(DateTime? dateTime)
    {
        if (!dateTime.HasValue) return;
        DateValue = dateTime.Value;
        DateValueChanged.InvokeAsync(DateValue);
    }
}
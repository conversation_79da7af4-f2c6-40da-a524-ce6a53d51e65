﻿namespace LendQube.Infrastructure.Core.AppSettings;

public sealed class DefaultAppConfigProvidersVM
{
    public DeployState DeployState { get; set; }
    public DisallowedEmails DisallowedEmails { get; set; }
    public Url Url { get; set; }
    public Encryption Encryption { get; set; }
    public AzureStorage AzureStorage { get; set; }
    public TwilioSettings Twilio { get; set; }
    public FirebaseCloudMessaging FirebaseCloudMessaging { get; set; }
    public SendGridSettings SendGrid { get; set; }
    public ClickSend ClickSend { get; set; }
    public Termii Termii { get; set; }
    public Terragon Terragon { get; set; }
    public Mailgun Mailgun { get; set; }
    public Telegram Telegram { get; set; }
    public TextLocal TextLocal { get; set; }
    public Smtp Smtp { get; set; }
}


public sealed class DeployState
{
    public bool IsDemo { get; set; }
    public EnabledApplications EnabledApplications { get; set; }
    public bool RemoveTriggersOnShutdown { get; set; }
}

public sealed class EnabledApplications
{
    public bool Collections { get; set; }
}

public sealed class DisallowedEmails
{
    public string DisallowedDomains { get; set; }
    public string NoMessageDomains { get; set; }
}

public sealed class Url
{
    public string WebAsset { get; set; }
    public string WebAdmin { get; set; }
    public string WebClient { get; set; }
    public string WebHook { get; set; }
    public string WebBackground { get; set; }
    public string Logo { get; set; }
    public string Contact { get; set; }
}

public sealed class Encryption
{
    public string Key { get; set; }
    public string VI { get; set; }
}

public sealed class AzureStorage
{
    public string Url { get; set; }
    public string ConnectionString { get; set; }
}

public sealed class TwilioSettings
{
    public string Sid { get; set; }
    public string Token { get; set; }
    public string WhatsApp { get; set; }
    public string Call { get; set; }
}

public sealed class FirebaseCloudMessaging
{
    public string ProjectId { get; set; }
}

public sealed class SendGridSettings
{
    public string ApiKey { get; set; }
    public string SenderEmail { get; set; }
    public string SenderName { get; set; }
}

public sealed class ClickSend
{
    public string BaseUrl { get; set; }
    public string UserName { get; set; }
    public string ApiKey { get; set; }
    public string Source { get; set; }
    public string From { get; set; }
}

public sealed class Terragon
{
    public string TransactionalSmsUsername { get; set; }
    public string TransactionalSmsPassword { get; set; }
    public string SenderId { get; set; }
    public string HttpApiIpAddress { get; set; }
    public int HttpApiPort { get; set; }
    public string HostAddress { get; set; }
}

public sealed class Mailgun
{
    public string SenderName { get; set; }
    public string From { get; set; }
    public string Domain { get; set; }
    public string Url { get; set; }
    public string Key { get; set; }
}

public sealed class Termii
{
    public string ApiUrl { get; set; }
    public string ApiKey { get; set; }
    public string SecretKey { get; set; }
    public string SenderId { get; set; }
}

public sealed class Telegram
{
    public string ApiId { get; set; }
    public string ApiHash { get; set; }
    public string PhoneNumber { get; set; }
}

public sealed class TextLocal
{
    public string ApiKey { get; set; }
    public string From { get; set; }
    public string BaseUrl { get; set; }
}

public sealed class Smtp
{
    public string Host { get; set; }
    public int Port { get; set; }
    public string From { get; set; }
    public string SenderName { get; set; }
}

# LendQube Platform - Comprehensive Developer Documentation

## Table of Contents
1. [Overview](#overview)
2. [Architecture Overview](#architecture-overview)
3. [Project Structure](#project-structure)
4. [Core Components](#core-components)
5. [User Management & Authentication](#user-management--authentication)
6. [Customer Management](#customer-management)
7. [Payment Processing](#payment-processing)
8. [Messaging System](#messaging-system)
9. [Workflows & Agent Management](#workflows--agent-management)
10. [Dashboards & Analytics](#dashboards--analytics)
11. [Background Services](#background-services)
12. [External API Integrations](#external-api-integrations)
13. [Database & Data Flow](#database--data-flow)
14. [Development Setup](#development-setup)
15. [Key Workflows](#key-workflows)

---

## Overview

**LendQube** is a complete software system for running a lending business. It handles everything from customer management to payment collection.

### What LendQube Does
LendQube manages:

- **Customer information**: Names, addresses, phone numbers, loan balances
- **Payment processing**: Credit card payments, bank transfers, payment tracking
- **Communication**: Emails, text messages, payment reminders
- **Collections**: Overdue account management, agent assignments
- **Reporting**: Business dashboards, performance metrics
- **Automation**: Background tasks that run 24/7

**Main Purpose**: Replace manual processes with automated digital systems.

---

## Architecture Overview

LendQube is organized in three main layers:

```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                       │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────────────┐ │
│  │ Web.Admin   │  │  Web.Api    │  │ Web.BackgroundService│ │
│  │ (Blazor UI) │  │ (REST API)  │  │   (Background Jobs) │ │
│  └─────────────┘  └─────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                  Infrastructure Layer                       │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │Infrastructure.  │  │Infrastructure.  │  │Infrastructure│ │
│  │   Core          │  │  Collection     │  │ .ExternalApi│ │
│  │(Base Services)  │  │(Business Logic) │  │(3rd Party)  │ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌───���─────────────────────────────────────────────────────────┐
│                     Domain Layer                            │
│  ┌─────────────────┐           ┌─────────────────────────┐  │
│  │  Entities.Core  │           │  Entities.Collection    │  │
│  │ (Base Entities) │           │ (Business Entities)     │  │
│  └─────────────────┘           └─────────────────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

### **Layer Breakdown:**

**🖥️ Presentation Layer (User Interfaces)**
- **Web.Admin**: Staff dashboard for managing customers and business operations
- **Web.Api**: Customer portal backend for mobile apps and websites
- **Web.BackgroundService**: Automated processes that run continuously

**🔧 Infrastructure Layer (Business Logic)**
- **Infrastructure.Core**: Basic services used by all parts of the system
- **Infrastructure.Collection**: Loan and collection-specific business logic
- **Infrastructure.ExternalApi**: Connections to third-party services

**📋 Domain Layer (Data Definitions)**
- **Entities.Core**: Basic data structures and system entities
- **Entities.Collection**: Customer, loan, and payment data structures

### **How They Connect:**
1. **Users** interact with **Web.Admin** or **Web.Api**
2. **User interfaces** call **Infrastructure** services
3. **Infrastructure** services work with **Entity** data
4. **Background services** run automated tasks
5. **All data** is stored in the database

---

## Project Structure

### 🌐 **The Front Desk (Web Applications)**
Think of these as the different ways people interact with the system:

- **`Web.Admin`**: The staff dashboard - where loan officers, managers, and customer service reps log in to do their daily work
- **`Web.Api`**: The customer app backend - powers mobile apps or websites where customers can check their accounts
- **`Web.BackgroundService`**: The night shift worker - runs automatically to process payments, send emails, and do maintenance

### 🔧 **The Engine Room (Infrastructure Projects)**
These are like the different departments in your lending company:

- **`Infrastructure.Core`**: The IT department - handles user logins, sends emails/texts, manages the database
- **`Infrastructure.Collection`**: The loan department - everything about loans, payments, and collecting money
- **`Infrastructure.ExternalApi`**: The partnerships team - connects to credit card companies, SMS providers, etc.

### 📋 **The Filing Cabinets (Entity Projects)**
These define what information the system stores:

- **`Entities.Core`**: Basic business forms - user accounts, system settings, general stuff every business needs
- **`Entities.Collection`**: Customer files - loan applications, payment history, contact info, everything about borrowers

### 🛠️ **The Toolbox (Supporting Files)**
- **`Devops/`**: Instructions for deploying the system to production servers
- **`TestScripts/`**: Tools for testing that everything works correctly

### **Real-World Analogy:**
Imagine you're running a small loan office:
- **Web.Admin** = Your office computer system where staff work
- **Web.Api** = Your customer website/mobile app  
- **Web.BackgroundService** = Automated systems that work overnight
- **Infrastructure** = Your office departments (IT, loans, partnerships)
- **Entities** = Your filing system with customer folders and forms

---

## Core Components

### 1. **The System Startup (How Everything Gets Connected)**
When LendQube starts up, it's like opening a business for the day - everything needs to be connected and ready:

**📁 File Location**: `Web.Admin/Program.cs`
```csharp
// From Web.Admin/Program.cs - This is like the morning checklist
builder.Services
    .AddBaseDIServices()           // Turn on basic office equipment
    .AddAdminServices()            // Set up the staff computers
    .AddAdminUserManagement()      // Enable the security system
    .AddRadzenServices()           // Load the user interface
    .AddBackgroundServices()       // Start the automated workers
    .AddAdminTriggers();          // Set up automatic responses
```

**Related Files**:
- `Infrastructure.Core/DependencyInjection/` - Basic office setup instructions
- `Infrastructure.Collection/DependencyInjection/` - Loan department setup
- `Web.Api/Program.cs` - Customer portal setup
- `Web.BackgroundService/Program.cs` - Automated worker setup

**In Simple Terms**: This is like a master switch that turns on all the different parts of the system and makes sure they can talk to each other.

### 2. **The Database Setup (Making Sure the Filing System is Ready)**
LendQube uses a database (like a digital filing cabinet) to store all information:

**📁 File Location**: `Web.Admin/Program.cs` (lines 35-39)
```csharp
await app.CreateMigration(scope);              // Update the filing system structure
await app.CreateCollectionPermissions(scope);  // Set up who can access what files
await app.FinishPermissionSetup(scope);        // Lock down security
```

**Related Files**:
- `Infrastructure.Core/Database/` - The main filing system rules
- `Web.Admin/Migrations/` - Instructions for updating the filing system
- `Infrastructure.Collection/Database/` - Loan-specific filing rules

**In Simple Terms**: This is like making sure your filing cabinets are organized correctly and that only the right people can access certain files. When the system starts, it automatically updates the filing system if needed.

---

## User Management & Authentication

### **Who Can Use the System**
LendQube has two types of people who can log in:

1. **Staff Members**: Employees who work for the lending company (loan officers, managers, customer service)
2. **Customers**: People who have borrowed money and need to check their accounts

### **The User Account System**
**📁 File Location**: `Infrastructure.Core/AdminUserManagement/ApplicationUser.cs`
```csharp
// This is like an employee badge or customer ID card
public class ApplicationUser : BaseEntityWithGuid
{
    public string UserName { get; set; }     // Like an employee ID number
    public string Email { get; set; }        // Email address for login
    public string FirstName { get; set; }    // Person's first name
    public string LastName { get; set; }     // Person's last name
    public string Role { get; set; }         // Job title (Staff, Customer, Manager)
    public PhoneNumber PhoneNumber { get; set; } // Phone number
    // ... additional properties
}
```

**Related Authentication Files**:
- `Infrastructure.Core/Authentication/` - The security guard system
- `Infrastructure.Core/PermissionsAndRoles/` - Rules about who can do what
- `Infrastructure.Core/AdminUserManagement/` - Tools for managing user accounts

### **How Login Works (Like a Security System)**
1. **Creating Accounts**: New employees get staff accounts, new borrowers get customer accounts
2. **Logging In**: People enter their email and password (like showing ID at the door)
3. **Permission Check**: The system checks what they're allowed to do (like different key cards for different rooms)
4. **Stay Logged In**: The system remembers you're logged in for a while, then asks you to log in again for security

### **Automatic Customer Account Creation**
When someone becomes a customer, the system automatically creates their login account:

**📁 File Location**: `Entities.Collection/Customers/CustomerProfile.cs` (lines 95-108)
```csharp
// This automatically creates a login account for new customers
public ApplicationUser GetUser(string domain)
{
    var user = new ApplicationUser()
    {
        UserName = Id,  // Customer number becomes their username
        Email = string.IsNullOrEmpty(Email) ? $"{Id}@{domain}" : Email,
        FirstName = FirstName,
        LastName = LastName,
        Role = SystemRoleConfig.CustomerRole  // Marks them as a customer
    };
    return user;
}
```

**Related Customer Files**:
- `Entities.Collection/Customers/` - All customer information files
- `Infrastructure.Collection/` - Customer business logic and services

**In Simple Terms**: When you add a new customer to the system, it automatically creates a login account for them so they can check their loan balance online.

---

## Customer Management

### **Customer Profile Structure**
The `CustomerProfile` entity is the heart of the customer management system:

**📁 File Location**: `Entities.Collection/Customers/CustomerProfile.cs`
```csharp
public class CustomerProfile : BaseEntityWithGuid
{
    // Basic Information
    public string AccountId { get; set; }        // Unique customer identifier
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public PhoneNumber MobileNumber { get; set; }
    
    // Financial Information
    public decimal BalanceTotal { get; set; }     // Total amount owed
    public decimal BalancePaid { get; set; }      // Amount already paid
    public decimal BalanceRemaining { get; set; } // Outstanding balance
    public decimal Discount { get; set; }         // Applied discounts
    
    // Collection Information
    public bool Blacklisted { get; set; }         // Blocked from new loans
    public bool CurrentlyAssigned { get; set; }   // Assigned to collection agent
    public DateOnly? NextCallbackDate { get; set; } // When to contact next
    
    // Related Data
    public virtual ICollection<CustomerAddress> Addresses { get; set; }
    public virtual ICollection<CustomerSchedule> Schedules { get; set; }
    public virtual ICollection<CustomerTransaction> Transactions { get; set; }
    public virtual ICollection<CustomerNote> Notes { get; set; }
    public virtual ICollection<CustomerPaymentMethod> PaymentMethods { get; set; }
}
```

**Related Customer Entity Files**:
- `Entities.Collection/Customers/CustomerAddress.cs` - Customer address information
- `Entities.Collection/Customers/CustomerContactDetail.cs` - Contact information
- `Entities.Collection/Customers/CustomerSchedule.cs` - Payment schedules
- `Entities.Collection/Customers/CustomerTransaction.cs` - Transaction history
- `Entities.Collection/Customers/CustomerNote.cs` - Staff notes
- `Entities.Collection/Customers/CustomerPaymentMethod.cs` - Saved payment methods
- `Entities.Collection/Customers/CustomerFlag.cs` - Customer flags and markers
- `Entities.Collection/Customers/CustomerHold.cs` - Account holds and restrictions

### **What Each Field Means**
- **AccountId**: A human-readable customer reference number
- **BalanceTotal**: The full amount the customer owes (principal + interest + fees)
- **BalancePaid**: How much the customer has already paid
- **BalanceRemaining**: Calculated field showing what's still owed
- **CurrentlyAssigned**: Whether a collection agent is actively working this account
- **NextCallbackDate**: When the system should remind staff to contact the customer

### **Customer Relationships**
Each customer can have:
- **Multiple Addresses**: Home, work, billing addresses
- **Payment Schedules**: When payments are due
- **Transaction History**: All payments and charges
- **Notes**: Staff comments and interaction history
- **Payment Methods**: Saved credit cards, bank accounts
- **Flags**: Special markers (VIP, problematic, etc.)

---

## Payment Processing

### **Payment Provider Architecture**
LendQube uses a flexible payment provider system that can integrate with multiple payment processors:

**📁 File Location**: `Infrastructure.Collection/Payments/IPaymentProvider.cs`
```csharp
public interface IPaymentProvider { }

// Different types of payment operations
internal interface ICreatesCustomerPaymentProvider
{
    Task<CustomerPaymentMethodConfiguration> CreateCustomer(CustomerScheduleVM profile, CancellationToken ct);
}

internal interface IChargeCardPaymentProvider
{
    Task<Result<bool>> CreateCharge(CustomerPaymentMethodConfiguration config, Transaction txn, CancellationToken ct);
    Task<(TransactionStatus, CustomerPaymentMethod)> ConfirmCharge(IUnitofWork uow, Transaction txn, CustomerPaymentMethod paymentMethod, CancellationToken ct);
}
```

**Payment System Files**:
- `Infrastructure.Collection/Payments/PaymentFactory.cs` - Creates payment providers
- `Infrastructure.Collection/Payments/TransactionHelper.cs` - Transaction utilities
- `Infrastructure.Collection/Payments/PaymentsVM.cs` - Payment view models
- `Infrastructure.Collection/Payments/Providers/` - Specific payment provider implementations
- `Infrastructure.ExternalApi/Acquired/` - Acquired payment provider integration

### **How Payment Processing Works**

1. **Customer Setup**
   - Customer provides payment method (credit card, bank account)
   - System creates a secure token with the payment provider
   - Payment method is saved for future use

2. **Creating a Charge**
   - System creates a payment request with the provider
   - Provider processes the payment
   - System receives confirmation or failure notification

3. **Payment Confirmation**
   - System updates customer balance
   - Transaction record is created
   - Customer is notified of payment status

### **Supported Payment Providers**
Currently integrated with:
- **Acquired**: Credit card processing (configuration in `externalapisettings.json`)
- Extensible architecture allows adding more providers

### **Transaction Flow**
```
Customer Payment Request
         ↓
Payment Provider (Acquired)
         ↓
Transaction Record Created
         ↓
Customer Balance Updated
         ↓
Notification Sent to Customer
```

---

## Messaging System

### **Multi-Channel Communication**
LendQube has a sophisticated messaging system that can send communications through multiple channels:

**📁 File Location**: `Entities.Core/Messaging/MessageChannel.cs` (enum definition)
```csharp
public enum MessageChannel
{
    Email,
    Sms,
    SmsAndEmail,
    PushNotification,
    PushNotificationAndEmail,
    PushNotificationAndSms,
    PushNotificationOrSms,
    SmsOrEmail,
    EmailOrSms,
    WhatsApp,
    Telegram,
    CustomerInbox,
    Text
}
```

**Core Messaging Files**:
- `Entities.Core/Messaging/` - Message entities and enums
- `Infrastructure.Core/Messaging/` - Core messaging services
- `Infrastructure.Collection/Messaging/` - Collection-specific messaging

### **Message Routing Logic**
The `MessageRouter` class intelligently routes messages based on channel preferences:

**📁 File Location**: `Infrastructure.Core/Messaging/MessageRouter.cs`
```csharp
// From Infrastructure.Core/Messaging/MessageRouter.cs
internal async Task<MessageStatus> RouteMessages(
    IUnitofWork uow, 
    long messageId, 
    ConcurrentDictionary<MessageChannel, List<PreparedMessageVM>> messages, 
    CancellationToken ct)
{
    // Determine which providers are available
    var emailProvider = GetEmailProvider();
    var textProvider = GetTextProvider();
    var customerInboxProvider = GetCustomerInboxProvider();
    
    // Route messages to appropriate channels
    // Handle fallback scenarios (e.g., if SMS fails, try email)
}
```

**Key Messaging Service Files**:
- `Infrastructure.Core/Messaging/MessageRouter.cs` - Routes messages to appropriate channels
- `Infrastructure.Core/Messaging/MessageComposer.cs` - Composes messages from templates
- `Infrastructure.Core/Messaging/MessageScheduler.cs` - Schedules message delivery
- `Infrastructure.Core/Messaging/MessagingFactory.cs` - Creates messaging components
- `Infrastructure.Core/Messaging/Providers/` - Message provider implementations
- `Infrastructure.Collection/Messaging/CollectionMessageTemplateKeyProvider.cs` - Collection message templates

### **How Messaging Works**

1. **Message Creation**
   - System generates a message (payment reminder, welcome email, etc.)
   - Message is prepared for multiple channels (HTML for email, plain text for SMS)

2. **Channel Selection**
   - System determines which channels to use based on:
     - Customer preferences
     - Message type
     - Available providers

3. **Delivery**
   - Messages are sent through appropriate providers
   - System tracks delivery status
   - Failed messages can trigger fallback channels

4. **Tracking**
   - All message activity is logged
   - Delivery status is tracked
   - Failed deliveries are recorded for retry

### **Message Templates**
Messages use templates that can be customized:
- **Payment Reminders**: "Your payment of $X is due on Y"
- **Welcome Messages**: "Welcome to LendQube, here's your account info"
- **Collection Notices**: "Your account is overdue, please contact us"

---

## Workflows & Agent Management

### **What are Workflows?**
Workflows are step-by-step processes for collection agents when customers don't pay on time.

**📁 Key Files:**
```
Infrastructure.Collection/Workflow/Debt/
├── WorkflowManagementService.cs .......... Assigns tasks to agents
├── AgentAvailabilityHandler.cs ........... Tracks which agents are working
├── WorkflowAssignmentService.cs .......... Rules for assigning customers to agents

Entities.Collection/Workflows/Debt/
├── AgentWorkflowTask.cs ................... Tasks assigned to agents
├── AgentWorkflowAvailability.cs .......... Agent status (available, busy, away)
├── DebtSegment.cs ......................... Customer categories (high risk, etc.)
```

### **How Agent Assignment Works**
1. **Customer falls behind on payment**
2. **System categorizes customer** (high risk, low risk, etc.)
3. **System finds available agent**
4. **Agent gets task in their queue**
5. **Agent contacts customer and records outcome**

### **Agent Task Actions**
**📁 File Location**: `Entities.Collection/Workflows/Debt/AgentWorkflowTask.cs`
```csharp
public enum WorkflowTaskAction
{
    NoContact,          // Couldn't reach customer
    ContactMade,        // Spoke with customer
    PaymentCollected,   // Customer paid
    PTPSetup,          // Payment plan arranged
    Escalated,         // Sent to supervisor
    CallBack           // Schedule follow-up
}
```

### **Agent Status System**
```csharp
public enum AgentAvailabilityStatus
{
    Available,      // Ready for new customers
    Busy,          // Working on current customers
    Away,          // On break
    SessionEnded   // Logged out
}
```

---

## Dashboards & Analytics

### **What are Dashboards?**
Dashboards show real-time business information - like a car dashboard but for your lending business.

**📁 Key Files:**
```
Entities.Collection/Analytics/
├── DashboardAnalytics.cs .................. Main business metrics
├── DashboardTimeAnalytics.cs ............. Daily/monthly trends

Infrastructure.Collection/Analytics/
├── ManageAnalyticsService.cs .............. Updates dashboard numbers
├── AnalyticsScript.Workflow.Agent.cs ..... Agent performance metrics
```

### **Main Dashboard Metrics**
**📁 File Location**: `Entities.Collection/Analytics/DashboardAnalytics.cs`
```csharp
public class DashboardAnalytics
{
    public int TotalCustomers { get; set; }              // Total customers
    public decimal TotalPlacementValue { get; set; }     // Total money loaned
    public decimal TotalPlacementValuePaid { get; set; } // Total money collected
    public int TotalClosedPlacements { get; set; }       // Completed loans
}
```

### **Dashboard Types**

#### **Manager Dashboard**
- Daily collections amount
- Number of overdue accounts
- Agent performance summary
- System alerts

#### **Agent Dashboard**
- Personal task list
- Customers to contact today
- Personal performance stats
- Next scheduled callbacks

#### **Executive Dashboard**
- Monthly collection trends
- Profit margins
- Customer growth
- System performance

### **How Analytics Update**
1. **Customer makes payment** → Analytics automatically update
2. **Agent completes task** → Performance metrics update
3. **New customer added** → Customer count increases
4. **Dashboard refreshes** → Users see latest numbers

---

## Background Services

### **Background Task Management**
The `Web.BackgroundService` project handles automated tasks:

**📁 File Location**: `Web.BackgroundService/Program.cs`
```csharp
// From Web.BackgroundService/Program.cs
builder.Services
    .AddCoreBackgroundServices()    // Core background tasks
    .AddBackgroundTriggers();       // Event-driven tasks

app.UseCoreBackgroundService();     // Start background processing
```

**Background Service Files**:
- `Web.BackgroundService/Program.cs` - Background service startup
- `Infrastructure.Core/BackgroundTasks/` - Core background task definitions
- `Infrastructure.Collection/BackgroundServices/CollectionBackgroundTaskManager.cs` - Collection-specific tasks
- `Entities.Core/BackgroundTasks/` - Background task entities

### **Types of Background Tasks**

1. **Scheduled Tasks**
   - Daily payment processing
   - Monthly statement generation
   - Overdue account identification

2. **Event-Driven Tasks**
   - Send welcome email when customer is created
   - Process payment confirmations
   - Update credit scores

3. **Maintenance Tasks**
   - Database cleanup
   - Log file rotation
   - Performance monitoring

### **How Background Services Work**

```
Timer/Event Trigger
        ↓
Background Task Queue
        ↓
Task Processor
        ↓
Database Update
        ↓
Notification (if needed)
```

**Example**: Payment Processing Background Task
1. Every morning at 9 AM, check for due payments
2. For each due payment, attempt to charge the customer's saved payment method
3. Update customer balance based on success/failure
4. Send notification to customer about payment status
5. Update collection workflow if payment fails

---

## External API Integrations

### **Third-Party Services**
LendQube integrates with external services for enhanced functionality:

#### **Acquired Payment Processing**
**📁 File Location**: `Infrastructure.ExternalApi/AppSettings/externalapisettings.json`
```json
// From Infrastructure.ExternalApi/AppSettings/externalapisettings.json
{
    "Acquired": {
        "BaseApiUrl": "https://test-api.acquired.com/v1/",
        "BaseAppUrl": "https://test-pay.acquired.com/v1/",
        "AppId": "12694857",
        "AppKey": "0d8b8884c627524eff4cb759691c2fc4"
    }
}
```

**External API Files**:
- `Infrastructure.ExternalApi/Acquired/` - Acquired payment provider implementation
- `Infrastructure.ExternalApi/Base/` - Base classes for external API integrations
- `Infrastructure.ExternalApi/DependencyInjection/` - External API service registration
- `Infrastructure.ExternalApi/ViewModels/` - External API data models

**What Acquired Does**:
- Processes credit card payments
- Handles secure payment tokenization
- Provides payment status updates
- Manages refunds and chargebacks

### **Integration Architecture**
```
LendQube System
       ↓
Infrastructure.ExternalApi
       ↓
Third-Party Service (Acquired)
       ↓
Response Processing
       ↓
Database Update
```

### **Adding New Integrations**
To add a new external service:
1. Create a new folder in `Infrastructure.ExternalApi`
2. Implement the required interfaces
3. Add configuration to `externalapisettings.json`
4. Register the service in dependency injection

---

## Database & Data Flow

### **Database Technology**
- **PostgreSQL**: Primary database
- **Entity Framework Core**: ORM for data access
- **Migrations**: Automatic schema updates

### **Key Data Entities**

#### **Customer Data**
```
CustomerProfile (Main customer record)
├── CustomerAddress (Multiple addresses)
├── CustomerContactDetail (Phone, email variations)
├── CustomerSchedule (Payment schedules)
├── CustomerTransaction (Payment history)
├── CustomerNote (Staff notes)
├── CustomerPaymentMethod (Saved payment methods)
├── CustomerFlag (Special markers)
└── CustomerHold (Account restrictions)
```

#### **Transaction Data**
```
Transaction (Individual payment/charge)
├── TransactionStatus (Pending, Success, Failed)
├── PaymentMethod (How payment was made)
├── Amount (Payment amount)
└── Timestamp (When it occurred)
```

### **Data Flow Examples**

#### **New Customer Registration**
```
1. Customer data entered in Web.Admin
2. CustomerProfile entity created
3. ApplicationUser account created
4. Welcome message queued
5. Background service sends welcome email
```

#### **Payment Processing**
```
1. Payment due date triggers background task
2. System retrieves customer payment method
3. External API call to payment provider
4. Transaction record created
5. Customer balance updated
6. Confirmation message sent
```

---

## Development Setup

### **Prerequisites**
- .NET 9 SDK
- PostgreSQL database
- Visual Studio 2022 or VS Code
- Git

### **Getting Started**
1. **Clone the repository**
   ```bash
   git clone [repository-url]
   cd LendQube
   ```

2. **Database Setup**
   - Install PostgreSQL
   - Create a new database
   - Update connection strings in `appsettings.json`

3. **Build the solution**
   ```bash
   dotnet build LendQube.sln
   ```

4. **Run migrations**
   ```bash
   dotnet ef database update --project Web.Admin
   ```

5. **Start the applications**
   ```bash
   # Admin interface
   dotnet run --project Web.Admin
   
   # API
   dotnet run --project Web.Api
   
   # Background services
   dotnet run --project Web.BackgroundService
   ```

### **Configuration Files**
- **`appsettings.json`**: Database connections, logging
- **`externalapisettings.json`**: Third-party service credentials
- **`appsettings.Production.json`**: Production-specific settings

---

## Key Workflows

### **1. Customer Onboarding Workflow**
```
New Customer Application
         ↓
Data Validation
         ↓
Credit Check (if applicable)
         ↓
Customer Profile Creation
         ↓
User Account Creation
         ↓
Welcome Message Sent
         ↓
Payment Method Setup
         ↓
Loan Schedule Creation
```

### **2. Payment Collection Workflow**
```
Payment Due Date Reached
         ↓
Automatic Payment Attempt
         ↓
Success? → Update Balance → Send Confirmation
         ↓
Failure? → Mark Overdue → Send Reminder
         ↓
Still Overdue? → Assign to Collection Agent
         ↓
Collection Activities (calls, emails)
         ↓
Payment Arrangement or Escalation
```

### **3. Message Delivery Workflow**
```
Message Trigger (payment due, welcome, etc.)
         ↓
Template Selection
         ↓
Customer Preference Check
         ↓
Channel Selection (email, SMS, both)
         ↓
Message Preparation
         ↓
Delivery Attempt
         ↓
Success? → Log Success
         ↓
Failure? → Try Fallback Channel → Log Result
```

### **4. Background Task Workflow**
```
Scheduled Time Reached
         ↓
Task Queue Check
         ↓
Task Execution
         ↓
Database Updates
         ↓
Error Handling (if needed)
         ↓
Completion Logging
         ↓
Next Schedule Setup
```

---

## Key Design Patterns

### **1. Repository Pattern**
Data access is abstracted through repositories:
```csharp
public interface IUnitofWork
{
    // Provides access to all repositories
    // Manages database transactions
}
```

### **2. Factory Pattern**
Payment providers are created using factories:
```csharp
public class PaymentFactory
{
    // Creates appropriate payment provider based on configuration
}
```

### **3. Strategy Pattern**
Different message channels use different strategies:
```csharp
public interface IMessageProvider
{
    Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct);
}
```

### **4. Dependency Injection**
All services are registered and injected automatically:
```csharp
builder.Services.AddScoped<ICustomerService, CustomerService>();
```

---

## Security Considerations

### **Data Protection**
- Customer financial data is encrypted
- Payment tokens are never stored in plain text
- Database connections use SSL

### **Authentication & Authorization**
- Role-based access control
- Session timeout management
- API key authentication for external integrations

### **Audit Trail**
- All customer interactions are logged
- Payment transactions are tracked
- User actions are recorded

---

## Performance Considerations

### **Database Optimization**
- Indexes on frequently queried fields
- Computed columns for calculated values
- Bulk operations for large data sets

### **Caching**
- Configuration data is cached
- Frequently accessed customer data is cached
- Message templates are cached

### **Background Processing**
- Long-running tasks are moved to background services
- Queue-based processing prevents blocking
- Retry mechanisms for failed operations

---

## Troubleshooting Common Issues

### **Database Connection Issues**
- Check PostgreSQL service is running
- Verify connection string in `appsettings.json`
- Ensure database exists and migrations are applied

### **Payment Processing Issues**
- Verify external API credentials in `externalapisettings.json`
- Check network connectivity to payment provider
- Review transaction logs for error details

### **Message Delivery Issues**
- Check message provider configuration
- Verify customer contact information is valid
- Review message logs for delivery status

---

## Future Enhancements

### **Planned Features**
- Mobile app for customers
- Advanced analytics and reporting
- Machine learning for risk assessment
- Additional payment providers
- Multi-tenant support

### **Technical Improvements**
- Microservices architecture
- Event sourcing for audit trails
- Real-time notifications
- API rate limiting
- Enhanced monitoring and alerting

---

## File Reference Guide

### **🌐 Web Applications**

#### **Web.Admin** (Blazor Server Admin Interface)
```
Web.Admin/
├── Program.cs                    # Application startup and configuration
├── Components/                   # Blazor components
├── Controllers/                  # MVC controllers for admin functions
├── Migrations/                   # Entity Framework database migrations
├── AppSettings/                  # Configuration files
└── wwwroot/                     # Static web assets
```

#### **Web.Api** (REST API)
```
Web.Api/
├── Program.cs                    # API startup and Swagger configuration
├── Controllers/                  # API controllers
└── Properties/                   # Project properties
```

#### **Web.BackgroundService** (Background Task Processing)
```
Web.BackgroundService/
├── Program.cs                    # Background service startup
├── Components/                   # Background service components
└── wwwroot/                     # Static assets
```

### **🏗️ Infrastructure Layer**

#### **Infrastructure.Core** (Base Infrastructure Services)
```
Infrastructure.Core/
├── AdminUserManagement/          # User account management
│   └── ApplicationUser.cs        # Main user entity
├── Authentication/               # Authentication services
├── BackgroundTasks/             # Background task framework
├── Database/                    # Database context and repositories
├── DependencyInjection/         # Service registration
├── Messaging/                   # Multi-channel messaging system
│   ├── MessageRouter.cs         # Routes messages to channels
│   ├── MessageComposer.cs       # Composes messages from templates
│   ├── MessageScheduler.cs      # Schedules message delivery
│   ├── MessagingFactory.cs      # Creates messaging components
│   └── Providers/               # Message provider implementations
├── PermissionsAndRoles/         # Role-based access control
├── FileManagement/              # File upload and management
├── Navigation/                  # UI navigation components
├── Reporting/                   # Report generation
└── ViewModels/                  # Data transfer objects
```

#### **Infrastructure.Collection** (Business Logic)
```
Infrastructure.Collection/
├── Analytics/                   # Business analytics
├── ApiControllers/              # Collection-specific API controllers
├── Authentication/              # Collection authentication
├── BackgroundServices/          # Collection background tasks
│   └── CollectionBackgroundTaskManager.cs
├── Database/                    # Collection database configuration
├── DependencyInjection/         # Collection service registration
├── Messaging/                   # Collection messaging
│   └── CollectionMessageTemplateKeyProvider.cs
├── Navigation/                  # Collection navigation
├── Payments/                    # Payment processing system
│   ├── IPaymentProvider.cs      # Payment provider interface
│   ├── PaymentFactory.cs        # Creates payment providers
│   ├── TransactionHelper.cs     # Transaction utilities
│   ├── PaymentsVM.cs           # Payment view models
│   └── Providers/               # Payment provider implementations
├── Schedules/                   # Payment scheduling
├── UploadServices/              # File upload services
├── ViewModels/                  # Collection view models
└── Workflow/                    # Business workflows
    └── Debt/                    # Debt collection workflows
```

#### **Infrastructure.ExternalApi** (Third-Party Integrations)
```
Infrastructure.ExternalApi/
├── Acquired/                    # Acquired payment provider
├── AppSettings/                 # External API configurations
│   └── externalapisettings.json # API keys and endpoints
├── Base/                        # Base classes for integrations
├── DependencyInjection/         # External API service registration
└── ViewModels/                  # External API data models
```

### **📊 Entity Layer**

#### **Entities.Core** (Base Entities)
```
Entities.Core/
├── Base/                        # Base entity classes
├── BaseUser/                    # Base user entities
├── Constants/                   # System constants
├── Enums/                       # System enumerations
├── Extensions/                  # Extension methods
├── Location/                    # Location-related entities
├── Logs/                        # Logging entities
├── Messaging/                   # Message entities
│   └── MessageChannel.cs        # Message channel enumeration
├── Reporting/                   # Report entities
├── Uploads/                     # File upload entities
└── VersionManagement/           # Version control entities
```

#### **Entities.Collection** (Business Entities)
```
Entities.Collection/
├── Analytics/                   # Analytics entities
├── Base/                        # Collection base entities
├── Collections/                 # Collection management entities
├── Customers/                   # Customer-related entities
│   ├── CustomerProfile.cs       # Main customer entity
│   ├── CustomerAddress.cs       # Customer addresses
│   ├── CustomerContactDetail.cs # Contact information
│   ├── CustomerSchedule.cs      # Payment schedules
│   ├── CustomerTransaction.cs   # Transaction history
│   ├── CustomerNote.cs          # Staff notes
│   ├── CustomerPaymentMethod.cs # Saved payment methods
│   ├── CustomerFlag.cs          # Customer flags
│   ├── CustomerHold.cs          # Account holds
│   ├── CustomerDiscount.cs      # Applied discounts
│   ├── CustomerPromiseToPay.cs  # Payment promises
│   └── Transaction.cs           # Individual transactions
├── Placements/                  # Loan placements
├── Setup/                       # System setup entities
└── Workflows/                   # Workflow entities
```

### **🛠️ Supporting Files**

#### **DevOps and Configuration**
```
Devops/
├── build-project.yaml           # Build pipeline configuration
└── [other deployment files]

TestScripts/                     # Testing utilities and scripts

Configuration Files:
├── .editorconfig               # Code formatting rules
├── .gitignore                  # Git ignore patterns
├── LendQube.sln               # Visual Studio solution file
├── README.md                   # Project readme
└── DEPLOYMENT_DOCUMENTATION.md # Deployment instructions
```

### **🔍 Key Files for Common Tasks**

#### **Adding New Customer Features**
- Start with: `Entities.Collection/Customers/`
- Business logic: `Infrastructure.Collection/`
- UI components: `Web.Admin/Components/`

#### **Payment Processing Changes**
- Interfaces: `Infrastructure.Collection/Payments/IPaymentProvider.cs`
- Factory: `Infrastructure.Collection/Payments/PaymentFactory.cs`
- External APIs: `Infrastructure.ExternalApi/`

#### **Messaging Modifications**
- Core routing: `Infrastructure.Core/Messaging/MessageRouter.cs`
- Templates: `Infrastructure.Collection/Messaging/`
- Channels: `Entities.Core/Messaging/MessageChannel.cs`

#### **Background Task Development**
- Task definitions: `Infrastructure.Core/BackgroundTasks/`
- Collection tasks: `Infrastructure.Collection/BackgroundServices/`
- Startup: `Web.BackgroundService/Program.cs`

#### **Database Changes**
- Entity definitions: `Entities.Core/` and `Entities.Collection/`
- Database context: `Infrastructure.Core/Database/`
- Migrations: `Web.Admin/Migrations/`

#### **Authentication and Authorization**
- User management: `Infrastructure.Core/AdminUserManagement/`
- Permissions: `Infrastructure.Core/PermissionsAndRoles/`
- Authentication: `Infrastructure.Core/Authentication/`

---

This documentation provides a comprehensive overview of the LendQube platform with specific file locations for each component. For specific implementation details, refer to the code comments and individual project documentation. For questions or clarifications, consult with the development team or create an issue in the project repository.
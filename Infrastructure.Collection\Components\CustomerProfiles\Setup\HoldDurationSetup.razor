﻿@page "/customers/holddurationsetup"
@using LendQube.Entities.Collection.Setup
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using Radzen.Blazor

@inherits GenericCrudTable<HoldDurationConfig>

@attribute [Authorize(Policy = ManageCustomersNavigation.HoldDurationSetupIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<HoldDurationConfig> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Length">Length</label>
            <InputNumber @bind-Value="context.Length" class="form-input" aria-required="true" placeholder="Length" />
            <ValidationMessage For="() => context.Length" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Duration">Duration</label>
            <RadzenDropDown @bind-Value=@context.Duration Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<HoldDuration>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Duration" Placeholder="Select duration" class="form-input" />
            <ValidationMessage For="() => context.Duration" class="text-danger" />
        </div>
    </div>
    ;

    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "Setup Hold Duration";
        FormBaseTitle = "Hold Duration";
        CreatePermission = ManageCustomersNavigation.HoldDurationSetupCreatePermission;
        EditPermission = ManageCustomersNavigation.HoldDurationSetupEditPermission;
        DeletePermission = ManageCustomersNavigation.HoldDurationSetupDeletePermission;
        NoGeneralSearch = true;
    }
}


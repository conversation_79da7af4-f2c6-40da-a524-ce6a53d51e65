﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Customers;

public class CustomerAddress : BaseEntityWithIdentityId<CustomerAddress>
{
    [DbGuid, Required]
    public string CustomerProfileId { get; set; }
    public string AddressLine1 { get; set; }
    public string AddressLine2 { get; set; }
    public string Locality { get; set; }
    public string PostCode { get; set; }
    public string City { get; set; }
    public string Country { get; set; }
    [Required]
    public string CountryCode { get; set; }

    public override string ToString() => $"{AddressLine1} <br> {AddressLine2} <br> {Locality} <br> {PostCode} <br> {City}";
}

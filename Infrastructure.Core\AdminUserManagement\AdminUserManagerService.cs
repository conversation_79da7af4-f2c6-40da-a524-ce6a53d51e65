﻿using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Logs;
using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;
using LendQube.Infrastructure.Core.Authentication;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Core.AdminUserManagement;

public sealed class AdminUserManagerService(UserManager<ApplicationUser> userManager,
    UserAccessService accessService, IHttpContextAccessor contextAccessor, IClock clock,
    GenericEntityCrudVMService<ApplicationUser, ApplicationAdminUserVM> crudService) : BaseSpecification<ApplicationUser>, IDisposable
{
    public async Task<GenericResponseVM> Delete(ApplicationAdminUserVM data, CancellationToken ct)
    {
        if (data.Email == SystemRoleConfig.SuperAdminEmail) return new GenericResponseVM { Successful = false, Message = "SuperAdmin cannot be deleted" };
        var result = await crudService.Db.DeleteAndSaveWithFilterAsync<ApplicationUser>(x => x.Id == data.Id && x.Email != SystemRoleConfig.SuperAdminEmail, ct);
        return new GenericResponseVM { Successful = result > 0 };
    }

    public Task<AdminUserEditVM> Get(Guid Id, CancellationToken ct) => crudService.Db.OneSelectAsync(Query<ApplicationUser,AdminUserEditVM>
        .Where(x => x.Id == Id)
        .Select(AdminUserEditVM.AdminUser), ct);

    public Task<AdminUserEditVM> GetByUsername(string Id, CancellationToken ct) => crudService.Db.OneSelectAsync(Query<ApplicationUser, AdminUserEditVM>
        .Where(x => x.UserName == Id)
        .Select(AdminUserEditVM.AdminUser), ct);

    public Task<IdentityResult> New(AdminUserRegisterVM data) => userManager.AddApplicationUser(data.GetUser(), data.Password, data.Roles, data.TransactionPin);

    public async Task<GenericResponseVM> Update(AdminUserEditVM data, string username)
    {
        var user = await userManager.FindByIdAsync(data.Id.ToString()) ?? await userManager.FindByNameAsync(data.Id.ToString());
        user.FirstName = data.FirstName;
        user.LastName = data.LastName;
        user.Email = data.Email;
        user.PhoneCode = data.PhoneCode;
        user.PhoneNumber = data.PhoneNumber;        

        var result = await userManager.UpdateAsync(user);

        if (result.Succeeded && !string.IsNullOrEmpty(data.Password))
        {
            if (data.Password.Contains(user.Email, StringComparison.OrdinalIgnoreCase) || (!string.IsNullOrEmpty(user.PhoneNumber) && data.Password.Contains(user.PhoneNumber, StringComparison.OrdinalIgnoreCase))
              || data.Password.Contains(user.FirstName, StringComparison.OrdinalIgnoreCase)
              || data.Password.Contains(user.LastName, StringComparison.OrdinalIgnoreCase)
              || data.Password.Contains(user.UserName, StringComparison.OrdinalIgnoreCase))
            {

                return new GenericResponseVM { Message = "Password cannot contain your name, email or phone number" };
            }

            result = await userManager.RemovePasswordAsync(user);
            if (result.Succeeded)
            {
                result = await userManager.AddPasswordAsync(user, data.Password);
                if (result.Succeeded)
                {
                    if (user.UserName != username)
                        user.MustChangePasswordOn = clock.GetCurrentInstant();

                    result = await userManager.UpdateAsync(user);
                }
            }
        }

        return new GenericResponseVM { Successful = result.Succeeded, Message = string.Join(",", result.Errors?.Select(x => x.Description)) };
    }

    public async Task<GenericResponseVM> ChangePassword(string userName, ChangePasswordVM data, CancellationToken ct)
    {
        var user = await userManager.FindByNameAsync(userName);

        var grantType = GrantType.ChangePin;

        var ip = contextAccessor.GetIpAddress();

        if (user == null || data.NewPassword.Contains(user.Email, StringComparison.OrdinalIgnoreCase) || (!string.IsNullOrEmpty(user.PhoneNumber) && data.NewPassword.Contains(user.PhoneNumber, StringComparison.OrdinalIgnoreCase))
             || data.NewPassword.Contains(user.FirstName, StringComparison.OrdinalIgnoreCase)
             || data.NewPassword.Contains(user.LastName, StringComparison.OrdinalIgnoreCase)
             || data.NewPassword.Contains(user.UserName, StringComparison.OrdinalIgnoreCase))
        {
            accessService.RecordFailedAccessIP(user.UserName, ip, grantType);

            return new GenericResponseVM { Message = "Password cannot contain your name, email or phone number" };
        }

        var result = await userManager.ChangePasswordAsync(user, data.OldPassword, data.NewPassword);
        if (result.Succeeded)
        {
            accessService.RecordAccessIP(user.UserName, ip, grantType);
            await crudService.UpdateWithFilter(x => x.UserName == userName && x.MustChangePasswordOn.HasValue, x => x.SetProperty(y => y.MustChangePasswordOn, (Instant?)null), ct);
        }
        else
        {
            accessService.RecordFailedAccessIP(user.UserName, ip, grantType);
        }

        return new GenericResponseVM { Successful = result.Succeeded, Message = string.Join(",", result.Errors?.Select(x => x.Description)) };
    }

    public Task<bool> ResetLockout(ApplicationAdminUserVM vm, CancellationToken ct) => crudService.UpdateWithFilter(x => x.UserName == vm.UserName, x => x.SetProperty(y => y.LockoutEnd, (DateTimeOffset?)null), ct);

    public async Task<GenericResponseVM> ChangeTransactionPin(string userName, ChangeTransactionPinVM data)
    {
        var user = await userManager.FindByNameAsync(userName);

        var grantType = GrantType.ChangePin;

        var ip = contextAccessor.GetIpAddress();

        if (!string.IsNullOrEmpty(user.WalletPinHash) && !string.IsNullOrEmpty(data.OldTransactionPin))
        {
            var verifyHash = userManager.PasswordHasher.VerifyHashedPassword(user, user.WalletPinHash, data.OldTransactionPin);
            if(verifyHash != PasswordVerificationResult.Success)
            {
                accessService.RecordFailedAccessIP(user.UserName, ip, grantType);
                return new GenericResponseVM { Message = "Old transaction pin is incorrect" };
            }
        }

        user.WalletPinHash = userManager.PasswordHasher.HashPassword(user, data.NewTransactionPin);
        var result = await userManager.UpdateAsync(user);

        if (result.Succeeded)
        {
            accessService.RecordAccessIP(user.UserName, ip, grantType);
        }
        else
        {
            accessService.RecordFailedAccessIP(user.UserName, ip, grantType);
        }

        return new GenericResponseVM { Successful = result.Succeeded, Message = string.Join(",", result.Errors?.Select(x => x.Description)) };
    }

    public ColumnList GetTableDefinition() => crudService.GetTableDefinition(new() { HasDateColumns = false });

    public async Task<bool> ResetUser2FA(ApplicationAdminUserVM vm)
    {

        var grantType = GrantType.AdminReset2FA;
        var ip = contextAccessor.GetIpAddress();

        var user = await userManager.FindByIdAsync(vm.Id.ToString());

        if (user == null)
        {
            accessService.RecordFailedAccessIP(vm.UserName, ip, grantType);
            return false;
        }

        var disable2FAResult = await userManager.SetTwoFactorEnabledAsync(user, false);
        if (!disable2FAResult.Succeeded)
        {
            accessService.RecordFailedAccessIP(vm.UserName, ip, grantType);
            return false;
        }

        var resetKeyResult = await userManager.ResetAuthenticatorKeyAsync(user);

        if (resetKeyResult.Succeeded)
        {
            accessService.RecordAccessIP(vm.UserName, ip, grantType);
        }
        else
        {
            accessService.RecordFailedAccessIP(vm.UserName, ip, grantType);
        }

        return resetKeyResult.Succeeded;
    }

    public ValueTask<TypedBasePageList<ApplicationAdminUserVM>> GetTypeBasedPagedData(DataFilterAndPage data, CancellationToken ct)
    {
        PrimaryCriteria = x => x.Role == SystemRoleConfig.AdminRole;

        if (!string.IsNullOrEmpty(data.TextFilter))
        {
            data.TextFilter = $"%{data.TextFilter}%";
            PrimaryCriteria = PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.FirstName, data.TextFilter)
            || EF.Functions.ILike(x.LastName, data.TextFilter) || EF.Functions.ILike(x.OtherNames, data.TextFilter) || EF.Functions.ILike(x.UserName, data.TextFilter)
            || EF.Functions.ILike(x.Email, data.TextFilter));
        }

        TagOrigination();
        data.OrderByColumn = null;
        data.OrderByColumnVM ??= nameof(ApplicationAdminUserVM.RegistrationDate);
        return crudService.GetTypeBasedPagedData(this, data, ApplicationAdminUserVM.AdminUser, ct: ct);
    }

    public void Dispose()
    {
        userManager.Dispose();
        GC.SuppressFinalize(this);        
    }
}

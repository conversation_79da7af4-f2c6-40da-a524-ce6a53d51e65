﻿using LendQube.Infrastructure.Core.AppSettings;
using Medallion.Threading;
using Microsoft.Extensions.Hosting;

namespace LendQube.Infrastructure.Core.Database.NotificationTriggers;

internal sealed class TriggerHostedService(IDistributedLockProvider distributedLockProvider, TriggerNotificationService service, DefaultAppConfig config) : BackgroundService
{
    private static readonly IReadOnlyList<string> ProjectsToExcludeFromLock = ["Web.Api"];
    private static readonly string appName = AppDomain.CurrentDomain.FriendlyName;

    private async Task CreateTriggerAndListen(CancellationToken stoppingToken)
    {
        var triggersCreated = await service.CreateAllTriggers(stoppingToken);
        if (!triggersCreated)
            return;

        await service.Listen(stoppingToken);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (!ProjectsToExcludeFromLock.Contains(appName))
        {
            await using var handle = await distributedLockProvider.TryAcquireLockAsync(appName, TimeSpan.FromMinutes(5), stoppingToken);
            if (handle == null)
                return;

            await CreateTriggerAndListen(stoppingToken);
        }
        else
            await CreateTriggerAndListen(stoppingToken);
    }

    public override async Task StopAsync(CancellationToken cancellationToken)
    {
        if (config.DeployState.RemoveTriggersOnShutdown)
            await service.RemoveTriggers(cancellationToken);
    }
}
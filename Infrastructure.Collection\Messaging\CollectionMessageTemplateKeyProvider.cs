﻿using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;

namespace LendQube.Infrastructure.Collection.Messaging;

public sealed class CollectionMessageTemplateKeyProvider(IUnitofWork uow) : IMessageTemplateKeyProvider
{
    private readonly IReadOnlyList<MessageTemplateKeys> supportedKeys = [MessageTemplateKeys.CompanyName, MessageTemplateKeys.AccountNumber, MessageTemplateKeys.Balance, MessageTemplateKeys.FortyPercentOfBalance];

    public async Task<Dictionary<string, List<TemplateKeyValue>>> GetKeysWithValues(List<string> userIds, List<string> keys, CancellationToken ct)
    {
        if (userIds.IsNullOrEmpty() || keys.IsNullOrEmpty())
            return [];

        var enumKeys = EnumExtensions.StringListToEnum<MessageTemplateKeys>(keys);
        if (supportedKeys.Any(x => enumKeys.Any(y => y == x)))
        {
            var data = await uow.Db.ManySelectAsync(Query<Placement>.Where(x => userIds.Contains(x.ProfileId))
                .Select(x => new TemplateKeySummaryData { ProfileId = x.ProfileId, CompanyName = x.Company, AccountNumber = x.SourceAccountNumber, CurrencySymbol = x.Profile.CurrencySymbol, Balance = x.Profile.BalanceRemaining }), ct);

            return data.GroupBy(x => x.ProfileId).ToDictionary(x => x.Key, x =>
            enumKeys.SelectMany(y =>
            x.Select
                (
                    z => y switch
                    {
                        MessageTemplateKeys.CompanyName => new TemplateKeyValue(MessageTemplateKeys.CompanyName.ToString().ToLower(), z.CompanyName),
                        MessageTemplateKeys.AccountNumber => new TemplateKeyValue(MessageTemplateKeys.AccountNumber.ToString().ToLower(), z.AccountNumber),
                        MessageTemplateKeys.Balance => new TemplateKeyValue(MessageTemplateKeys.Balance.ToString().ToLower(), z.BalanceString),
                        MessageTemplateKeys.FortyPercentOfBalance => new TemplateKeyValue(MessageTemplateKeys.FortyPercentOfBalance.ToString().ToLower(), z.FortyPercentOfBalance),
                        _ => null
                    }
                )
            )
            .Where(x => x != null)
            .GroupBy(x => x.Key)
            .Select(x => new TemplateKeyValue(x.Key, string.Join(", ", x.Select(y => y.Value))))
            .ToList());
        }

        return [];
    }
}

internal sealed class TemplateKeySummaryData
{
    public string ProfileId { get; set; }
    public string CompanyName { get; set; }
    public string AccountNumber { get; set; }
    public string CurrencySymbol { get; set; }
    public decimal Balance { get; set; }
    public string BalanceString => $"{CurrencySymbol}{Balance:n2}";
    public string FortyPercentOfBalance => $"{CurrencySymbol}{((Balance * 40) / 100m):n2}";
}
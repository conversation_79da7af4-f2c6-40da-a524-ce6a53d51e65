﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.Database.DataPager.Filters;

internal sealed class LessThanOrEqualsFilter() : ObjectFilter(ColumnFilterRule.LessThanOrEquals)
{
    public override Expression<Func<T, bool>> GenerateExpression<T>(Type propertyType, ComplexFilter vm, object value)
    {
        ParameterExpression pe = Expression.Parameter(typeof(T), "x");
        Expression expression = pe.GetDenaturedExpression(vm.ColumnName);

        BinaryExpression binaryExpression;
        if (propertyType.IsEnum())
        {
            ConstantExpression constantExpression = Expression.Constant(Convert.ToInt32(Enum.Parse(propertyType, value.ToString())));
            binaryExpression = Expression.LessThanOrEqual(Expression.ConvertChecked(expression, typeof(int)), constantExpression);
        }
        else if (propertyType.IsDateTime())
        {
            DateTime currentDate = value.ToString().ParseFromString().EndOfTheDay();
            UnaryExpression unaryExpression = Expression.ConvertChecked(Expression.Constant(currentDate), propertyType);
            binaryExpression = Expression.LessThanOrEqual(expression, unaryExpression);
        }
        else if (propertyType.IsDateOnly())
        {
            DateOnly currentDate = DateOnly.FromDateTime(value.ToString().ParseFromString()).AddDays(1);
            UnaryExpression unaryExpression = Expression.ConvertChecked(Expression.Constant(currentDate), propertyType);
            binaryExpression = Expression.LessThan(expression, unaryExpression);
        }
        else if (propertyType.IsInstant())
        {
            var currentDate = value.ToString().GetInstantFromStringGivenTimeZone(vm.UserTimeZone).StartOfNextDay();
            UnaryExpression unaryExpression = Expression.ConvertChecked(Expression.Constant(currentDate), propertyType);
            binaryExpression = Expression.LessThan(expression, unaryExpression);
        }
        else if (propertyType != typeof(string))
        {
            var converter = TypeDescriptor.GetConverter(propertyType);
            if (converter != null && converter.IsValid(value.ToString()))
            {
                value = converter.ConvertFrom(value.ToString());
                UnaryExpression unaryExpression = Expression.ConvertChecked(Expression.Constant(value), propertyType);
                binaryExpression = Expression.LessThanOrEqual(expression, unaryExpression);
            }
            else
            {
                return null;
            }
        }
        else
        {
            UnaryExpression unaryExpression = Expression.ConvertChecked(Expression.Constant(value), propertyType);
            binaryExpression = Expression.LessThanOrEqual(expression, unaryExpression);
        }

        return Expression.Lambda<Func<T, bool>>(binaryExpression, pe);
    }

    public override bool IsTypeSupported(ObjectFilterRule rule) => (Type.GetTypeCode(rule.Type) switch
    {
        TypeCode.Byte or TypeCode.SByte or TypeCode.UInt16 or TypeCode.UInt32 or TypeCode.UInt64 or TypeCode.Int16 or TypeCode.Int32 or TypeCode.Int64 or TypeCode.Decimal or TypeCode.Double or TypeCode.Single => true,
        _ => false,
    } || rule.Type.IsDate() || rule.Type.IsInstant()) && !rule.IsId && !rule.Type.IsFlagEnum();
}

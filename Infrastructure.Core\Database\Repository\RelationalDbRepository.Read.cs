﻿using System.Linq.Expressions;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.Specification;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.Repository;

partial class RelationalDbRepository
{
    #region Read Direct
    public ValueTask<T> ByIdAsync<T>(object id, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => GetDbSet<T>().FindAsync([id], cancellationToken: ct);

    public Task<bool> ExistsAsync<T>(Expression<Func<T, bool>> filter = null, CancellationToken ct = default) where T : class, IBaseEntityForRelationalDb =>
        filter == null ? GetDbSet<T>().AnyAsync(ct) : GetDbSet<T>().AnyAsync(filter, ct);

    public Task<int> CountAsync<T>(Expression<Func<T, bool>> filter = null, CancellationToken ct = default) where T : class, IBaseEntityForRelationalDb => GetDbSet<T>().QWhere(filter).CountAsync(ct);


    public T One<T>(SqlQueryBuilder<T> builder) where T : class, IBaseEntityForRelationalDb => builder.BuildOne(GetDbSet<T>());
    public Task<T> OneAsync<T>(SqlQueryBuilder<T> builder, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => builder.BuildOneAsync(GetDbSet<T>(), ct);

    public List<T> Many<T>(SqlQueryBuilder<T> builder) where T : class, IBaseEntityForRelationalDb => builder.BuildMany(GetDbSet<T>());
    public Task<List<T>> ManyAsync<T>(SqlQueryBuilder<T> builder, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => builder.BuildManyAsync(GetDbSet<T>(), ct);

    public TVM OneSelect<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder) where T : class, IBaseEntityForRelationalDb => builder.BuildOne(GetDbSet<T>());
    public Task<TVM> OneSelectAsync<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => builder.BuildOneAsync(GetDbSet<T>(), ct);

    public List<TVM> ManySelect<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder) where T : class, IBaseEntityForRelationalDb => builder.BuildMany(GetDbSet<T>());
    public Task<List<TVM>> ManySelectAsync<T, TVM>(SqlSelectQueryBuilder<T, TVM> builder, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => builder.BuildManyAsync(GetDbSet<T>(), ct);
    #endregion

    #region Read Via Spec
    public Task<bool> ExistsWithSpecAsync<T>(ISpecification<T> spec, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => spec.ToSqlBuilder().Query(GetDbSet<T>()).AnyAsync(ct);

    public IQueryable<T> NotTrackedWithSpec<T>(ISpecification<T> spec) where T : class, IBaseEntityForRelationalDb => spec.ToSqlBuilder().Query(GetDbSet<T>());

    public T OneWithSpec<T>(ISpecification<T> spec) where T : class, IBaseEntityForRelationalDb => spec.ToSqlBuilder().BuildOne(GetDbSet<T>());
    public Task<T> OneWithSpecAsync<T>(ISpecification<T> spec, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => spec.ToSqlBuilder().BuildOneAsync(GetDbSet<T>(), ct);

    public List<T> ManyWithSpec<T>(ISpecification<T> spec) where T : class, IBaseEntityForRelationalDb => spec.ToSqlBuilder().BuildMany(GetDbSet<T>());
    public Task<List<T>> ManyWithSpecAsync<T>(ISpecification<T> spec, CancellationToken ct) where T : class, IBaseEntityForRelationalDb => spec.ToSqlBuilder().BuildManyAsync(GetDbSet<T>(), ct);

    public TVM OneSelectWithSpec<T, TVM>(ISpecification<T> spec,
        Expression<Func<T, TVM>> selector,
        Expression<Func<TVM, bool>> filter = null,
        Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null) where T : class, IBaseEntityForRelationalDb
    => spec.ToSqlSelectBuilder(selector, filter, orderBy).BuildOne(GetDbSet<T>());

    public Task<TVM> OneSelectWithSpecAsync<T, TVM>(ISpecification<T> spec,
        Expression<Func<T, TVM>> selector,
        Expression<Func<TVM, bool>> filter = null,
        Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null,
        CancellationToken ct = default) where T : class, IBaseEntityForRelationalDb
    => spec.ToSqlSelectBuilder(selector, filter, orderBy).BuildOneAsync(GetDbSet<T>(), ct);

    public List<TVM> ManySelectWithSpec<T, TVM>(ISpecification<T> spec,
        Expression<Func<T, TVM>> selector,
        Expression<Func<TVM, bool>> filter = null,
        Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null) where T : class, IBaseEntityForRelationalDb
    => spec.ToSqlSelectBuilder(selector, filter, orderBy).BuildMany(GetDbSet<T>());

    public Task<List<TVM>> ManySelectWithSpecAsync<T, TVM>(ISpecification<T> spec,
        Expression<Func<T, TVM>> selector,
        Expression<Func<TVM, bool>> filter = null,
        Func<IQueryable<TVM>, IOrderedQueryable<TVM>> orderBy = null,
        CancellationToken ct = default) where T : class, IBaseEntityForRelationalDb
    => spec.ToSqlSelectBuilder(selector, filter, orderBy).BuildManyAsync(GetDbSet<T>(), ct);
    #endregion

}

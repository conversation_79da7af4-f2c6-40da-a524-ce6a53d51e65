﻿@page "/manageusers"

@using LendQube.Entities.Core.BaseUser
@using LendQube.Infrastructure.Core.AdminUserManagement
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using Radzen.Blazor

@inject AdminUserManagerService Service
@inject AdminUserRolesManagerService rolesService
@inject UserAccessService accessService
@inherits BaseTableComponentBase

@attribute [Authorize(Policy = ManageAdminUsersNavigation.UserIndexPermission)]


<PageTitle>@Title</PageTitle>

    <div class="pg-row grid grid-col-1 grid-tab-1">
        <div class="card">

            <div class="title-wrapper flex __justify-between __align-center">
                <span class="text_xl_medium">@SubTitle</span>
        </div>
        <StatusMessage @ref="TableMessage" />

        <DataTable T="ApplicationAdminUserVM" TableDefinition="TableDefinition" LoadData="Load" EditRow="StartEdit" DeleteRow="SubmitDelete" @ref="table" />

    </div>
</div>


<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Email">Email</label>
            <InputText @bind-Value="context.Email" class="form-input" aria-required="true" placeholder="Email" autocomplete="admin-noemail" />
            <ValidationMessage For="() => context.Email" class="text-danger" />
        </div>

        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="FirstName">First Name</label>
                <InputText @bind-Value="context.FirstName" class="form-input" aria-required="true" placeholder="First Name" autocomplete="admin-fname" />
                <ValidationMessage For="() => context.FirstName" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="LastName">Last Name</label>
                <InputText @bind-Value="context.LastName" class="form-input" aria-required="true" placeholder="Last Name" autocomplete="admin-lname" />
                <ValidationMessage For="() => context.LastName" class="text-danger" />
            </div>
        </div>

        <div class="form-row">
            <label class="form-label" for="OtherNames">Other Names</label>
            <InputText @bind-Value="context.OtherNames" class="form-input" aria-required="true" placeholder="Other Names" autocomplete="admin-oname" />
            <ValidationMessage For="() => context.OtherNames" class="text-danger" />
        </div>        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="PhoneCode">Phone Code</label>
                <InputText @bind-Value="context.PhoneCode" class="form-input" aria-required="true" placeholder="Phone Code" autocomplete="admin-pcode" />
                <ValidationMessage For="() => context.PhoneCode" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="PhoneNumber">Phone Number</label>
                <InputText @bind-Value="context.PhoneNumber" class="form-input" aria-required="true" placeholder="Phone Number" autocomplete="admin-pnumber" />
                <ValidationMessage For="() => context.PhoneNumber" class="text-danger" />
            </div>
        </div>

        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="Password">Password</label>
                <InputText type="password" @bind-Value="context.Password" class="form-input" aria-required="true" placeholder="Password" autocomplete="admin-pwd" />
                <ValidationMessage For="() => context.Password" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="ConfirmPassword">Confirm Password</label>
                <InputText type="password" @bind-Value="context.ConfirmPassword" class="form-input" aria-required="true" placeholder="Confirm Password" autocomplete="noadmin-password" />
                <ValidationMessage For="() => context.ConfirmPassword" class="text-danger" />
            </div>
        </div>
        <div class="form-row">
            <label class="form-label" for="TransactionPin">Transaction Pin</label>
            <InputText type="password" @bind-Value="context.TransactionPin" class="form-input" aria-required="true" placeholder="Transaction Pin" autocomplete="admin-pin" />
            <ValidationMessage For="() => context.TransactionPin" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Roles">Roles</label>
            <RadzenDropDown @bind-Value=@context.Roles Data=@roles Name="Roles" TextProperty="@nameof(ApplicationRole.Name)" ValueProperty="@nameof(ApplicationRole.Name)"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"
                            Multiple=true AllowClear=true Placeholder="Select roles" Chips=true class="form-input" />
            <ValidationMessage For="() => context.Roles" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Email">Email</label>
            <InputText @bind-Value="context.Email" class="form-input" aria-required="true" placeholder="Email" />
            <ValidationMessage For="() => context.Email" class="text-danger" />
        </div>

        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="FirstName">First Name</label>
                <InputText @bind-Value="context.FirstName" class="form-input" aria-required="true" placeholder="First Name" />
                <ValidationMessage For="() => context.FirstName" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="LastName">Last Name</label>
                <InputText @bind-Value="context.LastName" class="form-input" aria-required="true" placeholder="Last Name" />
                <ValidationMessage For="() => context.LastName" class="text-danger" />
            </div>
        </div>

        <div class="form-row">
            <label class="form-label" for="OtherNames">Other Names</label>
            <InputText @bind-Value="context.OtherNames" class="form-input" aria-required="true" placeholder="Other Names" />
            <ValidationMessage For="() => context.OtherNames" class="text-danger" />
        </div>        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="PhoneCode">Phone Code</label>
                <InputText @bind-Value="context.PhoneCode" class="form-input" aria-required="true" placeholder="Phone Code" />
                <ValidationMessage For="() => context.PhoneCode" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="PhoneNumber">Phone Number</label>
                <InputText @bind-Value="context.PhoneNumber" class="form-input" aria-required="true" placeholder="Phone Number" />
                <ValidationMessage For="() => context.PhoneNumber" class="text-danger" />
            </div>
        </div>

        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="Password">Password</label>
                <InputText type="password" @bind-Value="context.Password" class="form-input" aria-required="true" placeholder="Password" />
                <ValidationMessage For="() => context.Password" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="ConfirmPassword">Confirm Password</label>
                <InputText type="password" @bind-Value="context.ConfirmPassword" class="form-input" aria-required="true" placeholder="Confirm Password" />
                <ValidationMessage For="() => context.ConfirmPassword" class="text-danger" />
            </div>
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalComponent Policy="@ManageAdminUsersNavigation.UserRolesPermission" ModalId="@RolesModalName" 
                ModalCss="width-xlg" Title=@($"Manage Roles For User: {SelectedUser?.FullName}")
                ShowFooter="selectedRowsCount > 0">
     <BodyContent>
        <StatusMessage Message="ModalMessage" />
        <DataTable T="UserRoleVM" TableDefinition="rolesService.GetTableDefinition()" LoadData="LoadRoles" DeferLoading="true" CheckboxSelectionEvent="RegisterSelection" @ref="rolesTable" />
     </BodyContent>
     <FooterContent>
        <button class="btn btn--default" type="button" @onclick="() => rolesTable.LoadElement()">Discard</button>
        <LoadButton Label=@($"Save [{selectedRowsCount}]") OnClick="SaveRoles" />
     </FooterContent>

</ModalComponent>

<ModalComponent Policy="@ManageAdminUsersNavigation.UserRolesPermission" ModalId="@AccessLogModalName"
                ModalCss=" modal-dialog-large width-70vw" Title=@($"View Access Logs For User: {SelectedUser?.FullName}")>
    <BodyContent>
        <DataTable T="UserAccessLogVM" TableDefinition="accessService.GetTableDefinition()" LoadData="LoadAccessLogs" DeferLoading="true" @ref="accessLogTable" />
    </BodyContent>

</ModalComponent>

<ModalComponent Policy="@ManageAdminUsersNavigation.UserReset2FAPermission" ModalId="@Reset2faModalName"
                Title=@($"Reset 2FA for {SelectedUser?.FullName}") >
    <BodyContent>
        <p> Are you sure you want to reset 2FA for this user? </p>
        <p>
            <strong>Full Name: </strong>
            @SelectedUser?.FullName
        </p>
        <p>
            <strong>Email: </strong>
            @SelectedUser?.Email
        </p>
    </BodyContent>
    <FooterContent>
        <button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
        <LoadButton Label="Yes" OnClick="Reset2fa" />
    </FooterContent>
</ModalComponent>

<ModalComponent Policy="@ManageAdminUsersNavigation.UserResetLockoutPermission" ModalId="@ResetLockoutModalName"
                Title=@($"Reset Lockout for {SelectedUser?.FullName}")>
    <BodyContent>
        <p> Are you sure you want to reset lockout for this user? </p>
        <p>
            <strong>Full Name: </strong>
            @SelectedUser?.FullName
        </p>
        <p>
            <strong>Email: </strong>
            @SelectedUser?.Email
        </p>
    </BodyContent>
    <FooterContent>
        <button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
        <LoadButton Label="Yes" OnClick="ResetLockout" />
    </FooterContent>
</ModalComponent>


@code
{
    private DataTable<ApplicationAdminUserVM> table;
    private List<ApplicationRole> roles = [];

    private DataTable<UserRoleVM> rolesTable;
    private DataTable<UserAccessLogVM> accessLogTable;
    private int selectedRowsCount = 0;
    private ApplicationAdminUserVM SelectedUser { get; set; }

    private string RolesModalName => "RolesModal";
    private string AccessLogModalName => "AccessLogModal";
    private string Reset2faModalName => "Reset2fa";
    private string ResetLockoutModalName => "ResetLockout";

    [SupplyParameterFromForm]
    protected AdminUserRegisterVM AddModel { get; set; }

    [SupplyParameterFromForm]
    protected AdminUserEditVM EditModel { get; set; }

    protected override void OnInitialized()
    {
        Title = "Manage Users";
        SubTitle = "Administrative User Records";
        FormBaseTitle = "Administrative User";
        CreatePermission = ManageAdminUsersNavigation.UserCreatePermission;
        EditPermission = ManageAdminUsersNavigation.UserEditPermission;
        DeletePermission = ManageAdminUsersNavigation.UserDeletePermission;
        roles = rolesService.GetRoles();
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            AddModel = new();
            EditModel = new();

            await base.OnInitializedAsync();
            AddTopButton(CreatePermission, new TopActionButton(ModalName: AddModalName, Action: () => CloseMessage(ModalMessage)));

            AddRowButton(ManageAdminUsersNavigation.UserRolesPermission, new RowActionButton("Roles", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                SelectedUser = row as ApplicationAdminUserVM;
                await rolesTable.LoadElement();
                table.Loading = false;

                StateHasChanged();
                await JSRuntime.OpenModal(RolesModalName, Cancel);
            }));

            AddRowButton(ManageAdminUsersNavigation.UserAccessLogsPermission, new RowActionButton("Access Logs", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                SelectedUser = row as ApplicationAdminUserVM;
                await accessLogTable.LoadElement();
                table.Loading = false;

                StateHasChanged();
                await JSRuntime.OpenModal(AccessLogModalName, Cancel);
            }));

            AddRowButton(ManageAdminUsersNavigation.UserResetLockoutPermission, new RowActionButton("Reset Lockout", Icon: "unlock", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                SelectedUser = row as ApplicationAdminUserVM;
                await JSRuntime.OpenModal(ResetLockoutModalName, Cancel);
                table.Loading = false;      
            },
            ShowCondition: (object row) => (row as ApplicationAdminUserVM).LockoutEnd != null));

            AddRowButton(ManageAdminUsersNavigation.UserReset2FAPermission, new RowActionButton("Reset 2FA", ButtonClass: "btn--danger", Action: async (object row) =>
            {
                CloseMessage();
                SelectedUser = row as ApplicationAdminUserVM;
                StateHasChanged();
                await JSRuntime.OpenModal(Reset2faModalName, Cancel);
            },
            ShowCondition: (object row) => (row as ApplicationAdminUserVM).TwoFactorEnabled)); 
        }
    }

    #region Manage Users

    protected override ColumnList GetTableDefinition() => Service.GetTableDefinition();

    private ValueTask<TypedBasePageList<ApplicationAdminUserVM>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        CloseMessage();
        return Service.GetTypeBasedPagedData(filterAndPage, ct);
    }

    public ValueTask SubmitAdd() => BaseSaveAdd(async () =>
    {
        var result = await Service.New(AddModel);
        if (!result.Succeeded)
            CustomMessage = string.Join(",", result.Errors?.Select(x => x.Description));
        return result.Succeeded;
    },
    () =>
    {
        AddModel = new();
        return table.Refresh();
    });

    protected ValueTask StartEdit(ApplicationAdminUserVM data, CancellationToken ct) => BaseEdit(() =>
    {
        EditModel = AdminUserEditVM.Get(data);
        return Task.CompletedTask;
    }, ct);

    public ValueTask SubmitEdit() => BaseSaveEdit(async () =>
    {
        var result = await Service.Update(EditModel, UserName);
        if (!result.Successful)
            CustomMessage = result.Message;
        return result.Successful;
    },
    () =>
    {
        EditModel = new();
        return table.Refresh();
    });

    protected ValueTask<bool> SubmitDelete(ApplicationAdminUserVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(async () =>
    {
        var result = await Service.Delete(data, ct);
        CustomMessage = result.Message;
        return result.Successful;
    }, refresh);

    public async Task Reset2fa()
    {
        var resetResult = await Service.ResetUser2FA(SelectedUser);

        await JSRuntime.CloseModal(Reset2faModalName, Cancel);
        await table.Refresh();

        if (resetResult)
        {
            TableMessage.Success($"You have successfully reset the 2FA for {SelectedUser.FullName}");
        }
        else
        {
            TableMessage.Error($"Unable to reset 2FA for {SelectedUser.FullName}");
        }
    }

    public async Task ResetLockout()
    {
        var resetResult = await Service.ResetLockout(SelectedUser, Cancel);
        await JSRuntime.CloseModal(ResetLockoutModalName, Cancel);
        await table.Refresh();

        if (resetResult)
        {
            TableMessage.Success($"You have successfully reset the Lockout for {SelectedUser.FullName}");
        }
        else
        {
            TableMessage.Error($"Unable to reset Lockout for {SelectedUser.FullName}");
        }
    }
    #endregion

    #region Manage Roles

    private ValueTask<TypedBasePageList<UserRoleVM>> LoadRoles(DataFilterAndPage filterAndPage, CancellationToken ct) => rolesService.GetTypeBasedPagedData(SelectedUser, filterAndPage, ct);

    private void RegisterSelection(int count)
    {
        selectedRowsCount = count;
        StateHasChanged();
    }

    private async Task SaveRoles()
    {
        rolesTable.Loading = true;
        var roles = rolesTable.CheckboxChanges.Values.SelectMany(x => x.Select(y => y));
        var rolesToAdd = roles.Where(x => x.NewValue).Select(y => y.Row as UserRoleVM);
        var rolesToRemove = roles.Where(x => !x.NewValue).Select(y => y.Row as UserRoleVM);
        var assignCount = rolesToAdd.Count();
        var removeCount = rolesToRemove.Count();

        if (assignCount > 0 && !HasClaim(ManageAdminUsersNavigation.AssignRoleToUserClaimsPermission))
        {
            await JSRuntime.CloseModal(RolesModalName, Cancel);
            TableMessage.Error($"Assigning role is not permitted for this account. Attempt to assign {assignCount} role(s) detected.");
            return;
        }

        if (removeCount > 0 && !HasClaim(ManageAdminUsersNavigation.RemoveRoleFromUserClaimsPermission))
        {
            await JSRuntime.CloseModal(RolesModalName, Cancel);
            TableMessage.Error($"Removing role is not permitted for this account. Attempt to remove {removeCount} role(s) detected.");
            return;
        }

        var result = await rolesService.SaveRoles(UserName, SelectedUser, rolesToAdd, rolesToRemove);
        if (result.Successful)
        {
            await JSRuntime.CloseModal(RolesModalName, Cancel);
            TableMessage.Success($"{assignCount} role(s) added and {removeCount} removed for {SelectedUser.FullName} successfully.");
        }
        else
        {
            CustomMessage = result.Message;
            ModalMessage.Error($"Modifying role for {SelectedUser.FullName} failed. {CustomMessage}");
        }
        rolesTable.Loading = false;
        selectedRowsCount = 0;
        StateHasChanged();
    }

    #endregion

    #region Manage Access Logs

    private ValueTask<TypedBasePageList<UserAccessLogVM>> LoadAccessLogs(DataFilterAndPage filterAndPage, CancellationToken ct) => accessService.GetTypeBasedPagedData(SelectedUser.UserName, filterAndPage, ct);

    #endregion
}

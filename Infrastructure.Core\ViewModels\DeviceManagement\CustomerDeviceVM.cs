﻿using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Logs;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Infrastructure.Core.ViewModels.DeviceManagement;

public class CustomerDeviceVM
{
	[MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH), Required]
	public string DeviceId { get; set; }
	public CustomerDeviceType Type { get; set; }
	[MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH)]
	public string PushNotificationId { get; set; }
	[Required]
	public string AppVersion { get; set; }
	[Required]
	public string AppBuild { get; set; }
	[Required]
	public string MobileOsVersion { get; set; }
	[Required]
	public string MobileMake { get; set; }

	public CustomerDevice Get(string userId) => new()
	{
		UserId = userId,
		DeviceId = DeviceId,
		Type = Type,
		PushNotificationId = PushNotificationId,
		AppVersion = AppVersion,
		AppBuild = AppBuild,
		MobileOsVersion = MobileOsVersion,
		MobileMake = MobileMake,
	};
}

﻿using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace LendQube.Entities.Core.BackgroundTasks;

public class BackgroundTaskEventLog : BaseEntityWithIdentityId<BackgroundTaskEventLog>, IEntityHasEnum
{
    [DbGuid]
    public string UserId { get; set; }
    [TableDecorator(TableDecoratorType.ShowInInfo)]
    public BackgroundEventSource Source { get; set; }
    [TableDecorator(TableDecoratorType.ShowInInfo)]
    public BackgroundTask Event { get; set; }
    [TableDecorator(TableDecoratorType.ShowInInfo)]
    public BackgroundEventStatus Status { get; set; }
    [TableDecorator(TableDecoratorType.ShowInInfo, TableDecoratorType.HideColumn)]
    public string Data { get; set; }
    public Instant? RequeryDelayTime { get; set; }
    public Instant? TimeStarted { get; set; }
    public Instant? TimeCompleted { get; set; }
    public int Tries { get; set; }
    [TableDecorator(TableDecoratorType.ShowInInfo, TableDecoratorType.HideColumn)]
    public string Uri { get; set; }
    [TableDecorator(TableDecoratorType.ShowInInfo)]
    public string ResponseMessage { get; set; }

    [NotMapped, TableDecorator(TableDecoratorType.GroupActionCheckbox)]
    public bool Selected { get; set; }

    public override void Configure(EntityTypeBuilder<BackgroundTaskEventLog> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => e.UserId);
    }
    public void RegisterEnumInDataSource(NpgsqlDataSourceBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<BackgroundEventStatus>($"{CoreEntityConfig.DefaultSchema}.{nameof(BackgroundEventSource)}", nameTranslator);
    }

    public void RegisterEnumInDataSource(NpgsqlDbContextOptionsBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<BackgroundEventStatus>(nameof(BackgroundEventSource), CoreEntityConfig.DefaultSchema, nameTranslator);
    }
}

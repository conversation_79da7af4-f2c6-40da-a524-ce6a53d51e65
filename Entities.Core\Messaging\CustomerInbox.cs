﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;

namespace LendQube.Entities.Core.Messaging;

public class CustomerInbox : BaseEntityWithGuid, IEntityTypeConfiguration<CustomerInbox>
{
    [DbGuid]
    public string UserId { get; set; }
    public long MessageLogId { get; set; }
    public string Subject { get; set; }
    public MessageConfigTemplateType Type { get; set; }
    public string Template { get; set; }
    public Instant? ReadOn { get; set; }

    public void Configure(EntityTypeBuilder<CustomerInbox> builder)
    {
        builder.HasIndex(e => e.UserId);
    }
}

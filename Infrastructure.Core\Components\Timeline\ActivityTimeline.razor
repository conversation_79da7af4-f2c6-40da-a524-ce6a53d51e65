﻿@using LendQube.Infrastructure.Core.Extensions
@inject GenericSpecificationService<T> activityService 
@inject IJSRuntime jSRuntime
@typeparam T where T : BaseActivityTimeline<T>
@inherits AsyncComponentBase

<div class="activity-search">
    <div class="__search">
        <input class="form-input __sm search-input" @bind-value="pagedData.TextFilter" @bind-value:event="oninput" @onkeydown="@Search"
               type="text" placeholder="Search activity">
    </div>
</div>
<ul class="timeline">
    @if (data != null)
    {
        foreach (var item in data.Data)
        {
            <li class="timeline-item">
                <i data-feather="clock"></i>
                <div class="wrapper">
                    <span class="__date">
                        <LocalTime Value="item.CreatedDate.Value" />
                    </span>
                    <span class="__title">@item.Title</span>
                    <span class="__desc">
                        @item.Activity @(!string.IsNullOrWhiteSpace(item.CreatedByUser) ? $"by {item.CreatedByUser}" : "")
                    </span>
                </div>
            </li>
        }
    }
</ul>
<div class="pgnContainer">
    <div class="pagination pgnLeft">
        <span class="label">Show</span>
        <select class="form--select" @bind-value="pagedData.PageSize" @bind-value:event="oninput" @onchange="ChangePage">
            <option value="5">5</option>
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
        </select>
        <span class="label">records</span>
    </div>
    <div class="pagination pgnRight">
        <div class="pgnControls">
            <a @onclick="PreviousPage" class="prev @(data?.HasPreviousPage ?? false ? "" :"noAdjacent")">
                <svg width="24" height="24"
                     ViewBox="0 0 24 24" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M12.7333 11.9958L9.49996 15.2292C9.17496 15.5542 9.1833 16.0875 9.49996 16.4125C9.82496 16.7375 10.35 16.7375 10.675 16.4125L14.5 12.5875C14.825 12.2625 14.825 11.7375 14.5 11.4125L10.675 7.5875C10.35 7.2625 9.82496 7.2625 9.49996 7.5875C9.34392 7.74319 9.25623 7.95457 9.25623 8.175C9.25623 8.39543 9.34392 8.60681 9.49996 8.7625L12.7333 11.9958Z">
                    </path>
                </svg>
            </a>
            <input type="text" class="form--input" @bind-value="pagedData.Page" @bind-value:event="oninput" @onchange="ChangePage">
            <a @onclick="NextPage" class="next @(data?.HasNextPage ?? false ? "" :"noAdjacent")">
                <svg width="24" height="24"
                     ViewBox="0 0 24 24" fill="none"
                     xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd"
                          clip-rule="evenodd"
                          d="M12.7333 11.9958L9.49996 15.2292C9.17496 15.5542 9.1833 16.0875 9.49996 16.4125C9.82496 16.7375 10.35 16.7375 10.675 16.4125L14.5 12.5875C14.825 12.2625 14.825 11.7375 14.5 11.4125L10.675 7.5875C10.35 7.2625 9.82496 7.2625 9.49996 7.5875C9.34392 7.74319 9.25623 7.95457 9.25623 8.175C9.25623 8.39543 9.34392 8.60681 9.49996 8.7625L12.7333 11.9958Z">
                    </path>
                </svg>
            </a>
        </div>
    </div>
    @if (Loading)
    {
        <button class="btn btn--default __load-more __full loading"
                role="button">
        </button>
        
    }
</div>

@code {
    [Parameter, EditorRequired] public Func<DataFilterAndPage, GenericSpecificationService<T>, CancellationToken, ValueTask<TypedBasePageList<T>>> LoadData { get; set; }
    private DataFilterAndPage pagedData = new DataFilterAndPage { PageSize = 5, Page = 1, OrderByColumn = nameof(IBaseEntityWithSystemStamp.CreatedDate) };
    private TypedBasePageList<T> data;

    public bool Loading { get; set; } = true;


    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            await GetData();
        await jSRuntime.RunFeather(Cancel);
    }

    public async Task Refresh()
    {
        pagedData = new DataFilterAndPage { PageSize = 5, Page = 1, OrderByColumn = nameof(IBaseEntityWithSystemStamp.CreatedDate) };
        await GetData();
    }

    private async Task GetData()
    {
        Loading = true;
        StateHasChanged();
        data = null;
        data = await LoadData(pagedData, activityService, Cancel);

        Loading = false;
        StateHasChanged();
    }


    private async Task ChangePage()
    {
        if (data != null)
        {
            if (pagedData.Page <= data.LastPage && pagedData.Page > 0)
            {
                await GetData();
            }
            else
            {
                if (pagedData.Page != data.CurrentPage)
                    pagedData.Page = data.CurrentPage;
            }
        }
    }

    private async Task NextPage()
    {
        if (data != null && data.HasNextPage)
        {
            pagedData.Page += 1;
            await GetData();
        }
    }

    private async Task PreviousPage()
    {
        if (data != null && data.HasPreviousPage)
        {
            pagedData.Page -= 1;
            await GetData();
        }
    }

    private async Task Search(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await GetData();
        }
    }
}
# LendQube Agent Guidelines

## Build/Test Commands
- **Build**: `dotnet build` or `dotnet build --configuration Release`
- **Run API**: `dotnet run --project Web.Api`
- **Run Admin**: `dotnet run --project Web.Admin`
- **Run Background Service**: `dotnet run --project Web.BackgroundService`
- **Run Tests**: `dotnet run --project TestScripts` (interactive menu)
- **Run Specific Test**: `dotnet run --project TestScripts payment|email|sms|all`

## Architecture
- **Solution**: 8 projects (Web.Api, Web.Admin, Web.BackgroundService, Entities.Core/Collection, Infrastructure.Core/Collection/ExternalApi)
- **Database**: PostgreSQL (dev: ***********:5432, local: localhost:5432)
- **Background Jobs**: Hangfire with separate connection string
- **External APIs**: Payment gateway (Acquired), SMS providers, email SMTP

## Code Style
- **Naming**: PascalCase classes/methods/properties, camelCase variables
- **Async**: Use `async Task<T>` with CancellationToken parameter `ct`
- **Controllers**: Inherit from `ApiControllerBase` or `ApiAuthControllerBase`
- **Entities**: Inherit from `BaseEntityWithIdentityId<T>`
- **Namespaces**: File-scoped (`namespace LendQube.Web.Api.Controllers.Location;`)
- **Primary Constructors**: Extensively used for dependency injection
- **Records**: For simple data structures (`public record UploadResult(...)`)
- **Attributes**: Use for routing `[HttpGet]`, validation `[Required]`, decoration `[TableDecorator]`

﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Setup;

public class AutoDiscountConfig : BaseEntityWithIdentityId<AutoDiscountConfig>, IEntityHasNotifyTrigger
{
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.NoScriptTag)]
    public string Name { get; set; }
    public decimal? Amount { get; set; }
    public int? MinDays { get; set; }
    public int? MaxDays { get; set; }
    [Range(0, 100)]
    public decimal? Percentage { get; set; }
    public decimal? UpperLimit { get; set; }
    public AutoDiscountRule Rule { get; set; }
    public bool DoNotApplyIfDiscountSettlesAccount { get; set; }
    public bool ApplyToNewAccountsOnly { get; set; }
    public bool ApplyToOnlyAccountsWithNoDiscount { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public bool ApplyDiscountNow { get; set; }
    public bool AutoApplyDiscount { get; set; }
    public bool DiscountApplied { get; set; }
    public int TotalPlacementsAffected { get; set; }
    public decimal TotalAmountApplied { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long? FileUploadId { get; set; }
    public virtual CollectionFileUpload FileUpload { get; set; }

    public string Schema => CollectionEntityConfig.DefaultSchema;

    public TriggerChange[] ChangesToObserve => [TriggerChange.Insert, TriggerChange.Update];

    public TriggerType[] Types => [TriggerType.After];

    public bool TrackOldData => false;

    public bool ReturnOnlyId => false;

    public string ConditionScript => $@"NEW.""{nameof(ApplyDiscountNow)}"" IS TRUE AND NEW.""{nameof(DiscountApplied)}"" IS NOT TRUE";
}

public enum AutoDiscountRule
{
    SetupWithinDays
}
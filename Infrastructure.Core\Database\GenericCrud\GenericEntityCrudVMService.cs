﻿using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Telemetry;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.Database.GenericCrud;

public sealed class GenericEntityCrudVMService<T, TVM>(IUnitofWork uow, ILogManager<T> logger) : GenericEntityCrudService<T>(uow, logger) 
    where T : class, IBaseEntityForRelationalDb
{
    private readonly IUnitofWork uow = uow;

    public Task<TVM> GetVM(ISpecification<T> spec, Expression<Func<T, TVM>> selector, CancellationToken ct) => uow.Db.OneSelectWithSpecAsync(spec, selector, ct: ct);

    public Task<TVM> GetVM(SqlSelectQueryBuilder<T, TVM> query, CancellationToken ct) => uow.Db.OneSelectAsync(query, ct);

    public Task<List<TVM>> GetAllVM(Expression<Func<T, TVM>> selector, ISpecification<T> spec, CancellationToken ct) => uow.Db.ManySelectWithSpecAsync(spec, selector, ct: ct);

    public ValueTask<TypedBasePageList<TVM>> GetTypeBasedPagedData(ISpecification<T> spec, DataFilterAndPage filterAndPage, Expression<Func<T, TVM>> select, CancellationToken ct)
    {
        spec.DoAllFilter(filterAndPage);
        return new SelectPagedList<T, TVM>(uow.Db.NotTrackedWithSpec(spec), filterAndPage, spec, select, ct).ToType(ct);
    }

    public ColumnList GetTableDefinition(TableSettings<TVM> settings = null) => GenericColumnAndFilterService<T, TVM>.GetTableDefinition(settings ?? new());
}
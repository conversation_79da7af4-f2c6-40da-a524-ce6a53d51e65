﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.PromiseToPay;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.ViewModels.Base;
using MongoDB.Bson;

namespace LendQube.Infrastructure.Collection.Payments;

public sealed class PaymentFactory(Func<PaymentProvider, IPaymentProvider> providerAccessor, IUnitofWork uow)
{
    internal Task<Transaction> Get(string userId, string id, CancellationToken ct) => uow.Db.OneAsync(Query<Transaction>.Where(x => x.ProfileId == userId && x.Id == id), ct);

    internal async Task<Result<TransactionResultVM>> InitiateTransaction(CustomerScheduleVM profile, Transaction txn, CancellationToken ct, bool isOneTimePayment = false)
    {
        txn.Id = ObjectId.GenerateNewId().ToString();

        txn.ProfileId = profile.Id;
        txn.Amount = txn.UnitAmount * txn.Quantity;
        txn.TotalAmountPayable = txn.Amount + txn.Fee - txn.Discount;
        txn.CurrencyCode = profile.CurrencyCode;
        txn.CurrencySymbol = profile.CurrencySymbol;

        var paymentProvider = providerAccessor(txn.Provider);

        var paymentConfig = await uow.Db.OneAsync(Query<CustomerPaymentMethodConfiguration>.Where(x => x.ProfileId == profile.Id && x.Provider == txn.Provider), ct);

        if (paymentConfig is null)
        {
            if (paymentProvider is ICreatesCustomerPaymentProvider createCustomerService)
            {
                uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = "Customer created on payment provider", Title = $"{txn.Status}" });

                paymentConfig = await createCustomerService.CreateCustomer(profile, ct);
                if (paymentConfig is null)
                    return "Could not initiate transaction. Please try again";

                uow.Db.Insert(paymentConfig);
            }
            else if (paymentProvider is ISavesPaymentMethodPaymentProvider)
            {
                uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = "Customer provider config created", Title = $"{txn.Status}" });
                paymentConfig = new CustomerPaymentMethodConfiguration { ProfileId = profile.Id, Provider = PaymentProvider.Acquired, Currency = profile.CurrencyCode, ProviderId = null };
                uow.Db.Insert(paymentConfig);
            }
        }

        Result<bool> initiated = false;
        switch (txn.Type)
        {
            case PaymentType.SetupCard:
                txn.UnitAmount = txn.Amount = txn.TotalAmountPayable = 1;
                initiated = await (paymentProvider as ICreatesPaymentMethodProvider).CreateSetupCharge(paymentConfig, txn, ct);
                break;
            case PaymentType.Card:
                initiated = await (paymentProvider as IChargeCardPaymentProvider).CreateCharge(paymentConfig, txn, ct);
                break;
            default:
                break;
        }


        if (initiated.IsSuccessful && initiated.Data)
        {
            txn.Status = TransactionStatus.Validated;
            uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = "charge initiated on provider", Title = $"{txn.Status}" });

            uow.Db.Insert(txn);
            await uow.SaveAsync(ct);

            var providerPublicKey = string.Empty;
            if (paymentProvider is IProvidesKeyPaymentProvider providesKeyProvider)
                providerPublicKey = providesKeyProvider.GetKey();

            var data = new TransactionResultVM(txn, providerPublicKey)
            {
                PaymentMethods = []
            };

            var customerSavedCards = await uow.Db.ManySelectAsync(Query<CustomerPaymentMethod, CustomerPaymentMethodVM>
                .Where(x => x.ProfileId == profile.Id && x.CanBeReused == true && x.Config.Provider == txn.Provider)
                .Select(CustomerPaymentMethodVM.Mapping), ct);

            Parallel.ForEach(customerSavedCards, (card) =>
            {
                data.PaymentMethods.Add(new SavedPaymentMethods(card.Id, card.Data.Brand, card.Data.Last4));
            });

            data.PaymentMethods.Add(new SavedPaymentMethods(string.Empty, CardBrand.Unknown, isOneTimePayment ? "new card or bank" : "new card"));

            return data;
        }
        else if (!string.IsNullOrEmpty(initiated.Message))
        {
            uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = $"Initiating charge on provider failed. {initiated.Message}", Title = $"{txn.Status}" });

            uow.Db.Insert(txn);
            await uow.SaveAsync(ct);
        }

        return "Payment initiation failed";
    }

    internal async Task<Result<TransactionResultVM>> ConfirmTransaction(string userId, ProcessTransactionRequestVM vm, CancellationToken ct)
    {
        var txn = await Get(userId, vm.TxnId, ct);
        if (txn == null)
            return "Transaction not found";

        if (!TransactionHelper.ProcessingStatus.HasFlag(txn.Status))
            return new TransactionResultVM(txn);

        var paymentProvider = providerAccessor(txn.Provider);
        (TransactionStatus status, CustomerPaymentMethod paymentMethod) = (txn.Status, null);

        var paymentMethodExists = false;
        var selectedPaymentMethod = !string.IsNullOrEmpty(vm.PaymentMethodId) ? await uow.Db.OneAsync(Query<CustomerPaymentMethod>.Where(x => x.ProfileId == userId && x.Id == vm.PaymentMethodId && x.Config.Provider == txn.Provider), ct) : null;
        switch (txn.Type)
        {
            case PaymentType.SetupCard:
                (status, paymentMethod, paymentMethodExists) = selectedPaymentMethod != null ? (TransactionStatus.Successful, null, true) : await (paymentProvider as ICreatesPaymentMethodProvider).ConfirmSetupCharge(uow, txn, ct);
                break;
            case PaymentType.Card:
                (status, paymentMethod) = await (paymentProvider as IChargeCardPaymentProvider).ConfirmCharge(uow, txn, selectedPaymentMethod, ct);
                break;
            default:
                break;
        }

        txn.Status = status;

        if (txn.Status == TransactionStatus.Successful)
        {
            uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = "charge confirmed on provider", Title = $"{txn.Status}" });
        }

        if (paymentProvider is ISavesPaymentMethodPaymentProvider && paymentMethod != null)
        {
            var paymentConfig = await uow.Db.OneSelectAsync(Query<CustomerPaymentMethodConfiguration, long>.Where(x => x.ProfileId == userId && x.Provider == txn.Provider).Select(x => x.Id), ct);
            paymentMethod.CustomerPaymentMethodConfigurationId = paymentConfig;
            paymentMethod.ProfileId = userId;

            uow.Db.Insert(paymentMethod);

            uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = "Payment method saved", Title = $"{txn.Status}" });

        }

        if (txn.Type == PaymentType.SetupCard)
            txn.Status = paymentMethod != null || paymentMethodExists ? TransactionStatus.Refunded : TransactionStatus.Failed;

        await PromiseToPayService.CheckAndApplyToPtp(uow, txn, ct);

        uow.Db.Update(txn);
        await uow.SaveAsync(ct);

        var result = new TransactionResultVM(txn);
        return status == TransactionStatus.Failed ? Result<TransactionResultVM>.Failed(data: result) : Result<TransactionResultVM>.Successful(txn.Status == TransactionStatus.Refunded || txn.Status == TransactionStatus.Successful
            ? "Payment confirmed" : "Payment pending. Will be confirmed automatically", result);
    }

    internal async Task CreateAndConfirmTransaction(IUnitofWork uow, CustomerProfile profile, Transaction txn, CreateChargeAndConfirmPaymentMethodVM cardToCharge, CancellationToken ct)
    {
        txn.Amount = txn.UnitAmount * txn.Quantity;
        txn.TotalAmountPayable = txn.Amount + txn.Fee - txn.Discount;
        txn.CurrencyCode = profile.CurrencyCode;
        txn.CurrencySymbol = profile.CurrencySymbol;
        txn.ProfileId = profile.Id;

        var paymentProvider = providerAccessor(txn.Provider);
        var status = await (paymentProvider as ICreateAndConfirmCardChargePaymentProvider).CreateAndConfirmSingleCharge(uow, cardToCharge, txn, ct);
        if (!status.HasValue)
            return;

        txn.Status = status.Value;

        uow.Db.Insert(new TransactionHistory { TransactionId = txn.Id, Activity = "Attempt auto-charge", Title = $"{txn.Status}", CreatedByUser = "System" });

        uow.Db.Insert(txn);

        await PromiseToPayService.CheckAndApplyToPtp(uow, txn, ct);
    }
}
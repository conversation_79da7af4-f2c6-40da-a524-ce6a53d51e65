﻿using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class MessagingTriggerHandler(BackgroundTaskControlService service, MessagingFactory messagingFactory) : IHandleTriggerNotification<MessageLog>
{
    private readonly BackgroundEventSource Source = BackgroundEventSource.System;
    private readonly BackgroundTask Key = BackgroundTask.SendMessages;

    public Task OnStartup(CancellationToken ct) => Task.CompletedTask;

    public async ValueTask OnChanged(string? id, MessageLog oldData, MessageLog newData, TriggerChange change, CancellationToken ct)
    {
        var stopped = await service.CheckStoppedOrStopping(Source, Key, ct);
        if (stopped)
            return;

        await service.SetStatusToRunning(Source, Key, ct);

        if (long.TryParse(id, out var messageId))
        {
            await messagingFactory.SendMessage(messageId, ct);
        }

        await service.SetStatusToIdle(Source, Key, ct);
    }

}
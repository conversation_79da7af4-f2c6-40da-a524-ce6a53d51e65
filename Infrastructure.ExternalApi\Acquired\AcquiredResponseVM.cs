﻿namespace LendQube.Infrastructure.ExternalApi.Acquired;


public static class AcquiredConstants
{
    public const string PaymentLink = default;
}

internal sealed class AcquiredLoginResponseVM
{
    public string TokenType { get; set; }
    public int ExpiresIn { get; set; }
    public string AccessToken { get; set; }
}


public sealed class AcquiredGeneratePaymentLinkResponseVM
{
    public bool IsSuccessful => Status == "success";
    public string Status { get; set; }
    public string LinkId { get; set; }
}

public sealed class AcquiredCreateCustomerResponseVM
{
    public string CustomerId { get; set; }
}

public class AcquiredChargeCardResponseVM
{
    public bool IsSuccessful => Status == "success";
    public string TransactionId { get; set; }
    public string Status { get; set; }
    public string Reason { get; set; }
    public string IssuerResponseCode { get; set; }
}
﻿using System.Reflection;
using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Navigation;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Core.PermissionsAndRoles;

internal sealed class SuperAdminAccountSeeder(UserManager<ApplicationUser> userManager, IUnitofWork uow, ILogManager<SuperAdminAccountSeeder> logger)
{
    public async Task FinishPermissionSetup()
    {
        try
        {
            if (!await uow.Db.ExistsAsync<ApplicationRole>(x => x.Id == SystemRoleConfig.SuperAdminRoleId, default))
            {
                uow.Db.InsertBulk([
                    new ApplicationRole { Id = SystemRoleConfig.SuperAdminRoleId, Name = SystemRoleConfig.SuperAdminRole, NormalizedName = SystemRoleConfig.SuperAdminRole.ToUpper(), Description = "Top level role that grants all permissions" },
                    new ApplicationRole { Name = SystemRoleConfig.CustomerRole, NormalizedName = SystemRoleConfig.CustomerRole.ToUpper(), Description = "Registered customer role" }
                    ], default);
                await uow.SaveAsync(default);
            }

            if (!await uow.Db.ExistsAsync<ApplicationUser>(x => x.Email == SystemRoleConfig.SuperAdminEmail, default))
            {
                var user = new ApplicationUser()
                {
                    FirstName = "Super",
                    LastName = "Admin",
                    UserName = SecurityDriven.FastGuid.NewPostgreSqlGuid().ToString(),
                    Email = SystemRoleConfig.SuperAdminEmail,
                    RegistrationDate = SystemClock.Instance.GetCurrentInstant(),
                    Role = SystemRoleConfig.AdminRole,
                    PhoneCode = "+234",
                    PhoneNumber = "08063602609"
                };

                var result = await userManager.CreateAsync(user, SystemRoleConfig.SuperAdminPassword);
                if (result != IdentityResult.Success)
                {
                    throw new InvalidOperationException("Failed to create default super admin user");
                }
                else
                {
                    await userManager.AddToRoleAsync(user, SystemRoleConfig.SuperAdminRole);
                }
            }


            var sources = AllClaimsToUpdate.Select(x => x.Source).Distinct().ToList();
            PermissionSourceHelper.Sources.AddRange(sources);

            var allClaims = (await uow.Db.ManySelectAsync(Query<ApplicationClaims, AllClaimVM>.Where(x => sources.Contains(x.Source)).Select(x => new AllClaimVM(x.Source, x.Value)), default)).AsParallel();

            var distinctNavigationPermissions = AllClaimsToUpdate
                .Where(x => !allClaims.Any(a => a.Source == x.Source && a.Value == x.Value))
                .Select(x => new ApplicationClaims { Source = x.Source, Value = x.Value, Description = Navigator.GetPermissionDescription(x.Value) })
                .Where(x => !string.IsNullOrEmpty(x.Description)).ToList();

            var permissionsToCompare = AllClaimsToUpdate.Select(x => $"{x.Source}{x.Value}");
            var permissionsToDelete = allClaims.Where(x => !permissionsToCompare.Contains(x.Source + x.Value)).Select(x => $"{x.Source}{x.Value}").ToList();

            if (permissionsToDelete.Count > 0)
            {
                await uow.Db.DeleteAndSaveWithFilterAsync<ApplicationClaims>(x => permissionsToDelete.Contains(x.Source + x.Value), default);
                await uow.Db.DeleteAndSaveWithFilterAsync<ApplicationRoleClaim>(x => permissionsToDelete.Contains(x.Source + x.ClaimValue), default);
            }

            if (distinctNavigationPermissions.Count > 0)
            {
                uow.Db.InsertBulk(distinctNavigationPermissions, default);
                uow.Db.InsertBulk(distinctNavigationPermissions.Select(x => new ApplicationRoleClaim { Source = x.Source, RoleId = SystemRoleConfig.SuperAdminRoleId, ClaimType = x.Type, ClaimValue = x.Value }), default);
                await uow.SaveAsync(default);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Startup, ex, "Admin account and permissions not seeded properly");
        }

        AllClaimsToUpdate.Clear();
        Navigator.ClearPermissionDescriptions();
    }

    internal static List<ApplicationClaims> AllClaimsToUpdate { get; set; } = [];
    private record AllClaimVM(string Source, string Value);

}

internal static class PermissionSourceHelper
{
    public static readonly List<string> Sources = [];
}

public static class ClaimsSeederHelper
{
    public static WebApplication AddPermissions(this WebApplication app, Type type)
    {
        var navigationValues = Assembly.GetAssembly(type).GetTypes()
            .AsParallel()
            .Where(type => typeof(INavigatorHasPermissions).IsAssignableFrom(type))
            .SelectMany(type => type.GetFields(BindingFlags.Public | BindingFlags.Static))
            .Where(m => m.IsLiteral && !m.IsInitOnly && m.Name.EndsWith("Permission"))
            .Distinct()
            .Select(x => new { Permission = x.GetValue(x).ToString(), Source = x.DeclaringType.Name.Replace("Navigation", "") })
            .OrderBy(x => x.Source)
            .ThenBy(x => x.Permission);

        SuperAdminAccountSeeder.AllClaimsToUpdate.AddRange(navigationValues
         .Select(x => new ApplicationClaims { Source = x.Source, Value = x.Permission, Description = Navigator.GetPermissionDescription(x.Permission) })
         .Where(x => !string.IsNullOrEmpty(x.Description)).ToList());

        return app;
    }
}
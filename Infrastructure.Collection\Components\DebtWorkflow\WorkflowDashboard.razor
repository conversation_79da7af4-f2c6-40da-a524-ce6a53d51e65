﻿@page "/debtworkflow/dashboard"
@using LendQube.Entities.Collection.Workflows.Debt
@using LendQube.Infrastructure.Core.Database.Repository
@using NodaTime
@using Radzen.Blazor

@inject IUnitofWork uow
@attribute [Authorize(Policy = DebtWorkflowNavigation.DebtWorkflowDashboardAllPermission)]

@inherits AsyncComponentBase

<PageTitle>All Dashboard</PageTitle>


@if(analytics != null)
{
    <div class="pg-row grid grid-col-4 grid-tab-2">
        <div class="card">
            <div class="dropdown">
                <span class="dropdown-toggle" role="button" id="dropdownMenuLink"
                      data-bs-toggle="dropdown" aria-expanded="false">
                    @countAnalytics
                </span>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                    <li><a class="dropdown-item" @onclick='() => ChangeAccountAnalytics("Assigned")'>Total Assigned</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeAccountAnalytics("Opened")'>Total Opened</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeAccountAnalytics("Closed")'>Total Closed</a></li>
                </ul>
            </div>
            <span class="card-title">Accounts</span>
            <span class="card-value">@accountCount</span>
            <span class="card-label">@countAnalytics</span>
        </div>
        <div class="card">
            <div class="dropdown">
                <span class="dropdown-toggle" role="button" id="dropdownMenuLink"
                      data-bs-toggle="dropdown" aria-expanded="false">
                    @selectedActionsAnalytics
                </span>
                <ul class="dropdown-menu" aria-labelledby="dropdownMenuLink">
                    <li><a class="dropdown-item" @onclick='() => ChangeActionsAnalytics("Resolved")'>Total Resolved</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeActionsAnalytics("PTP")'>Total PTP</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeActionsAnalytics("Contact")'>Total Contacted</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeActionsAnalytics("Schedule")'>Total Schedule Created</a></li>
                    <li><a class="dropdown-item" @onclick='() => ChangeActionsAnalytics("Escalated")'>Total Escalated</a></li>
                </ul>
            </div>
            <span class="card-title">Actions</span>
            <span class="card-value">@actionsCount</span>
            <span class="card-label">@selectedActionsAnalytics</span>
        </div>
        <div class="card">
            <span class="card-title">Amount Collected</span>
            <span class="card-value">£@analytics.TotalAmountCollected.ToString("n2")</span>
            <span class="card-label">Total Value</span>
        </div>
        <div class="card">
            <span class="card-title">PTP Amount</span>
            <span class="card-value">£@analytics.TotalPTPAmount.ToString("n2")</span>
            <span class="card-label">Total Value</span>
        </div>
    </div>
}

<div class="pg-row grid grid-col-1 grid-tab-1">
    <div class="card" style="height: 70vh">
        <RadzenChart>
            <RadzenChartTooltipOptions Shared="true" />
            <RadzenLineSeries Smooth="true" Data="@timeSeries" CategoryProperty="@nameof(DebtWorkflowTimeAnalytics.Date)" Title="Assigned" LineType="LineType.Dashed" ValueProperty="@nameof(DebtWorkflowTimeAnalytics.TotalAssigned)">
                <RadzenMarkers Visible="true" MarkerType="MarkerType.Square" />
                <RadzenSeriesDataLabels Visible="false" />
            </RadzenLineSeries>
            <RadzenLineSeries Smooth="true" Data="@timeSeries" CategoryProperty="@nameof(DebtWorkflowTimeAnalytics.Date)" Title="Opened" LineType="LineType.Solid" ValueProperty="@nameof(DebtWorkflowTimeAnalytics.TotalOpened)">
                <RadzenMarkers Visible="true" MarkerType="MarkerType.Circle" />
                <RadzenSeriesDataLabels Visible="false" />
            </RadzenLineSeries>
            <RadzenLineSeries Smooth="true" Data="@timeSeries" CategoryProperty="@nameof(DebtWorkflowTimeAnalytics.Date)" Title="Closed" LineType="LineType.Dotted" ValueProperty="@nameof(DebtWorkflowTimeAnalytics.TotalClosed)">
                <RadzenMarkers Visible="true" MarkerType="MarkerType.Triangle" />
                <RadzenSeriesDataLabels Visible="false" />
            </RadzenLineSeries>
            <RadzenLineSeries Smooth="true" Data="@timeSeries" CategoryProperty="@nameof(DebtWorkflowTimeAnalytics.Date)" Title="Amount Collected" LineType="LineType.Solid" ValueProperty="@nameof(DebtWorkflowTimeAnalytics.TotalAmountCollected)">
                <RadzenMarkers Visible="true" MarkerType="MarkerType.Diamond" />
                <RadzenSeriesDataLabels Visible="false" />
            </RadzenLineSeries>
            <RadzenLineSeries Smooth="true" Data="@timeSeries" CategoryProperty="@nameof(DebtWorkflowTimeAnalytics.Date)" Title="PTP Amount" LineType="LineType.Dashed" ValueProperty="@nameof(DebtWorkflowTimeAnalytics.TotalPTPAmount)">
                <RadzenMarkers Visible="true" MarkerType="MarkerType.Square" />
                <RadzenSeriesDataLabels Visible="false" />
            </RadzenLineSeries>
            <RadzenCategoryAxis Padding="20" FormatString="{0:dd MMM}" />
            <RadzenValueAxis FormatString="{0:n2}">
                <RadzenGridLines Visible="true" />
                <RadzenAxisTitle Text="Value" />
            </RadzenValueAxis>
        </RadzenChart>
    </div>
</div>

@code 
{
    private string countAnalytics { get; set; } = "Assigned";
    private string accountCount = "0";


    private string selectedActionsAnalytics { get; set; } = "Resolved";
    private string actionsCount = "0";

    private DebtWorkflowAnalytics analytics;
    private List<DebtWorkflowTimeAnalytics> timeSeries = [];

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if(firstRender)
        {
            analytics = await uow.Db.OneAsync(Query<DebtWorkflowAnalytics>.All(), Cancel);
            ChangeAccountAnalytics(countAnalytics);
            ChangeActionsAnalytics(selectedActionsAnalytics);

            var now = SystemClock.Instance.GetCurrentInstant().InZone(DateTimeZone.Utc).Date;
            var thirtyDaysPast = now.PlusMonths(-1);

            timeSeries = await uow.Db.ManyAsync(Query<DebtWorkflowTimeAnalytics>.Where(x => x.Date <= now && x.Date >= thirtyDaysPast).OrderBy(x => x.OrderBy(y => y.Date)), Cancel);

            StateHasChanged();
        }
    }

    private void ChangeAccountAnalytics(string type)
    {
        countAnalytics = type;
        if (analytics == null)
        {
            accountCount = "0";
        }
        else
        {
            accountCount = type switch
            {
                "Assigned" => analytics.TotalAssigned.ToString("n0"),
                "Opened" => analytics.TotalOpened.ToString("n0"),
                _ => analytics.TotalClosed.ToString("n0"),
            };
        }

        StateHasChanged();
    }


    private void ChangeActionsAnalytics(string type)
    {
        selectedActionsAnalytics = type;
        if (analytics == null)
        {
            actionsCount = "0";
        }
        else
        {
            actionsCount = type switch
            {
                "PTP" => analytics.TotalPTP.ToString("n0"),
                "Contact" => analytics.TotalContacted.ToString("n0"),
                "Resolved" => analytics.TotalResolved.ToString("n0"),
                "Escalated" => analytics.TotalEscalated.ToString("n0"),
                _ => analytics.TotalSchedulesSetup.ToString("n0"),
            };
        }

        StateHasChanged();
    }
}
﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.Components.DebtWorkflow;

public class DebtSegmentVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<DebtSegment, DebtSegmentVM>> Mapping = data => new()
    {
        Id = data.Id,
        Name = data.Name,
        Priority = data.Priority,
        StartTime = data.Start,
        EndTime = data.End,
        RuleIds = data.RuleIds,
        Rules = data.Rules.Select(x => x.Rule.Name).ToList(),
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public DebtSegment Get() => new()
    {
        Id = Id,
        Name = Name,
        Start = StartTime,
        End = EndTime,
        Priority = Priority,
        RuleIds = RuleIds
    };

    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Name { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public DebtSegmentPriority Priority { get; set; }
    public TimeOnly? StartTime { get; set; }
    public TimeOnly? EndTime { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public List<long> RuleIds { get; set; }
    public List<string> Rules { get; set; }
}

public class AgentDebtSegmentMappingVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<AgentToDebtSegmentMapping, AgentDebtSegmentMappingVM>> Mapping = data => new()
    {
        Id = data.Id,
        UserId = data.UserId,
        Agent = data.User.FullName,
        RecordsPerTime = data.RecordsPerTime,
        Enabled = data.Enabled,
        DebtSegmentId = data.DebtSegmentId,
        DebtSegment = data.DebtSegment.Name,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public AgentToDebtSegmentMapping Get() => new()
    {
        Id = Id,
        UserId = UserId,
        DebtSegmentId = DebtSegmentId,
        RecordsPerTime = RecordsPerTime,
        Enabled = Enabled,
    };

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public Guid UserId { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Agent { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long DebtSegmentId { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string DebtSegment { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public int RecordsPerTime { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public bool Enabled { get; set; }
}

public record AgentUserVM(Guid Id, string Name);
public record DebtSegmentSelectorVM(long Id, string Name);
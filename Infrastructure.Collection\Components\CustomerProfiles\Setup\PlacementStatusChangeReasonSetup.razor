﻿@page "/customers/accountstatuschangereasonsetup"
@using LendQube.Entities.Collection.Placements
@using LendQube.Entities.Collection.Setup
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using Radzen.Blazor

@inherits GenericCrudTable<PlacementStatusChangeReasonConfig>

@attribute [Authorize(Policy = ManageCustomersNavigation.StatusChangeReasonSetupIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<PlacementStatusChangeReasonConfig> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Status">Target Status</label>
            <RadzenDropDown @bind-Value=@context.Status Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<PlacementStatus>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Status" Placeholder="Select target status" class="form-input" />
            <ValidationMessage For="() => context.Status" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Code">Code</label>
            <InputText @bind-Value="context.Code" class="form-input" aria-required="true" placeholder="Code" />
            <ValidationMessage For="() => context.Code" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Description">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
    </div>
    ;

    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "Setup Status Change Reasons";
        FormBaseTitle = "Status Change Reason";
        CreatePermission = ManageCustomersNavigation.StatusChangeReasonSetupCreatePermission;
        EditPermission = ManageCustomersNavigation.StatusChangeReasonSetupEditPermission;
        DeletePermission = ManageCustomersNavigation.StatusChangeReasonSetupDeletePermission;
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Code, filterAndPage.TextFilter) || EF.Functions.ILike(x.Description, filterAndPage.TextFilter);
    }
}


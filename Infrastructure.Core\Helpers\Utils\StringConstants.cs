﻿namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class StringConstants
{
    public const string GenericError = "Sorry, an error occurred. Please, retry";

    public const string ValidationError = "Request could not be validated. Please retry";

    public const string PasswordResetMessage = "An email will be sent to the specified mail if it exists on our system";

    public const string LoginTokenMessage = "An {0} will be sent to the specified {1} if it exists";

    public const string AdminAppName = "Silicon Admin";

    public const string SupportEmail = "<EMAIL>";

    public const string TempDomain = "@siliconcreditmanagement.com_temp";

    public const string ApiName = "Silicon Customer API";

    public const string ExcelLicenseName = "David";

}

public static class ConfigConstants
{
    public const string CookieName = "__Host-App.SLA";
    public const string CookieTFAName = "__Host-App.SLATFA";
    public const string CookieTFAUserName = "__Host-App.SLATFAUId";
    public const string CookieAntiforgeryName = "__Host-App.SLAAF";
    public const string MFAPolicy = "RequireMfa";
}
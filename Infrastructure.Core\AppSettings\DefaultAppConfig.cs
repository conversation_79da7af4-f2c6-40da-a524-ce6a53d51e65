﻿using Microsoft.Extensions.Options;

namespace LendQube.Infrastructure.Core.AppSettings;

public sealed class DefaultAppConfig(IOptions<DefaultAppConfigProvidersVM> config) : IAppConfig
{
    public DeployState DeployState => config.Value.DeployState;
    public DisallowedEmails DisallowedEmails => config.Value.DisallowedEmails;
    public Url Url => config.Value.Url;

    internal AzureStorage AzureStorage => config.Value.AzureStorage;
    internal Encryption Encryption => config.Value.Encryption;
    internal TwilioSettings Twilio => config.Value.Twilio;
    internal FirebaseCloudMessaging FirebaseCloudMessaging => config.Value.FirebaseCloudMessaging;
    internal SendGridSettings SendGrid => config.Value.SendGrid;
    internal ClickSend ClickSend => config.Value.ClickSend;
    internal Termii Termii => config.Value.Termii;
    internal Terragon Terragon => config.Value.Terragon;
    internal Mailgun Mailgun => config.Value.Mailgun;
    internal Telegram Telegram => config.Value.Telegram;
    internal TextLocal TextLocal => config.Value.TextLocal;
    internal Smtp Smtp => config.Value.Smtp;
}

﻿using System.Reflection;
using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.DeviceManagement;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Telemetry;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class FirebaseCloudMessagingProvider : AbstractMessageProvider
{
    public const string Name = "FirebaseCloudMessaging";
    private readonly CustomerDeviceService deviceService;
    private readonly DefaultAppConfig config;
    private readonly ILogManager<FirebaseCloudMessagingProvider> logger;
    private readonly int bulkLimit = 500;

    public FirebaseCloudMessagingProvider(IUnitofWork uow, CustomerDeviceService deviceService, DefaultAppConfig config, ILogManager<FirebaseCloudMessagingProvider> logger) : base(uow)
    {
        this.deviceService = deviceService;
        this.config = config;
        this.logger = logger;

        Config = MessagingCompiledQueries.GetProviderConfig(uow, Name) ?? new ProviderConfigVM { Disabled = true };
        if (!Config.Disabled)
            InitializeMessagingService();
    }

    protected override MessageChannel SupportedChannel => MessageChannel.PushNotification;

    public override ProviderConfigVM Config { get; }

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        var leadingMessage = messages[0];

        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        Dictionary<long, MessageStatus> result = [];

        messages = [.. messages.Where(x => x.Data.Any(y => y.ForUser))];
        if (!messages.IsNullOrEmpty())
        {
            var userIds = messages.SelectMany(m => m.Data.Select(x => new { m.MessageId, x.UserId }));
            var users = await deviceService.GetAllPushNotificationIds(userIds.Select(x => x.UserId), ct);
            await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = messages.Count > bulkLimit ? bulkLimit : messages.Count, CancellationToken = ct }, async (message, ct) =>
            {
                var internalUsers = users.Where(x => userIds.Any(y => y.MessageId == message.MessageId && y.UserId == x.UserId));

                if (message.HasSeparateTemplateValues)
                {
                    if (message.Channel == MessageChannel.PushNotification)
                    {
                        result[message.MessageId] = await SendSingleMessage(internalUsers, message, ct);
                    }
                    else
                    {
                        await Parallel.ForEachAsync(message.Data.Select((x, i) => (Value: x, Index: i)), new ParallelOptions { MaxDegreeOfParallelism = message.Data.Count > bulkLimit ? bulkLimit : message.Data.Count, CancellationToken = ct }, async (recipient, ct) =>
                        {
                            result[message.MessageId + recipient.Index] = await SendSingleMessage(internalUsers, message, recipient.Value, ct);
                        });
                    }
                }
                else
                {
                    var total = internalUsers.Count();
                    var skip = 0;

                    while (total > 0)
                    {
                        var toSkip = skip * bulkLimit;
                        var pagedData = internalUsers.Skip(toSkip).Take(bulkLimit);

                        result[message.MessageId] = await SendSingleMessageToManyRecipients(pagedData, message, ct);

                        skip++;
                        total -= bulkLimit;
                    }
                }
            });
        }

        var status = result.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, result, null, null, ct);
        return status;
    }

    public async Task<MessageStatus> SendSingleMessage(IEnumerable<UserPushNotification> users, PreparedMessageVM message, SinglePreparedMessageVM vm, CancellationToken ct)
    {
        bool successful = false;
        try
        {
            var pushNotificationIds = users.Where(x => x.UserId == vm.UserId).Select(x => x.Token).ToList();
            if (pushNotificationIds.IsNullOrEmpty()) return successful.ToMessageStatus();

            var msg = new MulticastMessage
            {
                Tokens = pushNotificationIds,
                Notification = new Notification
                {
                    Title = vm.Subject,
                    Body = vm.TextTemplate
                },
                Data = new Dictionary<string, string>
                {
                    { "Subject", vm.Subject },
                    { "IsDev", config.DeployState.IsDemo.ToString() },
                    { "MessageId", message.MessageId.ToString() }
                }
            };

            var response = await FirebaseMessaging.DefaultInstance.SendEachForMulticastAsync(msg, ct);

            successful = response.FailureCount < pushNotificationIds.Count;
        }
        catch (Exception ex)
        {
            successful = false;
            logger.LogError(EventSource.Messaging, EventAction.SendPushNotification, ex, $"Error occured while sending push notification {message.MessageId}");
        }

        return successful.ToMessageStatus();
    }

    public async Task<MessageStatus> SendSingleMessage(IEnumerable<UserPushNotification> users, PreparedMessageVM message, CancellationToken ct)
    {
        Dictionary<int, MessageStatus> result = [];
        try
        {
            if (users.IsNullOrEmpty()) return MessageStatus.Failed;
            var messages = message.Data.AsParallel().SelectMany(vm => users.Where(x => vm.UserId == x.UserId).Select(x => new Message
            {
                Token = x.Token,
                Notification = new Notification
                {
                    Title = vm.Subject,
                    Body = vm.TextTemplate
                },
                Data = new Dictionary<string, string>
                {
                    { "Subject", vm.Subject },
                    { "IsDev", config.DeployState.IsDemo.ToString() },
                    { "MessageId", message.MessageId.ToString() }
                }
            })).ToList();

            var total = messages.Count;
            var skip = 0;
            while (total > 0)
            {
                var response = await FirebaseMessaging.DefaultInstance.SendEachAsync([.. messages.Skip(skip * bulkLimit).Take(bulkLimit)], ct);
                result[skip] = (response.FailureCount < users.Count()).ToMessageStatus();
                skip++;
                total -= bulkLimit;
            }
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Messaging, EventAction.SendPushNotification, ex, $"Error occured while sending push notification {message.MessageId}");

        }

        return result.ToMessageStatus();
    }

    public async Task<MessageStatus> SendSingleMessageToManyRecipients(IEnumerable<UserPushNotification> users, PreparedMessageVM message, CancellationToken ct)
    {
        bool successful = false;
        try
        {
            var pushNotificationIds = users.Select(x => x.Token).ToList();

            if (pushNotificationIds.IsNullOrEmpty()) return MessageStatus.Failed;

            var msg = new MulticastMessage
            {
                Tokens = pushNotificationIds,
                Notification = new Notification
                {
                    Title = message.Subject,
                    Body = message.TextTemplate
                },
                Data = new Dictionary<string, string>
                {
                    { "Subject", message.Subject },
                    { "IsDev", config.DeployState.IsDemo.ToString() },
                    { "MessageId", message.MessageId.ToString() }
                }
            };

            var response = await FirebaseMessaging.DefaultInstance.SendEachForMulticastAsync(msg, ct);

            successful = response.FailureCount == 0;
            if (!successful)
            {
                var data = response.Responses?.Where(r => !r.IsSuccess)?.ToList() ?? [];
                logger.LogWarning(EventSource.Messaging, EventAction.SendPushNotification, "some messages were not delivered", data: data);
            }
        }
        catch (Exception ex)
        {
            successful = false;
            logger.LogError(EventSource.Messaging, EventAction.SendPushNotification, ex, $"Error occured while sending push notification {message.MessageId}");

        }
        return successful.ToMessageStatus();
    }

    private void InitializeMessagingService()
    {
        try
        {
            if (FirebaseApp.DefaultInstance is null)
            {
                string settingsFile = Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "AppSettings", "firebase-credentials.json");
                GoogleCredential credential = GoogleCredential.FromFile(settingsFile);

                credential = credential.CreateScoped("https://www.googleapis.com/auth/firebase.messaging");

                FirebaseApp.Create(new AppOptions()
                {
                    Credential = credential,
                    ProjectId = config.FirebaseCloudMessaging.ProjectId,
                });
            }
        }
        catch (Exception)
        {
        }
    }
}

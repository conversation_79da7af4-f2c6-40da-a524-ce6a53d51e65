﻿@page "/customerdeviceversion"

@using LendQube.Entities.Core.Logs
@using LendQube.Entities.Core.VersionManagement
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inherits GenericCrudTable<CustomerDeviceVersion>


@attribute [Authorize(Policy = SetupNavigation.CustomerDeviceVersionIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>


<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>


@code
{
    private RenderFragment<CustomerDeviceVersion> FormContent => context => @<div>
        <div class="form-row">
            <label class="form-label" for="Type">Device Type</label>
            <InputSelect @bind-Value="context.Type">
                <option label="Select Device Type" selected></option>
                @foreach (CustomerDeviceType item in Enum.GetValues(typeof(CustomerDeviceType)))
                {
                    if (item == context.Type)
                    {
                        <option value="@item" selected>@item.GetDisplayName()</option>
                    }
                    else
                    {
                        <option value="@item">@item.GetDisplayName()</option>
                    }
                }
            </InputSelect>
            <ValidationMessage For="() => context.Type" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="OldVersion">Old Version</label>
            <InputText @bind-Value="context.OldVersion" class="form-input" aria-required="true" placeholder="Old Version" />
            <ValidationMessage For="() => context.OldVersion" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="CurrentVersion">Current Version</label>
            <InputText @bind-Value="context.CurrentVersion" class="form-input" aria-required="true" placeholder="Current Version" />
            <ValidationMessage For="() => context.CurrentVersion" class="text-danger" />
        </div>
    </div>;

    protected override void OnInitialized()
    {
        Title = "Customer Device Versions";
        SubTitle = "Supported Device Versions";
        FormBaseTitle = "Device Version";
        CreatePermission = SetupNavigation.CustomerDeviceVersionCreatePermission;
        EditPermission = SetupNavigation.CustomerDeviceVersionEditPermission;
        DeletePermission = SetupNavigation.CustomerDeviceVersionDeletePermission;
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        var hasType = Enum.TryParse(filterAndPage.TextFilter, out CustomerDeviceType type);
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => x.OldVersion.Contains(filterAndPage.TextFilter) || x.CurrentVersion.Contains(filterAndPage.TextFilter) || (hasType && x.Type == type);
    }
   
}

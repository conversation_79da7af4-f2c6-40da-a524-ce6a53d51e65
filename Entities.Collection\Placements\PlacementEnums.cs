﻿namespace LendQube.Entities.Collection.Placements;

[Flags]
public enum PlacementStatus
{
    New = 1 << 0,
    Active = 1 << 1,
    Broken = 1 << 2,
    Settled = 1 << 3,
    Sold = 1 << 4,
    Closed = 1 << 5,
}

[Flags]
public enum SchedulePaymentStatus
{
    NotPaid = 1 << 0,
    PartiallyPaid = 1 << 1,
    Paid = 1 << 2,
    Cancelled = 1 << 3
}

[Flags]
public enum SchedulePeriodStatus
{
    NotDue = 1 << 0,
    Due = 1 << 1,
    PastDue = 1 << 2
}
using System;
using System.Threading.Tasks;

namespace LendQube.TestScripts;

/// <summary>
/// Simple test runner that executes basic LendQube functionality tests
/// This version doesn't depend on internal LendQube classes
/// </summary>
public class SimpleTestRunner
{
    public static async Task Main(string[] args)
    {
        Console.WriteLine("╔══════════════════════════════════════════════════════════════╗");
        Console.WriteLine("║                LendQube Simple Test Runner                  ║");
        Console.WriteLine("║              Basic Functionality Testing                    ║");
        Console.WriteLine("╚════════════════════════════════════════════════════════���═════╝");
        Console.WriteLine();

        if (args.Length > 0)
        {
            await RunSpecificTest(args[0]);
        }
        else
        {
            await RunInteractiveMenu();
        }
    }

    private static async Task RunSpecificTest(string testName)
    {
        switch (testName.ToLowerInvariant())
        {
            case "payment":
            case "gateway":
            case "acquired":
                await SimplePaymentGatewayTest.Main(new string[0]);
                break;
            case "email":
            case "mail":
            case "smtp":
                await SimpleEmailTest.Main(new string[0]);
                break;
            case "sms":
            case "text":
                await SimpleSmsTest.Main(new string[0]);
                break;
            case "all":
                await RunAllTests();
                break;
            default:
                Console.WriteLine($"❌ Unknown test: {testName}");
                Console.WriteLine("Available tests: payment, email, sms, all");
                break;
        }
    }

    private static async Task RunInteractiveMenu()
    {
        while (true)
        {
            Console.WriteLine("Select a test to run:");
            Console.WriteLine();
            Console.WriteLine("1. 💳 Payment Gateway Test (Acquired API)");
            Console.WriteLine("2. 📧 Email Test (SMTP)");
            Console.WriteLine("3. 📱 SMS Test (TextLocal/Termii)");
            Console.WriteLine("4. 🚀 Run All Tests");
            Console.WriteLine("5. ❌ Exit");
            Console.WriteLine();
            Console.Write("Enter your choice (1-5): ");

            var choice = Console.ReadLine();

            switch (choice)
            {
                case "1":
                    Console.WriteLine("\n" + new string('═', 60));
                    await SimplePaymentGatewayTest.Main(new string[0]);
                    Console.WriteLine(new string('═', 60) + "\n");
                    break;
                case "2":
                    Console.WriteLine("\n" + new string('═', 60));
                    await SimpleEmailTest.Main(new string[0]);
                    Console.WriteLine(new string('═', 60) + "\n");
                    break;
                case "3":
                    Console.WriteLine("\n" + new string('═', 60));
                    await SimpleSmsTest.Main(new string[0]);
                    Console.WriteLine(new string('═', 60) + "\n");
                    break;
                case "4":
                    await RunAllTests();
                    break;
                case "5":
                    Console.WriteLine("Goodbye! 👋");
                    return;
                default:
                    Console.WriteLine("❌ Invalid choice. Please enter 1-5.");
                    break;
            }

            Console.WriteLine("Press any key to return to menu...");
            Console.ReadKey();
            Console.Clear();
        }
    }

    private static async Task RunAllTests()
    {
        Console.WriteLine("\n🚀 Running All Tests - Basic System Check");
        Console.WriteLine(new string('═', 80));
        
        var startTime = DateTime.UtcNow;
        
        try
        {
            // Test 1: Payment Gateway
            Console.WriteLine("\n1️⃣  PAYMENT GATEWAY TESTING");
            Console.WriteLine(new string('─', 50));
            await SimplePaymentGatewayTest.Main(new string[0]);
            
            Console.WriteLine("\n" + new string('─', 50));
            Console.WriteLine("✅ Payment Gateway Tests Completed");
            
            // Test 2: Email Functions
            Console.WriteLine("\n2️⃣  EMAIL FUNCTION TESTING");
            Console.WriteLine(new string('─', 50));
            await SimpleEmailTest.Main(new string[0]);
            
            Console.WriteLine("\n" + new string('─', 50));
            Console.WriteLine("✅ Email Function Tests Completed");
            
            // Test 3: SMS Functions
            Console.WriteLine("\n3️⃣  SMS FUNCTION TESTING");
            Console.WriteLine(new string('─', 50));
            await SimpleSmsTest.Main(new string[0]);
            
            Console.WriteLine("\n" + new string('─', 50));
            Console.WriteLine("✅ SMS Function Tests Completed");
            
        }
        catch (Exception ex)
        {
            Console.WriteLine($"\n💥 Error during test execution: {ex.Message}");
        }
        finally
        {
            var endTime = DateTime.UtcNow;
            var duration = endTime - startTime;
            
            Console.WriteLine("\n" + new string('═', 80));
            Console.WriteLine("🏁 ALL TESTS COMPLETED");
            Console.WriteLine($"⏱️  Total Duration: {duration.TotalMinutes:F2} minutes");
            Console.WriteLine($"📅 Started: {startTime:yyyy-MM-dd HH:mm:ss} UTC");
            Console.WriteLine($"🏁 Finished: {endTime:yyyy-MM-dd HH:mm:ss} UTC");
            Console.WriteLine(new string('═', 80));
        }
    }
}
﻿using System.Diagnostics;
using System.Globalization;
using System.Runtime.InteropServices;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class DateTimeExtensions
{
    public const string DECISIONENGINEDATEFORMAT = "yyyy-MM-dd";
    public const string REMITADATEFORMAT = "dd/MM/yyyy";
    public const string SHORTDATEFORMAT = "dd-MM-yyyy";
    public const string JSSHORTDATEFORMAT = "DD-MM-YYYY";
    public const string ATTRIBUTESHORTDATEFORMAT = "{0:dd-MM-yyyy}";
    public const string MIDDATEFORMAT = "dd MMM, yyyy";
    public const string LONGDATEFORMAT = "dddd, dd MMM, yyyy";
    public const string LONGDATETIMEFORMAT = "dddd, dd MMM, yyyy hh:mm:ss tt\" GMT\"zzz";
    public const string LONGDATETIMEFORMATALLNUMS = "dd/MM/yyyy HH:mm:ss";
    public const string MBSAPIDATEFORMAT = "dd-MMM-yyyy";

    public static string ToShortDateFormat(this DateTime datetime) => datetime.ToString(SHORTDATEFORMAT);

    public static string ToMidDateFormat(this DateTime datetime) => datetime.ToString(MIDDATEFORMAT);

    public static string ToMidDateFormat(this DateOnly datetime) => datetime.ToString(MIDDATEFORMAT);

    public static string ToLongDateFormat(this DateTime datetime) => datetime.ToString(LONGDATEFORMAT);

    public static string ToLongDateFormat(this DateOnly datetime) => datetime.ToString(LONGDATEFORMAT);

    public static string ToLongDateTimeFormat(this DateTime datetime) => datetime.ToString(LONGDATETIMEFORMAT);

    public static DateTime ParseFromString(this string date)
    {
        var dates = date.Split("-");
        return new DateTime(int.Parse(dates[2]), int.Parse(dates[1]), int.Parse(dates[0]), 0, 0, 0);
    }

    public static DateTime ParseFromStringReverse(this string date)
    {
        var dates = date.Split(" ")[0]?.Split("-");
        if (dates.IsNullOrEmpty())
            throw new ArgumentNullException(date, "provided string is not a valid date of type yyyy-MM-dd");
        return new DateTime(int.Parse(dates[0]), int.Parse(dates[1]), int.Parse(dates[2]), 0, 0, 0);
    }

    public static DateTime ParseFromStringUtc(this string date) => ParseFromString(date).AdjustForUtc();


    public static DateTime ParseForOs(this string date, string format)
    {
        bool isWindows = RuntimeInformation.IsOSPlatform(OSPlatform.Windows);
        if (isWindows)
        {
            return DateTime.Parse(date);
        }
        else
        {
            var dateFormats = new[] { format };
            return DateTime.ParseExact(date, dateFormats, CultureInfo.InvariantCulture);
        }
    }

    public static DateTime EndOfTheDay(this DateTime date) => new DateTime(date.Year, date.Month, date.Day, 23, 59, 59).AdjustForUtc();

    public static DateTime AdjustForUtc(this DateTime date) => new DateTimeOffset(date, TimeSpan.FromHours(1)).UtcDateTime;
}

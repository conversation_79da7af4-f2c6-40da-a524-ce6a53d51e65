﻿@page "/collections/template"
@using LendQube.Entities.Collection.Collections
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.FileManagement
@using Radzen.Blazor
@using SecurityDriven
@inject IFileManagementService fileService

@inherits GenericCrudTable<CollectionTemplate>

@attribute [Authorize(Policy = CollectionNavigation.CollectionTemplateIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>


@code
{
    private RenderFragment<CollectionTemplate> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Name">Attach Template File (Excel/Csv)</label>
            <RadzenFileInput @bind-Value=@context.File @bind-FileName=@context.FileName TValue="string" Style="width: 100%" InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", "select file" }})"
                             accept="@MimeDetectorHelper.ExcelMimeTypes.GetExtensionsInWebAcceptFormat()" />

            <ValidationMessage For="() => context.File" class="text-danger" />
        </div>
    </div>;

    protected override void OnInitialized()
    {
        Title = "Collections";
        FormBaseTitle = "Template";
        SubTitle = "Collection Templates";
        CreatePermission = CollectionNavigation.CollectionTemplateCreatePermission;
        EditPermission = CollectionNavigation.CollectionTemplateEditPermission;
        DeletePermission = CollectionNavigation.CollectionTemplateDeletePermission;


        AddRowButton(new RowActionButton("Download Template", Icon: "download", Action: async (object row) =>
        {
            CloseMessage();
            table.Loading = true;
            var template = row as CollectionTemplate;
            await JSRuntime.DownloadFile(Path.GetFileName(template.FileUrl), template.FileUrl, Cancel);
            table.Loading = false;

            StateHasChanged();
        }, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as CollectionTemplate).FileUrl)));
    }
    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new() { HasId = false });

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.FileUrl, filterAndPage.TextFilter);
    }

    protected override ValueTask SubmitAdd() => BaseSaveAdd(async () =>
    {
        if (AddModel.File == null)
        {
            ModalMessage.Error("Please select a template to upload");
            return false;
        }

        var fileName = fileService.GenerateFileName(FastGuid.NewGuid().ToString(), AddModel.FileName);
        var uploadResult = await fileService.SaveBase64(TemplateHelperVM.TEMPLATES_UPLOADKEY, fileName, AddModel.File, MimeDetectorHelper.ExcelTypes, Cancel);
        if (uploadResult.IsFailed)
        {
            ModalMessage.Error($"Could not upload template file. {uploadResult.Message}");
            return false;
        }

        AddModel.FileUrl = uploadResult.Data;

        var result = await Service.CrudService.New(AddModel, Cancel);
        return result;
    }, 
    async () =>
    {
        AddModel = new();
        await table.Refresh();

    });

    protected override ValueTask SubmitEdit() => BaseSaveEdit(async () =>
    {
        if(EditModel.File != null)
        {
            await fileService.DeleteFile(EditModel.FileUrl, Cancel);
            var fileName = fileService.GenerateFileName(FastGuid.NewGuid().ToString(), EditModel.FileName);

            var uploadResult = await fileService.SaveBase64(TemplateHelperVM.TEMPLATES_UPLOADKEY, fileName, EditModel.File, MimeDetectorHelper.ExcelTypes, Cancel);
            if (uploadResult.IsFailed)
            {
                ModalMessage.Error($"Could not upload template file. {uploadResult.Message}");
                return false;
            }

            EditModel.FileUrl = uploadResult.Data;
        }

        if (string.IsNullOrEmpty(EditModel.FileUrl))
        {
            ModalMessage.Error("Could not upload template file");
            return false;
        }
        var result = await Service.CrudService.Update(EditModel, Cancel);
        return result;
    },
    async () =>
    {
        EditModel = new();
        await table.Refresh();

    });

    protected override async ValueTask<bool> SubmitDelete(CollectionTemplate data, Func<Task> refresh, CancellationToken ct) => await SaveDelete(async () =>
    {
        await fileService.DeleteFile(data.FileUrl, ct);
        return await Service.CrudService.Delete(x => x.Id == data.Id, ct);
    }, refresh);

}

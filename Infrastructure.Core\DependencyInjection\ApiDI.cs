﻿using System.IO.Compression;
using Asp.Versioning;
using LendQube.Infrastructure.Core.DeviceManagement;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Middleware;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Net.Http.Headers;
using Microsoft.OpenApi.Models;

namespace LendQube.Infrastructure.Core.DependencyInjection;

public static class ApiDI
{
    public static WebApplicationBuilder AddBaseApiDI(this WebApplicationBuilder builder)
    {
        builder.AddAppSettings();

        builder.Services.AddBaseDIServices();

        builder.Services.AddTransient<CustomerDeviceService>();

        builder.Services.AddResponseCompression(options =>
        {
            options.Providers.Add<BrotliCompressionProvider>();
            options.Providers.Add<GzipCompressionProvider>();
            options.EnableForHttps = true;
            options.MimeTypes = ResponseCompressionDefaults.MimeTypes.Concat(["image/svg+xml", "image/png", "image/jpg", "image/jpeg"]);
        });

        builder.Services.Configure<BrotliCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.Optimal;
        });

        builder.Services.Configure<GzipCompressionProviderOptions>(options =>
        {
            options.Level = CompressionLevel.Optimal;
        });

        if (builder.Configuration.GetValue<bool>("EnableSSL"))
        {
            builder.Services.AddHsts(options =>
            {
                options.Preload = true;
                options.IncludeSubDomains = true;
                options.MaxAge = TimeSpan.FromDays(366);
            });

            builder.Services.AddHttpsRedirection(options =>
            {
                options.RedirectStatusCode = StatusCodes.Status301MovedPermanently;
            });
        }

        builder.Services.AddRouting(options =>
        {
            options.LowercaseUrls = true;
            options.LowercaseQueryStrings = true;
        });

        builder.Services.AddSingleton<ILogManager<ApiExceptionHandler>, LogManager<ApiExceptionHandler>>()
            .AddExceptionHandler<ApiExceptionHandler>()
            .AddProblemDetails();

        builder.Services.AddApiVersioning(options =>
        {
            options.DefaultApiVersion = new ApiVersion(1);
            options.ReportApiVersions = true;
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.ApiVersionReader = new UrlSegmentApiVersionReader();
        })
        .AddMvc()
        .AddApiExplorer(options =>
        {
            options.GroupNameFormat = "'v'V";
            options.SubstituteApiVersionInUrl = true;
        });

        builder.Services.AddMvc(c => c.Conventions.Add(new ApiExplorerConvention(builder.Configuration)));
        builder.Services.AddGlobalRateLimiting();
        return builder;
    }

    public static WebApplicationBuilder AddApiIdentity(this WebApplicationBuilder builder)
    {
        builder.Services.AddOpenIddictApi()
        .AddIdentity()
        .AddCustomHasher();

        return builder;
    }

    public static WebApplicationBuilder ConfigureSwagger(this WebApplicationBuilder builder, string projectName)
    {
        if (!builder.Configuration.GetValue<bool>(InfrastructureCoreDI.IsDemoAppSettingsKey))
            return builder;

        builder.Services.AddEndpointsApiExplorer();
        builder.Services.AddSwaggerGen(
            c =>
            {
                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = $"Ethica Resolve {projectName} API",
                    Version = "v1",
                    Description = $"Ethica Resolve {projectName} API Services.",
                    Contact = new OpenApiContact
                    {
                        Name = StringConstants.SupportEmail
                    }
                });

                c.AddSecurityDefinition(
                    "oauth",
                    new OpenApiSecurityScheme
                    {
                        Flows = new OpenApiOAuthFlows
                        {
                            ClientCredentials = new OpenApiOAuthFlow
                            {
                                TokenUrl = new Uri("/connect/token", UriKind.Relative),
                                AuthorizationUrl = new Uri("/connect/token", UriKind.Relative),
                                RefreshUrl = new Uri("/connect/token", UriKind.Relative)
                            },
                        },
                        In = ParameterLocation.Header,
                        Name = HeaderNames.Authorization,
                        Type = SecuritySchemeType.OpenIdConnect,
                        OpenIdConnectUrl = new Uri("/.well-known/openid-configuration", UriKind.Relative)
                    }
                );

                c.AddSecurityRequirement(
                    new OpenApiSecurityRequirement
                    {
                        {
                            new OpenApiSecurityScheme
                            {
                                Reference = new OpenApiReference
                                    { Type = ReferenceType.SecurityScheme, Id = "oauth" },
                            },
                            Array.Empty<string>()
                        }
                    }
                );
            }
        );


        return builder;
    }

    public static WebApplication AddApiApp(this WebApplication app, WebApplicationBuilder builder)
    {
        app.UseResponseCompression();
        app.UseExceptionHandler();
        app.UseMiddleware<SecureHeadersMiddleware>();

        if (builder.Configuration.GetValue<bool>("EnableSSL"))
        {
            app.UseHsts();
            app.UseHttpsRedirection();
        }

        app.UseCors(builder => builder
            .AllowAnyOrigin()
            .AllowAnyHeader()
            .AllowAnyMethod());

        app.UseRouting();
        app.UseAuthentication();
        app.UseAuthorization();

        app.MapControllers();
        app.MapDefaultControllerRoute();
        app.UseRateLimiter();
        return app;
    }
}

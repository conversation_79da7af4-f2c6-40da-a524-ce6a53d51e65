﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components.Timeline;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericSpecification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageLog;

public partial class ViewMessageLog
{
    private ActivityTimeline<MessageLogActivity> activityTimeline;

    private ValueTask<TypedBasePageList<MessageLogActivity>> LoadActivity(DataFilterAndPage filterAndPage, GenericSpecificationService<MessageLogActivity> service, CancellationToken ct)
    {
        service.PrimaryCriteria = x => x.MessageLogId == Data.Id;
        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            service.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Title, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Activity, filterAndPage.TextFilter) || EF.Functions.ILike(x.CreatedByUser, filterAndPage.TextFilter));
        }

        return service.CrudService.GetTypeBasedPagedData(service, filterAndPage, ct: ct);
    }

    private async Task ClearActivity()
    {
        message.Close();
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<MessageLogActivity>(x => x.MessageLogId == LogId, Cancel);
        if (result > 0)
        {
            uow.Db.Insert(new MessageLogActivity { MessageLogId = LogId, Activity = "Activity log cleared" });
            await uow.SaveAsync(Cancel);
            message.Success("Message activity log cleared successfully");
        }
        else
        {
            message.Error("Could not clear activity log. Please try again");
        }

        await activityTimeline.Refresh();
        StateHasChanged();
    }
}

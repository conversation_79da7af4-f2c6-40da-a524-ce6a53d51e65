﻿@page "/reporting/run"
@using LendQube.Entities.Core.Reporting
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Reporting
@using Radzen.Blazor

@inherits GenericCrudTable<SystemReport>

@attribute [Authorize(Policy = ReportingNavigation.RunReportsIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-lg">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<SystemReport> FormContent => context =>@<div>
         <div class="form-row">
            <label class="form-label" for="Description">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="TypeName">Report Type</label>
                <RadzenDropDown @bind-Value=@context.TypeName Data=@reportTypes
                                TextProperty="@nameof(ReportTypes.TypeName)" ValueProperty="@nameof(ReportTypes.TypeName)"
                                Name="ReportTypeName" Placeholder="Select report type" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"/>
                <ValidationMessage For="() => context.TypeName" class="text-danger" />
            </div>

            @if(!string.IsNullOrEmpty(context.TypeName))
            {
                <div class="form-row">
                    <label class="form-label" for="ReportNames">Report Names</label>
                    <RadzenDropDown @bind-Value=@context.Names Data=@reportTypes.FirstOrDefault(x => x.TypeName == context.TypeName).Names Multiple=true AllowClear=true Chips=true
                                        Name="ReportNames" Placeholder="Select reports" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true" />
                    <ValidationMessage For="() => context.TypeName" class="text-danger" />
                </div>
            }
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="StartDate">Report Start Date</label>
                <RazdenInstantDatePicker @bind-DateValue="context.StartDate" Placeholder="Report Start Date" Name="StartDate" />
                <ValidationMessage For="() => context.StartDate" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="EndDate">Report End Date</label>
                <RazdenInstantDatePicker @bind-DateValue="context.EndDate" Placeholder="Report End Date" Name="EndDate" />
                <ValidationMessage For="() => context.EndDate" class="text-danger" />
            </div>
        </div>
        <div class="form-row">
            <label class="form-label" for="FileType">File Type</label>
            <RadzenDropDown @bind-Value=@context.FileType Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<ReportType>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="FileType" Placeholder="Select report file type" class="form-input" />
            <ValidationMessage For="() => context.FileType" class="text-danger" />
        </div>
    </div>
    ;

    private List<ReportTypes> reportTypes = [];
    protected override void OnInitialized()
    {
        Title = "Reporting";
        SubTitle = "Manually Run Reports";
        FormBaseTitle = "Run Report";
        CreatePermission = ReportingNavigation.RunReportsCreatePermission;
        DeletePermission = ReportingNavigation.RunReportsDeletePermission;
        BaseReportingService.StatusNotificationEvent += ReportTaskUpdate;
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            await base.OnInitializedAsync();

            AddRowButton(new RowActionButton("Download Report", Icon: "download", IconClass: "__edit", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                var report = row as SystemReport;
                await JSRuntime.DownloadFile(Path.GetFileName(report.FileUrl), report.FileUrl, Cancel);
                table.Loading = false;

                StateHasChanged();
            }, ShowCondition: (object row) => !string.IsNullOrWhiteSpace((row as SystemReport).FileUrl)));
        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        base.OnAfterRender(firstRender);

        if (firstRender)
            reportTypes = ReportingHelper.ReportNames.AsParallel().Where(x => HasClaim(x.RequiredPermission)).ToList();
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new() { HasEdit = false });

    protected override void DefinePrimaryCriteria(DataFilterAndPage filterAndPage)
    {
        Service.PrimaryCriteria = x => !x.ReportScheduleId.HasValue;
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = Service.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.TypeName, filterAndPage.TextFilter)
        || x.Names.Any(y => EF.Functions.ILike(y, filterAndPage.TextFilter)));
    }

    public void ReportTaskUpdate(object sender, ReportEventArgs e)
    {
        if (e.Owner != UserName)
            return;

        _ = InvokeAsync(async () =>
        {
            await table.Refresh();
            if (e.Status == ReportStatus.Queued || e.Status == ReportStatus.Running)
                TableMessage.Info(e.Message, e.Status == ReportStatus.Running);
            else
                TableMessage.Set(e.Successful, e.Message);
            StateHasChanged();
        });
    }

    public override void Dispose()
    {
        BaseReportingService.StatusNotificationEvent -= ReportTaskUpdate;
    }
}
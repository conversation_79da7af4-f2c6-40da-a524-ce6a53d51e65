﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Diagnostics;
using System.Reflection;

namespace LendQube.Entities.Core.Extensions;

[DebuggerStepThrough]
public static class EnumExtensions
{
    public static string GetDisplayName(this Enum enumValue)
    {
        try
        {
            return enumValue.GetType()
                            ?.GetMember(enumValue.ToString())
                            ?.First()
                            ?.GetCustomAttribute<DisplayAttribute>()
                            ?.GetName()
                            ?? enumValue?.ToString()?.SplitOnUpper();
        }
        catch (Exception)
        {
            return "None";
        }
    }

    public static T ToEnum<T>(this string value) where T : Enum => (T)Enum.Parse(typeof(T), value.Replace(" ", string.Empty), ignoreCase: true);

    public static bool IsFlagEnum(this Type type) => type.IsEnum && Attribute.IsDefined(type, typeof(FlagsAttribute));

    public static T ConvertEnum<T>(object o) => (T)Enum.ToObject(typeof(T), o);

    public static List<T> FlagsToList<T>(this Enum? value) where T : Enum => value == null ? [] : value.ToString().Split(',').Select(flag => (T)Enum.Parse(typeof(T), flag)).ToList();

    public static List<string> FlagsToDisplayList<T>(this Enum? value) where T : Enum => value == null ? [] : value.ToString().Split(',').Select(flag => ((Enum)Enum.Parse(typeof(T), flag)).GetDisplayName()).ToList();

    public static List<string> FlagsToStringList<T>(this Enum? value) where T : Enum => value == null ? [] : [.. value.ToString().Split(',')];

    public static bool IsFlagSet<T>(this T value, T flag) where T : Enum
    {
        long lValue = Convert.ToInt64(value);
        long lFlag = Convert.ToInt64(flag);
        return (lValue & lFlag) != 0;
    }

    public static IEnumerable<T> GetFlags<T>(this T value) where T : Enum
    {
        foreach (T flag in Enum.GetValues(typeof(T)).Cast<T>())
        {
            if (value.IsFlagSet(flag))
                yield return flag;
        }
    }

    public static T SetFlags<T>(this T value, T flags, bool on) where T : Enum
    {
        long lValue = Convert.ToInt64(value);
        long lFlag = Convert.ToInt64(flags);
        if (on)
        {
            lValue |= lFlag;
        }
        else
        {
            lValue &= (~lFlag);
        }
        return ConvertEnum<T>(lValue);
    }

    public static T SetFlags<T>(this T value, T flags) where T : Enum => value.SetFlags(flags, true);

    public static T ClearFlags<T>(this T value, T flags) where T : Enum => value.SetFlags(flags, false);

    public static T CombineFlags<T>(this IEnumerable<T> flags) where T : Enum
    {
        long lValue = 0;
        foreach (T flag in flags)
        {
            long lFlag = Convert.ToInt64(flag);
            lValue |= lFlag;
        }
        return ConvertEnum<T>(lValue);
    }

    public static string GetDescription<T>(this T value) where T : Enum
    {
        string name = Enum.GetName(typeof(T), value);
        if (name != null)
        {
            FieldInfo field = typeof(T).GetField(name);
            if (field != null)
            {
                if (Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) is DescriptionAttribute attr)
                {
                    return attr.Description;
                }
            }
        }
        return null;
    }

    public static List<string> GetListOfDescription<T>() where T : Enum
    {
        Type t = typeof(T);
        return !t.IsEnum ? null : Enum.GetValues(t).Cast<Enum>().Select(x => x.GetDescription()).ToList();
    }


    public static IReadOnlyList<string> GetEnumNames<T>() where T : Enum
    {
        Type t = typeof(T);
        return !t.IsEnum ? null : Enum.GetValues(t).Cast<Enum>().Select(x => x.ToString().ToLower()).ToList().AsReadOnly();
    }

    public static List<EnumDisplayItem> GetEnumValues<T>(List<T> valuesToRemove = default) where T : Enum
    {
        Type t = typeof(T);
        return !t.IsEnum ? [] : Enum.GetValues(t).Cast<T>().Where(x => valuesToRemove == null || !valuesToRemove.Contains(x)).Select(x => new EnumDisplayItem(x, x.GetDisplayName())).ToList();
    }

    public static List<T> EnumToList<T>(List<T> valuesToRemove = default) where T : Enum
    {
        Type t = typeof(T);
        return !t.IsEnum ? [] : Enum.GetValues(t).Cast<T>().Where(x => valuesToRemove == null || !valuesToRemove.Contains(x)).ToList();
    }


    public static List<string> EnumToStringList<T>(List<T> valuesToRemove = default) where T : Enum
    {
        Type t = typeof(T);
        return !t.IsEnum ? [] : Enum.GetValues(t).Cast<T>().Where(x => valuesToRemove == null || !valuesToRemove.Contains(x)).Select(x => x.ToString()).ToList();
    }

    public static List<string> EnumToDescriptiveStringList<T>(List<T> valuesToRemove = default) where T : Enum
    {
        Type t = typeof(T);
        return !t.IsEnum ? [] : Enum.GetValues(t).Cast<T>().Where(x => valuesToRemove == null || !valuesToRemove.Contains(x)).Select(x => x.ToString().SplitOnUpper()).ToList();
    }

    public static List<T> StringListToEnum<T>(List<string> values) where T : Enum
    {
        Type t = typeof(T);
        return !t.IsEnum ? [] : Enum.GetValues(t).Cast<T>().Where(x => values.Contains(x.ToString(), StringComparer.OrdinalIgnoreCase)).ToList();
    }

    public static string GetCategory(this Enum source)
    {
        FieldInfo fieldInfo = source.GetType().GetField(source.ToString());
        CategoryAttribute attribute = (CategoryAttribute)fieldInfo.GetCustomAttribute(typeof(CategoryAttribute), false);

        return attribute.Category;
    }
}


public sealed record EnumDisplayItem(Enum Value, string Name);
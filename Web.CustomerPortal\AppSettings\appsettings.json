{"EnableSSL": false, "AzureLogging": {"FileName": "azure-diagnostics-", "FileSizeLimit": 50024, "RetainedFileCountLimit": 5}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ConnectionStrings": {"BackgroundTaskDbConnectionString": "Host=***********;Port=5432;Database=lendqubehangfire;Username=********;Password=********;", "ConnectionString": "Host=***********;Port=5432;Database=lqdev;Username=********;Password=********;"}, "Password": {"Iterations": 65536}, "Providers": {"DeployState": {"IsDemo": true, "RemoveTriggersOnShutdown": false, "EnabledApplications": {"Collections": true}}, "Url": {"WebAsset": "https://cdn.ethicaresolve.co.uk/staticassets/", "WebAdmin": "https://localhost:44323", "WebClient": "https://localhost:5001", "WebHook": "https://localhost:44398", "WebBackground": "https://localhost:44362/", "Logo": "https://cdn.ethicaresolve.co.uk/staticassets/logo.svg", "Contact": "https://www.siliconcreditmanagement.com/contact/"}, "Encryption": {"key": "12=$?L(R-uaPdRqU", "VI": "%G*H-<PERSON>@NpRfUI022"}, "TextLocal": {"ApiKey": "Mzc0MjQxNDE0ZTM2Mzc2YjRlNDg3MjVhNDI3MjMwNjM=", "From": "Silicon", "BaseUrl": "https://api.txtlocal.com/send/"}, "Smtp": {"Host": "***********", "Port": 25, "From": "<EMAIL>", "SenderName": "Silicon"}, "AzureStorage": {"Url": "https://cdn.ethicaresolve.co.uk/", "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=lendqubefiles;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"}, "FirebaseCloudMessaging": {"ProjectId": "lendqube-55c80"}, "DisallowedEmails": {"DisallowedDomains": "@siliconcreditmanagement.com", "NoMessageDomains": "@siliconcreditmanagement.com_temp"}, "Telegram": {"ApiId": "********", "ApiHash": "190ac36c9ca6c637925516e03c424d66", "PhoneNumber": "+*************"}}, "ApplicationInsights": {"InstrumentationKey": "d51cf538-e72b-4caa-bef8-ea180b1428b3", "ConnectionString": "InstrumentationKey=d51cf538-e72b-4caa-bef8-ea180b1428b3;IngestionEndpoint=https://uksouth-1.in.applicationinsights.azure.com/;LiveEndpoint=https://uksouth.livediagnostics.monitor.azure.com/;ApplicationId=0f628780-7b1f-4279-a69c-9ee8fa65b488"}}
﻿using System.Collections.Concurrent;
using System.Globalization;
using System.Text;
using Coravel.Queuing.Interfaces;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.NotificationTriggers;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using Microsoft.AspNetCore.Components.Forms;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Core.FileManagement;

public abstract class AbstractFileUploadService<T>(IFileManagementService fileService, IQueue queue, BackgroundTaskControlService backgroundService,
    HttpClient httpClient) : IHandleTriggerNotification<T>
    where T : BaseFileUpload<T>, IEntityHasNotifyTrigger
{
    private readonly IUnitofWork uow = backgroundService.Uow;
    protected abstract ILogManager<T> Logger { get; }

    protected abstract string UPLOADKEY { get; }

    protected abstract BackgroundEventSource Source { get; }
    protected abstract BackgroundTask Key { get; }

    public async Task<Result<bool>> Upload(string type, T upload, IBrowserFile file, CancellationToken ct)
    {
        if (file == null || file.Size == 0)
            return "Please attach file to upload";

        try
        {
            await fileService.DeleteFile(upload.FileUrl, ct);
            var fileName = fileService.GenerateFileName($"{type}_{SecurityDriven.FastGuid.NewGuid()}", file.Name);
            upload.FileUrl = await fileService.SaveStream(UPLOADKEY, fileName, file.OpenReadStream(file.Size, ct), ct);

            upload.Status = UploadStatus.Queued;
            upload.AnalysisFileUrl = null;
            upload.Message = null;

            if (upload.Id > 0)
                uow.Db.Update(upload);
            else
                uow.Db.Insert(upload);

            await uow.SaveAsync(ct);

            return Result<bool>.Successful($"{type} uploaded successfully. {upload.Action.GetDisplayName()} running in background. Please wait ...");

        }
        catch (Exception ex)
        {
            Logger.LogError(EventSource.AdminWeb, EventAction.Upload, ex, "Creating Upload failed", data: upload);

            return "An error occurred during upload. Please try again";
        }
    }

    public async Task<Result<bool>> Queue(long id, UploadAction action, CancellationToken ct)
    {
        var result = await uow.Db.UpdateAndSaveWithFilterAsync<T>(x => x.Id == id && x.Action != action,
             x => x.SetProperty(y => y.Action, action)
             .SetProperty(y => y.Message, string.Empty)
             .SetProperty(y => y.Status, UploadStatus.Queued), ct);

        return result > 0 ? Result<bool>.Successful($"{action} processing, please wait ...") : "Upload could not be restarted. Try again";
    }

    public async Task<bool> Delete(T data, CancellationToken ct)
    {
        await fileService.DeleteFile(data.FileUrl, ct);
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<T>(x => x.Id == data.Id, ct);
        return result > 0;
    }


    #region Process Upload
    protected abstract Task<Result<MessageBrick>> UploadData(T upload, ExcelWorksheet worksheet, MessageBrick message, ConcurrentBag<UploadResult> resultsList, CancellationToken ct);

    protected virtual void RaiseEvent(SystemBackgroundTaskEventArgs data)
    {

    }

    private async Task ReadExcel(T upload, CancellationToken ct)
    {
        try
        {
            var file = await httpClient.ReadPhysicalFileAsByteArray(upload.FileUrl, ct);

            ExcelPackage.License.SetNonCommercialPersonal(StringConstants.ExcelLicenseName);
            using var package = new ExcelPackage(new MemoryStream(file));
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();

            await DoUpload(upload, worksheet, ct);
        }
        catch (Exception ex)
        {
            RaiseEvent(new SystemBackgroundTaskEventArgs(false, "An error occurred while uploading", upload.ModifiedByUserId ?? upload.CreatedByUserId));
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<T>(x => x.Id == upload.Id,
                 x => x.SetProperty(y => y.Status, UploadStatus.Failed)
                 .SetProperty(y => y.Message, "Error occurred, contact admin"), ct);

            Logger.LogError(EventSource.AdminWeb, EventAction.Upload, ex, "Processing excel upload failed", data: upload);
        }
    }

    private async Task ReadCsv(T upload, CancellationToken ct)
    {
        try
        {
            var file = await httpClient.ReadPhysicalFileAsString(upload.FileUrl, ct);

            ExcelTextFormat format = new()
            {
                Delimiter = ',',
                TextQualifier = '\"',
                FirstRowIsHeader = true,
                Encoding = Encoding.UTF8,
                Culture = new CultureInfo("en-GB"),
            };

            format.Culture.DateTimeFormat.ShortDatePattern = "dd/mm/yyyy";


            ExcelPackage.License.SetNonCommercialPersonal(StringConstants.ExcelLicenseName);
            using ExcelPackage package = new();
            ExcelWorksheet worksheet = package.Workbook.Worksheets.Add("Sheet 1");

            worksheet.Cells["A1"].LoadFromText(file, format, OfficeOpenXml.Table.TableStyles.None, true);

            await DoUpload(upload, worksheet, ct);
        }
        catch (Exception ex)
        {
            RaiseEvent(new SystemBackgroundTaskEventArgs(false, "An error occurred while uploading", upload.ModifiedByUserId ?? upload.CreatedByUserId));
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<T>(x => x.Id == upload.Id,
                 x => x.SetProperty(y => y.Status, UploadStatus.Failed)
                 .SetProperty(y => y.Message, "Error occurred, contact admin"), ct);

            Logger.LogError(EventSource.AdminWeb, EventAction.Upload, ex, "Processing csv upload failed", data: upload);
        }
    }

    private async Task DoUpload(T upload, ExcelWorksheet worksheet, CancellationToken ct)
    {

        if (worksheet != null)
            TrimEmptyRows(worksheet);

        ConcurrentBag<UploadResult> resultsList = [];

        var message = MessageBuilder.New(typeof(T).Name.SplitOnUpper(), upload.CreatedByUserId);

        var uploadResult = await UploadData(upload, worksheet, message, resultsList, ct);
        (var result, message) = uploadResult;

        var (errorsSaved, errorCount) = await SaveAnalysisExcel(resultsList, upload, ct);

        if (result)
            upload.Message = upload.Action == UploadAction.Analyze ? "Analysis Successful. Proceed to import" : "All records successfully applied";
        else
        {
            upload.Message = upload.Action == UploadAction.Analyze ? $"{errorCount} record(s) failed analysis. " : $"{errorCount} record(s) not applied. ";
            if (!errorsSaved)
                upload.Message += $"Analysis excel saving failed. Message: {uploadResult.Message}";
            else
                upload.Message += $"Check analysis Excel. Message: {uploadResult.Message}";
        }

        upload.Status = result ? upload.Action == UploadAction.Analyze ? UploadStatus.Analyzed : UploadStatus.Imported : UploadStatus.DoneWithErrors;

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<T>(x => x.Id == upload.Id, x =>
        x.SetProperty(y => y.Status, upload.Status)
        .SetProperty(y => y.Message, upload.Message)
        .SetProperty(y => y.AnalysisFileUrl, upload.AnalysisFileUrl),
        ct);

        RaiseEvent(new SystemBackgroundTaskEventArgs(result, upload.Message, upload.ModifiedByUserId ?? upload.CreatedByUserId));

        if (upload.Status == UploadStatus.Imported)
            message.Queue(queue);
    }

    private async Task<Result<long>> SaveAnalysisExcel(ConcurrentBag<UploadResult> data, T upload, CancellationToken ct)
    {
        try
        {
            if (data.IsEmpty) return 0;

            string export = $"Upload Analysis {upload.Id}";


            ExcelPackage.License.SetNonCommercialPersonal(StringConstants.ExcelLicenseName);
            using var package = new ExcelPackage();
            var worksheet = package.Workbook.Worksheets.Add(export);

            var flattenedData = data.AsParallel().Where(x => x != null).GroupBy(x => x.Row)
                .Select(x => new { x.Key, x.FirstOrDefault().Id, Messages = string.Join(", ", x.Select(y => y.Message)) }).OrderBy(x => x.Key).ToList();
            worksheet.Cells["A1"].LoadFromCollection(flattenedData);

            worksheet.Cells.AutoFitColumns();

            var dayTime = DateTime.UtcNow.ToShortDateFormat();
            var fileName = $"{dayTime}/Analysis-{upload.Id}_{SecurityDriven.FastGuid.NewGuid()}.xlsx";

            upload.AnalysisFileUrl = await fileService.SaveExcel(package, UPLOADKEY, fileName, ct);

            data.Clear();
            return flattenedData.Count;
        }
        catch (Exception ex)
        {
            Logger.LogError(EventSource.AdminWeb, EventAction.Upload, ex, "Saving analysis file for upload failed", data: upload);
        }

        return "Saving analysis file failed";
    }

    private static void TrimEmptyRows(ExcelWorksheet worksheet)
    {
        for (int i = worksheet.Dimension.End.Row; i >= worksheet.Dimension.Start.Row; i--)
        {
            bool isRowEmpty = true;
            //loop all columns in a row
            for (int j = worksheet.Dimension.Start.Column; j <= worksheet.Dimension.End.Column; j++)
            {
                if (worksheet.Cells[i, j].Value != null)
                {
                    isRowEmpty = false;
                    break;
                }
            }
            if (isRowEmpty)
            {
                worksheet.DeleteRow(i);
            }
        }
    }
    #endregion

    #region TriggerHandling
    public async ValueTask OnChanged(string? id, T oldData, T newData, TriggerChange change, CancellationToken ct)
    {
        var stopped = await backgroundService.CheckStoppedOrStopping(Source, Key, ct);
        if (stopped)
        {
            RaiseEvent(new SystemBackgroundTaskEventArgs(false, $"{Key.GetDisplayName()} upload service is stopped. Please start service to process uploads", newData.ModifiedByUserId ?? newData.CreatedByUserId));
            return;
        }

        await backgroundService.SetStatusToRunning(Source, Key, ct);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<T>(x => x.Id == newData.Id && x.Status == UploadStatus.Queued,
             x => x.SetProperty(y => y.Status, UploadStatus.Processing), ct);


        if (newData.FileUrl.EndsWith(".csv", StringComparison.OrdinalIgnoreCase))
            await ReadCsv(newData, ct);
        else
            await ReadExcel(newData, ct);

        await backgroundService.SetStatusToIdle(Source, Key, ct);
    }

    public Task OnStartup(CancellationToken ct) => uow.Db.UpdateAndSaveWithFilterAsync<T>(x => x.Status == UploadStatus.Queued,
             x => x.SetProperty(y => y.Status, UploadStatus.Queued), ct);
    #endregion
}

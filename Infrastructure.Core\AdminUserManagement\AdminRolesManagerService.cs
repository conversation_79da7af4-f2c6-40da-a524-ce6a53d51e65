﻿using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.AdminUserManagement;

public sealed class AdminRolesManagerService(GenericEntityCrudService<ApplicationRole> crudService) : BaseSpecification<ApplicationRole>
{
    public async Task<GenericResponseVM> Delete(ApplicationRole data, CancellationToken ct)
    {
        if (data.Name == SystemRoleConfig.SuperAdminRole || data.Name == SystemRoleConfig.CustomerRole)
        {
            return new GenericResponseVM { Message = $"{data.Name} role cannot be deleted" };
        }
        var result = await crudService.Delete(x => x.Id == data.Id, ct);
        return new GenericResponseVM { Successful = result };
    }

    public Task<bool> New(RoleVM data, CancellationToken ct) => crudService.New(new() { Name = data.Name, NormalizedName = data.Name.ToUpper(), Description = data.Description }, ct);

    public async Task<GenericResponseVM> Update(RoleVM vm, CancellationToken ct)
    {
        if (vm.Name == SystemRoleConfig.SuperAdminRole || vm.Name == SystemRoleConfig.CustomerRole)
        {
            return new GenericResponseVM { Message = $"{vm.Name} role cannot be updated" };
        }

        var result = await crudService.UpdateWithFilter(x => x.Id == vm.Id,
            x => x.SetProperty(y => y.Name, vm.Name)
            .SetProperty(y => y.NormalizedName, vm.Name.ToUpper())
            .SetProperty(y => y.Description, vm.Description),
            ct);

        return new GenericResponseVM { Successful = result };
    }

    public ColumnList GetTableDefinition() => crudService.GetTableDefinition(new()
    {
        HasDateColumns = false,
        ColumnsToAdd = [new() { Index = 0, Name = x => x.Name, ShowInDelete = true, ShowInFilter = true }]
    });

    public ValueTask<TypedBasePageList<ApplicationRole>> GetTypeBasedPagedData(DataFilterAndPage data, CancellationToken ct)
    {
        PrimaryCriteria = x => x.Name != SystemRoleConfig.CustomerRole;

        if (!string.IsNullOrEmpty(data.TextFilter))
        {
            data.TextFilter = $"%{data.TextFilter}%";
            PrimaryCriteria = PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Name, data.TextFilter)
            || EF.Functions.ILike(x.Description, data.TextFilter) || EF.Functions.ILike(x.NormalizedName, data.TextFilter));
        }

        TagOrigination();

        return crudService.GetTypeBasedPagedData(this, data, ct: ct);
    }
}


﻿namespace LendQube.Infrastructure.Core.Navigation;

public sealed class ReportingNavigation : INavigationDescriptor, INavigatorHasPermissions
{
    public bool IsDisabled { get; set; } = false;
    public const string GroupName = "Reporting";
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = GroupName,
            Icon = "settings",
            Permission = RunReportsIndexPermission,
            Controller = "Reporting",
            Url = "reporting/run",
            SubNavigation =
            [
                new() { Name = "Run", Icon = "globe", Permission = RunReportsIndexPermission, Controller = "RunReports", Url = "reporting/run" },
                new() { Name = "Schedule", Icon = "globe", Permission = ScheduleReportingIndexPermission, Controller = "ScheduleReporting", Url = "reporting/scheduler" },
                new() { Name = "Logs", Icon = "globe", Permission = ReportLogsIndexPermission, Controller = "AllReportLogs", Url = "reporting/logs" },
            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.ReportingIndex, GroupName, navs);
    }

    public void PreparePermissionDescriptions()
    {
        Navigator.PermissionDescription[RunReportsIndexPermission] = $"Can view manually run reports and access {GroupName}";
        Navigator.PermissionDescription[RunReportsCreatePermission] = "Can manually run reports";
        Navigator.PermissionDescription[RunReportsDeletePermission] = "Can delete manually run reports";

        Navigator.PermissionDescription[ReportLogsIndexPermission] = "Can view reporting logs";
        Navigator.PermissionDescription[ReportLogsClearLogsPermission] = "Can clear all reporting logs";

        Navigator.PermissionDescription[ScheduleReportingIndexPermission] = "Can view reporting schedule";
        Navigator.PermissionDescription[ScheduleReportingCreatePermission] = "Can create reporting schedule";
        Navigator.PermissionDescription[ScheduleReportingEditPermission] = "Can modify reporting schedule";
        Navigator.PermissionDescription[ScheduleReportingDeletePermission] = "Can delete reporting schedule";
    }

    public const string RunReportsIndexPermission = "Permission.RunReports.Index";
    public const string RunReportsCreatePermission = "Permission.RunReports.Create";
    public const string RunReportsDeletePermission = "Permission.RunReports.Delete";

    public const string ScheduleReportingIndexPermission = "Permission.ScheduleReporting.Index";
    public const string ScheduleReportingCreatePermission = "Permission.ScheduleReporting.Create";
    public const string ScheduleReportingEditPermission = "Permission.ScheduleReporting.Edit";
    public const string ScheduleReportingDeletePermission = "Permission.ScheduleReporting.Delete";

    public const string ReportLogsIndexPermission = "Permission.AllReportLogs.Index";
    public const string ReportLogsClearLogsPermission = "Permission.AllReportLogs.ClearLogs";

}

﻿namespace LendQube.Infrastructure.Collection.Reporting;

public static class CollectionsCycleCalculator
{
    public static int GetCycle(int ageInDays) => ageInDays switch
    {
        int n when (n >= 1 && n <= 7) => 1,// 1-7 days
        int n when (n >= 13 && n <= 21) => 2,// 13-21 days
        int n when (n >= 22 && n <= 31) => 3,// 22-31 days
        int n when (n >= 32 && n <= 60) => 4,// 1-2 months
        int n when (n > 60 && n <= 90) => 5,// 3 months
        int n when (n > 90 && n <= 180) => 6,// 3-6 months
        int n when (n > 180 && n <= 270) => 7,// 6-9 months
        int n when (n > 270 && n <= 365) => 8,// 9-12 months
        int n when (n > 365 && n <= 450) => 9,// 12-15 months
        int n when (n > 450) => 10,// 15-18 months 540
        _ => 0,// Age out of range
    };
}

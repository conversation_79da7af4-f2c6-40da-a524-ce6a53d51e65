﻿using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.ViewModels.Messaging;

namespace LendQube.Infrastructure.Core.Messaging.Configuration;

internal static class MessageRecipientHelper
{
    public static SinglePreparedMessageVM GetConsolidatedData(this MessageRecipientUserDataVM data, MessageRecipient receiver, IReadOnlyList<string> systemKeys, string logo, IEnumerable<string> systemTags, string[] disallowedDomains)
    {
        if (data == null)
            return null;

        List<PhoneNumber> phoneNumbers = data.PhoneNumbers == null ? [] : [.. data.PhoneNumbers];
        List<string> emails = [.. data.Emails.Where(x => disallowedDomains.All(y => !x.EndsWith(y, StringComparison.OrdinalIgnoreCase)))];
        if (data.PhoneNumber != null) phoneNumbers.Add(data.PhoneNumber);
        if (data.Email != null && disallowedDomains.All(y => !data.Email.EndsWith(y, StringComparison.OrdinalIgnoreCase))) emails.Add(data.Email);

        if (data.FirstName == null && data.FullName != null)
        {
            var names = data.FullName.Split(' ', StringSplitOptions.TrimEntries);
            data.FirstName = names[0];
            data.LastName = names.Length > 1 ? names[1] : "";
        }

        var message = new SinglePreparedMessageVM { Emails = emails, PhoneNumbers = phoneNumbers, Name = data.FullName, UserId = receiver.UserId };

        if (!receiver.TemplateValues.IsNullOrEmpty())
            message.TemplateValues.AddRange(receiver.TemplateValues);

        message.Attachments = receiver.Attachments;
        message.CopiedIn = receiver.CopiedIn;


        var keys = systemKeys.Where(y => message.TemplateValues.Any(x => x.Key == y && string.IsNullOrEmpty(x.Value)) || systemTags.Contains(y));
        foreach (var item in keys)
        {
            var value = string.Empty;
            if (item.Equals(MessageTemplateSystemTags.Name.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                value = data.FullName;
            }
            else if (item.Equals(MessageTemplateSystemTags.FirstName.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                value = data.FirstName;
            }
            else if (item.Equals(MessageTemplateSystemTags.Logo.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                value = logo;
            }

            message.TemplateValues.Add(new TemplateKeyValue(item, value));
        }

        message.TemplateValues = message.TemplateValues.Where(x => !string.IsNullOrEmpty(x.Value)).ToList();
        receiver.TemplateValues = message.TemplateValues;
        return message;
    }

    public static PreviewMessageVM GetPreviewData(this ViewMessageRecipientVM data, MessageLogEntryVM entry, string logo)
    {
        if (data == null)
            return null;

        var message = new PreviewMessageVM { Name = data.Name, Subject = entry.Subject, TemplateValues = data.OriginalTemplateValues ?? [] };

        var keys = EnumExtensions.GetEnumNames<MessageTemplateSystemTags>().Where(y => message.TemplateValues.Any(x => x.Key == y && string.IsNullOrEmpty(x.Value))).ToList();
        if (data.FirstName == null && data.Name != null)
        {
            var names = data.Name.Split(' ', StringSplitOptions.TrimEntries);
            data.FirstName = names[0];
        }

        Parallel.ForEach(keys, (item) =>
        {
            var value = string.Empty;
            if (item.Equals(MessageTemplateSystemTags.Name.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                value = data.Name;
            }
            else if (item.Equals(MessageTemplateSystemTags.FirstName.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                value = data.FirstName;
            }
            else if (item.Equals(MessageTemplateSystemTags.Logo.ToString(), StringComparison.OrdinalIgnoreCase))
            {
                value = logo;
            }

            message.TemplateValues.Add(new TemplateKeyValue(item, value));
        });

        return message;
    }
}

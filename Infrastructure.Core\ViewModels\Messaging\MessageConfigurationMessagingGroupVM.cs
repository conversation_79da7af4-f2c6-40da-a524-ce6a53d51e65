﻿using LendQube.Entities.Core.Messaging;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Infrastructure.Core.ViewModels.Messaging;

public sealed class MessageConfigurationMessagingGroupVM
{
    [Required]
    public List<long> Groups { get; set; } = [];

    public List<MessageConfigurationMessagingGroup> Get(long configId)
    {
        var result = new List<MessageConfigurationMessagingGroup>();
        foreach (var item in Groups)
        {
            result.Add(new MessageConfigurationMessagingGroup { MessageConfigurationId = configId, MessagingGroupId = item });
        }

        return result;
    }
}

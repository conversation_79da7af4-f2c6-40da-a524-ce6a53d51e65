﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Collection.PromiseToPay;

internal static class PromiseToPayService
{
    public static async Task CheckAndApplyToPtp(IUnitofWork uow, Transaction txn, CancellationToken ct)
    {
        if (txn.Status != TransactionStatus.Successful && txn.Status != TransactionStatus.Completed)
            return;

        var validPtps = await uow.Db.ManyAsync(Query<CustomerPromiseToPay>.Where(x => x.ProfileId == txn.ProfileId && x.DueDate >= txn.CreatedDate && !x.Redeemed).OrderBy(x => x.OrderBy(y => y.CreatedDate)).Track(), ct);

        var amountToApply = txn.TotalAmountPaid;

        foreach (var ptp in validPtps)
        {
            var balance = ptp.Amount - ptp.AmountPaid;
            ptp.AmountPaid += balance > amountToApply ? amountToApply : balance;

            ptp.Redeemed = ptp.Amount <= ptp.AmountPaid;


            amountToApply -= balance;

            if (amountToApply <= 0)
                break;
        }
    }
}
using System;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;

namespace LendQube.TestScripts;

/// <summary>
/// Simple payment gateway test that directly calls Acquired API endpoints
/// This bypasses internal LendQube classes to isolate the payment gateway issues
/// </summary>
public class SimplePaymentGatewayTest
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<SimplePaymentGatewayTest> _logger;
    private readonly IConfiguration _configuration;

    public SimplePaymentGatewayTest(HttpClient httpClient, ILogger<SimplePaymentGatewayTest> logger, IConfiguration configuration)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;
    }

    public static async Task Main(string[] args)
    {
        var host = CreateHostBuilder(args).Build();
        var tester = host.Services.GetRequiredService<SimplePaymentGatewayTest>();
        
        Console.WriteLine("=== Simple Payment Gateway Test ===");
        Console.WriteLine("Testing Acquired API endpoints directly");
        Console.WriteLine();

        await tester.RunTests();
        
        Console.WriteLine("\nPress any key to exit...");
        Console.ReadKey();
    }

    public static IHostBuilder CreateHostBuilder(string[] args) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((context, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory())
                      .AddJsonFile("appsettings.json", optional: false)
                      .AddJsonFile($"appsettings.{context.HostingEnvironment.EnvironmentName}.json", optional: true)
                      .AddEnvironmentVariables();
            })
            .ConfigureServices((context, services) =>
            {
                services.AddHttpClient<SimplePaymentGatewayTest>((sp, client) =>
                {
                    // Configure for Acquired API - will be set dynamically from config
                    client.DefaultRequestHeaders.Add("Accept", "application/json");
                    client.Timeout = TimeSpan.FromSeconds(30);
                });

                services.AddLogging(builder =>
                {
                    builder.AddConsole();
                    builder.SetMinimumLevel(LogLevel.Debug);
                });

                services.AddTransient<SimplePaymentGatewayTest>();
            });

    public async Task RunTests()
    {
        var cancellationToken = CancellationToken.None;

        // Get configuration
        var appId = _configuration["Providers:Acquired:AppId"];
        var appKey = _configuration["Providers:Acquired:AppKey"];
        var baseUrl = _configuration["Providers:Acquired:BaseApiUrl"];

        Console.WriteLine($"🔧 Configuration Check:");
        Console.WriteLine($"   Base URL: {baseUrl ?? "NOT SET"}");
        Console.WriteLine($"   App ID: {(string.IsNullOrEmpty(appId) ? "NOT SET" : "SET")}");
        Console.WriteLine($"   App Key: {(string.IsNullOrEmpty(appKey) ? "NOT SET" : "SET")}");
        Console.WriteLine();

        if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appKey) || string.IsNullOrEmpty(baseUrl))
        {
            Console.WriteLine("❌ Missing Acquired configuration. Please check appsettings.json");
            return;
        }

        // Set the base address from configuration
        _httpClient.BaseAddress = new Uri(baseUrl);

        // Test 1: Authentication
        var token = await TestAuthentication(appId, appKey, cancellationToken);
        
        if (string.IsNullOrEmpty(token))
        {
            Console.WriteLine("❌ Authentication failed. Cannot proceed with other tests.");
            return;
        }

        // Test 2: Create Customer
        await TestCreateCustomer(token, cancellationToken);
        
        // Test 3: Generate Payment Link
        await TestGeneratePaymentLink(token, cancellationToken);
        
        // Test 4: Get Transaction
        await TestGetTransaction(token, cancellationToken);
    }

    private async Task<string> TestAuthentication(string appId, string appKey, CancellationToken ct)
    {
        Console.WriteLine("🔍 Testing: Authentication");
        Console.WriteLine("─────────────────────────");

        try
        {
            var loginRequest = new
            {
                app_id = appId,
                app_key = appKey
            };

            var json = JsonSerializer.Serialize(loginRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            Console.WriteLine($"   Request: POST /login");
            Console.WriteLine($"   Payload: {json}");

            var response = await _httpClient.PostAsync("login", content, ct);
            var responseContent = await response.Content.ReadAsStringAsync(ct);

            Console.WriteLine($"   Status: {response.StatusCode}");
            Console.WriteLine($"   Response: {responseContent}");

            if (response.IsSuccessStatusCode)
            {
                var loginResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                
                if (loginResponse.TryGetProperty("access_token", out var accessToken) &&
                    loginResponse.TryGetProperty("token_type", out var tokenType))
                {
                    var token = $"{tokenType.GetString()} {accessToken.GetString()}";
                    Console.WriteLine("✅ Authentication: SUCCESS");
                    Console.WriteLine($"   Token: {token.Substring(0, Math.Min(50, token.Length))}...");
                    return token;
                }
            }

            Console.WriteLine("❌ Authentication: FAILED");
            Console.WriteLine($"   Error: Unable to extract token from response");
            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 Authentication: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
            return null;
        }
        finally
        {
            Console.WriteLine();
        }
    }

    private async Task TestCreateCustomer(string token, CancellationToken ct)
    {
        Console.WriteLine("🔍 Testing: Create Customer");
        Console.WriteLine("─────────────────────────");

        try
        {
            var customerRequest = new
            {
                ip = "***********",
                email = "<EMAIL>",
                first_name = "John",
                last_name = "Doe",
                dob = "1990-01-01",
                phone = new
                {
                    country_code = "44",
                    number = "7123456789"
                },
                billing = new
                {
                    address = new
                    {
                        line1 = "123 Test Street",
                        city = "London",
                        state = "LON", // Fixed: max 3 characters
                        postcode = "SW1A 1AA",
                        country = "GB"
                    }
                }
            };

            var json = JsonSerializer.Serialize(customerRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var request = new HttpRequestMessage(HttpMethod.Post, "customers")
            {
                Content = content
            };
            request.Headers.Add("Authorization", token);

            Console.WriteLine($"   Request: POST /customers");
            Console.WriteLine($"   Payload: {json}");

            var response = await _httpClient.SendAsync(request, ct);
            var responseContent = await response.Content.ReadAsStringAsync(ct);

            Console.WriteLine($"   Status: {response.StatusCode}");
            Console.WriteLine($"   Response: {responseContent}");

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("✅ Create Customer: SUCCESS");
            }
            else
            {
                Console.WriteLine("❌ Create Customer: FAILED");
                await LogDetailedError("Create Customer", response, responseContent);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 Create Customer: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
        }
        finally
        {
            Console.WriteLine();
        }
    }

    private async Task TestGeneratePaymentLink(string token, CancellationToken ct)
    {
        Console.WriteLine("🔍 Testing: Generate Payment Link");
        Console.WriteLine("─────────────────────────────────");

        try
        {
            var paymentLinkRequest = new
            {
                transaction = new
                {
                    order_id = $"TEST_{DateTime.UtcNow:yyyyMMddHHmmss}",
                    amount = 1000, // £10.00 in pence
                    currency = "GBP",
                    description = "Test Payment"
                },
                payment = new
                {
                    return_url = "https://example.com/return",
                    cancel_url = "https://example.com/cancel",
                    reference = DateTime.UtcNow.ToString("yyyyMMddHHmmss")
                },
                customer = new
                {
                    ip = "***********",
                    email = "<EMAIL>",
                    first_name = "John",
                    last_name = "Doe"
                },
                payment_methods = new[] { "card", "pay_by_bank" }, // Added payment methods
                is_recurring = false,
                webhook_url = "https://example.com/webhook"
            };

            var json = JsonSerializer.Serialize(paymentLinkRequest, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.SnakeCaseLower
            });

            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var request = new HttpRequestMessage(HttpMethod.Post, "payment-links")
            {
                Content = content
            };
            request.Headers.Add("Authorization", token);

            Console.WriteLine($"   Request: POST /payment-links");
            Console.WriteLine($"   Payload: {json}");

            var response = await _httpClient.SendAsync(request, ct);
            var responseContent = await response.Content.ReadAsStringAsync(ct);

            Console.WriteLine($"   Status: {response.StatusCode}");
            Console.WriteLine($"   Response: {responseContent}");

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("✅ Generate Payment Link: SUCCESS");
            }
            else
            {
                Console.WriteLine("❌ Generate Payment Link: FAILED");
                await LogDetailedError("Generate Payment Link", response, responseContent);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 Generate Payment Link: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
        }
        finally
        {
            Console.WriteLine();
        }
    }

    private async Task TestGetTransaction(string token, CancellationToken ct)
    {
        Console.WriteLine("🔍 Testing: Get Transaction");
        Console.WriteLine("───────────────────────────");

        try
        {
            var orderId = $"TEST_{DateTime.UtcNow:yyyyMMddHHmmss}";
            
            var request = new HttpRequestMessage(HttpMethod.Get, $"transactions?order_id={orderId}");
            request.Headers.Add("Authorization", token);

            Console.WriteLine($"   Request: GET /transactions?order_id={orderId}");

            var response = await _httpClient.SendAsync(request, ct);
            var responseContent = await response.Content.ReadAsStringAsync(ct);

            Console.WriteLine($"   Status: {response.StatusCode}");
            Console.WriteLine($"   Response: {responseContent}");

            if (response.IsSuccessStatusCode)
            {
                Console.WriteLine("✅ Get Transaction: SUCCESS");
            }
            else
            {
                Console.WriteLine("❌ Get Transaction: FAILED");
                await LogDetailedError("Get Transaction", response, responseContent);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine("💥 Get Transaction: EXCEPTION");
            Console.WriteLine($"   Exception: {ex.GetType().Name}");
            Console.WriteLine($"   Message: {ex.Message}");
        }
        finally
        {
            Console.WriteLine();
        }
    }

    private async Task LogDetailedError(string operation, HttpResponseMessage response, string responseContent)
    {
        Console.WriteLine($"   🔍 Detailed Error Analysis for {operation}:");
        Console.WriteLine($"      Status Code: {(int)response.StatusCode} {response.StatusCode}");
        Console.WriteLine($"      Reason Phrase: {response.ReasonPhrase}");
        
        // Log response headers
        Console.WriteLine($"      Response Headers:");
        foreach (var header in response.Headers)
        {
            Console.WriteLine($"        {header.Key}: {string.Join(", ", header.Value)}");
        }

        // Try to parse error response
        try
        {
            if (!string.IsNullOrEmpty(responseContent))
            {
                var errorResponse = JsonSerializer.Deserialize<JsonElement>(responseContent);
                Console.WriteLine($"      Parsed Error Response:");
                Console.WriteLine($"        {JsonSerializer.Serialize(errorResponse, new JsonSerializerOptions { WriteIndented = true })}");
            }
        }
        catch
        {
            Console.WriteLine($"      Raw Error Response: {responseContent}");
        }

        // Log to ILogger as well
        _logger.LogError("Payment Gateway Error - {Operation}: {StatusCode} {ReasonPhrase} - {ResponseContent}", 
            operation, response.StatusCode, response.ReasonPhrase, responseContent);
    }
}
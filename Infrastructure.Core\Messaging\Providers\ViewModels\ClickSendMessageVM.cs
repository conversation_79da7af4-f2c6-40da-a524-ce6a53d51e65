﻿using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.Core.Messaging.Providers.ViewModels;

internal sealed class ClickSendMessageVM
{
    [JsonPropertyName("to")]
    public string To { get; set; }

    [JsonPropertyName("from")]
    public string From { get; set; }

    [JsonPropertyName("source")]
    public string Source { get; set; }

    [JsonPropertyName("body")]
    public string Body { get; set; }
}

internal sealed class ClickSendMesageRequestVM
{
    [JsonPropertyName("messages")]
    public List<ClickSendMessageVM> Messages { get; set; }
}



internal sealed class CLickSendResponseDataVM
{
    [JsonPropertyName("total_price")]
    public decimal TotalPrice { get; set; }

    [JsonPropertyName("total_count")]
    public int TotalCount { get; set; }

    [JsonPropertyName("queued_count")]
    public int QueuedCount { get; set; }

    [JsonPropertyName("messages")]
    public List<ClickSendResponseMessageVM> Messages { get; set; }
}

internal sealed class ClickSendResponseMessageVM
{
    [JsonPropertyName("to")]
    public string To { get; set; }

    [JsonPropertyName("body")]
    public string Body { get; set; }

    [JsonPropertyName("from")]
    public string From { get; set; }

    [JsonPropertyName("message_id")]
    public string MessageId { get; set; }

    [JsonPropertyName("message_parts")]
    public int MessageParts { get; set; }

    [JsonPropertyName("message_price")]
    public string MessagePrice { get; set; }

    [JsonPropertyName("custom_string")]
    public string CustomString { get; set; }

    [JsonPropertyName("country")]
    public string Country { get; set; }

    [JsonPropertyName("carrier")]
    public string Carrier { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }
}

internal sealed class ClickSendMessageResponseVM
{
    [JsonPropertyName("http_code")]
    public int HttpCode { get; set; }

    [JsonPropertyName("response_code")]
    public string ResponseCode { get; set; }

    [JsonPropertyName("response_msg")]
    public string ResponseMsg { get; set; }

    [JsonPropertyName("data")]
    public CLickSendResponseDataVM Data { get; set; }
}


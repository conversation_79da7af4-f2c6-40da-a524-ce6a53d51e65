@page "/make-payment"
@using LendQube.Infrastructure.Core.Database.DbContexts
@using LendQube.Entities.Collection.Customers
@using LendQube.Entities.Collection.Placements
@using LendQube.Infrastructure.Collection.Schedules
@using LendQube.Infrastructure.Collection.Payments
@using LendQube.Infrastructure.Collection.ViewModels.PlacementData
@using LendQube.Infrastructure.Core.ViewModels.Base
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@using LendQube.Entities.Core.Extensions
@inject AppDbContext DbContext
@inject ILogger<MakePayment> Logger
@inject ManageScheduleService ScheduleService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>Make a Payment - LendQube Customer Portal</PageTitle>

@if (customer == null)
{
    <div class="loading-container">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p>Loading your payment information...</p>
    </div>
}
else
{
    <div class="payment-container">
        <div class="payment-header">
            <h1>Make a Payment</h1>
            <p>Customer: @customer.FirstName @customer.LastName (@customer.AccountId)</p>
        </div>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger">
                @errorMessage
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success">
                @successMessage
            </div>
        }

        <div class="payment-form-container">
            <EditForm Model="@paymentModel" OnValidSubmit="@InitiatePayment" FormName="MakePaymentForm">
                <DataAnnotationsValidator />

                <div class="form-section">
                    <h3>Payment Details</h3>
                    
                    <div class="form-group">
                        <label for="amount">Payment Amount (£)</label>
                        <InputNumber @bind-value="paymentModel.Amount"
                                   class="form-control" 
                                   id="amount" 
                                   placeholder="0.00"
                                   step="0.01"
                                   disabled="@isProcessing" />
                        <ValidationMessage For="@(() => paymentModel.Amount)" />
                    </div>



                    <div class="form-group">
                        <p><strong>Note:</strong> Payment will be processed using your default payment method on file.</p>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary" disabled="@isProcessing">
                        @if (isProcessing)
                        {
                            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                            <span>Processing Payment...</span>
                        }
                        else
                        {
                            <span>Initiate Payment</span>
                        }
                    </button>



                    <a href="/dashboard?customerId=@CustomerId" class="btn btn-secondary">
                        <span>← Back to Dashboard</span>
                    </a>
                </div>
            </EditForm>
        </div>

        <div class="payment-info">
            <h3>Payment Information</h3>
            <p>This is a one-time payment that will be applied to reduce your outstanding balance.</p>
            <p>Current Balance: £@(customer?.BalanceRemaining.ToString("N2") ?? "0.00")</p>


        </div>
    </div>
}



@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? CustomerId { get; set; }

    [SupplyParameterFromForm]
    public PaymentModel paymentModel { get; set; } = new();

    private CustomerProfile? customer;
    private bool isProcessing = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool hasUnpaidSchedules = false;

    public class PaymentModel
    {
        [Required(ErrorMessage = "Payment amount is required")]
        [Range(0.01, 999999.99, ErrorMessage = "Payment amount must be between £0.01 and £999,999.99")]
        public decimal Amount { get; set; }

        public RepaymentTransactionType Type { get; set; } = RepaymentTransactionType.ReduceBalance;
    }

    protected override async Task OnInitializedAsync()
    {
        try
        {
            Logger.LogInformation("MakePayment page initializing with CustomerId: {CustomerId}", CustomerId);

            if (!string.IsNullOrEmpty(CustomerId))
            {
                await LoadCustomerData();
            }
            else
            {
                Logger.LogWarning("MakePayment page accessed without CustomerId parameter");
                errorMessage = "Customer ID is required. Please log in again.";
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error initializing MakePayment page");
            errorMessage = "Error loading payment page. Please try again.";
        }
    }

    private async Task LoadCustomerData()
    {
        try
        {
            Logger.LogInformation("Loading customer payment data for ID: {CustomerId}", CustomerId);

            // Load customer with schedules
            customer = await DbContext.Set<CustomerProfile>()
                .Include(c => c.Schedules)
                .Where(c => c.AccountId == CustomerId)
                .FirstOrDefaultAsync();

            if (customer != null)
            {
                hasUnpaidSchedules = customer.Schedules?.Any(x => x.PaymentStatus != SchedulePaymentStatus.Paid) == true;
                Logger.LogInformation("Customer payment data loaded successfully for: {CustomerId}, HasUnpaidSchedules: {HasUnpaidSchedules}", CustomerId, hasUnpaidSchedules);
            }
            else
            {
                errorMessage = "Customer not found. Please log in again.";
                Logger.LogWarning("Customer not found for ID: {CustomerId}", CustomerId);
            }
        }
        catch (Exception ex)
        {
            errorMessage = "Error loading payment information. Please try again.";
            Logger.LogError(ex, "Error loading customer payment data for ID: {CustomerId}", CustomerId);
        }
    }

    private async Task InitiatePayment()
    {
        try
        {
            isProcessing = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            StateHasChanged();

            Logger.LogInformation("Initiating payment for Customer: {CustomerId}, Amount: {Amount}",
                CustomerId, paymentModel.Amount);

            // Use the already determined hasUnpaidSchedules flag

            // Create the payment request using the appropriate payment type
            var paymentRequest = new InitiateTransactionRequest
            {
                Amount = paymentModel.Amount,
                Type = hasUnpaidSchedules ? RepaymentTransactionType.ReduceBalance : RepaymentTransactionType.RecalculateRepayment
            };

            Logger.LogInformation("Payment type determined for Customer: {CustomerId}, HasUnpaidSchedules: {HasUnpaidSchedules}, PaymentType: {PaymentType}",
                CustomerId, hasUnpaidSchedules, paymentRequest.Type);

            // Call the payment service using reflection to access the internal method
            var result = await CallInitiatePaymentMethod(customer!.Id.ToString(), paymentRequest);

            if (result.IsSuccessful)
            {
                Logger.LogInformation("Payment initiated successfully for Customer: {CustomerId}", CustomerId);

                // Extract payment link from the transaction result
                var paymentLink = result.Data?.Txn?.ProviderId;

                if (!string.IsNullOrEmpty(paymentLink))
                {
                    Logger.LogInformation("Redirecting customer {CustomerId} to payment link: {PaymentLink}", CustomerId, paymentLink);

                    // Store transaction ID for later confirmation
                    var transactionId = result.Data?.Txn?.TxnId;

                    // Append return URL to payment link if it doesn't already contain one
                    var returnUrl = "http://localhost:5000";
                    var finalPaymentLink = paymentLink;

                    if (!paymentLink.Contains("redirect_url") && !paymentLink.Contains("return_url"))
                    {
                        var separator = paymentLink.Contains("?") ? "&" : "?";
                        finalPaymentLink = $"{paymentLink}{separator}redirect_url={Uri.EscapeDataString(returnUrl)}";
                    }

                    Logger.LogInformation("Final payment link with return URL: {FinalPaymentLink}", finalPaymentLink);

                    // Redirect to external payment provider (Acquired) with return URL
                    Navigation.NavigateTo(finalPaymentLink, forceLoad: true);
                }
                else
                {
                    successMessage = $"Payment of £{paymentModel.Amount:N2} has been initiated successfully!";

                    // Reset form and redirect to dashboard
                    paymentModel = new PaymentModel();
                    await Task.Delay(2000);
                    Navigation.NavigateTo($"/dashboard?customerId={CustomerId}", replace: true);
                }
            }
            else
            {
                errorMessage = result.Message ?? "Payment initiation failed. Please try again.";
                Logger.LogWarning("Payment initiation failed for Customer: {CustomerId}. Error: {Error}", CustomerId, result.Message);
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"Payment initiation failed: {ex.Message}";
            Logger.LogError(ex, "Error initiating payment for Customer: {CustomerId}", CustomerId);
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
        }
    }

    private async Task<Result<ScheduleResponseVM>> CallInitiatePaymentMethod(string customerId, InitiateTransactionRequest request)
    {
        try
        {
            Logger.LogInformation("Customer Portal: Initiating payment for customer: {CustomerId}, Amount: {Amount}",
                customerId, request.Amount);

            // Use reflection to call the internal InitiatePayment method
            var method = typeof(ManageScheduleService).GetMethod("InitiatePayment",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method != null)
            {
                var task = (Task<Result<ScheduleResponseVM>>)method.Invoke(ScheduleService, new object[] { customerId, request, CancellationToken.None })!;
                var result = await task;

                Logger.LogInformation("Payment initiation result for customer {CustomerId}: {IsSuccessful}",
                    customerId, result.IsSuccessful);

                return result;
            }

            Logger.LogError("InitiatePayment method not found on ManageScheduleService");
            return Result<ScheduleResponseVM>.Failed("Payment service not available");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error initiating payment for customer: {CustomerId}", customerId);
            return Result<ScheduleResponseVM>.Failed($"Payment initiation failed: {ex.Message}");
        }
    }



}

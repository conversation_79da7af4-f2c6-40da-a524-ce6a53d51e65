﻿using System.Text.Json;
using System.Text.Json.Serialization;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.SerializersAndConverters;

public sealed class NullableDateTimeConverter : JsonConverter<DateTime?>
{
    public override DateTime? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        if (string.IsNullOrEmpty(value)) return null;
        return value.ParseForOs("dd MMM, yyyy hh:mm:ss tt");
    }

    public override void Write(Utf8JsonWriter writer, DateTime? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.HasValue ? value.Value.ToString("dd MMM, yyyy hh:mm:ss tt") : "");
    }
}


public sealed class DateOnlyConverter : JsonConverter<DateOnly?>
{
    public override DateOnly? Read(ref Utf8JsonReader reader, Type typeToConvert, JsonSerializerOptions options)
    {
        var value = reader.GetString();
        if (string.IsNullOrEmpty(value)) return null;
        var date = value.ParseForOs("dd MMM, yyyy hh:mm:ss tt");
        return DateOnly.FromDateTime(date);
    }

    public override void Write(Utf8JsonWriter writer, DateOnly? value, JsonSerializerOptions options)
    {
        writer.WriteStringValue(value.HasValue ? value.Value.ToString("dd MMM, yyyy") : "");
    }
}

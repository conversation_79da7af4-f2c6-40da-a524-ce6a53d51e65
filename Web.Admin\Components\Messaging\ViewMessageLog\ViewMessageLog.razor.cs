﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageLog;
public partial class ViewMessageLog
{
    [Inject] IJSRuntime JSRuntime { get; set; }
    [Inject] HttpClient HttpClient { get; set; }
    [Inject] GeneralGenericCrudVMService Service { get; set; }
    [Inject] MessagingFactory Factory { get; set; }
    [Inject] DefaultAppConfig Config { get; set; }

    [Parameter]
    public long LogId { get; set; }
    private string Title { get; set; } = "View Message Log";
    private MessageLogVM Data { get; set; } = new();
    private StatusMessage message, modalMessage;
    private IUnitofWork uow;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadData();
            StateHasChanged();
            await JSRuntime.RunFeather(Cancel);
        }
    }

    private async Task LoadData()
    {
        Data = await uow.Db.OneSelectAsync(Query<MessageLog, MessageLogVM>.Select(MessageLogVM.Mapping).Where(x => x.Id == LogId), Cancel);
        if (Data != null)
            await entryTable.LoadElement();

        SetupEntries();
        SetupRecipients();
    }

    protected override void OnInitialized()
    {
        uow = Service.Uow;
    }
}

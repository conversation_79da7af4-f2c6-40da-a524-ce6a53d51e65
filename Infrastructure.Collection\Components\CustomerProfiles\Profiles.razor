﻿@page "/customers/profile"

@using LendQube.Entities.Collection.Customers
@using LendQube.Entities.Core.BaseUser
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Infrastructure.Core.AppSettings
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inject NavigationManager navigationManager
@inject UserAccessService accessService
@inject DefaultAppConfig config
@inherits GenericCrudTable<CustomerProfile>

@attribute [Authorize(Policy = ManageCustomersNavigation.CustomerProfileIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalComponent Policy="@ManageCustomersNavigation.CustomerProfileIndexPermission" ModalId="@AccessLogModalName"
                ModalCss=" modal-dialog-large width-70vw" Title=@($"View Access Logs For User: {SelectedUser?.FullName}")>
    <BodyContent>
        <DataTable T="UserAccessLogVM" TableDefinition="accessService.GetTableDefinition()" LoadData="LoadAccessLogs" DeferLoading="true" @ref="accessLogTable" />
    </BodyContent>

</ModalComponent>

@code
{
    private DataTable<UserAccessLogVM> accessLogTable;
    private CustomerProfile SelectedUser { get; set; }

    private string AccessLogModalName => "AccessLogModal";

    protected override void OnInitialized()
    {
        Title = "Customers";
        FormBaseTitle = "Customer Profile";
        SubTitle = "Manage Customer Profiles";
        DeletePermission = ManageCustomersNavigation.CustomerProfileDeletePermission;
    }

    protected override async Task OnInitializedAsync()
    {
        if (TableDefinition == null)
        {
            await base.OnInitializedAsync();

            AddRowButton(ManageCustomersNavigation.CustomerProfileViewPermission, new RowActionButton("View", Icon: "eye", Action: (object row) =>
            {
                var profile = row as CustomerProfile;
                navigationManager.NavigateTo($"customers/profile/{profile.Id}");
                return Task.CompletedTask;
            }));

            AddRowButton(ManageCustomersNavigation.CustomerProfileIndexPermission, new RowActionButton("Access Logs", Action: async (object row) =>
            {
                CloseMessage();
                table.Loading = true;
                SelectedUser = row as CustomerProfile;
                await accessLogTable.LoadElement();
                table.Loading = false;

                StateHasChanged();
                await JSRuntime.OpenModal(AccessLogModalName, Cancel);
            }));

        }
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.AccountId, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.FirstName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.LastName, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Email, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.PhoneNumber.Number, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.PhoneNumber.Code + " " + x.PhoneNumber.Number, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.MobileNumber.Number, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.MobileNumber.Code + " " + x.MobileNumber.Number, filterAndPage.TextFilter)
            || x.CustomerContacts.Any(y => EF.Functions.ILike(y.PhoneNumber.Number, filterAndPage.TextFilter))
            || x.CustomerContacts.Any(y => EF.Functions.ILike(y.PhoneNumber.Code + " " + y.PhoneNumber.Number, filterAndPage.TextFilter))
            || x.CustomerContacts.Any(y => EF.Functions.ILike(y.Email, filterAndPage.TextFilter))
            || x.Flags.Any(y => EF.Functions.ILike(y.Flag.Flag, filterAndPage.TextFilter));
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition(new() { HasId = false });

    protected override ValueTask<bool> SubmitDelete(CustomerProfile data, Func<Task> refresh, CancellationToken ct) => SaveDelete(async () =>
     {
         await Service.CrudService.Db.DeleteAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == data.Id, ct);
         await Service.CrudService.Db.DeleteAndSaveWithFilterAsync<Transaction>(x => x.ProfileId == data.Id && (config.DeployState.IsDemo || x.Status >= TransactionStatus.Successful), ct);
         return await Service.CrudService.Delete(x => x.Id == data.Id, ct);
     }, refresh);


    #region Manage Access Logs

    private ValueTask<TypedBasePageList<UserAccessLogVM>> LoadAccessLogs(DataFilterAndPage filterAndPage, CancellationToken ct) => accessService.GetTypeBasedPagedData(SelectedUser.Id, filterAndPage, ct);


    #endregion
}

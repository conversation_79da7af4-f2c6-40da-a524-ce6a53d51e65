﻿using Coravel.Invocable;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Logs;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Schedules.BackgroundServices;

internal sealed class PlacementPendingReminderService(IUnitofWork uow, IClock clock) : IInvocable, ICancellableInvocable
{
    public CancellationToken CancellationToken { get; set; }

    public async Task Invoke()
    {
        var users = await uow.Db.ManySelectAsync(Query<CustomerProfile, string>
            .Where(x => (!string.IsNullOrEmpty(x.Email) || x.PhoneNumber != null)
            && x.BalanceRemaining > 0 && x.Placements.Any(y => y.Status < PlacementStatus.Settled) && !x.OneTimeCodes.Any() && !uow.Db.Queryable<UserAccessLog>().Any(y => y.CreatedByUserId == x.Id)
            && !x.Holds.Any(y => !y.Disabled && y.Action.HasFlag(HoldAction.DisableSystemMessages) && (!y.ExpiresOn.HasValue || clock.GetCurrentInstant() < y.ExpiresOn)))
            .Select(x => x.Id), CancellationToken);

        if (users.IsNullOrEmpty())
            return;

        _ = await MessageBuilder.New("Download App Reminder", null)
                  .Message(MessageConfigNames.DownloadAppReminder.GetDisplayName())
                  .WithRecipients(users)
                  .Send(uow, CancellationToken);

    }
}

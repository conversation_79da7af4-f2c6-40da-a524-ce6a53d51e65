﻿using Coravel.Invocable;
using LendQube.Entities.Collection.Setup;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Telemetry;

namespace LendQube.Infrastructure.Collection.Discounts;

public sealed class AutoDiscountBackgroundService(IUnitofWork uow, ILogManager<AutoDiscountBackgroundService> logger) : IInvocable
{
    public async Task Invoke()
    {
        var discounts = await uow.Db.ManyAsync(Query<AutoDiscountConfig>.Where(x => x.AutoApplyDiscount), default);

        foreach (var discount in discounts)
        {
            await AutoDiscountService.ApplyDiscount(uow, logger, discount, default);
        }
    }
}

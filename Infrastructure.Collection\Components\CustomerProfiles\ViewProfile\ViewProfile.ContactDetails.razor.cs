﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.BaseUser;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<CustomerContactDetail> contactsTable;
    private ColumnList contactDetailsTableDefinition;
    private string AddContactModal => "AddContactModal";

    private CustomerContactDetail AddContactModel { get; set; } = new();

    private void SetupContact()
    {
        contactDetailsTableDefinition = CrudService.GetTableDefinition<CustomerContactDetail>(new()
        {
            ShowUserInfo = true,
            HasDelete = HasClaim(ManageCustomersNavigation.CustomerProfileViewDeleteContactPermission)
        });

        contactDetailsTableDefinition.TopActionButtons.Add(new TopActionButton("Add Contact", ModalName: AddContactModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddContactPermission)));

        contactDetailsTableDefinition.RowActionButtons.Add(new RowActionButton("Make Preferred", Icon: "thumbs-up", Action: async (object row) =>
        {
            TableMessage.Close();
            notesTable.Loading = true;
            await MakeContactPreferred(row as CustomerContactDetail);
            notesTable.Loading = false;
            TableMessage.Success(CustomMessage);
        }, ShowCondition: (row) => !(row as CustomerContactDetail).Preferred && HasClaim(ManageCustomersNavigation.CustomerProfileViewAddContactPermission)));


        contactDetailsTableDefinition.RowActionButtons.Add(new RowActionButton("Set As Login", Icon: "key", Action: async (object row) =>
        {
            TableMessage.Close();
            notesTable.Loading = true;
            await UpdateContactLogin(row as CustomerContactDetail);
            notesTable.Loading = false;
            TableMessage.Success(CustomMessage);
        }, ShowCondition: (_) => HasClaim(ManageCustomersNavigation.CustomerProfileViewUpdateContactLoginPermission)));

        contactsTable.SetTableDefinition(contactDetailsTableDefinition);
    }


    private ValueTask<TypedBasePageList<CustomerContactDetail>> LoadContacts(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerContactDetail>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Email, filterAndPage.TextFilter));
        }

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, ct);
    }


    private ValueTask SubmitNewContact() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddContactPermission, AddContactModal, async () =>
    {
        AddContactModel.ProfileId = Data.Id;
        AddContactModel.PhoneNumber = !string.IsNullOrEmpty(AddContactModel.PhoneCode) && !string.IsNullOrEmpty(AddContactModel.Number)
        ? new Entities.Core.Constants.PhoneNumber(AddContactModel.PhoneCode, AddContactModel.Number.CleanPhoneNumber(AddContactModel.PhoneCode)) : null;

        uow.Db.Insert(AddContactModel);
        await uow.SaveAsync(Cancel);

        if(AddContactModel.Preferred)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerContactDetail>(x => x.ProfileId == ProfileId && x.Id != AddContactModel.Id && x.Preferred, x => x.SetProperty(y => y.Preferred, false), Cancel);
            CustomMessage = "Contact set as preferred";
        }
        return true;
    }, () =>
    {
        AddContactModel = new();
        StateHasChanged();
        return contactsTable.Refresh();
    });

    private ValueTask<bool> DeleteContact(CustomerContactDetail data, Func<Task> refresh, CancellationToken ct) => SaveDelete(ManageCustomersNavigation.CustomerProfileViewDeleteContactPermission, async () =>
    {
        var result = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerContactDetail>(x => x.Id == data.Id, ct);
        return result > 0;
    }, refresh);

    private async Task MakeContactPreferred(CustomerContactDetail data)
    {
        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerContactDetail>(x => x.ProfileId == ProfileId && x.Id != data.Id && x.Preferred, x => x.SetProperty(y => y.Preferred, false), Cancel);
        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerContactDetail>(x => x.ProfileId == ProfileId && x.Id == data.Id, x => x.SetProperty(y => y.Preferred, true), Cancel);

        data.Preferred = true;
        CustomMessage = "Contact set as preferred";
        StateHasChanged();
    }


    private async Task UpdateContactLogin(CustomerContactDetail data)
    {
        if(string.IsNullOrEmpty(data.Email) && !data.PhoneNumber.IsValid())
        {
            CustomMessage = "This contact cannot be used as login details";
            return;
        }

        var customerProfile = await uow.Db.OneAsync(Query<CustomerProfile>.Where(x => x.Id == ProfileId).Track(), Cancel);
        var hasPhoneNumber = customerProfile.PhoneNumber != null;

        if (!await uow.Db.ExistsAsync<CustomerContactDetail>(x => x.ProfileId == ProfileId && (x.Email == customerProfile.Email ||
        (x.PhoneNumber != null && hasPhoneNumber && (x.PhoneNumber.Code == customerProfile.PhoneNumber.Code && x.PhoneNumber.Number == customerProfile.PhoneNumber.Number))), Cancel))
        {
            uow.Db.Insert(new CustomerContactDetail
            {
                ProfileId = ProfileId,
                Email = customerProfile.Email,
                PhoneNumber = customerProfile.PhoneNumber,
            });
        }

        customerProfile.Email = string.IsNullOrEmpty(data.Email) ? customerProfile.Email : data.Email;
        customerProfile.PhoneNumber = data.PhoneNumber ?? customerProfile.PhoneNumber;
        await uow.SaveAsync(Cancel);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<ApplicationUser>(x => x.UserName == ProfileId, x =>
        x.SetProperty(y => y.Email, y => string.IsNullOrEmpty(data.Email) ? y.Email : data.Email)
        .SetProperty(y => y.NormalizedEmail, y => string.IsNullOrEmpty(data.Email) ? y.NormalizedEmail : data.Email.ToUpperInvariant())
        .SetProperty(y => y.PhoneCode, y => data.PhoneNumber == null ? y.PhoneCode : data.PhoneNumber.Code)
        .SetProperty(y => y.PhoneNumber, y => data.PhoneNumber == null ? y.PhoneNumber : data.PhoneNumber.Number)
        , Cancel);

        CustomMessage = "Contact set as login details";
    }
}

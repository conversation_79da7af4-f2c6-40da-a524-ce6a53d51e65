﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Setup;

public class DiscountConfig : BaseEntityWithIdentityId<DiscountConfig>
{
    [Required]
    public Guid RoleId { get; set; }
    public virtual ApplicationRole Role { get; set; }
    [Required, Range(0, 100)]
    public decimal PercentageLimit { get; set; }
    public bool CanOverride { get; set; }

    public override void Configure(EntityTypeBuilder<DiscountConfig> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.RoleId).IsUnique();
    }
}

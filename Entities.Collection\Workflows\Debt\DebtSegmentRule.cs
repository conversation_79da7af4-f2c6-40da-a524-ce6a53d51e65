﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Workflows.Debt;

public class DebtSegmentRule : BaseEntityWithIdentityId<DebtSegmentRule>
{
    [Required, ValidString(ValidStringRule.OnlyTextOrNumberOrSpecialCharactersWithSpacing), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Name { get; set; }
    [Required, ValidString(ValidStringRule.NoScriptTag), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Instruction { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public SegmentType Type { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public int? MinTypeNumber { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public int? MaxTypeNumber { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public SegmentStatusType? StatusType { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long? FlagId { get; set; }
    public virtual CustomerFlagTemplate? Flag { get; set; }
    public bool Disabled { get; set; }


    public override void Configure(EntityTypeBuilder<DebtSegmentRule> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.Name).IsUnique();
    }
}

public enum SegmentType
{
    Due,
    DueIn,
    PastDue,
    Flag,
    Status,
    PTPDueIn,
    PTPDue,
    PTPPastDue,
    CallBack
}

public enum SegmentStatusType
{
    New,
    NoPayment,
    NoContact,
    NoLogin,
    PaymentWithNoSchedule,
    PaymentFailed,
    ScheduleWithNoPaymentMethod
}
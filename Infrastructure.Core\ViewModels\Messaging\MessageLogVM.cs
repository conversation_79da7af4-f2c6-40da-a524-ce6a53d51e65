﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.ViewModels.Messaging;

public sealed class MessageLogVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<MessageLog, MessageLogVM>> Mapping = data => new MessageLogVM
    {
        Id = data.Id,
        OriginatedFrom = data.OriginatedFrom,
        Status = data.Status,
        Configs = data.MessageLogEntries.Select(x => x.Name),
        TotalEntries = data.MessageLogEntries.Count,
        TotalRecipients = data.MessageLogEntries.Sum(y => y.Recipients.Count),
        AttemptCount = data.AttemptCount,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public static readonly Expression<Func<MessageLog, MessageLogVM>> SelectorMapping = data => new MessageLogVM
    {
        Id = data.Id,
        OriginatedFrom = data.OriginatedFrom,
        Status = data.Status,
        Configs = data.MessageLogEntries.Select(x => x.Name),
        ConfigSubjects = data.MessageLogEntries.Select(x => x.Subject),
        TotalEntries = data.MessageLogEntries.Count,
        TotalRecipients = data.MessageLogEntries.Sum(y => y.Recipients.Count),
        AttemptCount = data.AttemptCount,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string OriginatedFrom { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.SkipFilter)]
    public string Entries => string.Join(", ", Configs);
    [TableDecorator(TableDecoratorType.HideColumn)]
    public IEnumerable<string> Configs { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public MessageStatus Status { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public int TotalEntries { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.NoSort)]
    public int TotalRecipients { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public int AttemptCount { get; set; }

    [TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.SkipFilter)]
    public string Subjects => string.Join(", ", ConfigSubjects.Distinct());

    [TableDecorator(TableDecoratorType.HideColumn)]
    public IEnumerable<string> ConfigSubjects { get; set; } = [];
}

public class MessageLogEntryVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<MessageLogEntry, MessageLogEntryVM>> Mapping = data => new MessageLogEntryVM
    {
        Id = data.Id,
        Name = data.Name,
        Subject = data.Subject,
        Channels = data.Channels,
        Keys = data.Keys,
        CopiedIn = data.CopiedIn,
        TotalRecipients = data.Recipients.Count,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public static readonly Expression<Func<MessageLogEntry, MessageLogEntryVM>> ViewMapping = data => new MessageLogEntryVM
    {
        Id = data.Id,
        NeedsConfiguration = data.Config.NeedsConfiguration,
        Name = data.Name,
        Subject = data.Subject,
        Channels = data.Channels,
        TextRequired = data.TextRequired,
        EmailRequired = data.EmailRequired,
        TextTemplate = data.TextTemplate,
        HtmlTemplate = data.HtmlTemplate,
        BodyTextTemplate = data.BodyTextTemplate,
        BodyHtmlTemplate = data.BodyHtmlTemplate,
        ContainerTextTemplate = data.ContainerTextTemplate,
        ContainerHtmlTemplate = data.ContainerHtmlTemplate,
        Keys = data.Keys,
        TotalRecipients = data.Recipients.Count,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public bool NeedsConfiguration { get; set; }
    public string Name { get; set; }
    public string Subject { get; set; }
    public MessageChannel Channels { get; set; }

    [TableDecorator(TableDecoratorType.HideColumn)]
    public bool TextRequired { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public bool EmailRequired { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string TextTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string HtmlTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string BodyTextTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string BodyHtmlTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string ContainerTextTemplate { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string ContainerHtmlTemplate { get; set; }

    [TableDecorator(TableDecoratorType.HideColumn)]
    public List<string> Keys { get; set; }
    [DisplayName("Keys")]
    public string KeysFormatted => Keys.IsNullOrEmpty() ? string.Empty : string.Join(", ", Keys);
    public int TotalRecipients { get; set; }
    [DisplayName("Copied In")]
    public string CopiedInFormatted => CopiedIn.IsNullOrEmpty() ? string.Empty : string.Join(", ", CopiedIn.Select(x => $"{x.Name}: {x.Email} {(x.BlindCopy ? "(Bcc)" : "")}"));
    [RemoveColumn]
    public List<MessageCopiedIn> CopiedIn { get; set; }
}

internal sealed class MessageRecipientVM
{
    public static readonly Func<IRelationalDbRepository, Expression<Func<MessageRecipient, MessageRecipientVM>>> SelectorMapping = (IRelationalDbRepository db) => data => new()
    {
        UserData = db.Queryable<ApplicationUser>().Where(x => x.UserName == data.UserId)
                  .Select(ViewMessageRecipientUserDataVM.Mapping).FirstOrDefault(),
        GroupData = db.Queryable<MessagingGroup>().Where(x => x.Id == data.MessagingGroupId)
                  .Select(ViewMessagingGroupVM.Mapping).FirstOrDefault(),
        AdHoc = data.AdHoc,
        TemplateValues = data.TemplateValues,
        Attachments = data.Attachments,
        CopiedIn = data.CopiedIn,
    };


    public ViewMessageRecipientUserDataVM UserData { get; set; }
    public ViewMessagingGroupVM GroupData { get; set; }
    public AdHocRecipient? AdHoc { get; set; }
    public List<TemplateKeyValue> TemplateValues { get; set; }
    public List<MessageAttachment> Attachments { get; set; }
    public List<MessageCopiedIn> CopiedIn { get; set; }
}

internal sealed class ViewMessageRecipientVM
{
    public static readonly Func<MessageRecipientVM, ViewMessageRecipientVM> Mapping = item => new()
    {
        IsUser = item.UserData != null || item.AdHoc != null,
        IsGroup = item.GroupData != null,
        FirstName = item.UserData?.FirstName,
        Name = item.UserData != null || item.AdHoc != null ? item.UserData?.FullName ?? item.AdHoc?.Name : item.GroupData != null ?
        $"Group - {item.GroupData?.Name}: {(item.GroupData?.HasBuilder ?? false ? "Query Builder, " : "")}Directory: {item.GroupData?.Total}" : string.Empty,

        Email = item.UserData?.Email ?? item.AdHoc?.Email,
        PhoneNumber = item.UserData?.PhoneNumber ?? item.AdHoc?.PhoneNumber.ToString(),
        TemplateValues = item.TemplateValues.IsNullOrEmpty() ? "" : string.Join("<br> ", item.TemplateValues.Select(x => $"{x.Key}: {x.Value}")),
        CopiedIn = item.CopiedIn.IsNullOrEmpty() ? "" : string.Join("<br> ", item.CopiedIn.Select(x => $"{x.Name}: {x.Email} {(x.BlindCopy ? "(Bcc)" : "")}")),
        Attachments = item.Attachments.IsNullOrEmpty() ? "" : string.Join("<br> ", item.Attachments),
        OriginalTemplateValues = item.TemplateValues
    };


    public string Name { get; set; }
    public string Email { get; set; }
    public string PhoneNumber { get; set; }
    public string TemplateValues { get; set; }
    public string CopiedIn { get; set; }
    public string Attachments { get; set; }

    [TableDecorator(TableDecoratorType.HideColumn)]
    public string FirstName { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public bool IsUser { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public bool IsGroup { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public List<TemplateKeyValue> OriginalTemplateValues { get; set; }
}

internal sealed class ViewMessagingGroupVM
{
    public static readonly Expression<Func<MessagingGroup, ViewMessagingGroupVM>> Mapping = data =>
    new()
    {
        Id = data.Id,
        Name = data.Name,
        Total = data.Directory.Count,
        HasBuilder = data.QueryDirectory != null
    };
    public long Id { get; set; }
    public string Name { get; set; }
    public int Total { get; set; }
    public bool HasBuilder { get; set; }
}

internal sealed class ViewMessageRecipientUserDataVM
{
    public static readonly Expression<Func<ApplicationUser, ViewMessageRecipientUserDataVM>> Mapping = data =>
    new()
    {
        FirstName = data.FirstName,
        FullName = data.FullName,
        PhoneNumber = data.PhoneCode + " " + data.PhoneNumber,
        Email = data.Email,
    };

    public string FirstName { get; set; }
    public string FullName { get; set; }
    public string PhoneNumber { get; set; }
    public string Email { get; set; }
}

public sealed class PreviewMessageVM
{
    public string Name { get; set; }
    public string Subject { get; set; }
    public string TextTemplate { get; set; }
    public bool TextRequired { get; set; }
    public string HtmlTemplate { get; set; }
    public bool EmailRequired { get; set; }
    public List<TemplateKeyValue> TemplateValues { get; set; } = [];
}

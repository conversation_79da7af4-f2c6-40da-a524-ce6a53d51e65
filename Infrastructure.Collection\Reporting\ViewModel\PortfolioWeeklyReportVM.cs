﻿using NodaTime;

namespace LendQube.Infrastructure.Collection.Reporting.ViewModel;

public class PortfolioWeeklyReportVM
{
    public LocalDate Date { get; set; }

    public int TotalAccounts { get; set; }
    public int TotalCustomers { get; set; }
    public int ReturnedToClient { get; set; }
    public int StatusChange { get; set; }
    public decimal PercentageStatusChange => TotalAccounts > 0 && StatusChange > 0 ? (StatusChange / TotalAccounts) * 100m : 0;

    public decimal AmountCollectedToDate { get; set; }
    public decimal AmountCollectedThisWeek { get; set; }
    public int NonPerformingTotal { get; set; }
    public decimal NonPerformingBalance { get; set; }

    public int TotalArrangements { get; set; }
    public int TotalActiveAccounts { get; set; }
    public decimal ActivePPMonthlyValue { get; set; }
    public decimal ActiveInvoiceBalance { get; set; }

    public int TotalBrokenAccounts { get; set; }
    public decimal BrokenPPMonthlyValue { get; set; }
    public decimal BrokenInvoiceBalance { get; set; }
    public int TotalReEngagedAccounts { get; set; }

    public int TotalSettledAccounts { get; set; }
    public decimal SettledAmountCollected { get; set; }

    public decimal PredictedCurrentMonth { get; set; }
    public decimal ActualCurrentMonth { get; set; }
    public decimal DifferenceBetweenPredictedAndActualCurrentMonth => PredictedCurrentMonth - ActualCurrentMonth;
    public decimal PredictedUpcomingMonth { get; set; }

    public int TotalClosedAccounts { get; set; }
    public int ClosedAccountsReturnedToClient { get; set; }
    public int ClosedAccountsBankruptcyOrder { get; set; }
    public int CloseAccountIndividualVoluntaryArrangement { get; set; }
    public int ClosedAccountDeceased { get; set; }
    public int ClosedAccountTrustDeed { get; set; }
    public int ClosedAccountDebtReliefOrder { get; set; }
    public int ClosedAccountCampaignEnded { get; set; }

    public int TotalHeldAccounts { get; set; }
    public int TotalHeld14Days { get; set; }
    public int TotalHeld7Days { get; set; }
    public int TotalHeldComplaint { get; set; }
    public int TotalHeldDebtRespite { get; set; }

    public int SettledAccountsPaidInFull { get; set; }
    public int SettledAccountsSettledInFull { get; set; }

    public int TotalDebtMgtInvolvement { get; set; }
    public int DebtMgtAuthorizationNoPaymentDate { get; set; }
    public int DebtRespiteShemeHold { get; set; }
    public int DebtManagementPlan { get; set; }

    public int TotalComplaints { get; set; }
    public int OpenComplaints { get; set; }
    public int ClosedComplaints { get; set; }
    public int FOSComplaints { get; set; }
}

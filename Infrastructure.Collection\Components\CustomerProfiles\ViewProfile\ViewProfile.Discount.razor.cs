﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

public partial class ViewProfile
{
    private DataTable<CustomerDiscountVM> discountTable;
    private ColumnList discountTableDefinition;
    private string AddDiscountModal => "AddDiscountModal";

    private CustomerDiscount AddDiscountModel { get; set; } = new();

    private DiscountConfig allowedDiscount = null;
    private async Task SetupDiscountConfig()
    {
        allowedDiscount = await uow.Db.OneAsync(Query<DiscountConfig>.Where(x => x.Role.Name == Role), Cancel);
        discountTableDefinition = CrudService.GetTableDefinition<CustomerDiscount, CustomerDiscountVM>(new()
        {
            ShowUserInfo = true
        });

        if (allowedDiscount != null)
        {
            discountTableDefinition.TopActionButtons.Add(new TopActionButton("Add Discount", ModalName: AddDiscountModal, ShowCondition: () => HasClaim(ManageCustomersNavigation.CustomerProfileViewAddDiscountPermission)));
        }

        discountTable.SetTableDefinition(discountTableDefinition);
    }

    private ValueTask<TypedBasePageList<CustomerDiscountVM>> LoadDiscount(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<CustomerDiscount>
        {
            PrimaryCriteria = x => x.ProfileId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x =>
            EF.Functions.ILike(x.Reason, filterAndPage.TextFilter));
        }

        return CrudService.GetTypeBasedPagedData(spec, filterAndPage, CustomerDiscountVM.Mapping, ct: ct);
    }

    private ValueTask SubmitNewDiscount() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddDiscountPermission, AddDiscountModal, async () =>
    {
        if (allowedDiscount == null)
        {
            CustomMessage = "User not allowed to offer discounts";
            return false;
        }

        var placement = placementsDropDown.FirstOrDefault(x => x.Id == AddDiscountModel.PlacementId);
        if (placement == null)
        {
            CustomMessage = "Please select placement to apply discount to";
            return false;
        }

        if (!allowedDiscount.CanOverride)
        {
            var maxDiscount = placement.BalanceRemaining * (allowedDiscount.PercentageLimit / 100m);
            if (AddDiscountModel.Discount > maxDiscount)
            {
                CustomMessage = $"Max allowed discount is {allowedDiscount.PercentageLimit:n2}% which translates to amount {maxDiscount:n2}";
                return false;
            }
        }

        AddDiscountModel.ProfileId = Data.Id;
        AddDiscountModel.MaxPercentage = allowedDiscount.PercentageLimit;
        AddDiscountModel.BalanceBeforeDiscount = Data.BalanceRemaining;
        AddDiscountModel.BalanceAfterDiscount = Data.BalanceRemaining - AddDiscountModel.Discount;

        uow.Db.Insert(AddDiscountModel);

        uow.Db.Insert(new PlacementActivity
        {
            PlacementId = placement.Id.Value,
            Title = "Discount",
            Activity = $"{Data.CurrencySymbol}{AddDiscountModel.Discount:n2} applied"
        });
        uow.Db.Insert(new CustomerActivity
        {
            ProfileId = Data.Id,
            Title = "Discount",
            Activity = $"{Data.CurrencySymbol}{AddDiscountModel.Discount:n2} applied to {placement.Info}"
        });
        await uow.SaveAsync(Cancel);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<Placement>(x => x.Id == placement.Id, x => x.SetProperty(y => y.Discount, y => y.Discount + AddDiscountModel.Discount), Cancel);
        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == Data.Id, x => x.SetProperty(y => y.Discount, y => y.Discount + AddDiscountModel.Discount), Cancel);

        Data.Discount += AddDiscountModel.Discount;
        Data.BalanceRemaining -= AddDiscountModel.Discount;

        var targetSchedule = await uow.Db.OneAsync(Query<CustomerSchedule>.Where(x => x.ProfileId == Data.Id && x.PaymentStatus != SchedulePaymentStatus.Paid).OrderBy(x => x.OrderBy(y => y.DueDate)), Cancel);
        if (targetSchedule != null)
        {
            var discountToApply = Math.Min(targetSchedule.Balance, AddDiscountModel.Discount);

            targetSchedule.AmountPaid += discountToApply;
            targetSchedule.PaymentStatus = targetSchedule.AmountPaid == targetSchedule.Amount ? SchedulePaymentStatus.Paid : targetSchedule.AmountPaid > 0 ? SchedulePaymentStatus.PartiallyPaid : SchedulePaymentStatus.NotPaid;

            var customerTxn = new CustomerTransaction
            {
                ProfileId = Data.Id,
                AmountPaid = AddDiscountModel.Discount,
                AmountTried = AddDiscountModel.Discount,
                PaymentApplied = true,
                PaymentMethod = "Discount",
                PaymentProvider = PaymentProvider.Discount,
                PaymentType = "Discount",
                Successful = true,
            };

            uow.Db.Insert(customerTxn);
            uow.Db.Update(targetSchedule);

            await uow.SaveAsync(Cancel);
        }

        MessageBuilder.New($"{MessageConfigNames.Discount}", UserName)
            .Message($"{MessageConfigNames.Discount}")
            .WithRecipient(Data.Id, [
                new($"{MessageTemplateKeys.Amount}", $"{Data.CurrencySymbol}{AddDiscountModel.Discount:n2}"),
                new($"{MessageTemplateKeys.Balance}", $"{Data.CurrencySymbol}{Data.BalanceRemaining:n2}")
                ])
            .Queue(Queue);

        return true;
    }, () =>
    {
        AddDiscountModel = new();
        StateHasChanged();
        return discountTable.Refresh();
    });

}

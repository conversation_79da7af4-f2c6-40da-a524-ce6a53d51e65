﻿using System.Diagnostics;
using System.Diagnostics.CodeAnalysis;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.Extensions;

[DebuggerStepThrough]
public static class IQueryableExtensions
{
    private static IOrderedQueryable<T> OrderingHelper<T>(IQueryable<T> source, string propertyName, bool descending, bool anotherLevel)
    {
        try
        {
            ParameterExpression param = Expression.Parameter(typeof(T), string.Empty); // I don't care about some naming
            MemberExpression property = Expression.PropertyOrField(param, propertyName);
            LambdaExpression sort = Expression.Lambda(property, param);
            MethodCallExpression call = Expression.Call(
                typeof(Queryable),
                (!anotherLevel ? "OrderBy" : "ThenBy") + (descending ? "Descending" : string.Empty),
                [typeof(T), property.Type],
                source.Expression,
                Expression.Quote(sort));
            return (IOrderedQueryable<T>)source.Provider.CreateQuery<T>(call);
        }
        catch (Exception)
        {
            return (IOrderedQueryable<T>)source;
        }
    }

    private static List<T> OrderingHelper<T>(List<T> source, string propertyName, bool descending, bool anotherLevel)
    {
        try
        {

            ParameterExpression param = Expression.Parameter(typeof(T), string.Empty); // I don't care about some naming
            MemberExpression property = Expression.PropertyOrField(param, propertyName);
            LambdaExpression sort = Expression.Lambda(property, param);
            MethodCallExpression call = Expression.Call(
                typeof(Queryable),
                (!anotherLevel ? "OrderBy" : "ThenBy") + (descending ? "Descending" : string.Empty),
                [typeof(T), property.Type],
                source.AsQueryable().Expression,
                Expression.Quote(sort));
            return [.. source.AsQueryable().Provider.CreateQuery<T>(call)];
        }
        catch (Exception)
        {
            return source;
        }
    }
    public static IOrderedQueryable<T> OrderBy<T>(this IQueryable<T> source, string propertyName) => OrderingHelper(source, propertyName, false, false);
    public static List<T> OrderBy<T>(this List<T> source, string propertyName) => OrderingHelper(source, propertyName, false, false);
    public static IOrderedQueryable<T> OrderByDescending<T>(this IQueryable<T> source, string propertyName) => OrderingHelper(source, propertyName, true, false);
    public static List<T> OrderByDescending<T>(this List<T> source, string propertyName) => OrderingHelper(source, propertyName, true, false);
    public static IOrderedQueryable<T> ThenBy<T>(this IOrderedQueryable<T> source, string propertyName) => OrderingHelper(source, propertyName, false, true);
    public static IOrderedQueryable<T> ThenByDescending<T>(this IOrderedQueryable<T> source, string propertyName) => OrderingHelper(source, propertyName, true, true);
    public static bool IsNullOrEmpty<T>([NotNullWhen(false)] this IList<T> list) => list == null || list.Count == 0 || list.AsParallel().All(x => x == null || ((x is string) && string.IsNullOrEmpty(x.ToString())));
    public static bool IsNullOrEmpty([NotNullWhen(false)] this Guid? id) => id == null || Equals(id, Guid.Empty);
    public static bool IsNullOrEmpty<T>([NotNullWhen(false)] this IEnumerable<T> enumerable) => enumerable == null || !enumerable.Any() || enumerable.AsParallel().All(x => x == null || ((x is string) && string.IsNullOrEmpty(x.ToString())));
}
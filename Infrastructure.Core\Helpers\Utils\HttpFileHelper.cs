﻿namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class HttpFileHelper
{
    public static async Task<string> ReadPhysicalFileAsString(this HttpClient client, string path, CancellationToken ct, bool retry = false)
    {
        if (!path.StartsWith("http"))
            return path;
        try
        {
            using var response = await client.GetAsync(path, ct);
            if (response.IsSuccessStatusCode)
            {
                var file = await response.Content.ReadAsStringAsync(ct);
                if (string.IsNullOrEmpty(file))
                    throw new FileNotFoundException($"File located at \"{path}\" could not be read");

                return file;
            }

            throw new FileNotFoundException($"File {path} not found");
        }
        catch (Exception)
        {
            if (!retry)
                return await ReadPhysicalFileAsString(client, path, ct, true);
            else
                throw;
        }
    }

    public static async Task<Stream> ReadPhysicalFileAsStream(this HttpClient client, string path, CancellationToken ct, bool retry = false)
    {
        try
        {
            using var response = await client.GetAsync(path, ct);
            if (response.IsSuccessStatusCode)
            {
                var file = await response.Content.ReadAsStreamAsync(ct);
                return file ?? throw new FileNotFoundException($"File located at \"{path}\" could not be read");
            }

            throw new FileNotFoundException($"File {path} not found");
        }
        catch (Exception)
        {
            if (!retry)
                return await ReadPhysicalFileAsStream(client, path, ct, true);
            else
                throw;
        }

    }

    public static async Task<byte[]> ReadPhysicalFileAsByteArray(this HttpClient client, string path, CancellationToken ct, bool retry = false)
    {
        try
        {
            using var response = await client.GetAsync(path, ct);
            if (response.IsSuccessStatusCode)
            {
                var file = await response.Content.ReadAsByteArrayAsync(ct);
                return file ?? throw new FileNotFoundException($"File located at \"{path}\" could not be read");
            }

            throw new FileNotFoundException($"File {path} not found");
        }
        catch (Exception)
        {
            if (!retry)
                return await ReadPhysicalFileAsByteArray(client, path, ct, true);
            else
                throw;
        }
    }
}

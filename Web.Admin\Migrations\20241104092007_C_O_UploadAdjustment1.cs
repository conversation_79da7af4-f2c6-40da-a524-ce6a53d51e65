﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_UploadAdjustment1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_DiscountConfig_Role",
                schema: "collection",
                table: "DiscountConfig");

            migrationBuilder.DropColumn(
                name: "Role",
                schema: "collection",
                table: "DiscountConfig");

            migrationBuilder.AddColumn<Guid>(
                name: "RoleId",
                schema: "collection",
                table: "DiscountConfig",
                type: "uuid",
                maxLength: 255,
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"));

            migrationBuilder.CreateIndex(
                name: "IX_DiscountConfig_RoleId",
                schema: "collection",
                table: "DiscountConfig",
                column: "RoleId",
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropIndex(
                name: "IX_DiscountConfig_RoleId",
                schema: "collection",
                table: "DiscountConfig");

            migrationBuilder.DropColumn(
                name: "RoleId",
                schema: "collection",
                table: "DiscountConfig");

            migrationBuilder.AddColumn<string>(
                name: "Role",
                schema: "collection",
                table: "DiscountConfig",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.CreateIndex(
                name: "IX_DiscountConfig_Role",
                schema: "collection",
                table: "DiscountConfig",
                column: "Role",
                unique: true);
        }
    }
}

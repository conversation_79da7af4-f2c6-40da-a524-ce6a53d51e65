﻿using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Database.NotificationTriggers;

public sealed class TriggerControlService(TriggerConfiguration configuration, AppDbContext context, ILogManager<TriggerControlService> logger)
{
    internal async Task CreateTrigger<T>(CancellationToken ct) where T : class
	{
		if (configuration.Triggers.Count == 0)
		{
			logger.LogWarning(EventSource.Infrastructure, EventAction.Triggers, "There are no triggers defined");
			return;
		}

		var tableName = typeof(T).Name;
		var trigger = configuration.Triggers.FirstOrDefault(x => x.Table == tableName);

		if (trigger == null)
		{
			logger.LogWarning(EventSource.Infrastructure, EventAction.Triggers, "There are no triggers defined for {table}", data: tableName);
			return;
		}

		try
		{

			var createScript = NotifyTriggerDbScripts.CreateTrigger(trigger);
            _ = await context.Database.ExecuteSqlRawAsync(createScript, ct);
		}
		catch (Exception ex)
		{
			logger.LogError(EventSource.Infrastructure, EventAction.Triggers, ex, "Unable to create trigger {tableName}", data: trigger.Table);
		}
	}

	internal async Task RemoveTrigger(NotifyTrigger trigger, CancellationToken ct)
	{
		try
        {
            var createScript = NotifyTriggerDbScripts.RemoveTrigger(trigger);
            _ = await context.Database.ExecuteSqlRawAsync(createScript, ct);
		}
		catch (Exception ex)
		{
			logger.LogError(EventSource.Infrastructure, EventAction.Triggers, ex, "Unable to remove trigger {tableName}", data: trigger.Table);
		}
	}
}

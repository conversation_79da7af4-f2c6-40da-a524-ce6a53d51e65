﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.Setup;

public class DiscountConfigVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<DiscountConfig, DiscountConfigVM>> Mapping = data => new()
    {
        Id = data.Id,
        RoleId = data.RoleId,
        RoleName = data.Role.Name,
        PercentageLimit = data.PercentageLimit,
        CanOverride = data.CanOverride,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public DiscountConfig Get() => new()
    {
        Id = Id,
        RoleId = RoleId,
        PercentageLimit = PercentageLimit,
        CanOverride = CanOverride,
    };

    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public Guid RoleId { get; set; }
    public string RoleName { get; set; }
    public decimal PercentageLimit { get; set; }
    public bool CanOverride { get; set; }
}

public class HoldConfigVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<HoldConfig, HoldConfigVM>> Mapping = data => new()
    {
        Id = data.Id,
        Reason = data.Reason,
        Description = data.Description,
        DefaultDurationId = data.DefaultDurationId,
        DefaultDurationData = data.DefaultDuration,
        Action = data.Action,
        AllowDefaultOverride = data.AllowDefaultOverride,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public HoldConfig Get() => new()
    {
        Id = Id,
        Reason = Reason,
        Description = Description,
        DefaultDurationId = DefaultDurationId,
        Action = Action,
        AllowDefaultOverride = AllowDefaultOverride,
    };

    public string Reason { get; set; }
    public string Description { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public long? DefaultDurationId { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn, TableDecoratorType.SkipFilter)]
    public HoldDurationConfig? DefaultDurationData { get; set; }
    public string DefaultDuration => DefaultDurationData?.Label ?? string.Empty;

    [TableDecorator(TableDecoratorType.HideColumn)]
    public HoldAction Action { get; set; }
    [DisplayName("Action")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ActionFormatted => string.Join(", ", Action.FlagsToDisplayList<HoldAction>());
    public bool AllowDefaultOverride { get; set; }
}
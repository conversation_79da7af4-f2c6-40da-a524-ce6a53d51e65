﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Logs;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.EntityFrameworkCore;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Discounts;

internal static class AutoDiscountService
{
    public static async ValueTask ApplyDiscount<T>(IUnitofWork uow, ILogManager<T> logger, AutoDiscountConfig discount, CancellationToken ct) where T : class
    {
        Expression<Func<CustomerProfile, bool>> discountQuery = x => (x.PhoneNumber != null || !string.IsNullOrEmpty(x.Email)) && x.Placements.Any(y => y.Status < PlacementStatus.Settled);

        if (discount.ApplyToNewAccountsOnly)
            discountQuery = discountQuery.CombineWithAndAlso(x => x.Placements.Any(y => y.Status == PlacementStatus.New));

        if (discount.ApplyToOnlyAccountsWithNoDiscount)
            discountQuery = discountQuery.CombineWithAndAlso(x => x.Discount == 0);

        if (discount.FileUploadId.HasValue)
            discountQuery = discountQuery.CombineWithAndAlso(x => x.FileUploadId == discount.FileUploadId || x.Placements.Any(y => y.FileUploadId == discount.FileUploadId));

        var ruleName = GetNameBasedOnDiscountType(discount);
        discountQuery = discountQuery.CombineWithAndAlso(x => !x.Discounts.Any(x => x.Reason == ruleName));

        var message = MessageBuilder.New($"{MessageConfigNames.Discount}", discount.CreatedByUserId)
            .Message($"{MessageConfigNames.Discount}");

        List<AutoDiscountMessageVM> result = [];

        switch (discount.Rule)
        {
            case AutoDiscountRule.SetupWithinDays:
                result = await SetupWithinDaysDiscountApplication(uow, discountQuery, discount, ruleName, ct);
                break;
            default:
                break;
        }

        if (result.Count > 0)
        {
            result = [.. result.OrderBy(x => x.UserId)];

            _ = await message
                .WithRecipientsWithIndividualKeys(result.Select(x => x.UserId).ToList(), result.ToDictionary(x => x.UserId, x => x.Keys))
                .Send(uow, logger, ct);
        }
    }

    private static async Task<List<AutoDiscountMessageVM>> SetupWithinDaysDiscountApplication(IUnitofWork uow, Expression<Func<CustomerProfile, bool>> discountQuery, AutoDiscountConfig config, string ruleName, CancellationToken ct)
    {
        var profileQuery = Query<CustomerProfile>.Where(discountQuery.CombineWithAndAlso(x => x.AppUser.LastLoginDate.HasValue));

        profileQuery = (config.MinDays, config.MaxDays) switch
        {
            (not null, not null) => profileQuery.AndWhere(x => uow.Db.Queryable<UserAccessLog>().Any(y => y.CreatedByUserId == x.Id &&
            y.CreatedDate - x.CreatedDate >= Duration.FromDays(config.MinDays.Value) &&
            y.CreatedDate - x.CreatedDate <= Duration.FromDays(config.MaxDays.Value))),
            (not null, null) => profileQuery.AndWhere(x => uow.Db.Queryable<UserAccessLog>().Any(y => y.CreatedByUserId == x.Id &&
            y.CreatedDate - x.CreatedDate >= Duration.FromDays(config.MinDays.Value))),
            (null, not null) => profileQuery.AndWhere(x => uow.Db.Queryable<UserAccessLog>().Any(y => y.CreatedByUserId == x.Id &&
            y.CreatedDate - x.CreatedDate <= Duration.FromDays(config.MaxDays.Value))),
            _ => profileQuery,
        };

        var profiles = await uow.Db.ManyAsync(profileQuery.Include(x => x.Include(y => y.Placements).Include(y => y.Schedules)).Track(), ct);

        return await ApplyDiscount(uow, profiles, config, ruleName, ct);
    }

    private static async Task<List<AutoDiscountMessageVM>> ApplyDiscount(IUnitofWork uow, List<CustomerProfile> profiles, AutoDiscountConfig config, string ruleName, CancellationToken ct)
    {
        List<AutoDiscountMessageVM> result = [];

        List<CustomerDiscount> discountsToApply = [];
        List<PlacementActivity> placementActivity = [];
        List<CustomerActivity> customerActivity = [];
        List<CustomerTransaction> customerTransactions = [];

        foreach (var profile in profiles)
        {
            var totalAmountApplied = 0m;
            var discountAmount = (config.Amount ?? 0) + (config.Percentage.HasValue ? profile.BalanceRemaining * (config.Percentage.Value / 100m) : 0);

            var profileBalance = profile.BalanceRemaining;
            foreach (var placement in profile.Placements.Where(x => x.Status < PlacementStatus.Settled).OrderByDescending(x => x.CreatedDate))
            {
                var amountToApply = placement.BalanceRemaining > discountAmount ? discountAmount : placement.BalanceRemaining;

                if (placement.BalanceRemaining <= amountToApply && config.DoNotApplyIfDiscountSettlesAccount)
                    continue;

                var discount = new CustomerDiscount
                {
                    ProfileId = profile.Id,
                    PlacementId = placement.Id,
                    BalanceBeforeDiscount = profileBalance,
                    BalanceAfterDiscount = profileBalance - amountToApply,
                    Discount = amountToApply,
                    MaxPercentage = 0,
                    Reason = ruleName,
                };

                discountsToApply.Add(discount);
                placementActivity.Add(new PlacementActivity
                {
                    PlacementId = placement.Id,
                    Title = "Discount",
                    Activity = $"{profile.CurrencySymbol}{amountToApply:n2} applied"
                });

                customerActivity.Add(new CustomerActivity
                {
                    ProfileId = profile.Id,
                    Title = "Discount",
                    Activity = $"{profile.CurrencySymbol}{amountToApply:n2} applied to {placement.Company + ": " + placement.SourceAccountNumber}"
                });

                config.TotalAmountApplied += amountToApply;
                config.TotalPlacementsAffected += 1;

                placement.Discount += amountToApply;
                profile.Discount += amountToApply;

                profileBalance -= amountToApply;
                discountAmount -= amountToApply;

                totalAmountApplied += amountToApply;

                if (placement.BalanceRemaining - amountToApply <= 0)
                    placement.Status = PlacementStatus.Settled;

                result.Add(new()
                {
                    UserId = profile.Id,
                    Keys = [
                       new($"{MessageTemplateKeys.Amount}", $"{profile.CurrencySymbol}{amountToApply:n2}"),
                       new($"{MessageTemplateKeys.CompanyName}", placement.Company),
                       new($"{MessageTemplateKeys.Balance}", $"{profile.CurrencySymbol}{placement.BalanceRemaining - amountToApply:n2}")
                   ]
                });

                if (discountAmount <= 0 || profileBalance <= 0)
                    break;
            }
            if (!profile.Schedules.IsNullOrEmpty())
            {
                var targetSchedule = profile.Schedules.Where(s => s.PaymentStatus != SchedulePaymentStatus.Paid && s.Balance > 0).FirstOrDefault();

                if (targetSchedule != null)
                {
                    var discountToApply = Math.Min(targetSchedule.Balance, totalAmountApplied);

                    if (discountToApply > 0)
                    {
                        targetSchedule.AmountPaid += discountToApply;

                        targetSchedule.PaymentStatus = targetSchedule.AmountPaid == targetSchedule.Amount
                            ? SchedulePaymentStatus.Paid
                            : SchedulePaymentStatus.PartiallyPaid;

                        uow.Db.Update(targetSchedule);
                        await uow.SaveAsync(ct);
                    }
                }
            }


            if (totalAmountApplied > 0)
            {
                customerTransactions.Add(new CustomerTransaction
                {
                    ProfileId = profile.Id,
                    AmountPaid = totalAmountApplied,
                    AmountTried = totalAmountApplied,
                    PaymentApplied = true,
                    PaymentMethod = "Discount",
                    PaymentProvider = PaymentProvider.Discount,
                    PaymentType = "Discount",
                    Successful = true,
                    TransactionId = $"{config.Id}"
                });
            }
        }


        if (profiles.Count > 0)
        {
            config.DiscountApplied = true;
            uow.Db.Update(config);

            uow.Db.InsertBulk(discountsToApply, ct);
            uow.Db.InsertBulk(placementActivity, ct);
            uow.Db.InsertBulk(customerActivity, ct);
            uow.Db.InsertBulk(customerTransactions, ct);

            await uow.SaveAsync(ct);
        }

        return result;
    }

    private static string GetNameBasedOnDiscountType(AutoDiscountConfig config) => config.Rule switch
    {
        AutoDiscountRule.SetupWithinDays => (config.MinDays, config.MaxDays) switch
        {
            (not null, not null) => $"Auto Discount - Setup Within {config.MinDays} to {config.MaxDays} Days",
            (not null, null) => $"Auto Discount - Setup Within {config.MinDays} Days",
            (null, not null) => $"Auto Discount - Setup Within {config.MaxDays} Days",
            _ => $"Auto Discount - Setup Within Days (No Limit)",
        },
        _ => ""
    };
}

internal sealed class AutoDiscountMessageVM
{
    public string UserId { get; set; }
    public List<TemplateKeyValue> Keys { get; set; }
}
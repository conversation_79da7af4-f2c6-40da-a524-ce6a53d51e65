$(function () {
    feather.replace();

    $(':submit').on('click', function () {
        if ($(this).closest("form").length > 0) {
            $(this).append('&nbsp;  <i class="fa fa-spinner fa-spin"></i>');
            $(this).addClass("disabled");
        }
    });
});

window.addEventListener("load", () => {
    const qrCodeDataElement = document.getElementById("qrCodeData");
    if (qrCodeDataElement) {
        const uri = qrCodeDataElement?.getAttribute('data-url');
        new QRCode(document.getElementById("qrCode"),
        {
            text: uri,
            width: 150,
            height: 150
        });
    }
});
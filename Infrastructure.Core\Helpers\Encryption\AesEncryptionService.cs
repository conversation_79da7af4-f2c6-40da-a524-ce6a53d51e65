﻿using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Helpers.Utils;
using System.Security.Cryptography;
using System.Text;

namespace LendQube.Infrastructure.Core.Helpers.Encryption;

public sealed class AesEncryptionService(DefaultAppConfig config) : IDisposable
{
    private readonly Aes encryptor = Aes.Create();

    public async Task<string> AESEncryptAndEncode(string clearText, CancellationToken ct)
    {
        encryptor.Mode = CipherMode.CBC;
        encryptor.Padding = PaddingMode.PKCS7;
        encryptor.Key = Encoding.ASCII.GetBytes(config.Encryption.Key);
        encryptor.IV = Encoding.ASCII.GetBytes(config.Encryption.VI);

        using MemoryStream ms = new();
        using CryptoStream cs = new(ms, encryptor.CreateEncryptor(), CryptoStreamMode.Write);
        byte[] clearBytes = Encoding.UTF8.GetBytes(clearText);
        await cs.WriteAsync(clearBytes, ct);
        cs.Close();

        return Convert.ToBase64String(ms.ToArray()).Base64Encode();
    }

    public async Task<string> AESDecodeAndDecrypt(string cipherText, CancellationToken ct)
    {
        encryptor.Mode = CipherMode.CBC;
        encryptor.Key = Encoding.ASCII.GetBytes(config.Encryption.Key);
        encryptor.IV = Encoding.ASCII.GetBytes(config.Encryption.VI);

        using MemoryStream ms = new();
        using CryptoStream cs = new(ms, encryptor.CreateDecryptor(), CryptoStreamMode.Write);
        byte[] cipherBytes = cipherText.Base64DecodeToBytes();
        await cs.WriteAsync(cipherBytes, ct);
        cs.Close();

        return Encoding.ASCII.GetString(ms.ToArray());
    }

    public void Dispose()
    {
        encryptor.Dispose();
        GC.SuppressFinalize(this);
    }
}
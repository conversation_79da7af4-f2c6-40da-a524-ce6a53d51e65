﻿using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.Extensions.Caching.Distributed;

namespace LendQube.Infrastructure.Core.PermissionsAndRoles;

public sealed class TicketStore(IDistributedCache cache) : ITicketStore
{
    public Task RemoveAsync(string key) => cache.RemoveAsync(key);

    public Task RenewAsync(string key, AuthenticationTicket ticket)
    {
        var options = new DistributedCacheEntryOptions();
        var expiresUtc = ticket.Properties.ExpiresUtc;

        if (expiresUtc.HasValue)
        {
            var timespan = expiresUtc.Value - DateTimeOffset.UtcNow;
            options.SetSlidingExpiration(timespan);
        }
        else
        {
            options.SetSlidingExpiration(TimeSpan.FromMinutes(10));
        }

        var ticketToStore = SerializeToBytes(ticket);
        return cache.SetAsync(key, ticketToStore, options);
    }

    public async Task<AuthenticationTicket> RetrieveAsync(string key)
    {
        var byteTicket = await cache.GetAsync(key);
        var ticket = DeserializeFromBytes(byteTicket);
        return ticket;
    }

    public async Task<string> StoreAsync(AuthenticationTicket ticket)
    {
        var key = ticket.Principal.Identity.Name;
        await RenewAsync(key, ticket);
        return key;
    }

    private static byte[] SerializeToBytes(AuthenticationTicket source)
        => TicketSerializer.Default.Serialize(source);

    private static AuthenticationTicket DeserializeFromBytes(byte[] source)
        => source == null ? null : TicketSerializer.Default.Deserialize(source);
}
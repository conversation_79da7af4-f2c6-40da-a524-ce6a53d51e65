﻿@page "/manageclaims"
@using LendQube.Entities.Core.BaseUser
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.PermissionsAndRoles
@using Radzen.Blazor

@inherits GenericCrudTable<ApplicationClaims>

@attribute [Authorize(Policy = ManageAdminUsersNavigation.ClaimIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Source">Source</label>
            <RadzenDropDown @bind-Value=@context.Source Data=@PermissionSourceHelper.Sources Name="Source"
                            FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true"
                            AllowClear=true Placeholder="Select source" class="form-input" />
            <ValidationMessage For="() => context.Source" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Type">Type</label>
            <InputText @bind-Value="context.Type" class="form-input" aria-required="true" placeholder="Type" />
            <ValidationMessage For="() => context.Type" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Value">Value</label>
            <InputText @bind-Value="context.Value" class="form-input" aria-required="true" placeholder="Value" />
            <ValidationMessage For="() => context.Value" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Name">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Description">Description</label>
            <InputText @bind-Value="context.Description" class="form-input" aria-required="true" placeholder="Description" />
            <ValidationMessage For="() => context.Description" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>


@code
{
    protected override void OnInitialized()
    {
        Title = "Manage Permissions";
        FormBaseTitle = "Permissions";
        SubTitle = "All Permissions";
        CreatePermission = ManageAdminUsersNavigation.ClaimCreatePermission;
        EditPermission = ManageAdminUsersNavigation.ClaimEditPermission;
        DeletePermission = ManageAdminUsersNavigation.ClaimDeletePermission;
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.Like(x.Source, filterAndPage.TextFilter)
        || EF.Functions.Like(x.Value, filterAndPage.TextFilter)
        || EF.Functions.Like(x.Description, filterAndPage.TextFilter);
    }

    protected override ValueTask SubmitEdit() => SaveEdit((data, ct) => Service.CrudService.UpdateWithFilter(x => x.Id == data.Id, x => x.SetProperty(y => y.Description, data.Description), ct), table.Refresh);
}


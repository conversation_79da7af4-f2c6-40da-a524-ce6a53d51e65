﻿using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.ViewModels.Messaging;

public sealed class MessageConfigurationVM : IBaseEntityWithNumberId
{
    private static readonly Dictionary<MessageChannel, IReadOnlyList<MessageChannel>> prohibitedChannelCombinations =
    new() {
        { MessageChannel.Sms, [MessageChannel.SmsOrEmail, MessageChannel.EmailOrSms, MessageChannel.SmsAndEmail, MessageChannel.PushNotificationAndSms, MessageChannel.PushNotificationOrSms] },
        { MessageChannel.Email, [MessageChannel.SmsOrEmail, MessageChannel.EmailOrSms, MessageChannel.SmsAndEmail, MessageChannel.PushNotificationOrEmail, MessageChannel.PushNotificationAndEmail] },
        { MessageChannel.PushNotification, [MessageChannel.PushNotificationAndSms, MessageChannel.PushNotificationOrSms, MessageChannel.PushNotificationOrEmail, MessageChannel.PushNotificationAndEmail] },
    };

    public static readonly Expression<Func<MessageConfiguration, MessageConfigurationVM>> Mapping = data => new MessageConfigurationVM
    {
        Id = data.Id,
        Name = data.Name,
        Description = data.Description,
        Subject = data.Subject,
        Channels = data.Channels,
        SenderEmail = data.SenderEmail,
        SenderName = data.SenderName,
        NeedsConfiguration = data.NeedsConfiguration,
        DoNotSendIfExists = data.DoNotSendIfExists,
        ExistsCheckWindow = data.ExistsCheckWindow,
        CreatedDate = data.CreatedDate,
        CreatedByUser = data.CreatedByUser,
        CreatedByIp = data.CreatedByIp,
        LastModifiedDate = data.LastModifiedDate,
        ModifiedByUser = data.ModifiedByUser,
        ModifiedByIp = data.ModifiedByIp
    };

    public MessageConfiguration Get() => new()
    {
        Id = Id,
        Name = Name,
        Description = Description,
        Subject = Subject,
        Channels = ChannelsList.CombineFlags(),
        DoNotSendIfExists = DoNotSendIfExists,
        ExistsCheckWindow = ExistsCheckWindow,
        SenderEmail = SenderEmail,
        SenderName = SenderName,
    };

    public (bool, string) Validate()
    {
        var invalidEntries = prohibitedChannelCombinations.Where(x => ChannelsList.Contains(x.Key)).ToDictionary(x => x.Key, x => x.Value.Where(y => ChannelsList.Contains(y)));
        if (invalidEntries.IsNullOrEmpty() || invalidEntries.Values.IsNullOrEmpty())
            return (true, string.Empty);

        var message = "These channel combinations are not allowed \n" +
            string.Join("\n\t", invalidEntries.Select(x => $"{x.Key} with {string.Join(", ", x.Value)}"));

        return (true, message);
    }


    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string Name { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.NoScriptTag)]
    public string Description { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.NoScriptTag)]
    public string Subject { get; set; }

    [TableDecorator(TableDecoratorType.HideColumn)]
    public MessageChannel Channels { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete), DataType(DataType.EmailAddress)]
    public string SenderEmail { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string SenderName { get; set; }
    public bool NeedsConfiguration { get; set; }
    public int ExistsCheckWindow { get; set; }
    public bool DoNotSendIfExists { get; set; }

    [DisplayName("Channels")]
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ChannelsFormatted => string.Join(", ", Channels.FlagsToDisplayList<MessageChannel>());

    [RemoveColumn]
    public List<MessageChannel> ChannelsList { get; set; } = [];
}

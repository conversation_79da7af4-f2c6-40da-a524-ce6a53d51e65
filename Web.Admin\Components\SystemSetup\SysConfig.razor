﻿@page "/sysconfig"
@using LendQube.Entities.Core.BackgroundTasks
@using LendQube.Infrastructure.Core.AppSettings
@using LendQube.Infrastructure.Core.BackgroundTasks
@using LendQube.Infrastructure.Core.Components.FormsAndModals

@inject BackgroundTaskManageService Service
@inject DefaultAppConfig config

@inherits BaseTableComponentBase

@attribute [Authorize(Policy = SetupNavigation.SysConfigIndexPermission)]

<PageTitle>@Title</PageTitle>
<div class="pg-row grid grid-col-1 grid-tab-1">
	<div class="card">

		<div class="title-wrapper flex __justify-between __align-center">
			<span class="text_xl_medium">@SubTitle</span>
		</div>

		<AuthorizeView Policy="@SetupNavigation.BackgroundTasksPermission" Context="authContext">
			<div class="pg-actions">

				<div class="actions-item ml-auto">
					<a class="btn btn--default" type="button" href="@config.Url.WebBackground" target="_blank">
						Open Background Service Manager
					</a>
				</div>
			</div>

		</AuthorizeView>

		<StatusMessage @ref="TableMessage" />

		<DataTable T="BackgroundTaskEventControl" TableDefinition="TableDefinition" LoadData="Load" DeleteRow="SubmitDelete" @ref="table" />

	</div>
</div>

<ModalComponent Policy="@CreatePermission" ModalId="@StartAllModalName" ModalCss="width-xlg" Title="@($"Start All {FormBaseTitle}")">
	<BodyContent>
		Are you sure you want to start all background events?
	</BodyContent>
	<FooterContent>
		<button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
		<LoadButton Label="Yes" OnClick="StartAll" />
	</FooterContent>
</ModalComponent>

<ModalComponent Policy="@CreatePermission" ModalId="@StopAllModalName" ModalCss="width-xlg" Title="@($"Stop All {FormBaseTitle}")">
	<BodyContent>
		Are you sure you want to stop all background events?
	</BodyContent>
	<FooterContent>
		<button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
		<LoadButton Label="Yes" OnClick="StopAll" />
	</FooterContent>
</ModalComponent>

<ModalComponent Policy="@DeletePermission" ModalId="@ClearLogsModalName" ModalCss="width-xlg" Title=@($"Clear {FormBaseTitle}")>
	<BodyContent>
		Are you sure you want to clear all background events?
	</BodyContent>
	<FooterContent>
		<button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
		<LoadButton Label="Yes" OnClick="ClearLogs" />
	</FooterContent>
</ModalComponent>

@code {
	private DataTable<BackgroundTaskEventControl> table;
	private BackgroundTaskEventControl SelectedEventControl { get; set; }
	private string StartAllModalName = "StartAllModal";
	private string StopAllModalName = "StopAllModal";
	private string ClearLogsModalName => "ClearLogsModal";


	protected override void OnInitialized()
	{
		Title = "System Configurations";
		SubTitle = "Manage System Config (DO NOT OPERATE without clear instructions)";
		FormBaseTitle = "Background Task Events";
		CreatePermission = SetupNavigation.SysConfigCreatePermission;
		DeletePermission = SetupNavigation.SysConfigCreatePermission;
	}

	protected override async Task OnInitializedAsync()
	{
		if (TableDefinition == null)
		{
			await base.OnInitializedAsync();

			AddTopButton(CreatePermission, new TopActionButton("Start All", Icon: "", ModalName: StartAllModalName, Action: () => CloseMessage(ModalMessage)));

			AddTopButton(CreatePermission, new TopActionButton("Stop All", "btn--danger", Icon: "", ModalName: StopAllModalName, Action: () => CloseMessage(ModalMessage)));

			AddTopButton(DeletePermission, new TopActionButton("Clear All", "btn--danger", Icon: "trash-2", ModalName: ClearLogsModalName, Action: () => CloseMessage(ModalMessage)));

			AddRowButton(CreatePermission, new RowActionButton("Start", "btn--success", Icon: "play-circle", Action: async (object row) =>
			{
				CloseMessage();
				table.Loading = true;
				SelectedEventControl = row as BackgroundTaskEventControl;
				await Start(SelectedEventControl.Source, SelectedEventControl.Event);
				table.Loading = false;

				StateHasChanged();
			}, ShowCondition: (object row) =>
			{
				SelectedEventControl = row as BackgroundTaskEventControl;
				return SelectedEventControl.Status == BackgroundControlState.Stopping || SelectedEventControl.Status == BackgroundControlState.Stopped;
			}));

			AddRowButton(CreatePermission, new RowActionButton("Stop", "btn--danger", Icon: "stop-circle", Action: async (object row) =>
			{
				CloseMessage();
				table.Loading = true;
				SelectedEventControl = row as BackgroundTaskEventControl;
				await Stop(SelectedEventControl.Id);
				table.Loading = false;

				StateHasChanged();
			}, ShowCondition: (object row) =>
			{
				SelectedEventControl = row as BackgroundTaskEventControl;
				return SelectedEventControl.Status == BackgroundControlState.Running || SelectedEventControl.Status == BackgroundControlState.Idle;
			}));
		}
	}

	protected override ColumnList GetTableDefinition() => Service.GetTableDefinition();

	private ValueTask<TypedBasePageList<BackgroundTaskEventControl>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
	{
		CloseMessage();
		return Service.GetTypeBasedPagedData(filterAndPage, ct);
	}

	private async Task Start(BackgroundEventSource controlSource, BackgroundTask controlEvent)
	{
		CloseMessage();
		var result = await Service.StartSingle(controlSource, controlEvent, Cancel);
		await table.Refresh();

		TableMessage.Set(result, $"Event {controlSource} : {controlEvent} starting", $"Event {controlSource} : {controlEvent} could not be started");
	}

	private async Task StartAll()
	{
		CloseMessage();
		await Service.StartAll(Cancel);
		await JSRuntime.CloseModal(StartAllModalName, Cancel);
		await table.Refresh();
		TableMessage.Info("All events successfully started");

		StateHasChanged();
	}

	private async Task Stop(long id)
	{
		CloseMessage();
		var result = await Service.StopSingle(id, Cancel);
		await table.Refresh();

		var (event1, event2, success) = result;
		TableMessage.Set(success, $"Event {event1} : {event2} stopping", $"Event {event1} : {event2} could not be stopped");
	}

	private async Task StopAll()
	{
		CloseMessage();
		await Service.StopAll(Cancel);
		await JSRuntime.CloseModal(StopAllModalName, Cancel);
		await table.Refresh();
		TableMessage.Info("All events stopping");

		StateHasChanged();
	}

	private ValueTask<bool> SubmitDelete(BackgroundTaskEventControl data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.Delete(data.Id, ct), refresh);

	private async Task ClearLogs()
	{
		CloseMessage();
		var success = await Service.DeleteAll(Cancel);
		await JSRuntime.CloseModal(ClearLogsModalName, Cancel);
		await table.Refresh();

		TableMessage.Set(success, "All events cleared", $"Unable clear all events");
	}
}

﻿using Microsoft.ApplicationInsights.DataContracts;
using Serilog.Events;
using Serilog.Sinks.ApplicationInsights.TelemetryConverters;

namespace LendQube.Infrastructure.Core.Telemetry;

internal sealed class IncludeRenderedMessageConverter : TraceTelemetryConverter
{
    public override void ForwardPropertiesToTelemetryProperties(LogEvent logEvent,
        ISupportProperties telemetryProperties, IFormatProvider formatProvider) => ForwardPropertiesToTelemetryProperties(logEvent, telemetryProperties, formatProvider,
            includeLogLevel: true,
            includeRenderedMessage: true,
            includeMessageTemplate: false);
}

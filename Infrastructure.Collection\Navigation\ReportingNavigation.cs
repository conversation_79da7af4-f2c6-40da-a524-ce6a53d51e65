﻿using LendQube.Infrastructure.Core.Navigation;

namespace LendQube.Infrastructure.Collection.Navigation;

public class ReportingNavigation : INavigatorHasPermissions
{
    public void PreparePermissionDescriptions()
    {
        Navigator.PermissionDescription[RunBusinessReportsIndexPermission] = "Can run business reports";

    }

    public const string RunBusinessReportsIndexPermission = "Permission.RunReports.BusinessReport";
}

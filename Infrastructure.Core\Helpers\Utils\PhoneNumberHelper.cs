﻿using System.Text.Json;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Core.SerializersAndConverters;

namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class PhoneNumberHelper
{
    public static string CleanPhoneNumber(this string phoneNumber, string phoneCode)
    {
        return (string.IsNullOrEmpty(phoneNumber)
               ? null
               : phoneNumber.StartsWith('0')
                   ? phoneNumber[1..]
                   : phoneNumber.StartsWith(phoneCode)
                       ? phoneNumber[phoneCode.Length..]
                       : phoneNumber.StartsWith(phoneCode[1..])
                           ? phoneNumber[(phoneCode.Length - 1)..]
                           : phoneNumber)?.RemoveWhitespaces();
    }

    public static PhoneNumber GetPhoneNumberFromJsonElement(this object value)
    {
        if(value is null) return null;
        return ((JsonElement)value).Deserialize<PhoneNumber>(JsonOptions.CamelCaseAndEnumAsStringOptions);
    }
}

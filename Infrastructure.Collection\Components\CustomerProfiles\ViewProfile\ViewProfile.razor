﻿@page "/customers/profile/{ProfileId}"
@page "/debtworkflow/agentworkdesk/{ProfileId}/{WorkTaskId:long?}"
@page "/debtworkflow/agentworkflowtimeanalytics/{ProfileId}/{WorkTaskId:long?}/{ViewMode:bool}"
@using LendQube.Entities.Collection.Customers
@using LendQube.Entities.Collection.Placements
@using LendQube.Entities.Collection.Setup
@using LendQube.Entities.Collection.Workflows.Debt
@using LendQube.Infrastructure.Collection.Components.DebtWorkflow
@using LendQube.Infrastructure.Collection.Helpers
@using LendQube.Infrastructure.Collection.Payments
@using LendQube.Infrastructure.Collection.ViewModels.PlacementData
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Components.Timeline
@using Radzen.Blazor

@attribute [Authorize(Policy = ManageCustomersNavigation.CustomerProfileViewPermission)]

@inherits AbstractBaseTableComponentBase

<PageTitle>@Title</PageTitle>
<StatusMessage @ref="@TableMessage" />

<div class="pg-actions">
    <ul class="breadcrumbs">
        <li class="__item">
            @if (WorkTaskId.HasValue)
            {
                if(ViewMode)
                {
                    <a href="/debtworkflow/agentworkflowtimeanalytics">
                        <i data-feather="arrow-left"></i>
                    </a>
                }
                else
                {
                    <a href="/debtworkflow/agentworkdesk">
                        <i data-feather="arrow-left"></i>
                    </a>
                }
            }
            else
            {
                <a href="/customers/profile">
                    <i data-feather="arrow-left"></i>
                </a>
            }
        </li>
        <li class="__item">
            <a>@Title: @Data?.FirstName @Data?.LastName</a>
        </li>
    </ul>
    @if(!string.IsNullOrEmpty(Data.Id))
    {
        <div class="actions-item ml-auto">
            @if (Data.HasActivePlacements)
            {
                <AuthorizeView Policy="@ManageCustomersNavigation.CustomerProfileTakePaymentPermission" Context="authContext">
                    <button type="button"
                            class="btn btn--primary"
                            data-bs-toggle="modal" data-bs-target="#@InitiatePaymentModalName">
                        <i data-feather="credit-card"></i>&nbsp;&nbsp; Take Payment
                    </button>
                </AuthorizeView>
            }

            <AuthorizeView Policy="@ManageCustomersNavigation.CustomerProfileResetOtpPermission" Context="authContext">
                <LoadButton Label="Reset Otp" OnClick="ResetOtp" Css="btn--danger" />
            </AuthorizeView>
            <AuthorizeView Policy="@ManageCustomersNavigation.CustomerProfileClearIAndEPermission" Context="authContext">
                @if(Data.IncomeAndExpenditure != null)
                {
                    <LoadButton Label="Clear I&E" OnClick="ClearIAndE" Css="btn--danger" />
                }
            </AuthorizeView>
            <AuthorizeView Policy="@ManageCustomersNavigation.CustomerProfileResetAmendSchedulePermission" Context="authContext">
                @if (!Data.CanReschedule)
                {
                    <LoadButton Label="Reset Amend Schedule" OnClick="ClearAmendSchedule" Css="btn--default" />
                    
                }
            </AuthorizeView>
        </div>
    }
</div>
<div class="pg-row grid loan-grid grid-tab-1">

    <div class="info-col">
        <div class="card info-item">
            <div class="__header flex __justify-between">
                <div class="title">
                    <span class="__title">@Data?.FirstName @Data?.LastName</span>
                    <span class="__amount">@Data?.AccountId</span>

                    <div class="status">
                        @if (Data.HasFlags)
                        {
                            <span class="lz __yellow">
                                Flagged
                            </span>
                        }
                        @if (Data.HasActiveHold)
                        {
                            <span class="lz __red">
                                On-Hold
                            </span>
                        }
                    </div>

                </div>
            </div>
            <div class="detail-wrapper grid-2">
                <div class="detail-item">
                    <span class="label">Created On</span>
                    <span class="value"><LocalTime Value="Data?.CreatedDate" /></span>
                </div>
                <div class="detail-item">
                    <span class="label">Email</span>
                    <span class="value">@Data?.Email</span>
                </div>
                <div class="detail-item">
                    <span class="label">Mobile Number</span>
                    <span class="value">@Data?.MobileNumber?.ToString()</span>
                </div>
                <div class="detail-item">
                    <span class="label">Phone Number</span>
                    <span class="value">@Data?.PhoneNumber?.ToString()</span>
                </div>
                <div class="detail-item">
                    <span class="label">Date of Birth</span>
                    <span class="value"><LocalTime Value="Data?.DateOfBirth" /></span>
                </div>
                <div class="detail-item">
                    <span class="label">Gender</span>
                    <span class="value">@Data?.Gender.GetDisplayName()</span>
                </div>
                <div class="detail-item">
                    <span class="label">Total Placements</span>
                    <span class="value">@totalPlacements</span>
                </div>
                <div class="detail-item">
                    <span class="label">Last Reschedule Date</span>
                    <span class="value"><LocalTime Value="Data?.LastRescheduleDate" /></span>
                </div>
                <div class="detail-item">
                    <span class="label">Payment Frequency</span>
                    <span class="value">@Data.PaymentFrequency</span>
                </div>
                <div class="detail-item">
                    <span class="label">Reschedule Count</span>
                    <span class="value">@Data.TotalRescheduleCount</span>
                </div>
                <div class="detail-item">
                    <span class="label">Balance Total</span>
                    <span class="value">@($"{Data?.CurrencySymbol}{Data.BalanceTotal:n2}")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Balance Paid</span>
                    <span class="value">@($"{Data?.CurrencySymbol}{Data.BalancePaid:n2}")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Discount</span>
                    <span class="value">@($"{Data?.CurrencySymbol}{Data.Discount:n2}")</span>
                </div>
                <div class="detail-item">
                    <span class="label">Balance Outstanding</span>
                    <span class="value">@($"{Data?.CurrencySymbol}{Data.BalanceRemaining:n2}")</span>
                </div>
            </div>
        </div>
        <div class="card activity-item">
            <div class="accordion" id="activityAccordion">
                <div class="accordion-item">
                    <span class="accordion-header" id="activityHeading">
                        <button class="accordion-button" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#collapseActivity" aria-expanded="true"
                                aria-controls="collapseActivity">
                            Activity
                        </button>
                    </span>
                    <div id="collapseActivity"
                         class="accordion-collapse collapse show"
                         aria-labelledby="activityHeading"
                         data-bs-parent="#activityAccordion">
                        <div class="accordion-body">
                            @if (!string.IsNullOrEmpty(Data.Id))
                            {
                                <ActivityTimeline T="CustomerActivity" LoadData="LoadActivity" @ref=activityTimeline />
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="details-col">
        @if(task != null)
        {
            <div class="card">
                <div class="app_details">
                    <ul class="nav nav-tabs" id="fieldsTab" role="tablist">

                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="taskinfo-tab"
                                    data-bs-toggle="tab" data-bs-target="#taskinfo"
                                    type="button" role="tab"
                                    aria-controls="taskinfo"
                                    aria-selected="true">
                                Task Info
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="taskactions-tab"
                                    data-bs-toggle="tab" data-bs-target="#taskactions"
                                    type="button" role="tab"
                                    aria-controls="taskactions"
                                    aria-selected="false">
                                Task Actions
                            </button>
                        </li>
                    </ul>
                    <div class="tab-content" id="myTabContent">

                        <div class="tab-pane fade  show active" id="taskinfo"
                             role="tabpanel" aria-labelledby="taskinfo-tab">

                            <div class="detail-wrapper grid-4 grid-tab-3">
                                <div class="detail-item">
                                    <span class="label">Assigned To</span>
                                    <span class="value">@workTaskVM.AssignedToFullName</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">Assigned On</span>
                                    <span class="value"><LocalTime Value="task.Assigned" /></span>
                                </div>

                                <div class="detail-item">
                                    <span class="label">Opened On</span>
                                    <span class="value"><LocalTime Value="task.Opened" /></span>
                                </div>
                                @if(task.IsEscalated)
                                {
                                    <div class="detail-item">
                                        <span class="label">Escalated By</span>
                                        <span class="value">@workTaskVM.EscalatedByFullName</span>
                                    </div>

                                    <div class="detail-item">
                                        <span class="label">Escalation Reason</span>
                                        <span class="value">@task.EscalationReason</span>
                                    </div>
                                }
                                <div class="detail-item">
                                    <span class="label">Segment</span>
                                    <span class="value">@workTaskVM.DebtSegmentName</span>
                                </div>

                                <div class="detail-item">
                                    <span class="label">Segment Priority</span>
                                    <span class="value">

                                        <span class="lz __@(workTaskVM.DebtSegmentPriority == DebtSegmentPriority.Low ? "green" : workTaskVM.DebtSegmentPriority == DebtSegmentPriority.Medium ? "yellow" : "red")">
                                            @workTaskVM.DebtSegmentPriority.GetDisplayName()
                                        </span>
                                        
                                    </span>
                                </div>

                                @foreach (var rule in workTaskVM.Rules)
                                {
                                    <div class="detail-item">
                                        <span class="label">Segment Rule: @rule.Name</span>
                                        <span class="value">@rule.Instruction</span>
                                    </div>
                                }
                            </div>
                        </div>

                        <div class="tab-pane fade" id="taskactions"
                             role="tabpanel" aria-labelledby="taskactions-tab">


                            <div class="table-wrapper">
                                @if (isAssignedUser && !ViewMode)
                                {
                                    <div class="search-wrapper flex">
                                        <button type="button"
                                                class="btn btn--primary w_border btn__sm __icon"
                                                data-bs-toggle="modal" data-bs-target="#@(RecordWorkflowActionModalName)" @onclick="() => LoadTaskNotes()">
                                            <i data-feather="edit"></i> Record Action
                                        </button>
                                        <button type="button"
                                                class="btn btn--default w_border btn__sm __icon"
                                                data-bs-toggle="modal" data-bs-target="#@(WorkflowEscalateModalName)" @onclick="() => LoadEscalationUsers()">
                                            <i data-feather="arrow-up-right"></i> Escalate
                                        </button>
                                        <button type="button"
                                                class="btn btn--danger w_border btn__sm __icon"
                                                data-bs-toggle="modal" data-bs-target="#@(FinishWorkflowModalName)" @onclick="() => ConfirmFinishWorkflow()">
                                            <i data-feather="x-circle"></i> Finish
                                        </button>
                                    </div>
                                }
                                <div class="overflow-wrap">
                                    <table class="table table-responsive-lg  table-borderless repay-table">
                                        <thead>
                                            <tr>
                                                <th>Action</th>
                                                <th>Has Note</th>
                                                <th class="right">Amount Collected</th>
                                                <th>Call Back Date</th>
                                                <th>Performed On</th>   
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in task.Tasks)
                                            {
                                                <tr class="table-row">
                                                    <td>
                                                        @item.Action.GetDisplayName()
                                                    </td>
                                                    <td>
                                                        @(item.NoteId.HasValue ? "Yes" : "No")
                                                    </td>
                                                    <td class="right">@($"{Data?.CurrencySymbol}{item.Amount:n2}")</td>
                                                    <td>
                                                        <LocalTime Value="item.NextCallbackDate"/>
                                                    </td>
                                                    <td>
                                                        <LocalTime Value="item.When" />
                                                    </td>
                                                    
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-footer flex __justify-between">
                                    <span class="records">
                                        @task.Tasks.Count
                                        records
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        }
        <div class="card">
            <div class="accordion loan-details" id="loanAccordion">

                <div class="accordion-item">
                    <h2 class="accordion-header" id="flagsHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#flagsCollapse" aria-expanded="false"
                                aria-controls="flagsCollapse" @onclick="() => flagsTable.LoadElement()">
                            <i data-feather="flag"></i>
                            Flags
                        </button>
                    </h2>
                    <div id="flagsCollapse" class="accordion-collapse collapse"
                         aria-labelledby="flagsHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerFlagVM" DeferLoading="true" DeleteRow="DeleteFlag" LoadData="LoadFlags" @ref="flagsTable" />
                        </div>
                    </div>
                </div>


                <div class="accordion-item">
                    <h2 class="accordion-header" id="viewAddressHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#viewAddressCollapse" aria-expanded="false"
                                aria-controls="viewAddressCollapse">
                            <i data-feather="align-center"></i>
                            Addresses
                        </button>
                    </h2>
                    <div id="viewAddressCollapse" class="accordion-collapse collapse"
                         aria-labelledby="viewAddressHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <div class="detail-wrapper grid-4 grid-tab-3">
                                @foreach (var item in Data.Addresses)
                                {

                                    <div class="detail-item">
                                        <span class="value">@((MarkupString)item.ToString())</span>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="contactDetailsHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#contactDetailsCollapse" aria-expanded="false"
                                aria-controls="contactDetailsCollapse" @onclick="() => contactsTable.LoadElement()">
                            <i data-feather="book-open"></i>
                            Manage Contact Details
                        </button>
                    </h2>
                    <div id="contactDetailsCollapse" class="accordion-collapse collapse"
                         aria-labelledby="contactDetailsHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerContactDetail" DeferLoading="true"  DeleteRow="DeleteContact" LoadData="LoadContacts" @ref="contactsTable" />
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="viewIncomeAndExpenditureHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#viewIncomeAndExpenditureCollapse" aria-expanded="false"
                                aria-controls="viewIncomeAndExpenditureCollapse">
                            <i data-feather="align-center"></i>
                            Income And Expenditure
                        </button>
                    </h2>
                    <div id="viewIncomeAndExpenditureCollapse" class="accordion-collapse collapse"
                         aria-labelledby="viewIncomeAndExpenditureHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">

                            <div class="detail-wrapper grid-4 grid-tab-3">
                                <div class="detail-item">
                                    <span class="label">
                                        Full Name
                                    </span>
                                    <span class="value">@Data.IncomeAndExpenditure?.FullName</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">Phone Number</span>
                                    <span class="value">@Data.IncomeAndExpenditure?.PhoneNumber.ToString()</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">
                                       Address
                                    </span>
                                    <span class="value">@Data.IncomeAndExpenditure?.Address</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">
                                        Total Monthly Income
                                    </span>
                                    <span class="value">@Data.CurrencySymbol @Data.IncomeAndExpenditure?.TotalIncome.ToString("n2")</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">
                                        Total Monthly Expenditure
                                    </span>
                                    <span class="value">@Data.CurrencySymbol @Data.IncomeAndExpenditure?.TotalExpenditure.ToString("n2")</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">
                                        Net Disposable Income
                                    </span>
                                    <span class="value">@Data.CurrencySymbol @Data.IncomeAndExpenditure?.NetDisposableIncome.ToString("n2")</span>
                                </div>
                                <div class="detail-item">
                                    <span class="label">
                                        Created On
                                    </span>
                                    <span class="value"><LocalTime Value="Data.IncomeAndExpenditure?.CreatedDate" /></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="viewPlacementHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#viewPlacementCollapse" aria-expanded="false"
                                aria-controls="viewPlacementCollapse" @onclick="() => placementTable.LoadElement()">
                            <i data-feather="align-center"></i>
                            Placements
                        </button>
                    </h2>
                    <div id="viewPlacementCollapse" class="accordion-collapse collapse"
                         aria-labelledby="viewPlacementHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="PlacementInProfileVM" DeferLoading="true" TableDefinition="PlacementTableDefinition" LoadData="LoadPlacements" @ref="placementTable" />
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="repayHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#repayCollapse" aria-expanded="false"
                                aria-controls="repayCollapse">
                            <i data-feather="calendar"></i>
                            Repayment Schedule
                        </button>
                    </h2>
                    <div id="repayCollapse" class="accordion-collapse collapse"
                         aria-labelledby="repayHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <div class="table-wrapper">
                                <div class="d-flex align-items-center">
                                    <AuthorizeView Policy="@ManageCustomersNavigation.CustomerProfileCreateSchedulePermission">
                                        @if (Data.Schedules.Count == 0)
                                        {
                                            <button type="button"
                                                    class="btn btn--default w_border btn__sm __icon me-2"
                                                    data-bs-toggle="modal" data-bs-target="#@(CreateScheduleModalName)">
                                                <i data-feather="edit"></i> Create Schedule
                                            </button>
                                        }
                                    </AuthorizeView>
                                    <AuthorizeView Policy="@ManageCustomersNavigation.CustomerProfileUpdateSchedulePermission">
                                        @if (Data.Schedules.Any(x => ScheduleHelper.UnsettledStatus.HasFlag(x.PaymentStatus)))
                                        {
                                            <button type="button"
                                                    class="btn btn--primary w_border btn__sm __icon me-2"
                                                    data-bs-toggle="modal" data-bs-target="#@(UpdateScheduleModalName)">
                                                <i data-feather="edit"></i> Update Schedule
                                            </button>
                                        }
                                    </AuthorizeView>

                                    <AuthorizeView Policy="@ManageCustomersNavigation.CustomerProfileDeleteSchedulePermission">
                                        @if (Data.Schedules.Count > 0 && !Data.Schedules.Any(x => x.PaymentStatus == SchedulePaymentStatus.Paid || x.PaymentStatus == SchedulePaymentStatus.PartiallyPaid))
                                        {
                                            <button type="button"
                                                    class="btn btn--danger w_border btn__sm __icon"
                                                    data-bs-toggle="modal" data-bs-target="#DeleteScheduleModal">
                                                <i data-feather="trash-2"></i> Delete Schedule
                                            </button>
                                        }
                                    </AuthorizeView>

                                </div>

                                <div class="overflow-wrap">
                                    <table class="table table-responsive-lg  table-borderless repay-table">
                                        <thead>
                                            <tr>
                                                <th>S/N</th>
                                                <th>Month/Year</th>
                                                <th class="right">Amount</th>
                                                <th class="right">Amount Paid</th>
                                                <th class="right">Balance</th>
                                                <th>Due Date</th>
                                                <th>CPA Date</th>
                                                <th>Period Status</th>
                                                <th>Payment Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var item in Data.Schedules.OrderBy(x => x.Period))
                                            {
                                                <tr class="table-row">
                                                    <td>
                                                        @item.Period
                                                    </td>
                                                    <td>
                                                        @item.DueDate.ToString("MM/yyyy")
                                                    </td>
                                                    <td class="right">@($"{Data?.CurrencySymbol}{item.Amount:n2}")</td>
                                                    <td class="right">@($"{Data?.CurrencySymbol}{item.AmountPaid:n2}")</td>
                                                    <td class="right">@($"{Data?.CurrencySymbol}{item.Balance:n2}")</td>
                                                    <td>
                                                        @item.DueDate.ToMidDateFormat()
                                                    </td>
                                                    <td>
                                                        @item.CPADate.ToMidDateFormat()
                                                    </td>
                                                    <td>
                                                        <span class="lz __@(item.PeriodStatus == SchedulePeriodStatus.NotDue ? "green" : item.PeriodStatus == SchedulePeriodStatus.Due ? "yellow" : "red")">@item.PeriodStatus.GetDisplayName()</span>
                                                    </td>
                                                    <td>
                                                        <span class="lz __@(item.PaymentStatus == SchedulePaymentStatus.Paid ? "green" : item.PaymentStatus == SchedulePaymentStatus.NotPaid ? "red" : "yellow")">@item.PaymentStatus.GetDisplayName()</span>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                                <div class="table-footer flex __justify-between">
                                    <span class="records">
                                        @Data.Schedules.Count()
                                        records
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>


                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="viewTxnHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#viewTxnCollapse" aria-expanded="false"
                                aria-controls="viewTxnCollapse" @onclick="() => transactionsTable.LoadElement()">
                            <i data-feather="align-center"></i>
                            Transactions
                        </button>
                    </h2>
                    <div id="viewTxnCollapse" class="accordion-collapse collapse"
                         aria-labelledby="viewTxnHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerTransaction" DeferLoading="true" TableDefinition="CrudService.GetTableDefinition<CustomerTransaction>(new())" LoadData="LoadTxns" @ref="transactionsTable" />
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="messageTemplateHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#messageTemplateCollapse" aria-expanded="false"
                                aria-controls="messageTemplateCollapse" @onclick="() => messagesTable.LoadElement()">
                            <i data-feather="mail"></i>
                            Messages
                        </button>
                    </h2>
                    <div id="messageTemplateCollapse" class="accordion-collapse collapse"
                         aria-labelledby="messageTemplateHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerMessageEntryVM" DeferLoading="true" LoadData="LoadCustomerMessages" @ref="messagesTable" />
                        </div>
                    </div>
                </div>

                <div class="accordion-item">
                    <h2 class="accordion-header" id="notesHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#notesCollapse" aria-expanded="false"
                                aria-controls="notesCollapse" @onclick="() => notesTable.LoadElement()">
                            <i data-feather="book-open"></i>
                            Notes
                        </button>
                    </h2>
                    <div id="notesCollapse" class="accordion-collapse collapse"
                         aria-labelledby="notesHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerNoteVM" DeferLoading="true" EditRow="StartEditNote" DeleteRow="DeleteNote" LoadData="LoadNotes" @ref="notesTable" />
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="ptpHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#ptpCollapse" aria-expanded="false"
                                aria-controls="ptpCollapse" @onclick="() => ptpTable.LoadElement()">
                            <i data-feather="bookmark"></i>
                            Promises To Pay
                        </button>
                    </h2>
                    <div id="ptpCollapse" class="accordion-collapse collapse"
                         aria-labelledby="ptpHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerPromiseToPayVM" DeferLoading="true" DeleteRow="DeletePTP" LoadData="LoadPTP" @ref="ptpTable" />
                        </div>
                    </div>
                </div>
                <div class="accordion-item">
                    <h2 class="accordion-header" id="discountsHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#discountsCollapse" aria-expanded="false"
                                aria-controls="discountsCollapse" @onclick="() => discountTable.LoadElement()">
                            <i data-feather="gift"></i>
                            Discounts
                        </button>
                    </h2>
                    <div id="discountsCollapse" class="accordion-collapse collapse"
                         aria-labelledby="discountsHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerDiscountVM" DeferLoading="true"  LoadData="LoadDiscount" @ref="discountTable" />
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="placementStatusHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#placementStatusCollapse" aria-expanded="false"
                                aria-controls="placementStatusCollapse" @onclick="() => statusChangeTable.LoadElement()">
                            <i data-feather="repeat"></i>
                            Placement Status Changes
                        </button>
                    </h2>
                    <div id="placementStatusCollapse" class="accordion-collapse collapse"
                         aria-labelledby="placementStatusHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="PlacementStatusChangeVM" DeferLoading="true"  LoadData="LoadStatusChange" @ref="statusChangeTable" />
                        </div>
                    </div>
                </div>
                
                <div class="accordion-item">
                    <h2 class="accordion-header" id="holdHeader">
                        <button class="accordion-button collapsed" type="button"
                                data-bs-toggle="collapse"
                                data-bs-target="#holdCollapse" aria-expanded="false"
                                aria-controls="holdCollapse" @onclick="() => holdTable.LoadElement()">
                            <i data-feather="anchor"></i>
                            Manage Account Hold
                        </button>
                    </h2>
                    <div id="holdCollapse" class="accordion-collapse collapse"
                         aria-labelledby="holdHeader"
                         data-bs-parent="#loanAccordion">
                        <div class="accordion-body">
                            <DataTable T="CustomerHoldVM" DeferLoading="true" DeleteRow="DeleteHold"  LoadData="LoadHold" @ref="holdTable" />
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

@if(isAssignedUser)
{
    <ModalEditComponentWithoutPolicy ModalId="@RecordWorkflowActionModalName" Title="Record Workflow Action"
                                     ModalMessage="@ModalMessage" Model="@RecordWorkflowActionModel" OnValidSubmit="@RecordWorkflowAction"
                    ModalCss="width-md">
    <BodyContent>

            <div class="form-row">
                <label class="form-label" for="Action">Action</label>
                <RadzenDropDown @bind-Value=@context.Action Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<WorkflowTaskAction>([WorkflowTaskAction.Escalated]))
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="Priority" AllowClear=true Placeholder="Select action" class="form-input" />
                <ValidationMessage For="() => context.Action" class="text-danger" />
            </div>

            <div class="form-row">
                <label class="form-label" for="NoteId">Note</label>
                <RadzenDropDown @bind-Value=@context.NoteId Data=@workflowNotes
                                TextProperty="@nameof(RequiredNoteVM.Note)" ValueProperty="@nameof(RequiredNoteVM.Id)"
                                Name="NoteId" AllowClear=true Placeholder="Select applicable note" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive"
                                FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
                <ValidationMessage For="() => context.NoteId" class="text-danger" />
            </div>

            @if(context.Action == WorkflowTaskAction.PaymentCollected)
            {
                <div class="form-row">
                    <label class="form-label" for="PaymentId">Applied Payment</label>
                    <RadzenDropDown @bind-Value=@context.PaymentId Data=@workflowPayments
                                    TextProperty="@nameof(AgentWorkflowPaymentVM.AmountFormatted)" ValueProperty="@nameof(AgentWorkflowPaymentVM.Id)"
                                    Name="PaymentId" Placeholder="Select applied payment" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive"
                                    FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
                    <ValidationMessage For="() => context.PaymentId" class="text-danger" />
                </div>
            }
            
            @if(context.Action == WorkflowTaskAction.PTPSetup)
            {
                <div class="form-row">
                    <label class="form-label" for="PtpId">PTP</label>
                    <RadzenDropDown @bind-Value=@context.PtpId Data=@workflowPtps
                                    TextProperty="@nameof(AgentWorkflowPtpVM.AmountFormatted)" ValueProperty="@nameof(AgentWorkflowPtpVM.Id)"
                                    Name="PtpId" Placeholder="Select promise to pay" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive"
                                    FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
                    <ValidationMessage For="() => context.PtpId" class="text-danger" />
                </div>
            }

            @if (context.Action == WorkflowTaskAction.CallBack)
            {
                <div class="form-row">
                    <label class="form-label" for="NextCallbackDate">Next Callback Date</label>
                    <RadzenDatePicker @bind-Value=@context.NextCallbackDate class="form-input" Name="NextCallbackDate"
                                      Placeholder="Select the date for callback" />
                    <ValidationMessage For="() => context.NextCallbackDate" class="text-danger" />
                </div>
            }
        </BodyContent>
    </ModalEditComponentWithoutPolicy>

    <ModalEditComponentWithoutPolicy ModalId="@WorkflowEscalateModalName" Title="Escalate Account"
                                     ModalMessage="@ModalMessage" Model="@WorkflowEscalateModel" OnValidSubmit="@EscalateWorkflow"
                                     ModalCss="width-md">
        <BodyContent>

            <div class="form-row">
                <label class="form-label" for="EscalateTo">Escalate To</label>
                <RadzenDropDown @bind-Value=@context.EscalateTo Data=@workflowEscalationUsers
                                TextProperty="@nameof(AgentUserVM.Name)" ValueProperty="@nameof(AgentUserVM.Id)"
                                Name="EscalateTo" Placeholder="Select user" class="form-input" FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive"
                                FilterOperator="Radzen.StringFilterOperator.Contains" AllowFiltering="true" />
                <ValidationMessage For="() => context.EscalateTo" class="text-danger" />
            </div>

            <div class="form-row">
                <label class="form-label" for="Reason">Reason</label>
                <InputTextArea @bind-Value="context.Reason" />
                <ValidationMessage For="() => context.Reason" class="text-danger" />
            </div>
        </BodyContent>
    </ModalEditComponentWithoutPolicy>


    <ModalComponentWithoutPolicy ModalId="@FinishWorkflowModalName" ModalCss="width-xlg" Title="Finish Workflow">
        <BodyContent>
            @finishWorkflowMessage
        </BodyContent>
        <FooterContent>
            <button class="btn btn--default" type="button" data-bs-dismiss="modal">No</button>
            <LoadButton Label="Yes" OnClick="FinishWorkflow" />
        </FooterContent>
    </ModalComponentWithoutPolicy>
}

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileTakePaymentPermission" ModalId="@InitiatePaymentModalName" Title="Make Payment"
                    ModalMessage="@ModalMessage" Model="@InitiatePaymentVM" OnValidSubmit="@InitiateMakePayment"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Amount">Amount</label>
            <InputNumber @bind-Value="context.Amount" class="form-input" aria-required="true" placeholder="Amount" min="1" />
            <ValidationMessage For="() => context.Amount" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Type">Payment Type</label>

            <RadzenDropDown @bind-Value=@context.Type Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<RepaymentTransactionType>(Data.Schedules.Count == 0 ? 
                [RepaymentTransactionType.ReduceBalance, RepaymentTransactionType.AmendSchedule] : [RepaymentTransactionType.AmendSchedule]))
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Type" Placeholder="Select Payment Type" class="form-input" />
            <ValidationMessage For="() => context.Type" class="text-danger" />
        </div>
        

        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Send Payment Link To Customer
                    <InputCheckbox class="check-input" @bind-Value="context.NotifyCustomer" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileCreateSchedulePermission" ModalId="@CreateScheduleModalName" Title="Create Schedule"
                    ModalMessage="@ModalMessage" Model="@UpdateScheduleModel" OnValidSubmit="@SubmitCreateSchedule"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Frequency">Payment Frequency</label>

            <RadzenDropDown @bind-Value=@context.Frequency Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<SchedulePaymentFrequency>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Frequency" Placeholder="Select Schedule Frequency" class="form-input" />
            <ValidationMessage For="() => context.Frequency" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="StartDate">Start Date</label>
            <RazdenNullableDateTimePicker @bind-DateValue="context.StartDate" Placeholder="Start Date" Name="StartDate" />
            <ValidationMessage For="() => context.StartDate" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Amount">Amount</label>
            <InputNumber @bind-Value="context.Amount" class="form-input" aria-required="true" placeholder="Amount" min="1" />
            <ValidationMessage For="() => context.Amount" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>


<ModalEditComponent Policy="@(ManageCustomersNavigation.CustomerProfileCreateSchedulePermission + "," + ManageCustomersNavigation.CustomerProfileTakePaymentPermission)" ModalId="@ConfirmPaymentModalName" Title="Confirm Payment"
                    ModalMessage="@ModalMessage" Model="@ConfirmPaymentVM" OnValidSubmit="@SubmitConfirmPayment"
                    ModalCss="width-md">
    <BodyContent>

        @if(InitiatePaymentVM.NotifyCustomer)
        {
            <p>Ask the customer to check their mail/sms, follow the link and either add their card or make a payment. After this is done click Save to finish. </p>
        }
        <p>If the customer already has a card added, you can choose the card as the payment method and click Save to finish</p>

        <div class="detail-wrapper grid-2">
            <div class="detail-item">
                <span class="label">Type</span>
                <span class="value">@ScheduleResponseVM.Type</span>
            </div>
            <div class="detail-item">
                <span class="label">Amount</span>
                <span class="value">@ScheduleResponseVM.CurrencySymbol @ScheduleResponseVM.Amount.ToString("n2")</span>
            </div>
            <div class="detail-item">
                <span class="label">Balance After Payment</span>
                <span class="value">@ScheduleResponseVM.CurrencySymbol @ScheduleResponseVM.BalanceAfterPayment.ToString("n2")</span>
            </div>
            <div class="detail-item">
                <span class="label">Start Date</span>
                <span class="value"><LocalTime Value="ScheduleResponseVM.StartDate" Format="dd MMM, yyyy" /></span>
            </div>
            

        </div>
        <br />

        <div class="detail-wrapper grid-1">

            <div class="detail-item">
                <span class="label">Payment Link</span>
                <span class="value"><a href="@ScheduleResponseVM.Txn.ProviderId" target="_blank">Open Payment Link</a></span>
                <small class="text_sm_medium text_small">This has been sent to the customer either by email or sms. DO NOT OPEN unless you are taking the customer card details over the phone to make the payment on their behalf</small>
            </div>
        </div>
        <br/>
        <div class="form-row">
            <label class="form-label" for="PaymentMethodId">Payment Method</label>

            <RadzenDropDown @bind-Value=@context.PaymentMethodId Data=@ScheduleResponseVM.Txn.PaymentMethods
                            TextProperty="@nameof(SavedPaymentMethods.DisplayValue)" ValueProperty="@nameof(SavedPaymentMethods.Id)"
                            Name="PaymentMethodId" Placeholder="Select Payment Method" class="form-input" />
            <ValidationMessage For="() => context.PaymentMethodId" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>



<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileUpdateSchedulePermission" ModalId="@UpdateScheduleModalName" Title="Update Schedule" 
ModalMessage="@ModalMessage" Model="@UpdateScheduleModel" OnValidSubmit="@SubmitUpdateSchedule"
                    ModalCss="width-md">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Frequency">Payment Frequency</label>

            <RadzenDropDown @bind-Value=@context.Frequency Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<SchedulePaymentFrequency>())
                            TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                            Name="Frequency" Placeholder="Select Schedule Frequency" class="form-input" />
            <ValidationMessage For="() => context.Frequency" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="StartDate">Start Date</label>
            <RazdenNullableDateTimePicker @bind-DateValue="context.StartDate" Placeholder="Start Date" Name="StartDate" />
            <ValidationMessage For="() => context.StartDate" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Amount">Amount</label>
            <InputNumber @bind-Value="context.Amount" class="form-input" aria-required="true" placeholder="Amount" min="1" />
            <ValidationMessage For="() => context.Amount" class="text-danger" />
        </div>
    </BodyContent>
</ModalEditComponent>

<ModalComponent Policy="@ManageCustomersNavigation.CustomerProfileDeleteSchedulePermission" 
                         ModalId="DeleteScheduleModal" 
                         Title="Delete Schedule">
    <BodyContent>
        <p>Are you sure you want to delete all unpaid schedules? This action cannot be undone.</p>
    </BodyContent>
    <FooterContent>
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" @onclick="DeleteCustomerSchedules" data-bs-dismiss="modal">Delete</button>
    </FooterContent>
</ModalComponent>

<ModalComponentWithoutPolicy ModalId="@PreviewMessageModal" ModalCss="modal-dialog-large width-70vw" Title=@($"Preview Message For \"{previewMessageModel.Name}\"") >
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Description">Subject</label>
            <input type="text" class="form-input" value="@previewMessageModel.Subject" aria-required="true" readonly />
        </div>
        @if (previewMessageModel.TextRequired)
        {
            <div class="form-row">
                <label class="form-label" for="Description">Text Template</label>
                <textarea aria-required="true" readonly>
                    @previewMessageModel.TextTemplate
                </textarea>
            </div>
        }

        @if (previewMessageModel.EmailRequired)
        {
            <div class="form-row">
                <RadzenHtmlEditor @bind-Value=@previewMessageModel.HtmlTemplate style="height: 50vh" ShowToolbar="false" Disabled="true" />
            </div>
        }
    </BodyContent>
</ModalComponentWithoutPolicy>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileViewAddNotePermission" Model="@AddNoteModel" OnValidSubmit="@SubmitNewNote" FormName="@AddNoteModal" Title="New Note"
                                 ModalId="@AddNoteModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        @NoteFormContent(context)
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileViewEditNotePermission" Model="@EditNoteModel" OnValidSubmit="@SubmitEditNote" FormName="@EditNoteModal" Title="Modify Note"
                                 ModalId="@EditNoteModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        @NoteFormContent(context)
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileViewAddContactPermission" Model="@AddContactModel" OnValidSubmit="@SubmitNewContact" FormName="@AddContactModal" Title="Add Contact"
                    ModalId="@AddContactModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Email">Email</label>
            <InputText @bind-Value="context.Email" class="form-input" aria-required="true" placeholder="Email" />
            <ValidationMessage For="() => context.Email" class="text-danger" />
        </div>
        <div class="grid-col-2">

            <div class="form-row">
                <label class="form-label" for="PhoneCode">Phone Code</label>
                <InputText @bind-Value="context.PhoneCode" class="form-input" aria-required="true" placeholder="Phone Code" />
                <ValidationMessage For="() => context.PhoneCode" class="text-danger" />
            </div>

            <div class="form-row">
                <label class="form-label" for="Number">Phone Number</label>
                <InputText @bind-Value="context.Number" class="form-input" aria-required="true" placeholder="Number" />
                <ValidationMessage For="() => context.Number" class="text-danger" />
            </div>
        </div>
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Mark as Preferred
                    <InputCheckbox class="check-input" @bind-Value="context.Preferred" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileViewAddFlagPermission" Model="@AddFlagModel" OnValidSubmit="@SubmitNewFlag" FormName="@AddFlagModal" Title="Add Flag"
                    ModalId="@AddFlagModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Flag">Flag</label>
            <RadzenDropDown @bind-Value=@context.FlagId Data=@flagTemplates
                            TextProperty="@nameof(CustomerFlagTemplate.Flag)" ValueProperty="@nameof(CustomerFlagTemplate.Id)"
                            Name="Flag" Placeholder="Select flag" class="form-input" />
            <ValidationMessage For="() => context.Flag" class="text-danger" />
        </div>
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileViewAddPtpPermission" Model="@AddPtpModel" OnValidSubmit="@SubmitNewPTP" FormName="@AddPtpModal" Title="Add Promise To Pay"
                    ModalId="@AddPtpModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Amount">Amount</label>
            <InputNumber @bind-Value="context.Amount" class="form-input" aria-required="true" placeholder="Amount" />
            <ValidationMessage For="() => context.Amount" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="DueDate">Due Date</label>
            <RazdenInstantDatePicker @bind-DateValue="context.DueDate" Name="DueDate" placeholder="Due Date" />
            <ValidationMessage For="() => context.DueDate" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Placement">Note</label>
            <RadzenDropDown @bind-Value=@context.NoteId Data=@ptpNotesVM
                            TextProperty="@nameof(RequiredNoteVM.Note)" ValueProperty="@nameof(RequiredNoteVM.Id)"
                            Name="Type" Placeholder="Select note" class="form-input" />
            <ValidationMessage For="() => context.NoteId" class="text-danger" />
        </div>
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileViewAddDiscountPermission" Model="@AddDiscountModel" OnValidSubmit="@SubmitNewDiscount" FormName="@AddDiscountModal" Title="Add Discount"
                    ModalId="@AddDiscountModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Placement">Placement</label>
            <RadzenDropDown @bind-Value=@context.PlacementId Data=@placementsDropDown.Where(x => x.Id.HasValue && x.Status < PlacementStatus.Settled)
                            TextProperty="@nameof(CustomerDropDownPlacementVM.Info)" ValueProperty="@nameof(CustomerDropDownPlacementVM.Id)"
                            Name="Type" Placeholder="Select placement" class="form-input" />
            <ValidationMessage For="() => context.Placement" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Discount">Discount</label>
            <InputNumber @bind-Value="context.Discount" class="form-input" aria-required="true" placeholder="Discount" />
            <ValidationMessage For="() => context.Discount" class="text-danger" />
            @if(allowedDiscount != null)
            {
                if(context.PlacementId > 0)
                {
                    <small class="text_sm_medium text_small">Maximum allowed @allowedDiscount.PercentageLimit% which is 
                        @Data.CurrencySymbol @((placementsDropDown.FirstOrDefault(x => x.Id == context.PlacementId).BalanceRemaining * (allowedDiscount.PercentageLimit / 100m)).ToString("n2"))</small>
                }
                else
                {
                    <small class="text_sm_medium text_small">Maximum allowed @allowedDiscount.PercentageLimit%</small>                    
                }
            }
        </div>
        <div class="form-row">
            <label class="form-label" for="Reason">Reason</label>
            <InputText @bind-Value="context.Reason" class="form-input" aria-required="true" placeholder="Reason" />
            <ValidationMessage For="() => context.Reason" class="text-danger" />
        </div>
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileChangePlacementStatusPermission" Model="@AddStatusChangeModel" OnValidSubmit="@SubmitNewStatusChange" FormName="@AddStatusChangeModal" Title="Change Placement Status"
                    ModalId="@AddStatusChangeModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Placement">Placement</label>
            <RadzenDropDown @bind-Value=@context.PlacementId Data=@placementsDropDown.Where(x => x.Id.HasValue)
                            TextProperty="@nameof(CustomerDropDownPlacementVM.Info)" ValueProperty="@nameof(CustomerDropDownPlacementVM.Id)"
                            Name="Type" Placeholder="Select placement" class="form-input" />
            <ValidationMessage For="() => context.Placement" class="text-danger" />
        </div>
        @if(context.PlacementId > 0)
        {
            <div class="form-row">
                <label class="form-label" for="ToStatus">New Status</label>
                <RadzenDropDown @bind-Value=@context.ToStatus Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<PlacementStatus>([placementsDropDown.FirstOrDefault(x => x.Id == context.PlacementId).Status]))
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="ToStatus" Placeholder="Select new status" class="form-input" />
                <ValidationMessage For="() => context.ToStatus" class="text-danger" />
            </div>
        }
        @if (statusChangeReasons.Where(x => x.Status == context.ToStatus).Count() > 0)
        {
            <div class="form-row">
                <label class="form-label" for="Reason">Reason</label>
                <RadzenDropDown @bind-Value=@context.ReasonId Data=@statusChangeReasons.Where(x => x.Status == context.ToStatus)
                                TextProperty="@nameof(PlacementStatusChangeReasonConfig.Description)" ValueProperty="@nameof(PlacementStatusChangeReasonConfig.Id)"
                                Name="Type" Placeholder="Select reason" class="form-input" />
                <ValidationMessage For="() => context.ReasonId" class="text-danger" />
            </div>
        }
        <div class="form-row">
            <label class="form-label" for="Comment">Comment</label>
            <InputTextArea @bind-Value="context.Comment" />
            <ValidationMessage For="() => context.Comment" class="text-danger" />
        </div>
    </BodyContent>

</ModalEditComponent>

<ModalEditComponent Policy="@ManageCustomersNavigation.CustomerProfileAddHoldPermission" Model="@AddHoldModel" OnValidSubmit="@SubmitNewHold" FormName="@AddHoldModal" Title="Add Hold"
                    ModalId="@AddHoldModal" ModalMessage="ModalMessage" ModalCss="width-lg">
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="Placement">Placement</label>
            <RadzenDropDown @bind-Value=@context.PlacementId Data=@placementsDropDown
                            TextProperty="@nameof(CustomerDropDownPlacementVM.Info)" ValueProperty="@nameof(CustomerDropDownPlacementVM.Id)"
                            Name="Placement" Placeholder="Select placement" class="form-input" />
            <ValidationMessage For="() => context.Placement" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Hold">Hold</label>
            <RadzenDropDown @bind-Value=@context.HoldConfigId Data=@holdConfig
                            TextProperty="@nameof(HoldConfig.Reason)" ValueProperty="@nameof(HoldConfig.Id)"
                            Name="Hold" Placeholder="Select hold config" class="form-input" />
            <ValidationMessage For="() => context.Hold" class="text-danger" />
        </div>
        @if(context.HoldConfigId > 0 && holdConfig.Any(x => x.Id == context.HoldConfigId && x.AllowDefaultOverride))
        {
            <div class="form-row">
                <div class="check-group">
                    <label class="check-label">
                        Override Config Defaults
                        <InputCheckbox class="check-input" @bind-Value="context.HoldDefaultsOverriden" />
                        <span class="checkmark"></span>
                    </label>
                </div>
            </div>
        }
        @if(context.HoldDefaultsOverriden)
        {
            <div class="form-row">
                <label class="form-label" for="Action">Action</label>
                <RadzenDropDown @bind-Value=@context.ActionList Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<HoldAction>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="Action" Multiple=true AllowClear=true Placeholder="Select action" Chips=true class="form-input"
                                FilterCaseSensitivity="Radzen.FilterCaseSensitivity.CaseInsensitive" FilterOperator="Radzen.StringFilterOperator.StartsWith" AllowFiltering="true" />
                <ValidationMessage For="() => context.ActionList" class="text-danger" />
            </div>
            <div class="form-row">
                <label class="form-label" for="ExpiresOn">Expires On</label>
                <InputText type="text" @bind-Value="context.Expiry" class="form-input hasDate" aria-required="true" placeholder="Expires On" autocomplete="off" />
                <ValidationMessage For="() => context.ExpiresOn" class="text-danger" />
            </div>
        }
        <div class="form-row">
            <label class="form-label" for="Comment">Comment</label>
            <InputTextArea @bind-Value="context.Comment" />
            <ValidationMessage For="() => context.Comment" class="text-danger" />
        </div>
    </BodyContent>

</ModalEditComponent>


@code{
    private RenderFragment<CustomerNote> NoteFormContent => context => @<div>
        <div class="form-row">
            <label class="form-label" for="Placement">Placement</label>
            <RadzenDropDown @bind-Value=@context.PlacementId Data=@placementsForNote
                            TextProperty="@nameof(CustomerDropDownPlacementVM.Info)" ValueProperty="@nameof(CustomerDropDownPlacementVM.Id)"
                            Name="Type"  Placeholder="Select placement" class="form-input" />
            <ValidationMessage For="() => context.Placement" class="text-danger" />
        </div>
        <div class="grid-col-2">
            <div class="form-row">
                <label class="form-label" for="Type">Type</label>
                <RadzenDropDown @bind-Value=@context.Type Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<CustomerNoteType>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="Type" Placeholder="Select note type" class="form-input" Change="() => LoadNoteTemplates(context)" />
                <ValidationMessage For="() => context.Type" class="text-danger" />
            </div>

            <div class="form-row">
                <label class="form-label" for="ContactType">Contact Type</label>
                <RadzenDropDown @bind-Value=@context.ContactType Data=@(LendQube.Entities.Core.Extensions.EnumExtensions.GetEnumValues<CustomerContactType>())
                                TextProperty="@nameof(EnumDisplayItem.Name)" ValueProperty="@nameof(EnumDisplayItem.Value)"
                                Name="ContactType"  Placeholder="Select contact type" class="form-input" Change="() => LoadNoteTemplates(context)" />
                <ValidationMessage For="() => context.ContactType" class="text-danger" />
            </div>
        </div>

        @if(!context.Templates.IsNullOrEmpty())
        {
            <div class="form-row">
            <label class="form-label" for="Templates">Templates</label>
                <RadzenDropDown @bind-Value=@context.Note Data=@context.Templates
                            TextProperty="@nameof(CustomerNoteTemplate.Name)" ValueProperty="@nameof(CustomerNoteTemplate.Template)"
                                Name="Type" Placeholder="Select note template" class="form-input" />
            </div>
        }

        <div class="form-row">
            <label class="form-label" for="Note">Note</label>
            <InputTextArea @bind-Value=context.Note rows="5"/>
            <ValidationMessage For="() => context.Note" class="text-danger" />
        </div>
    </div>;
}
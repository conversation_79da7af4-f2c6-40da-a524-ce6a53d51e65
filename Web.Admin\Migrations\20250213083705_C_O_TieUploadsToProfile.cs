﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_TieUploadsToProfile : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<long>(
                name: "FileUploadId",
                schema: "collection",
                table: "CustomerProfile",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "MessageConfigId",
                schema: "collection",
                table: "CollectionFileUpload",
                type: "bigint",
                nullable: true);

            migrationBuilder.AddColumn<long>(
                name: "FileUploadId",
                schema: "collection",
                table: "AutoDiscountConfig",
                type: "bigint",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerProfile_FileUploadId",
                schema: "collection",
                table: "CustomerProfile",
                column: "FileUploadId");

            migrationBuilder.CreateIndex(
                name: "IX_CollectionFileUpload_MessageConfigId",
                schema: "collection",
                table: "CollectionFileUpload",
                column: "MessageConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_AutoDiscountConfig_FileUploadId",
                schema: "collection",
                table: "AutoDiscountConfig",
                column: "FileUploadId");

            migrationBuilder.AddForeignKey(
                name: "FK_AutoDiscountConfig_CollectionFileUpload_FileUploadId",
                schema: "collection",
                table: "AutoDiscountConfig",
                column: "FileUploadId",
                principalSchema: "collection",
                principalTable: "CollectionFileUpload",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CollectionFileUpload_MessageConfiguration_MessageConfigId",
                schema: "collection",
                table: "CollectionFileUpload",
                column: "MessageConfigId",
                principalSchema: "core",
                principalTable: "MessageConfiguration",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CustomerProfile_CollectionFileUpload_FileUploadId",
                schema: "collection",
                table: "CustomerProfile",
                column: "FileUploadId",
                principalSchema: "collection",
                principalTable: "CollectionFileUpload",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_AutoDiscountConfig_CollectionFileUpload_FileUploadId",
                schema: "collection",
                table: "AutoDiscountConfig");

            migrationBuilder.DropForeignKey(
                name: "FK_CollectionFileUpload_MessageConfiguration_MessageConfigId",
                schema: "collection",
                table: "CollectionFileUpload");

            migrationBuilder.DropForeignKey(
                name: "FK_CustomerProfile_CollectionFileUpload_FileUploadId",
                schema: "collection",
                table: "CustomerProfile");

            migrationBuilder.DropIndex(
                name: "IX_CustomerProfile_FileUploadId",
                schema: "collection",
                table: "CustomerProfile");

            migrationBuilder.DropIndex(
                name: "IX_CollectionFileUpload_MessageConfigId",
                schema: "collection",
                table: "CollectionFileUpload");

            migrationBuilder.DropIndex(
                name: "IX_AutoDiscountConfig_FileUploadId",
                schema: "collection",
                table: "AutoDiscountConfig");

            migrationBuilder.DropColumn(
                name: "FileUploadId",
                schema: "collection",
                table: "CustomerProfile");

            migrationBuilder.DropColumn(
                name: "MessageConfigId",
                schema: "collection",
                table: "CollectionFileUpload");

            migrationBuilder.DropColumn(
                name: "FileUploadId",
                schema: "collection",
                table: "AutoDiscountConfig");
        }
    }
}

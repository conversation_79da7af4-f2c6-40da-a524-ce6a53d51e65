﻿using Coravel.Queuing.Interfaces;
using LendQube.Entities.Core.Logs;
using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Specification;
using NodaTime;

namespace LendQube.Infrastructure.Core.Telemetry;

public sealed class UserAccessService(IClock clock, IQueue queue, GenericEntityCrudVMService<UserAccessLog, UserAccessLogVM> crudService) : BaseSpecification<UserAccessLog>
{
    public Task<bool> CheckRecentIpAccess(string user, string ip, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(ip))
            return Task.FromResult(false);
        return crudService.Db.ExistsAsync<UserAccessLog>(x => x.CreatedByUserId == user && x.Status == AccessStatus.Granted && x.CreatedByIp.Equals(ip) && (clock.GetCurrentInstant() - x.CreatedDate.Value) <= Duration.FromDays(5), ct);
    }

    public void RecordAccessIP(string userId, string ip, GrantType type) => RecordAccessIp(userId, ip, type, AccessStatus.Granted);

    public void RecordFailedAccessIP(string userId, string ip, GrantType type) => RecordAccessIp(userId, ip, type, AccessStatus.Failed);

    private void RecordAccessIp(string userId, string ip, GrantType type, AccessStatus status)
    {
        if (string.IsNullOrEmpty(ip))
            return;

        var app = AppDomain.CurrentDomain.FriendlyName;
        var access = new UserAccessLog
        {
            Status = status,
            GrantType = type,
            Application = app,
            CreatedDate = clock.GetCurrentInstant(),
            CreatedByUserId = userId,
            CreatedByIp = ip,
        };

        if (app == "Web.BackgroundService")
        {
            crudService.Db.Insert(access);
            crudService.Uow.Save();
            return;
        }

        queue.QueueInvocableWithPayload<BackgroundUserAccessService, UserAccessLog>(access);
    }

    public ColumnList GetTableDefinition() => crudService.GetTableDefinition(new() { HasDateColumns = false });

    public ValueTask<TypedBasePageList<UserAccessLogVM>> GetTypeBasedPagedData(string userId, DataFilterAndPage pagedParam, CancellationToken ct)
    {
        PrimaryCriteria = x => x.CreatedByUserId == userId;
        pagedParam.OrderByColumnVM = nameof(UserAccessLogVM.CreatedDate);

        return crudService.GetTypeBasedPagedData(this, pagedParam, UserAccessLogVM.Mapping, ct: ct);
    }

}

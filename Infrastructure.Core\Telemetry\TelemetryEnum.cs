﻿namespace LendQube.Infrastructure.Core.Telemetry;

public enum EventSource
{
    AdminWeb = 0,
    CustomerWeb,
    ExternalApi,
    Webhook,
    BackgroundTask,
    Messaging,
    Infrastructure,
    Wallet,
    Transactions
}


public enum EventAction
{
    None = -1,
    Startup = 0,
    Start = 1,
    Processing = 2,
    End = 3,
    Add = 4,
    Delete = 5,
    SendMail = 6,
    Upload = 7,
    ExceptionOrError = 8,
    Update = 9,
    Workflow = 10,
    Warning = 11,
    Authentication = 12,
    Signup = 13,
    Payment = 14,
    Cancel = 15,
    History = 16,
    Api = 17,
    Webhook = 18,
    OnBoarding = 19,
    SendSms = 20,
    FileProcessing = 21,
    Validation,
    Triggers,
    SendPushNotification,
    RateLimiting,
    Vending,
    Deposit,
    Reports
}

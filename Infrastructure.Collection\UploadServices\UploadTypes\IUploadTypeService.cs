﻿using System.Collections.Concurrent;
using LendQube.Entities.Collection.Collections;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes;

internal interface IUploadTypeService
{
    Task<Result<MessageBrick>> SaveData(CollectionFileUpload upload, ExcelWorksheet worksheet, MessageBrick message, ConcurrentBag<UploadResult> resultList, CancellationToken ct);
}


internal record UploadResultVM(long Row, string Id, string Message);
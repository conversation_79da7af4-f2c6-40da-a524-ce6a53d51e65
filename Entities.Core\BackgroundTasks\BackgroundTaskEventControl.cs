﻿using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace LendQube.Entities.Core.BackgroundTasks;

public class BackgroundTaskEventControl : BaseEntityWithIdentityId<BackgroundTaskEventControl>, IEntityHasEnum
{
    public BackgroundEventSource Source { get; set; }
    public BackgroundTask Event { get; set; }
    public BackgroundControlState Status { get; set; }

    public override void Configure(EntityTypeBuilder<BackgroundTaskEventControl> builder)
    {
        base.Configure(builder);
        builder.HasIndex(e => new { e.Source, e.Event }).IsUnique();
    }

    public void RegisterEnumInDataSource(NpgsqlDataSourceBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<BackgroundControlState>($"{CoreEntityConfig.DefaultSchema}.{nameof(BackgroundControlState)}", nameTranslator);
    }
    public void RegisterEnumInDataSource(NpgsqlDbContextOptionsBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<BackgroundControlState>(nameof(BackgroundControlState), CoreEntityConfig.DefaultSchema, nameTranslator);
    }
}

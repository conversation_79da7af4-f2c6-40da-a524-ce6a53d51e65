﻿using Coravel.Invocable;
using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Messaging;
using Medallion.Threading;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Schedules.BackgroundServices;

internal sealed class CompleteTransactionsService(IUnitofWork uow, IClock clock, IDistributedLockProvider distributedLockProvider, PaymentFactory paymentFactory) : IInvocable, ICancellableInvocable
{
    public CancellationToken CancellationToken { get; set; }

    public async Task Invoke()
    {
        var now = clock.GetCurrentInstant();
        var incompleteTxns = await uow.Db.ManySelectAsync(Query<Transaction, CompletePaymentVM>.Where(x => now - x.CreatedDate > Duration.FromMinutes(2) && TransactionHelper.InProgressStatus.Contains(x.Status))
            .Select(x => new CompletePaymentVM(x.ProfileId, x.Id)), CancellationToken);

        var message = MessageBuilder.New("Background Complete Payment", null);
        foreach (var txn in incompleteTxns)
        {
            await ApplyPaymentHandler.ConfirmPayment(uow, clock, distributedLockProvider, paymentFactory, txn.ProfileId, new ProcessTransactionRequestVM(txn.TxnId), CancellationToken, message);
        }
        _ = await message.Send(uow, CancellationToken);

        _ = await uow.Db.DeleteAndSaveWithFilterAsync<Transaction>(x => x.Status == TransactionStatus.Validated && (now - x.CreatedDate) >= Duration.FromDays(7), CancellationToken);
        _ = await uow.Db.DeleteAndSaveWithFilterAsync<CustomerTransaction>(x => string.IsNullOrEmpty(x.PaymentType) && !x.Successful && (now - x.CreatedDate) >= Duration.FromDays(7), CancellationToken);
    }
}

file record CompletePaymentVM(string ProfileId, string TxnId);
﻿using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore.Components.Forms;
using Microsoft.AspNetCore.Http;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Core.FileManagement;

public sealed class FileManagementService(BlobServiceClient client, UriComposer uriComposer, ILogManager<FileManagementService> logger, DefaultAppConfig config) : IFileManagementService
{
    private readonly string prefixKey = config.DeployState.IsDemo ? "dev" : "prod";

    #region Generate File Names
    public string GenerateUrlFileName(string name) => uriComposer.FriendlyUrl(name);

    public string GenerateFileName(string name, string uploadedFileName)
    {
        var dayTime = DateTime.UtcNow.ToShortDateFormat();
        return $"{dayTime}/" + uriComposer.FriendlyUrl(name) + Path.GetExtension(uploadedFileName);
    }

    public string GenerateFileNameWithExtension(string name, string extension = "jpg")
    {
        var dayTime = DateTime.UtcNow.ToShortDateFormat();
        return $"{dayTime}/{uriComposer.FriendlyUrl(name)}_{SecurityDriven.FastGuid.NewGuid()}.{extension}";
    }
    #endregion

    #region Get Files
    public async Task<List<string>> GetAllFilesInBlob(string containerName, string name, CancellationToken ct)
    {
        containerName = containerName.ToLowerInvariant();
        var container = client.GetBlobContainerClient(containerName);
        var blobs = container.GetBlobsAsync(prefix: prefixKey + "/" + name, cancellationToken: ct);
        var list = await blobs.ToListAsync(ct);
        var urls = new List<string>();
        list.ForEach(item =>
        {
            urls.Add(config.AzureStorage.Url + containerName + "/" + item.Name);
        });

        return urls;
    }
    #endregion

    #region Delete Files
    public Task DeleteFile(string name, CancellationToken ct)
    {
        if (!string.IsNullOrEmpty(name))
        {
            if (name.StartsWith("http", StringComparison.OrdinalIgnoreCase))
            {
                name = name.Replace(config.AzureStorage.Url, string.Empty);
                var service = name.Split("/")[0];
                var fileName = string.Join("/", name.Split("/")[1..]);
                return DeleteBlobData(service, fileName, ct: ct);

            }
            else
            {
                var filePath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", name);
                if (File.Exists(filePath))
                    File.Delete(filePath);
            }
        }

        return Task.CompletedTask;
    }

    public async Task DeleteBlobData(string containerName, string fileName, string blobName = null, CancellationToken ct = default)
    {
        try
        {
            containerName = containerName.ToLowerInvariant();
            var container = client.GetBlobContainerClient(containerName);
            if (string.IsNullOrEmpty(fileName))
            {
                var prefix = string.IsNullOrEmpty(blobName) ? prefixKey : prefixKey + "/" + blobName;
                var blobs = container.GetBlobsAsync(prefix: prefix, cancellationToken: ct);
                var list = await blobs.ToListAsync(ct);
                foreach (var item in list)
                {
                    _ = await container.DeleteBlobAsync(item.Name, cancellationToken: ct);
                }
            }
            else
            {
                _ = await container.DeleteBlobIfExistsAsync(fileName, cancellationToken: ct);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Could not delete file: {containerName}-{fileName}");
        }
    }
    #endregion

    #region Save Base64
    public async Task<Result<string>> SaveBase64(string containerName, string storagePath, string file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false, bool validateNotEdited = false, long maxFileSize = 0)
    {
        try
        {
            if (string.IsNullOrEmpty(file))
                return Result<string>.Failed();

            var fileParts = file.Split(',');
            byte[] fileBytes = Convert.FromBase64String(fileParts.Length == 1 ? file : fileParts[1]);

            if (maxFileSize > 0 && fileBytes.Length > maxFileSize)
                return Result<string>.Failed($"Max file size allowed is {maxFileSize.ToFileSize()}");

            (bool validFile, string extension) = fileBytes.ValidateFileType(validExtensions);
            if (!validFile)
                return Result<string>.Failed();

            if (validateNotEdited && fileBytes.ValidateFileTamper(extension))
                return Result<string>.Failed();

            var filePath = await SaveBytes(containerName, $"{storagePath}.{extension}", fileBytes, ct, skipCreateCheck);
            if (!string.IsNullOrEmpty(filePath))
                return Result<string>.Successful(data: filePath);
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, "File processing failed");
        }

        return Result<string>.Failed();
    }

    public Task<string> SaveBase64WithTimeStampValidation(string containerName, string storagePath, string file, TimeSpan validityPeriod, string[] validExtensions, CancellationToken ct)
    {
        try
        {
            if (string.IsNullOrEmpty(file))
                return Task.FromResult(string.Empty);

            var fileParts = file.Split(',');
            byte[] fileBytes = Convert.FromBase64String(fileParts.Length == 1 ? file : fileParts[1]);
            (bool validFile, string extension) = fileBytes.ValidateFileTypeAndCreationDate(validityPeriod, validExtensions);
            if (!validFile)
                return Task.FromResult(string.Empty);

            return SaveBytes(containerName, $"{storagePath}.{extension}", fileBytes, ct);
        }
        catch (Exception)
        {

            return Task.FromResult(string.Empty);
        }
    }
    #endregion

    #region Save Bytes

    public async Task<Result<string>> SaveBytes(string containerName, string storagePath, byte[] file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false)
    {
        try
        {
            if (file == null)
                return Result<string>.Failed();

            (bool validFile, string extension) = file.ValidateFileType(validExtensions);
            if (!validFile)
                return Result<string>.Failed();

            var result = await SaveBytes(containerName, $"{storagePath}.{extension}", file, ct, skipCreateCheck);
            if (!string.IsNullOrEmpty(result))
                return Result<string>.Successful(data: result);
        }
        catch (Exception)
        {
        }

        return Result<string>.Failed();
    }

    public async Task<string> SaveBytes(string containerName, string fileName, byte[] file, CancellationToken ct, bool skipCreateCheck = false)
    {
        try
        {
            var (blobClient, url) = await GetBlobClient(containerName, fileName, ct, skipCreateCheck);
            _ = await blobClient.UploadAsync(new BinaryData(file), new BlobUploadOptions { Conditions = null, HttpHeaders = new BlobHttpHeaders { ContentType = ContentTypeHelper.GetContentType(Path.GetExtension(fileName)) } }, ct);

            return url;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Could not SaveFileToBlob: {containerName}, {fileName}");
        }

        return string.Empty;
    }
    #endregion
    #region Save Stream
    public async Task<string> SaveStream(string containerName, string fileName, Stream file, CancellationToken ct, bool skipCreateCheck = false)
    {
        try
        {
            var (blobClient, url) = await GetBlobClient(containerName, fileName, ct, skipCreateCheck);
            _ = await blobClient.UploadAsync(file, new BlobUploadOptions { Conditions = null, HttpHeaders = new BlobHttpHeaders { ContentType = ContentTypeHelper.GetContentType(Path.GetExtension(fileName)) } }, ct);

            return url;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Could not SaveFileToBlob: {containerName}, {fileName}");
        }

        return string.Empty;
    }

    #endregion

    #region Save Browser File
    public async Task<Result<string>> SaveRadzenFile(string containerName, string storagePath, Radzen.FileInfo file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false, bool validateNotEdited = false, long maxFileSize = 0)
    {
        try
        {
            if (file == null)
                return Result<string>.Failed();

            using var fileStream = file.OpenReadStream(file.Size, ct);

            if (maxFileSize > 0 && fileStream.Length > maxFileSize)
                return Result<string>.Failed($"Max file size allowed is {maxFileSize.ToFileSize()}");

            var stream = new MemoryStream(new byte[fileStream.Length]);
            await fileStream.CopyToAsync(stream, ct);

            (bool validFile, string extension) = stream.ValidateFileType(validExtensions);
            if (!validFile)
                return Result<string>.Failed();

            if (validateNotEdited && stream.ValidateFileTamper(extension))
                return Result<string>.Failed();

            var filePath = await SaveStream(containerName, $"{storagePath}.{extension}", stream, ct, skipCreateCheck);
            if (!string.IsNullOrEmpty(filePath))
                return Result<string>.Successful(data: filePath);
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, "File processing failed");
        }

        return Result<string>.Failed();
    }

    public async Task<Result<string>> SaveBrowserFile(string containerName, string storagePath, IBrowserFile file, string[] validExtensions, CancellationToken ct, bool skipCreateCheck = false, bool validateNotEdited = false, long maxFileSize = 0)
    {
        try
        {
            if (file == null)
                return Result<string>.Failed();

            using var fileStream = file.OpenReadStream(file.Size, ct);

            if (maxFileSize > 0 && fileStream.Length > maxFileSize)
                return Result<string>.Failed($"Max file size allowed is {maxFileSize.ToFileSize()}");

            var stream = new MemoryStream(new byte[fileStream.Length]);
            await fileStream.CopyToAsync(stream, ct);

            (bool validFile, string extension) = stream.ValidateFileType(validExtensions);
            if (!validFile)
                return Result<string>.Failed();

            if (validateNotEdited && stream.ValidateFileTamper(extension))
                return Result<string>.Failed();

            var filePath = await SaveStream(containerName, $"{storagePath}.{extension}", stream, ct, skipCreateCheck);
            if (!string.IsNullOrEmpty(filePath))
                return Result<string>.Successful(data: filePath);
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, "File processing failed");
        }

        return Result<string>.Failed();
    }
    #endregion

    #region Save Form File
    public async Task<Result<string>> SaveFormFile(string service, string name, IFormFile file, CancellationToken ct)
    {
        if (file == null || file.Length == 0)
        {
            Result<string>.Failed("No file attached");
        }

        if (file.Length > 6.ToMB())
        {
            Result<string>.Failed("File size greater than 6MB");
        }

        var uploadedFileName = file.FileName.Replace(" ", string.Empty);
        var fileName = GenerateFileName(name, uploadedFileName);
        return await UploadFileToBlobAsync(service, fileName, file, ["jpg", "jpeg", "png"], ct);
    }

    #endregion

    #region Save Excel
    public async Task<string> SaveExcel(ExcelPackage package, string containerName, string fileName, CancellationToken ct)
    {
        try
        {
            var (blobClient, url) = await GetBlobClient(containerName, fileName, ct);
            var file = await package.GetAsByteArrayAsync(ct);
            _ = await blobClient.UploadAsync(new BinaryData(file), new BlobUploadOptions { Conditions = null, HttpHeaders = new BlobHttpHeaders { ContentType = ContentTypeHelper.GetContentType(Path.GetExtension(fileName)) } }, ct);

            return url;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Could not SaveFileToBlob: {containerName}, {fileName}");
        }

        return string.Empty;
    }

    #endregion

    #region Private
    private async Task<Result<string>> UploadFileToBlobAsync(string containerName, string fileName, IFormFile file, string[] validExtensions, CancellationToken ct)
    {
        try
        {
            var (blobClient, url) = await GetBlobClient(containerName, fileName, ct);
            using var stream = file.OpenReadStream();
            (bool valid, string extension) = stream.ValidateFileType(validExtensions);
            if (!valid)
                return Result<string>.Failed("File type invalid");

            _ = await blobClient.UploadAsync(stream, new BlobUploadOptions { Conditions = null, HttpHeaders = new BlobHttpHeaders { ContentType = ContentTypeHelper.GetContentType(Path.GetExtension(fileName)) } }, ct);

            return Result<string>.Successful(data: url);
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Could not UploadFileToBlobAsync: {containerName}, {fileName}");
        }

        return Result<string>.Failed("File upload failed");
    }

    private async Task<(BlobClient, string)> GetBlobClient(string containerName, string fileName, CancellationToken ct, bool skipCreateCheck = false)
    {
        containerName = containerName.ToLowerInvariant();
        var container = client.GetBlobContainerClient(containerName);

        if (!skipCreateCheck)
            _ = await container.CreateIfNotExistsAsync(PublicAccessType.Blob, cancellationToken: ct);

        var url = prefixKey + "/" + fileName;
        var blobClient = container.GetBlobClient(url);
        return (blobClient, config.AzureStorage.Url + containerName + "/" + url);
    }

    #endregion
}

﻿@using System.Security.Claims
@implements IDisposable

@inject NavigationManager NavigationManager

@if (!currentNavigation.IsNullOrEmpty())
{
    <div class="layout-nav">
        <div class="nav-menu">
            <div class="menu-block">

                @foreach (var item in currentNavigation.Where(x => string.IsNullOrEmpty(x.Permission) || claims.Any(z => z.Value == x.Permission)))
                {
                    <div class="menu-group">
                        <NavLink target="@item.Target" class="menu-item" href="@item.NavLink" Match="NavLinkMatch.Prefix">
                            @item.Name
                        </NavLink>
                    </div>
                }
            </div>
        </div>
    </div>
}
@code {
    [CascadingParameter]
    private Task<AuthenticationState> authenticationState { get; set; }
    private List<NavigatorVM> currentNavigation;
    private IEnumerable<Claim> claims;

    protected override async Task OnInitializedAsync()
    {
        if(currentNavigation.IsNullOrEmpty())
        {
            if (authenticationState is not null && claims.IsNullOrEmpty())
            {
                var authState = await authenticationState;
                claims = authState.User.Claims;
            }
            UpdateSideMenu(NavigationManager.Uri);
        }
    }

    protected override void OnAfterRender(bool firstRender)
    {
        if (firstRender)
        {
            NavigationManager.LocationChanged += OnLocationChanged;
        }
    }

    private void UpdateSideMenu(string uri)
    {
        if(!claims.IsNullOrEmpty())
        {
            var url = NavigationManager.ToBaseRelativePath(uri);
            var currentControllerName = url;
            if(!string.IsNullOrEmpty(currentControllerName))
            {
                var firstIndexSlash = currentControllerName.IndexOf('/');
                if(firstIndexSlash > 0)
                    currentControllerName = currentControllerName[..firstIndexSlash].ToLower();
            }
            currentNavigation = Navigator.GeneralNavigator.Where(x => 
                (string.IsNullOrEmpty(x.Value.Permission) || claims.Any(z => z.Value == x.Value.Permission)) && 
                (x.Value.SubNavigation.Any(y => y.NavLink == url) || x.Value.SubNavigation.Any(y => url.StartsWith(y.NavLink, StringComparison.OrdinalIgnoreCase))) &&
                (x.Value.SubNavigation.Any(y => y.Controller?.ToLower() == currentControllerName || (!string.IsNullOrEmpty(currentControllerName) && (y.Url?.ToLower().StartsWith(currentControllerName) ?? false)))))
                .Select(x => x.Value.SubNavigation).FirstOrDefault();
        }
    }

    private void OnLocationChanged(object sender, LocationChangedEventArgs e)
    {
        UpdateSideMenu(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}

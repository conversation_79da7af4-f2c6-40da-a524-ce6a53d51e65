﻿using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class C_O_Addworkflow : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CollectionUpload",
                schema: "collection");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload,Acquired")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .OldAnnotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure,PlacementAmend")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload,Acquired")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.AddColumn<bool>(
                name: "CurrentlyAssigned",
                schema: "collection",
                table: "CustomerProfile",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<Instant>(
                name: "LastAssignedDate",
                schema: "collection",
                table: "CustomerProfile",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "AgentWorkflowAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowAnalytics", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowAnalytics_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowAvailability",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    Start = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    End = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowAvailability", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowAvailability_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowTimeAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Date = table.Column<LocalDate>(type: "date", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowTimeAnalytics", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTimeAnalytics_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CollectionFileUpload",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    AnalysisFileUrl = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CollectionFileUpload", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DebtSegment",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Priority = table.Column<int>(type: "integer", nullable: false),
                    Start = table.Column<TimeOnly>(type: "time without time zone", nullable: true),
                    End = table.Column<TimeOnly>(type: "time without time zone", nullable: true),
                    RuleIds = table.Column<List<long>>(type: "bigint[]", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtSegment", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DebtSegmentRule",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Instruction = table.Column<string>(type: "text", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    TypeNumber = table.Column<int>(type: "integer", nullable: true),
                    StatusType = table.Column<int>(type: "integer", nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtSegmentRule", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DebtWorkflowAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtWorkflowAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DebtWorkflowTimeAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Date = table.Column<LocalDate>(type: "date", nullable: false),
                    TotalAssigned = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalOpened = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalClosed = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalEscalated = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalAmountCollected = table.Column<decimal>(type: "numeric(18,2)", nullable: false, defaultValue: 0m),
                    TotalPTP = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalSchedulesSetup = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalContacted = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    TotalResolved = table.Column<int>(type: "integer", nullable: false, defaultValue: 0),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtWorkflowTimeAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ReportSchedule",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    ReportTypeName = table.Column<string>(type: "text", nullable: false),
                    ReportNames = table.Column<List<string>>(type: "text[]", nullable: false),
                    FileType = table.Column<int>(type: "integer", nullable: false),
                    Frequency = table.Column<int>(type: "integer", nullable: false),
                    FrequencyNumber = table.Column<int>(type: "integer", nullable: false),
                    Days = table.Column<int>(type: "integer", nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    LastRunDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    StartDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    RunCount = table.Column<int>(type: "integer", nullable: false),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    Starting = table.Column<bool>(type: "boolean", nullable: false),
                    TimeZone = table.Column<string>(type: "text", nullable: true),
                    MessagingGroupId = table.Column<long>(type: "bigint", nullable: false),
                    ConfigId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ReportSchedule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ReportSchedule_MessageConfiguration_ConfigId",
                        column: x => x.ConfigId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ReportSchedule_MessagingGroup_MessagingGroupId",
                        column: x => x.MessagingGroupId,
                        principalSchema: "core",
                        principalTable: "MessagingGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SystemFileUpload",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    Action = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    AnalysisFileUrl = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemFileUpload", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowAvailabilityStatusChangeLog",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    AvailabilityId = table.Column<long>(type: "bigint", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowAvailabilityStatusChangeLog", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowAvailabilityStatusChangeLog_AgentWorkflowAvail~",
                        column: x => x.AvailabilityId,
                        principalSchema: "collection",
                        principalTable: "AgentWorkflowAvailability",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentToDebtSegmentMapping",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    DebtSegmentId = table.Column<long>(type: "bigint", nullable: false),
                    RecordsPerTime = table.Column<int>(type: "integer", nullable: false),
                    Enabled = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentToDebtSegmentMapping", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentToDebtSegmentMapping_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AgentToDebtSegmentMapping_DebtSegment_DebtSegmentId",
                        column: x => x.DebtSegmentId,
                        principalSchema: "collection",
                        principalTable: "DebtSegment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AgentWorkflowTask",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    AvailabilityId = table.Column<long>(type: "bigint", nullable: true),
                    DebtSegmentId = table.Column<long>(type: "bigint", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    EscalatedTaskId = table.Column<long>(type: "bigint", nullable: true),
                    EscalatedToUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    EscalatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    EscalationReason = table.Column<string>(type: "text", nullable: true),
                    Assigned = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    Opened = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Removed = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    IsEscalated = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Tasks = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AgentWorkflowTask", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AgentWorkflowAvailability_AvailabilityId",
                        column: x => x.AvailabilityId,
                        principalSchema: "collection",
                        principalTable: "AgentWorkflowAvailability",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AspNetUsers_EscalatedByUserId",
                        column: x => x.EscalatedByUserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AspNetUsers_EscalatedToUserId",
                        column: x => x.EscalatedToUserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_CustomerProfile_CustomerProfileId",
                        column: x => x.CustomerProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_AgentWorkflowTask_DebtSegment_DebtSegmentId",
                        column: x => x.DebtSegmentId,
                        principalSchema: "collection",
                        principalTable: "DebtSegment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "DebtSegmentRules",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    DebtSegmentId = table.Column<long>(type: "bigint", nullable: false),
                    RuleId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DebtSegmentRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_DebtSegmentRules_DebtSegmentRule_RuleId",
                        column: x => x.RuleId,
                        principalSchema: "collection",
                        principalTable: "DebtSegmentRule",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_DebtSegmentRules_DebtSegment_DebtSegmentId",
                        column: x => x.DebtSegmentId,
                        principalSchema: "collection",
                        principalTable: "DebtSegment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "SystemReport",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TypeName = table.Column<string>(type: "text", nullable: false),
                    Names = table.Column<List<string>>(type: "text[]", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    FileType = table.Column<int>(type: "integer", nullable: false),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true),
                    StartDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    EndDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    RunCount = table.Column<int>(type: "integer", nullable: false),
                    AutoRun = table.Column<bool>(type: "boolean", nullable: false),
                    ReportScheduleId = table.Column<long>(type: "bigint", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SystemReport", x => x.Id);
                    table.ForeignKey(
                        name: "FK_SystemReport_ReportSchedule_ReportScheduleId",
                        column: x => x.ReportScheduleId,
                        principalSchema: "core",
                        principalTable: "ReportSchedule",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AgentToDebtSegmentMapping_DebtSegmentId",
                schema: "collection",
                table: "AgentToDebtSegmentMapping",
                column: "DebtSegmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentToDebtSegmentMapping_UserId_DebtSegmentId",
                schema: "collection",
                table: "AgentToDebtSegmentMapping",
                columns: new[] { "UserId", "DebtSegmentId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowAnalytics_UserId",
                schema: "collection",
                table: "AgentWorkflowAnalytics",
                column: "UserId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowAvailability_UserId",
                schema: "collection",
                table: "AgentWorkflowAvailability",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowAvailabilityStatusChangeLog_AvailabilityId",
                schema: "collection",
                table: "AgentWorkflowAvailabilityStatusChangeLog",
                column: "AvailabilityId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_AvailabilityId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "AvailabilityId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_CustomerProfileId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "CustomerProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_DebtSegmentId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "DebtSegmentId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_EscalatedByUserId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "EscalatedByUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_EscalatedToUserId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "EscalatedToUserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTask_UserId",
                schema: "collection",
                table: "AgentWorkflowTask",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AgentWorkflowTimeAnalytics_UserId_Date",
                schema: "collection",
                table: "AgentWorkflowTimeAnalytics",
                columns: new[] { "UserId", "Date" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegment_Name",
                schema: "collection",
                table: "DebtSegment",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRule_Name",
                schema: "collection",
                table: "DebtSegmentRule",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRules_DebtSegmentId",
                schema: "collection",
                table: "DebtSegmentRules",
                column: "DebtSegmentId");

            migrationBuilder.CreateIndex(
                name: "IX_DebtSegmentRules_RuleId",
                schema: "collection",
                table: "DebtSegmentRules",
                column: "RuleId");

            migrationBuilder.CreateIndex(
                name: "IX_DebtWorkflowTimeAnalytics_Date",
                schema: "collection",
                table: "DebtWorkflowTimeAnalytics",
                column: "Date",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ReportSchedule_ConfigId",
                schema: "core",
                table: "ReportSchedule",
                column: "ConfigId");

            migrationBuilder.CreateIndex(
                name: "IX_ReportSchedule_MessagingGroupId",
                schema: "core",
                table: "ReportSchedule",
                column: "MessagingGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_SystemReport_ReportScheduleId",
                schema: "core",
                table: "SystemReport",
                column: "ReportScheduleId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AgentToDebtSegmentMapping",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowAvailabilityStatusChangeLog",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowTask",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "AgentWorkflowTimeAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CollectionFileUpload",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtSegmentRules",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtWorkflowAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtWorkflowTimeAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "SystemFileUpload",
                schema: "core");

            migrationBuilder.DropTable(
                name: "SystemReport",
                schema: "core");

            migrationBuilder.DropTable(
                name: "AgentWorkflowAvailability",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtSegmentRule",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DebtSegment",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "ReportSchedule",
                schema: "core");

            migrationBuilder.DropColumn(
                name: "CurrentlyAssigned",
                schema: "collection",
                table: "CustomerProfile");

            migrationBuilder.DropColumn(
                name: "LastAssignedDate",
                schema: "collection",
                table: "CustomerProfile");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .Annotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported,Failed")
                .Annotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure,PlacementAmend")
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload,Acquired")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened")
                .OldAnnotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .OldAnnotation("Npgsql:Enum:collection.PaymentProvider", "Stripe,Upload,Acquired")
                .OldAnnotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card,Bank")
                .OldAnnotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .OldAnnotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .OldAnnotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .OldAnnotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .OldAnnotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .OldAnnotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .OldAnnotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .OldAnnotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.CreateTable(
                name: "CollectionUpload",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    AnalysisFileUrl = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Description = table.Column<string>(type: "character varying(512)", maxLength: 512, nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Message = table.Column<string>(type: "text", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CollectionUpload", x => x.Id);
                });
        }
    }
}

﻿using Microsoft.AspNetCore.Http;
using Serilog.Core;
using Serilog.Events;
using System.Security.Claims;

namespace LendQube.Infrastructure.Core.Telemetry;

internal sealed class LoggedInUserEnricher(IHttpContextAccessor httpContextAccessor) : ILogEventEnricher
{
    private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor;

    public LoggedInUserEnricher() : this(new HttpContextAccessor())
    {
    }

    public void Enrich(LogEvent logEvent, ILogEventPropertyFactory propertyFactory)
    {
        var user = _httpContextAccessor.HttpContext?.User;
        if (!(user?.Identity?.IsAuthenticated ?? false))
            return;

        var userEmail = user?.FindFirstValue(ClaimTypes.Email);
        var userId = user?.Identity?.Name;

        logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("UserEmail", userEmail));
        logEvent.AddPropertyIfAbsent(propertyFactory.CreateProperty("UserId", userId));
    }
}

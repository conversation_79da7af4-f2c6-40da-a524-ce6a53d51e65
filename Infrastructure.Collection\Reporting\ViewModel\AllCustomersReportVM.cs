﻿using NodaTime;
using OfficeOpenXml.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;

namespace LendQube.Infrastructure.Collection.Reporting.ViewModel;

[EpplusTable(PrintHeaders = true, AutofitColumns = true)]
public class AllCustomersReportVM
{
    [EpplusTableColumn(Order = 0, Header = "Account Number")]
    public string AccountNumber { get; set; }
    [EpplusTableColumn(Order = 1, Header = "Company Name")]
    public string CompanyName { get; set; }
    [EpplusIgnore]
    public LocalDate PlacementDate { get; set; }
    [EpplusTableColumn(Order = 2, Header = "Placement Date")]
    public string PlacementDateFormatted => PlacementDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 3, Header = "Debt Status")]
    public CustomerDebtStatus DebtStatus { get; set; }
    [EpplusIgnore]
    public int AccountAgeInDays { get; set; }
    [NotMapped, EpplusTableColumn(Order = 4, Header = "Progress through collections cycle")]
    public int CollectionsCycleProgress => CollectionsCycleCalculator.GetCycle(AccountAgeInDays);
    [EpplusTableColumn(Order = 5, Header = "Debt at Escalation", NumberFormat = "#,##0.00")]
    public decimal DebtAtEscalation { get; set; }
    [EpplusTableColumn(Order = 6, Header = "Total Paid", NumberFormat = "#,##0.00")]
    public decimal? TotalPaid { get; set; }
    [EpplusTableColumn(Order = 7, Header = "Outstanding Balance", NumberFormat = "#,##0.00")]
    public decimal OutstandingBalance { get; set; }
    [EpplusTableColumn(Order = 8, Header = "Plan Status")]
    public string PlanStatus { get; set; }
    [EpplusIgnore]
    public LocalDate? LastSuccessfulPaymentAt { get; set; }
    [EpplusTableColumn(Order = 9, Header = "Last Successful Payment At")]
    public string LastSuccessfulPaymentAtFormatted => LastSuccessfulPaymentAt?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusIgnore]
    public LocalDate? NextScheduledPaymentAt { get; set; }
    [EpplusTableColumn(Order = 10, Header = "Next Scheduled Payment At")]
    public string NextScheduledPaymentAtFormatted => NextScheduledPaymentAt?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusIgnore]
    public LocalDate? PlanSetupAt { get; set; }
    [EpplusTableColumn(Order = 11, Header = " Plan Setup At")]
    public string PlanSetupAtFormatted => PlanSetupAt?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 12, Header = "Plan Length")]
    public int? PlanLength { get; set; }
    [EpplusTableColumn(Order = 13, Header = "Plan Payment Frequency")]
    public string PlanPaymentFrequency { get; set; }
    [EpplusTableColumn(Order = 14, Header = "Plan Payment Amount", NumberFormat = "#,##0.00")]
    public decimal? PlanPaymentAmount { get; set; }
    [EpplusIgnore]
    public LocalDate? ClosedAt { get; set; }
    [EpplusTableColumn(Order = 15, Header = "Closed At")]
    public string ClosedAtFormatted => ClosedAt?.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 16, Header = "Closure Code")]
    public string ClosureCode { get; set; }
}

public enum CustomerDebtStatus
{
    Open,
    Closed
}

public enum CustomerPlanStatus
{
    [Display(Name = "No Plan Setup")]
    NoPlan,
    [Display(Name = "Plan - Active")]
    Active,
    [Display(Name = "Promise - Broken")]
    Broken,
    [Display(Name = "Plan - Finished")]
    Finished,
}
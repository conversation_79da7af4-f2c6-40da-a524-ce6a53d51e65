﻿@typeparam TModel
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components
@using System.Threading.Tasks

<AuthorizeView Policy="@Policy" Context="authContext">
	<div class="modal fade" id="@ModalId" tabindex="-1" aria-labelledby=@($"{ModalId}_Label") aria-modal="true" role="dialog" data-bs-focus="false">
		<div class="modal-dialog @ModalCss">
			<div class="modal-content">
				<div class="modal-header">
					<div class="__title">
						<h5 class="modal-title" id=@($"{ModalId}_Label")>@Title</h5>
					</div>
					<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
				</div>
				<div class="modal-body">
					<StatusMessage Message="ModalMessage" />
					<EditForm Model="@Model" OnValidSubmit="HandleSubmit" FormName="@FormName" @attributes="AdditionalAttributes" class="modal-form big-form">
						<DataAnnotationsValidator />
						@BodyContent(Model)
						<div class="form-row">
							<button type="submit" id=@($"{ModalId}_Button") class="btn btn--primary" disabled="@IsSubmitting">
								Save
								@if (IsSubmitting)
								{
									<span> &nbsp;</span>
									<i class="fa fa-spinner fa-spin"></i>
								}
							</button>
						</div>
					</EditForm>
				</div>
			</div>
		</div>
	</div>
</AuthorizeView>

@code {
	[Parameter, EditorRequired] public string ModalId { get; set; }
	[Parameter, EditorRequired] public string Policy { get; set; }
	[Parameter, EditorRequired] public string Title { get; set; } 
	[Parameter] public string ModalCss { get; set; } = "width-lg";
	[Parameter, EditorRequired] public TModel Model { get; set; }
	[Parameter, EditorRequired] public Func<ValueTask> OnValidSubmit { get; set; }
	[Parameter, EditorRequired] public RenderFragment<TModel> BodyContent { get; set; }
	[Parameter, EditorRequired] public StatusMessageBuilder ModalMessage { get; set; }
	[Parameter] public string FormName { get; set; } = $"EditForm_{SecurityDriven.FastGuid.NewGuid()}";
	[Parameter(CaptureUnmatchedValues = true)] public Dictionary<string, object> AdditionalAttributes { get; set; }


	private bool IsSubmitting { get; set; } = false;

	private async Task HandleSubmit()
	{
		if (IsSubmitting)
			return;
		ModalMessage.Close();
		IsSubmitting = true;

		try
		{
			await OnValidSubmit();
		}
		finally
		{
			IsSubmitting = false;
		}
	}
}


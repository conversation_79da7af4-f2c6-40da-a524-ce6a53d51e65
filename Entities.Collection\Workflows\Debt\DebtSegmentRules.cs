﻿using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Workflows.Debt;

public class DebtSegmentRules : BaseEntityWithIdentityId<DebtSegmentRules>
{
	public long DebtSegmentId { get; set; }
	public virtual DebtSegment DebtSegment { get; set; }
	public long RuleId { get; set; }
	public virtual DebtSegmentRule Rule { get; set; }

	public override void Configure(EntityTypeBuilder<DebtSegmentRules> builder)
	{
		base.Configure(builder);

		builder.HasOne(x => x.DebtSegment)
			.WithMany(x => x.Rules);
	}
}

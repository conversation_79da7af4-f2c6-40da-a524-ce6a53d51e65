﻿using System.Net;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.AspNetCore;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using OpenIddict.Server.AspNetCore;
using static OpenIddict.Abstractions.OpenIddictConstants;

namespace LendQube.Infrastructure.Collection.Authentication;

[Route(ApiConstants.URL)]
public sealed class AuthorizationController(CustomerAuthService service, ILogManager<AuthorizationController> logger) : ApiControllerBase
{

    [HttpPost("~/connect/token")]
    [Produces("application/json")]
    public async Task<IActionResult> Login(CancellationToken ct)
    {
        var data = HttpContext.GetOpenIddictServerRequest();
        try
        {
            if (data is null)
            {
                throw new InvalidOperationException("The OpenID Connect request cannot be retrieved.");
            }

            var login = await service.Authenticate(HttpContext, data, ct);

            if (login.IsSuccessful)
            {
                var principal = await service.CreateTicketAsync(data, login.AppUser.Id, ct);
                return SignIn(principal, OpenIddictServerAspNetCoreDefaults.AuthenticationScheme);
            }
            else
            {
                return Forbid(
                    authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
                    properties: new AuthenticationProperties(new Dictionary<string, string>
                    {
                        [OpenIddictServerAspNetCoreConstants.Properties.Error] = login.Response?.Error,
                        [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = login.Response?.ErrorDescription
                    }));
            }

        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.CustomerWeb, EventAction.ExceptionOrError, ex, "Login auth failed");
        }

        return Forbid(
            authenticationSchemes: OpenIddictServerAspNetCoreDefaults.AuthenticationScheme,
            properties: new AuthenticationProperties(new Dictionary<string, string>
            {
                [OpenIddictServerAspNetCoreConstants.Properties.Error] = Errors.TemporarilyUnavailable,
                [OpenIddictServerAspNetCoreConstants.Properties.ErrorDescription] = "The login service is temporarily unavailable"
            }));
    }

    [HttpPost("request-login-token")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Result<bool>), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(Result<bool>), (int)HttpStatusCode.BadRequest)]
    public async Task<IResult> RequestLoginToken([FromBody] UserTokenRequestVM vm, CancellationToken ct)
    {
        var resp = await service.RequestToken(vm, HttpContext.GetIpAddress(), ct);

        if (resp.IsSuccessful)
            return TypedResults.Ok(resp);

        return TypedResults.BadRequest(resp);
    }

    [HttpPost("reset-password")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Result<bool>), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(Result<bool>), (int)HttpStatusCode.BadRequest)]
    public async Task<IResult> InitiatePasswordReset([FromBody] ResetPasswordVM vm, CancellationToken ct)
    {
        var resp = await service.InitiatePasswordReset(vm, ct);

        if (resp.IsSuccessful)
            return TypedResults.Ok(resp);

        return TypedResults.BadRequest(resp);
    }

    [HttpPost("complete-reset-password")]
    [Produces("application/json")]
    [ProducesResponseType(typeof(Result<bool>), (int)HttpStatusCode.OK)]
    [ProducesResponseType(typeof(Result<bool>), (int)HttpStatusCode.BadRequest)]
    public async Task<IResult> CompletePasswordReset([FromBody] SetPasswordVM vm, CancellationToken ct)
    {
        var resp = await service.CompletePasswordReset(vm, ct);

        if (resp.IsSuccessful)
            return TypedResults.Ok(resp);

        return TypedResults.BadRequest(resp);
    }
}
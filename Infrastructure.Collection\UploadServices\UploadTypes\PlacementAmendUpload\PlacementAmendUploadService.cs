﻿using System.Collections.Concurrent;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Location;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.PlacementUpload;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using Microsoft.EntityFrameworkCore;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.PlacementAmendUpload;

internal sealed class PlacementAmendUploadService(IUnitofWork uow, ILogManager<PlacementAmendUploadService> logger) : IUploadTypeService
{
    public async Task<Result<MessageBrick>> SaveData(CollectionFileUpload upload, ExcelWorksheet worksheet, MessageBrick message, ConcurrentBag<UploadResult> resultList, CancellationToken ct)
    {
        string errorMessage = string.Empty;
        try
        {
            var countries = await uow.Db.ManySelectAsync(Query<Currency, CountryWithCurrencyVM>.Select(CountryWithCurrencyVM.Mapping), ct);

            //load all data into collections
            var start = worksheet.Dimension.Start;
            var end = worksheet.Dimension.End;
            var loadedData = worksheet.Cells[$"{start.Address}:{end.Address}"].ToCollectionWithMappings
                (
                    row =>
                    {
                        return new AmendUploadVM
                        {
                            AccountNumber = row.GetValue<string>(0),
                            Company = row.GetValue<string>(1),
                            FirstName = row.GetValue<string>(2),
                            LastName = row.GetValue<string>(3),
                            Email = row.GetValue<string>(4),
                            MobileNumber = row.GetValue<string>(5),
                            Country = row.GetValue<string>(6),
                            DateOfBirth = row.GetValue<string>(7),

                        };
                    },
                    options =>
                    {
                        options.HeaderRow = null;
                        options.DataStartRow = 1;
                    }
                );

            if (loadedData.Count == 0)
            {
                errorMessage = "No records found";
                resultList.Add(new(0, string.Empty, errorMessage));
                return errorMessage;
            }


            var parallelData = loadedData.AsParallel();
            var placementAccountIds = parallelData.Select(x => x.AccountNumber).ToList();

            var existingPlacements = await uow.Db.ManyAsync(Query<Placement>.Where(x => placementAccountIds.Contains(x.SourceAccountNumber)).Include(x => x.Include(y => y.Profile).ThenInclude(y => y.AppUser)).Track(), ct);

            if (existingPlacements.Count == 0)
            {
                errorMessage = "No placements found to amend. Check that the account number provided matches an uploaded placement number";
                resultList.Add(new(0, string.Empty, errorMessage));
                return errorMessage;
            }

            //check that data is valid
            if (upload.Action != UploadAction.Import)
            {
                Parallel.ForEach(parallelData, new ParallelOptions { MaxDegreeOfParallelism = loadedData.Count, CancellationToken = ct }, (item, state, index) =>
                {
                    item.ValidateData(index + 2, existingPlacements, resultList);
                });
            }

            if (upload.Action == UploadAction.Analyze)
                return message;


            message.Message(MessageConfigNames.WelcomeLetterAndApology.GetDisplayName());
            //Do update
            Parallel.ForEach(parallelData, new ParallelOptions { MaxDegreeOfParallelism = loadedData.Count, CancellationToken = ct }, (item) =>
            {
                var placement = existingPlacements.FirstOrDefault(x => x.SourceAccountNumber == item.AccountNumber);
                if (placement == null)
                    return;

                var appUser = placement.Profile?.AppUser;

                if (!string.IsNullOrEmpty(item.Company) && !item.Company.Trim().Equals(placement.Company.Trim(), StringComparison.OrdinalIgnoreCase))
                {
                    placement.Company = item.Company.Trim();
                    message.WithRecipient(placement.ProfileId,
                    [
                        new($"{MessageTemplateKeys.CompanyName}", placement.Company)
                    ]);
                }

                if (!string.IsNullOrEmpty(item.Email))
                {
                    placement.Profile.Email = item.Email;
                    if (appUser != null)
                    {
                        appUser.Email = item.Email;
                        appUser.NormalizedEmail = item.Email.ToUpperInvariant();
                    }
                }

                if (!string.IsNullOrEmpty(item.FirstName))
                {
                    placement.Profile.FirstName = item.FirstName;
                    if (appUser != null)
                    {
                        appUser.FirstName = item.FirstName;
                    }
                }

                if (!string.IsNullOrEmpty(item.LastName))
                {
                    placement.Profile.LastName = item.LastName;
                    if (appUser != null)
                    {
                        appUser.LastName = item.LastName;
                    }
                }

                if (!string.IsNullOrEmpty(item.MobileNumber))
                {
                    var country = countries.FirstOrDefault(x => x.CountryName.Equals(item.Country, StringComparison.OrdinalIgnoreCase));
                    if (country != null)
                    {
                        placement.Profile.MobileNumber = new PhoneNumber(country.PhoneCode, item.MobileNumber.CleanPhoneNumber(country.PhoneCode));
                        if (appUser != null)
                        {
                            appUser.PhoneNumber = placement.Profile.MobileNumber.ToString();
                        }
                    }
                }

                if (!string.IsNullOrEmpty(item.DateOfBirth))
                    placement.Profile.DateOfBirth = item.DateOfBirth.ToDate();
            });

            await uow.SaveAsync(ct);

            return message;
        }
        catch (Exception ex)
        {
            resultList.Add(new(0, string.Empty, ex.Message));
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Placement amendment upload failed for upload: {upload.Description}");
        }

        return "Placement amendment upload failed. Consult admin";
    }
}

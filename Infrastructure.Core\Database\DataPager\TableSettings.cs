﻿using LendQube.Entities.Core.Attributes;
using LendQube.Infrastructure.Core.Database.DataPager.Filters;
using LendQube.Infrastructure.Core.Extensions;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.Database.DataPager;

public sealed class TableSettings<T>
{
    public bool HasId { get; set; }
    public bool HasDateColumns { get; set; } = true;
    public bool HasEdit { get; set; }
    public bool HasDelete { get; set; }
    public bool HasInfo { get; set; }
    public bool TurnOffFilter { get; set; }
    public bool TurnOffSorting { get; set; }
    public bool ShowUserInfo { get; set; }
    public IReadOnlyList<ColumnsToAdd<T>> ColumnsToAdd { get; set; } = null;
    public IReadOnlyList<ColumnFilterOptionFromDb> ColumnFilterOptions { get; set; } = null;
}

public class ColumnsToAdd<T>
{
    public int Index { get; set; }
    public Expression<Func<T, object>> Name { get; set; }
    public string NameString { get; set; }
    public bool ShowInDelete { get; set; }
    public bool ShowInInfo { get; set; }
    public bool ShowInFilter { get; set; }
    public TableDecoratorType[] DecoratorTypes =>
        ShowInInfo && ShowInDelete ? [TableDecoratorType.ShowInInfo, TableDecoratorType.ShowInDelete] : ShowInInfo ? [TableDecoratorType.ShowInInfo] : ShowInDelete ? [TableDecoratorType.ShowInDelete] : [];
}

public sealed class ColumnList
{
    public List<ColumnHeader> ColumnHeaders { get; set; }
    public List<ColumnFilter> ColumnFilters { get; set; }
    public IEnumerable<string> DropDowns { get; set; }
    public List<TopActionButton> TopActionButtons { get; set; } = [];
    public List<RowActionButton> RowActionButtons { get; set; } = [];
    public bool HasEdit { get; set; }
    public bool HasDelete { get; set; }
    public bool HasInfo { get; set; }
    public bool NoGeneralSearch { get; set; }
    public bool HasActionSlot => HasEdit || HasDelete || HasInfo || !RowActionButtons.IsNullOrEmpty();

}

public sealed class ColumnHeader
{
    public string Name { get; set; }
    public string Title { get; set; }
    public bool IsSortable { get; set; } = true;
    public bool IsSortableVM { get; set; } = false;
    public bool IsHidden { get; set; }
    public TableDecoratorType[] DecoratorTypes { get; set; }
}

public sealed class ColumnFilter
{
    public string Name { get; set; }
    public string DisplayName { get; set; }
    public ColumnFilterDataType DataType { get; set; }
    public IReadOnlyList<ObjectFilter> Rules { get; set; }
    public object Value { get; set; }
    public IReadOnlyList<string> DataTypeValueList { get; set; }
}

public sealed class ColumnFilterOptionFromDb
{
    public string ColumnName { get; set; }
    public IEnumerable<string> Options { get; set; }
}

public enum ColumnFilterDataType
{
    Text,
    Number,
    Date,
    BoolList,
    EnumList,
    DBList,
    PhoneNumber,
    Object
}

public enum ColumnFilterRule
{
    [Display( Name = "Equals")]
    IsEquals,
    [Display(Name = "Not Equal To")]
    NotEquals,
    GreaterThan,
    [Display(Name = "Greater Than Or Equal To")]
    GreaterThanOrEquals,
    LessThan,
    [Display(Name = "Less Than Or Equal To")]
    LessThanOrEquals,
    Contains,
    [Display(Name = "Does Not Contain")]
    NotContains,
    StartsWith,
    EndsWith,
    [Display(Name = "Is Empty")]
    IsNull,
    [Display(Name = "Is Not Empty")]
    IsNotNull,
    CountEquals,
    CountNotEqualTo,
    CountGreaterThan,
    [Display(Name = "Count Greater Than Or Equal To")]
    CountGreaterThanOrEqualTo,
    CountLessThan,
    [Display(Name = "Count Less Than Or Equal To")]
    CountLessThanOrEqualTo,
}

public enum ColumnFilterBoolOptions
{
    Yes = 1,
    No = 0
}

public sealed record RowActionButton(string Name, string ButtonClass = "btn--default", string IconClass = "__default", string Icon = null, Func<object, Task> Action = null, string Url = null, Func<object, bool> ShowCondition = null);

public sealed record CheckboxAction(bool OriginalValue, bool NewValue, object Row);

public sealed record TopActionButton(string Name = "Add New", string ButtonClass = "btn--primary", string Icon = "plus", Func<Task> Action = null, string Url = null, string ModalName = null, Func<bool> ShowCondition = null);
﻿using System.Collections.Concurrent;
using Coravel.Queuing.Interfaces;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Constants;
using LendQube.Entities.Core.Messaging;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.FileManagement;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class MessagingGroupUploadService(IFileManagementService fileService, IQueue queue, BackgroundTaskControlService backgroundService,
    HttpClient httpClient, DefaultAppConfig config, ILogManager<SystemFileUpload> logger) :
    AbstractFileUploadService<SystemFileUpload>(fileService, queue, backgroundService, httpClient)
{
    private readonly IUnitofWork uow = backgroundService.Uow;
    protected override ILogManager<SystemFileUpload> Logger => logger;

    protected override string UPLOADKEY => "MessagingGroupUpload";
    protected override BackgroundEventSource Source => BackgroundEventSource.System;
    protected override BackgroundTask Key => BackgroundTask.MessagingGroupUpload;

    public string TemplateFilePath => $"{config.AzureStorage.Url}templates/MessageGroupUploadTemplate.xlsx";

    public static event EventHandler<SystemBackgroundTaskEventArgs>? StatusNotificationEvent;

    protected override void RaiseEvent(SystemBackgroundTaskEventArgs data)
    {
        StatusNotificationEvent?.Invoke(this, data);
    }

    protected override async Task<Result<MessageBrick>> UploadData(SystemFileUpload upload, ExcelWorksheet worksheet, MessageBrick message, ConcurrentBag<UploadResult> resultsList, CancellationToken ct)
    {
        try
        {
            var messagingGroupExists = await uow.Db.ExistsAsync<MessagingGroup>(x => x.Name == upload.Description, ct);
            if (messagingGroupExists)
                return "Messaging group exists. Either delete or change the description to create a new group";

            var start = worksheet.Dimension.Start;
            var end = worksheet.Dimension.End;
            var loadedData = worksheet.Cells[$"{start.Address}:{end.Address}"].ToCollectionWithMappings
                (
                    row => new MessageGroupUploadData
                    {
                        Name = row.GetText(0)?.Trim(),
                        Email = row.GetText(1)?.Trim()?.ToUpperInvariant(),
                        PhoneCode = row.GetText(2)?.Trim(),
                        PhoneNumber = row.GetValue<string>(3)?.Trim(),
                    },
                    options =>
                    {
                        options.HeaderRow = null;
                        options.DataStartRow = 1;
                    }
                );

            if (loadedData.Count == 0)
            {
                resultsList.Add(new(0, string.Empty, "No records found"));
                return "No records found";
            }

            var parallelData = loadedData.AsParallel();
            //check that data is valid
            if (upload.Action != UploadAction.Import)
            {
                Parallel.ForEach(parallelData, new ParallelOptions { MaxDegreeOfParallelism = loadedData.Count, CancellationToken = ct }, (item, state, index) =>
                {
                    var row = index + 2;
                    if (string.IsNullOrEmpty(item.Name))
                        resultsList.Add(new(row, string.Empty, "No name"));

                    if (string.IsNullOrEmpty(item.Email))
                        resultsList.Add(new(row, string.Empty, "No email"));

                    if (string.IsNullOrEmpty(item.PhoneCode))
                        resultsList.Add(new(row, string.Empty, "No phone code"));

                    if (string.IsNullOrEmpty(item.PhoneNumber))
                        resultsList.Add(new(row, string.Empty, "No phone number"));
                });
            }

            if (upload.Action == UploadAction.Analyze)
                return message;

            //prepare for insert

            var messageGroup = new MessagingGroup
            {
                Name = upload.Description,
            };

            uow.Db.Insert(messageGroup);
            await uow.SaveAsync(ct);

            var directory = parallelData.Select(x => new MessagingGroupEntry
            {
                MessagingGroupId = messageGroup.Id,
                Name = x.Name,
                Emails = [.. x.Email.Split(';', StringSplitOptions.TrimEntries)],
                PhoneNumbers = [.. x.PhoneNumber.Split(";", StringSplitOptions.TrimEntries).Select(y => new PhoneNumber(x.PhoneCode, x.PhoneNumber.CleanPhoneNumber(x.PhoneCode)))],
            });

            uow.Db.InsertBulk(directory, ct);
            await uow.SaveAsync(ct);

            return message;
        }
        catch (Exception ex)
        {
            resultsList.Add(new(0, string.Empty, ex.Message));
            Logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Message group upload failed for upload: {upload.Description}");
        }

        return "Uploading group data failed";
    }
}

internal sealed class MessageGroupUploadData
{
    public string Name { get; set; }
    public string Email { get; set; }
    public string PhoneCode { get; set; }
    public string PhoneNumber { get; set; }
}
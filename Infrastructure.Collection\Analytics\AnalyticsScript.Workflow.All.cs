﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Infrastructure.Core.Database.AnalyticsTriggers;

namespace LendQube.Infrastructure.Collection.Analytics;

static partial class AnalyticsScript
{
    private static void PrepareAllWorkflowDashboardAnalyticsTrigger(AnalyticsTriggerModel trigger)
    {
        PrepareAllTotalAssignedTrigger(trigger);
        PrepareAllTotalOpenedTrigger(trigger);
        PrepareAllTotalClosedTrigger(trigger);
    }

    private static void PrepareAllTotalAssignedTrigger(AnalyticsTriggerModel trigger)
    {
        //add dashboard trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(DebtWorkflowAnalytics),
            Condition = $@"{AnalyticsTriggerHelpers.NewRecordCondition} AND NEW.""{nameof(AgentWorkflowTask.Assigned)}"" IS NOT NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(DebtWorkflowAnalytics.TotalAssigned),
                    RightExpression = $@" ""{nameof(DebtWorkflowAnalytics.TotalAssigned)}"" + 1"
                }
            ],
            UpdateScriptWhere = @"""Id"" = 1",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(DebtWorkflowAnalytics.TotalAssigned), nameof(DebtWorkflowAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(DebtWorkflowAnalytics.CreatedDate)}""" }]
            }
        });

        //add dashboard time trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(DebtWorkflowTimeAnalytics),
            Condition = $@"{AnalyticsTriggerHelpers.NewRecordCondition} AND NEW.""{nameof(AgentWorkflowTask.Assigned)}"" IS NOT NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(DebtWorkflowTimeAnalytics.TotalAssigned),
                    RightExpression = $@" ""{nameof(DebtWorkflowTimeAnalytics.TotalAssigned)}"" + 1"
                }
            ],
            UpdateScriptWhere = $@"""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate}",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(DebtWorkflowTimeAnalytics.Date), nameof(DebtWorkflowTimeAnalytics.TotalAssigned), nameof(DebtWorkflowTimeAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDate }, new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(DebtWorkflowTimeAnalytics.CreatedDate)}""" }]

            }
        });
    }
    private static void PrepareAllTotalOpenedTrigger(AnalyticsTriggerModel trigger)
    {
        //add dashboard trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(DebtWorkflowAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Opened)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Opened)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(DebtWorkflowAnalytics.TotalOpened),
                    RightExpression = $@" ""{nameof(DebtWorkflowAnalytics.TotalOpened)}"" + 1"
                }
            ],
            UpdateScriptWhere = @"""Id"" = 1",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(DebtWorkflowAnalytics.TotalOpened), nameof(DebtWorkflowAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(DebtWorkflowAnalytics.CreatedDate)}""" }]
            }
        });

        //add dashboard time trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(DebtWorkflowTimeAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Opened)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Opened)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(DebtWorkflowTimeAnalytics.TotalOpened),
                    RightExpression = $@" ""{nameof(DebtWorkflowTimeAnalytics.TotalOpened)}"" + 1"
                }
            ],
            UpdateScriptWhere = $@"""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate}",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(DebtWorkflowTimeAnalytics.Date), nameof(DebtWorkflowTimeAnalytics.TotalOpened), nameof(DebtWorkflowTimeAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDate }, new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(DebtWorkflowTimeAnalytics.CreatedDate)}""" }]

            }
        });
    }

    private static void PrepareAllTotalClosedTrigger(AnalyticsTriggerModel trigger)
    {
        //add dashboard trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(DebtWorkflowAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Removed)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Removed)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(DebtWorkflowAnalytics.TotalClosed),
                    RightExpression = $@" ""{nameof(DebtWorkflowAnalytics.TotalClosed)}"" + 1"
                }
            ],
            UpdateScriptWhere = @"""Id"" = 1",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(DebtWorkflowAnalytics.TotalClosed), nameof(DebtWorkflowAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(DebtWorkflowAnalytics.CreatedDate)}""" }]
            }
        });

        //add dashboard time trigger
        trigger.Triggers.Add(new AnalyticsTriggerModelScript
        {
            TargetSchema = CollectionEntityConfig.DefaultSchema,
            TargetTable = nameof(DebtWorkflowTimeAnalytics),
            Condition = $@"NEW.""{nameof(AgentWorkflowTask.Removed)}"" IS NOT NULL AND OLD.""{nameof(AgentWorkflowTask.Removed)}"" IS NULL",
            UpdateScript =
            [
                new()
                {
                    LeftExpression = nameof(DebtWorkflowTimeAnalytics.TotalClosed),
                    RightExpression = $@" ""{nameof(DebtWorkflowTimeAnalytics.TotalClosed)}"" + 1"
                }
            ],
            UpdateScriptWhere = $@"""{nameof(DebtWorkflowTimeAnalytics.Date)}"" = {AnalyticsTriggerHelpers.CurrentDate}",
            InsertScript = new()
            {
                TargetColumnNames = [nameof(DebtWorkflowTimeAnalytics.Date), nameof(DebtWorkflowTimeAnalytics.TotalClosed), nameof(DebtWorkflowTimeAnalytics.CreatedDate)],
                SourceValues = [new() { Type = InsertValueType.Value, Value = AnalyticsTriggerHelpers.CurrentDate }, new() { Type = InsertValueType.Value, Value = "1" },
                    new() { Type = InsertValueType.Column, Value = $@"""{nameof(DebtWorkflowTimeAnalytics.CreatedDate)}""" }]

            }
        });
    }
}

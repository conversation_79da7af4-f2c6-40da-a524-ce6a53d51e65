﻿using LendQube.Entities.Core.Attributes;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace LendQube.Entities.Core.Base;

public abstract class BaseEntityWithGuid : IBaseEntityWithStringId, IAddToDbContext
{
    [DbGuid, DatabaseGenerated(DatabaseGeneratedOption.None), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.ShowInInfo), Required(AllowEmptyStrings = false)]
    public string Id { get; set; }
}


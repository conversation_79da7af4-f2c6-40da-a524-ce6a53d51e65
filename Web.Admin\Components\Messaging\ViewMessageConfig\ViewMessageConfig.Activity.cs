﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components.Timeline;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericSpecification;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageConfig;

public partial class ViewMessageConfig
{
    private ActivityTimeline<MessagingConfigurationActivity> activityTimeline;

    private ValueTask<TypedBasePageList<MessagingConfigurationActivity>> LoadActivity(DataFilterAndPage filterAndPage, GenericSpecificationService<MessagingConfigurationActivity> service, CancellationToken ct)
    {
        service.PrimaryCriteria = x => x.MessageConfigurationId == Data.Id;
        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            service.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Title, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Activity, filterAndPage.TextFilter) || EF.Functions.ILike(x.CreatedByUser, filterAndPage.TextFilter));
        }

        return service.CrudService.GetTypeBasedPagedData(service, filterAndPage, ct: ct);
    }
}

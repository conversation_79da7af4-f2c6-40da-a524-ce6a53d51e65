﻿using LendQube.Entities.Collection.Customers;

namespace LendQube.Infrastructure.Collection.Payments;

public static class TransactionHelper
{
    public const string PaymentMethodId = "PaymentMethodId";
    public const string PaymentMethod = "Payment Method";
    public const string TransactionType = "TransactionType";
    public static readonly TransactionStatus FinalStatus = TransactionStatus.Completed | TransactionStatus.Successful | TransactionStatus.Failed | TransactionStatus.Refunded;
    public static readonly TransactionStatus ProcessingStatus = TransactionStatus.Initiated | TransactionStatus.Validated | TransactionStatus.Queued | TransactionStatus.Processing;
    public static readonly List<TransactionStatus> InProgressStatus = [TransactionStatus.Validated, TransactionStatus.Processing, TransactionStatus.Successful];
}
﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Base;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace LendQube.Entities.Collection.Customers;

public class CustomerPaymentMethod : BaseEntityWithGuid, IEntityHasEnum, IEntityTypeConfiguration<CustomerPaymentMethod>
{
    [DbGuid, Required]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public long CustomerPaymentMethodConfigurationId { get; set; }
    public virtual CustomerPaymentMethodConfiguration Config { get; set; }
    public string ProviderId { get; set; }
    public bool CanBeReused { get; set; }
    public PaymentMethodStatus Status { get; set; }
    public CardResponseVM Data { get; set; }
    public DateOnly? ExpiryDate { get; set; }

    public void Configure(EntityTypeBuilder<CustomerPaymentMethod> builder)
    {
        builder.OwnsOne(x => x.Data, d => d.ToJson());
    }
    public void RegisterEnumInDataSource(NpgsqlDataSourceBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<PaymentMethodStatus>($"{CollectionEntityConfig.DefaultSchema}.{nameof(PaymentMethodStatus)}", nameTranslator);
    }
    public void RegisterEnumInDataSource(NpgsqlDbContextOptionsBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<PaymentMethodStatus>(nameof(PaymentMethodStatus), CollectionEntityConfig.DefaultSchema, nameTranslator);
    }
}

public enum PaymentMethodStatus
{
    Active,
    Suspended,
    FailedLastCharge,
    Disabled
}

public record CardResponseVM(string Last4, CardBrand Brand);
﻿using LendQube.Entities.Core.Attributes;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Entities.Core.Constants;

public sealed class AddressVM
{
    [Required, ValidString(ValidStringRule.OnlyTextOrNumberOrCommaOrPeriodOrApostropheOrHyphenWithSpacing)]
    public string AddressLine1 { get; set; }
    [ValidString(ValidStringRule.OnlyTextOrNumberOrCommaOrPeriodOrApostropheOrHyphenWithSpacing)]
    public string AddressLine2 { get; set; }
    [Required, DataType(DataType.PostalCode)]
    public string PostalCode { get; set; }
    [Required, ValidString(ValidStringRule.OnlyTextAndNumberWithSpacing)]
    public string Locality { get; set; }
    [Required, ValidString(ValidStringRule.OnlyTextAndNumberWithSpacing)]
    public string City { get; set; }
    [Required, MaxLength(EntityConstants.DEFAULT_COUNTRY_CODE_FIELD_LENGTH), ValidString(ValidStringRule.OnlyText)]
    public string CountryCode { get; set; }
}

﻿using System.Text;

namespace LendQube.Infrastructure.Core.Helpers.Utils;

public static class ConversionHelper
{
    public static string Base64Encode(this string plainText, Encoding encoding = null) =>
        Convert.ToBase64String((encoding ?? Encoding.UTF8).GetBytes(plainText));

    public static string Base64Decode(this string base64EncodedData, Encoding encoding = null) =>
        (encoding ?? Encoding.UTF8).GetString(Convert.FromBase64String(base64EncodedData));

    public static byte[] Base64DecodeToBytes(this string base64EncodedData, Encoding encoding = null) =>
        Convert.FromBase64String(base64EncodedData.Base64Decode(encoding));
}
﻿using System.Globalization;
using Coravel.Invocable;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using NodaTime;

namespace LendQube.Infrastructure.Collection.PromiseToPay;

public sealed class PromiseToPayReminderService(IUnitofWork uow, IClock clock, ILogManager<PromiseToPayReminderService> logger) : IInvocable, ICancellableInvocable
{
    public CancellationToken CancellationToken { get; set; }

    public async Task Invoke()
    {
        var now = clock.GetCurrentInstant().InUtc().Date;
        var oneWeekBeforeDuePtp = await uow.Db.ManySelectAsync(Query<CustomerPromiseToPay>.Where(x => !x.Redeemed && x.DueDate.Value.InUtc().Date - now == Period.FromWeeks(1))
            .Select(x => new { x.ProfileId, x.Profile.CurrencySymbol, Balance = x.Amount - x.AmountPaid, x.DueDate, Companies = x.Profile.Placements.Select(x => x.Company).ToList() }), CancellationToken);
        var oneDayBeforeDuePtp = await uow.Db.ManySelectAsync(Query<CustomerPromiseToPay>.Where(x => !x.Redeemed && x.DueDate.Value.InUtc().Date - now == Period.FromDays(1))
            .Select(x => new { x.ProfileId, x.Profile.CurrencySymbol, Balance = x.Amount - x.AmountPaid, x.DueDate, Companies = x.Profile.Placements.Select(x => x.Company).ToList() }), CancellationToken);

        oneWeekBeforeDuePtp = [.. oneWeekBeforeDuePtp.OrderBy(x => x.ProfileId)];
        oneDayBeforeDuePtp = [.. oneDayBeforeDuePtp.OrderBy(x => x.ProfileId)];

        var oneWeekUserIds = oneWeekBeforeDuePtp.Select(x => x.ProfileId).ToList();
        var oneWeekSenderInfo = oneWeekBeforeDuePtp.ToDictionary(x => x.ProfileId, x => new List<TemplateKeyValue>
        {
            new($"{MessageTemplateKeys.Amount}", $"{x.CurrencySymbol}{x.Balance:n2}"),
            new($"{MessageTemplateKeys.CompanyName}", string.Join(", ", x.Companies)),
            new($"{MessageTemplateKeys.DueDate}", x.DueDate.Value.InZone(InstantExtensions.LONDON_TIMEZONE).ToString("dd MMM, yyyy", CultureInfo.InvariantCulture))
        });

        var oneDayUserIds = oneDayBeforeDuePtp.Select(x => x.ProfileId).ToList();
        var oneDaySenderInfo = oneDayBeforeDuePtp.ToDictionary(x => x.ProfileId, x => new List<TemplateKeyValue>
        {
            new($"{MessageTemplateKeys.Amount}", $"{x.CurrencySymbol}{x.Balance:n2}"),
            new($"{MessageTemplateKeys.CompanyName}", string.Join(", ", x.Companies)),
            new($"{MessageTemplateKeys.DueDate}", x.DueDate.Value.InZone(InstantExtensions.LONDON_TIMEZONE).ToString("dd MMM, yyyy", CultureInfo.InvariantCulture))
        });

        _ = await MessageBuilder.New(MessageConfigNames.PTPReminder.GetDisplayName(), null)
            .Message(MessageConfigNames.PTPDueInOneWeek.GetDisplayName())
            .WithRecipientsWithIndividualKeys(oneWeekUserIds, oneWeekSenderInfo)
            .Message(MessageConfigNames.PTPDueInOneDay.GetDisplayName())
            .WithRecipientsWithIndividualKeys(oneDayUserIds, oneDaySenderInfo)
            .Send(uow, logger, CancellationToken);
    }
}

﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Setup;

public class CustomerNoteTemplate : BaseEntityWithIdentityId<CustomerNoteTemplate>
{
    [Required, MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH)]
    public string Name { get; set; }
    [Required]
    public CustomerNoteType Type { get; set; }
    public CustomerContactType? ContactType { get; set; }
    [Required]
    public string Template { get; set; }

    public override void Configure(EntityTypeBuilder<CustomerNoteTemplate> builder)
    {
        base.Configure(builder);

        builder.HasIndex(x => x.Name).IsUnique()
            .IncludeProperties(x => new { x.Type, x.ContactType });
    }
}

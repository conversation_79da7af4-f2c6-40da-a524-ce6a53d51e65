﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Core.Messaging;

[DbTableFillFactor(70)]
public class MessageConfiguration : BaseEntityWithHiloId, IEntityTypeConfiguration<MessageConfiguration>
{
    [Required]
    public string Name { get; set; }
    [Required]
    public string Description { get; set; }
    public string Subject { get; set; }
    public string SenderEmail { get; set; }
    public string SenderName { get; set; }
    [Required]
    public MessageChannel Channels { get; set; }
    public List<string> Keys { get; set; } = [];
    public bool DoNotSendIfExists { get; set; }
    public int ExistsCheckWindow { get; set; }
    public long? BodyTemplateId { get; set; }
    public virtual MessagingTemplate? BodyTemplate { get; set; }
    public long? ContainerTemplateId { get; set; }
    public virtual MessagingTemplate? ContainerTemplate { get; set; }
    public string TextTemplate { get; set; }
    public string HtmlTemplate { get; set; }
    public virtual ICollection<MessagingGroup> SendDirectory { get; set; }
    public virtual ICollection<MessagingConfigurationActivity> Activity { get; set; }

    public void Configure(EntityTypeBuilder<MessageConfiguration> builder)
    {
        builder.HasIndex(e => e.Name)
            .IsUnique()
            .IncludeProperties(e => e.Channels);

        builder.HasMany(e => e.SendDirectory)
            .WithMany(e => e.Configs)
            .UsingEntity<MessageConfigurationMessagingGroup>();
    }

    public bool TextRequired => Channels.IsFlagSet(MessageChannel.Text);
    public bool TextConfigured => !string.IsNullOrWhiteSpace(TextTemplate) || //local text template was provided
        ((BodyTemplate != null && BodyTemplate.Types.HasFlag(MessageConfigTemplateType.Text)) || (ContainerTemplate != null && ContainerTemplate.Types.HasFlag(MessageConfigTemplateType.Text))); //configured text template was provided

    public bool EmailRequired => Channels.IsFlagSet(MessageChannel.Email | MessageChannel.SmsOrEmail | MessageChannel.PushNotificationAndEmail | MessageChannel.PushNotificationOrEmail | MessageChannel.SmsAndEmail | MessageChannel.EmailOrSms);
    public bool EmailConfigured => !string.IsNullOrWhiteSpace(HtmlTemplate) ||
        ((BodyTemplate != null && BodyTemplate.Types.HasFlag(MessageConfigTemplateType.Html)) || (ContainerTemplate != null && ContainerTemplate.Types.HasFlag(MessageConfigTemplateType.Html)));

    public bool NeedsConfiguration => string.IsNullOrEmpty(Subject) || (string.IsNullOrEmpty(TextTemplate) && string.IsNullOrEmpty(HtmlTemplate) && !ContainerTemplateId.HasValue && !BodyTemplateId.HasValue);
}

public class MessageConfigurationMessagingGroup : IBaseEntityForRelationalDb, IEntityDoesNotUseHiLoIdentity
{
    public long MessageConfigurationId { get; set; }
    public long MessagingGroupId { get; set; }
}

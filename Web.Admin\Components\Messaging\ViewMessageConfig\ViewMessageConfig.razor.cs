﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericSpecification;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.FileManagement;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageConfig;

public partial class ViewMessageConfig
{
    [Inject] IJSRuntime jSRuntime { get; set; }
    [Inject] IFileManagementService fileService { get; set; }
    [Inject] HttpClient httpClient { get; set; }
    [Inject] GenericVMSpecificationService<MessagingGroup, MessagingGroupVM> groupService { get; set; }

    [Parameter]
    public long MessageId { get; set; }
    private string Title { get; set; } = "Configure Message";
    private MessageConfiguration Data { get; set; } = new();
    private StatusMessage message;
    private IEnumerable<string> systemKeys { get; set; } = EnumExtensions.GetEnumNames<MessageTemplateSystemTags>();
    private IUnitofWork uow;

    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
        {
            await LoadData();
            await jSRuntime.RunFeather(Cancel);
        }
    }

    protected override void OnInitialized()
    {
        uow = groupService.CrudService.Uow;
        GroupTableDefinition = groupService.CrudService.GetTableDefinition(new() { HasDelete = true });
        GroupTableDefinition.TopActionButtons.Add(new TopActionButton("Add Directory", ModalName: addGroupModal));
    }

    private async Task Reload()
    {
        await LoadData();

        if (hasPendingChanges)
        {
            hasPendingChanges = false;
        }
    }

    private async Task LoadData()
    {
        Data = await uow.Db.OneAsync(Query<MessageConfiguration>.Where(x => x.Id == MessageId).Include(x => x.Include(y => y.BodyTemplate).Include(y => y.ContainerTemplate)), Cancel);

        if (Data != null)
        {
            AllGroups = await uow.Db.ManyAsync(Query<MessagingGroup>.Where(x => !x.ConfigGroups.Any(y => y.MessageConfigurationId == Data.Id)), Cancel);

            if (Data.TextRequired && !Data.TextConfigured)
            {
                message.Warning("This message contains text channels but no text template has been configured");
            }
            else if (Data.EmailRequired && !Data.EmailConfigured)
            {
                message.Warning("This message contains email channels but no email template has been configured");
            }
            else if (string.IsNullOrEmpty(Data.Subject))
            {
                message.Warning("No subject has been set for this message");
            }

            var configuredTemplateQuery = Query<MessagingTemplate>.Where(x => !x.DisabledOn.HasValue);

            if (Data.TextRequired)
            {
                if (!string.IsNullOrEmpty(Data.TextTemplate))
                {
                    pagedTextTemplate.Text = Data.TextTemplate;
                    hasTextCustomChanges = true;
                }

                configuredTemplateQuery.AndWhere(x => x.Types.HasFlag(MessageConfigTemplateType.Text));
            }

            if (Data.EmailRequired)
            {
                if (!string.IsNullOrEmpty(Data.HtmlTemplate))
                {
                    htmlTemplate = await httpClient.ReadPhysicalFileAsString(Data.HtmlTemplate, ct: Cancel);
                    hasHtmlCustomChanges = true;

                }

                configuredTemplateQuery.AndWhere(x => x.Types.HasFlag(MessageConfigTemplateType.Html));
            }

            ConfiguredTemplates = await uow.Db.ManyAsync(configuredTemplateQuery, Cancel);
            await LoadContainerTemplate();
            await LoadBodyTemplate();
            await groupTable.LoadElement();

            StateHasChanged();
        }
    }
}
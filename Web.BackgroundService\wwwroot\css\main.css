:root {
    --core-primary: #3fbb53;
  --core-light-accent: #89ff9e;
    --core-dark-accent: #17471f;
  --neutral-0: #ffffff;
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;
  --neutral-950: #0a0a0a;
  --blue-100: #d4f3ff;
  --blue-600: #0077ff;
  --blue-900: #0852a0;
  --green-100: #d7ffe6;
  --green-600: #00c14e;
  --green-900: #0a5d2e;
  --red-100: #fee8d6;
  --red-600: #e34013;
  --red-900: #792215;
  --yellow-100: #fffec2;
  --yellow-600: #ce9900;
  --yellow-900: #734510;
}

:root {
  --content-primary: var(--neutral-950);
  --content-secondary: var(--neutral-600);
  --content-tertiary: var(--neutral-400);
  --content-inverse: var(--neutral-0);
  --content-link: var(--core-dark-accent);
  --background-screen: var(--neutral-0);
  --background-surface: var(--neutral-0);
  --background-neutral: var(--neutral-100);
  --background-dark-neutral: var(--neutral-200);
  --background-inverse: var(--neutral-950);
  --border-primary: var(--core-primary);
  --border-light-neutral: var(--neutral-200);
  --border-dark-neutral: var(--neutral-400);
  --interactive-primary: var(--core-primary);
  --interactive-accent: var(--core-light-accent);
  --interactive-control: var(--core-dark-accent);
  --interactive-secondary: var(--neutral-500);
  --info-surface: var(--blue-100);
  --info-text: var(--blue-900);
  --info-fill: var(--blue-600);
  --info-on-fill: var(--blue-100);
  --success-surface: var(--green-100);
  --success-text: var(--green-900);
  --success-fill: var(--green-600);
  --success-on-fill: var(--green-100);
  --critical-surface: var(--red-100);
  --critical-text: var(--red-900);
  --critical-fill: var(--red-600);
  --critical-on-fill: var(--red-100);
  --warning-surface: var(--yellow-100);
  --warning-text: var(--yellow-900);
  --warning-fill: var(--yellow-600);
  --warning-on-fill: var(--yellow-100);
  --btn-radius: 6px;
  --input-radius: 6px;
  --card-radius: 6px;
  --font-stack: "MatterSQ", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen-Sans, Ubuntu, Cantarell,
      "Helvetica Neue", sans-serif;
}

/*basic reset*/
* {
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

*:focus {
  outline: none;
}

/*GLOBAL PAGE STYLES*/
html,
body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: var(--font-stack);
  font-weight: 400;
  line-height: 1.43;
  width: 100%;
  color: var(--content-primary);
  scroll-behavior: smooth;
}

html {
  font-size: 10px;
}

body {
  font-size: 1.4rem;
  line-height: 1.43;
}

#wrapper {
  min-height: 100%;
  min-width: 100%;
  margin: 0;
  padding: 0;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
}

#footer-control {
  -webkit-box-flex: 1;
  -ms-flex: 1 0 auto;
  flex: 1 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
}
#footer-control > div {
  width: 100%;
}

a {
  text-decoration: underline;
  font-weight: 500;
  -webkit-transition: all 0.4s linear;
  transition: all 0.4s linear;
  font-size: 1.4rem;
  color: var(--content-link);
  user-select: none;
  display: inline-block;
  outline: none !important;
}
a.btn {
  text-decoration: none;
}

a:active,
a:focus {
  text-decoration: none;
  outline: none !important;
  box-shadow: none !important;
}

a:hover {
  color: var(--content-link);
  text-decoration: none;
  outline: none !important;
  box-shadow: none !important;
}

li {
  list-style: none;
}

button:focus {
  outline: none;
}

@font-face {
  font-family: "MatterSQ";
  font-style: normal;
  font-weight: 400;
  font-display: swap;
  src: url("../fonts/MatterSQ-Regular.woff") format("woff"), url("../fonts/MatterSQ-Regular.woff2") format("woff2");
}
@font-face {
  font-family: "MatterSQ";
  font-style: normal;
  font-weight: 500;
  font-display: swap;
  src: url("../fonts/MatterSQ-Medium.woff") format("woff"), url("../fonts/MatterSQ-Medium.woff2") format("woff2");
}
@font-face {
  font-family: "MatterSQ";
  font-style: normal;
  font-weight: 600;
  font-display: swap;
  src: url("../fonts/MatterSQ-SemiBold.woff") format("woff"), url("../fonts/MatterSQ-SemiBold.woff2") format("woff2");
}
h1,
h2,
h3,
h4,
h5,
h6 {
  font-family: var(--font-stack);
  color: var(--content-primary);
  font-weight: 600;
}

h1 {
  font-size: 48px;
  line-height: 1.25;
}

h2 {
  font-size: 40px;
  line-height: 1.2;
}

h3 {
  font-size: 36px;
  line-height: 1.22;
}

h4 {
  font-size: 28px;
  line-height: 1.29;
}

h5 {
  font-size: 24px;
  line-height: 1.33;
}

h6 {
  font-size: 20px;
  line-height: 1.6;
}

.text_xl_semibold {
  font-size: 1.8rem;
  line-height: 1.5;
  font-weight: 600;
}

.text_xl_medium {
  font-size: 1.8rem;
  line-height: 1.5;
  font-weight: 500;
}

.text_l_semibold {
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 600;
}

.text_l_medium {
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 500;
}

.text_l {
  font-size: 1.6rem;
  line-height: 1.5;
  font-weight: 400;
}

.text_medium {
  font-size: 1.4rem;
  line-height: 1.43;
  font-weight: 500;
}

.text_regular {
  font-size: 1.4rem;
  line-height: 1.43;
  font-weight: 400;
}

.text_sm_medium {
  font-size: 1.3rem;
  line-height: 1.38;
  font-weight: 500;
}

.text_sm_regular {
  font-size: 1.3rem;
  line-height: 1.38;
  font-weight: 400;
}

.text_xs_semibold {
  font-size: 1.2rem;
  line-height: 1.33;
  font-weight: 600;
}

.text_xs_medium {
  font-size: 1.2rem;
  line-height: 1.33;
  font-weight: 500;
}

p {
  font-family: var(--font-stack);
  font-weight: 400;
  font-size: 1.4rem;
  line-height: 1.43;
  color: var(--content-primary);
}

.semibold_font {
  font-weight: 600 !important;
}

.medium_font {
  font-weight: 500 !important;
}

.base-font {
  font-size: 1.4rem !important;
  line-height: 1.43;
  font-weight: 400;
}

.text-uppercase {
  text-transform: uppercase;
}

button:focus,
button:active {
  box-shadow: none !important;
  outline: none !important;
}

.btn {
  cursor: pointer;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  border: 0;
  -webkit-transition: all 0.3s linear;
  transition: all 0.3s linear;
  padding: 0 16px;
  font-size: 1.4rem;
  border-radius: var(--btn-radius);
  height: 36px;
  line-height: 36px;
  font-weight: 500;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}
.btn:hover {
  position: relative;
}
.btn.__center {
  margin-left: auto;
  margin-right: auto;
}
.btn.__full {
  width: 100%;
  margin-left: 0 !important;
}
.btn.disabled, .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}
.btn.__icon {
  display: flex;
  align-items: center;
}
.btn.__icon svg,
.btn.__icon .svg {
  height: 18px;
  width: 18px;
  margin-right: 6px;
}

.btn__sm {
  line-height: 32px !important;
  height: 32px;
  padding: 0 10px;
}

.btn__lg {
  line-height: 44px !important;
  height: 44px;
  padding: 0 20px;
  font-size: 1.6rem;
}

.loading:before {
  position: relative;
  -webkit-transition: opacity 0.1s linear;
  transition: opacity 0.1s linear;
  border-radius: 50%;
  content: "";
  display: inline-block;
  height: 16px;
  margin-right: 12px;
  width: 16px;
  -webkit-animation: spinner 0.9s linear infinite;
  animation: spinner 0.9s linear infinite;
}

.btn--primary {
  background: var(--interactive-primary);
  color: var(--content-inverse) !important;
}
.btn--primary:hover {
  background: var(--interactive-control);
}

.btn--default {
  border: 1px solid var(--border-dark-neutral);
  background: var(--background-surface);
  color: var(--content-primary) !important;
}
.btn--default:hover {
  border-color: var(--border-light-neutral);
}
.btn--default.loading:before {
  border: 2px solid var(--content-primary);
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.btn--gray {
  border: none;
  background: var(--background-neutral);
  color: var(--content-primary) !important;
}
.btn--gray:hover {
  border-color: none;
}
.btn--gray.loading:before {
  border: 2px solid var(--content-primary);
  border-bottom-color: transparent;
  border-left-color: transparent;
}

.btn--danger {
  background: var(--critical-fill);
  color: var(--content-inverse) !important;
}
.btn--danger:hover {
  background: var(--critical-surface);
  color: var(--critical-text) !important;
}
.btn--danger.__secondary {
  background: var(--background-surface);
  color: var(--critical-fill) !important;
  border: 1px solid var(--critical-fill);
}
.btn--danger.__secondary:hover {
  background: var(--critical-surface);
}

.btn--success {
  background: var(--success-fill);
  color: var(--content-inverse) !important;
}
.btn--success:hover {
  background: var(--success-surface);
  color: var(--success-text) !important;
}
.btn--success.__secondary {
  background: var(--background-surface);
  color: var(--success-fill) !important;
  border: 1px solid var(--success-fill);
}
.btn--success.__secondary:hover {
  background: var(--success-surface);
}

.btn--edit {
  background: var(--info-fill);
  color: var(--content-inverse) !important;
}
.btn--edit:hover {
  background: var(--info-surface);
  color: var(--info-text) !important;
}
.btn--edit.__secondary {
  background: var(--background-surface);
  color: var(--info-fill) !important;
  border: 1px solid var(--info-fill);
}
.btn--edit.__secondary:hover {
  background: var(--info-surface);
}

.btn--link {
  background: none;
  color: var(--content-link) !important;
}

.btn--icon {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 36px;
  width: 36px;
  padding: 0 !important;
  border-radius: 6px;
  border: 1px solid transparent !important;
}
.btn--icon.__edit {
  background: var(--info-surface) !important;
  color: var(--info-text) !important;
}
.btn--icon.__default {
  background: var(--background-surface) !important;
  color: var(--content-primary) !important;
  border-color: var(--border-light-neutral) !important;
}
.btn--icon.__danger {
  background: var(--critical-surface) !important;
  color: var(--critical-text) !important;
}
.btn--icon svg,
.btn--icon span.svgs {
  height: 18px;
  width: 18px;
}

.flex {
  display: flex;
}
.flex.__row {
  flex-direction: row;
}
.flex.__row-reverse {
  flex-direction: row-reverse;
}
.flex.__column {
  flex-direction: column;
}
.flex.__column-reverse {
  flex-direction: column-reverse;
}
.flex.__justify-between {
  justify-content: space-between;
}
.flex.__align-center {
  align-items: center;
}
.flex.__align-start {
  align-items: flex-start;
}
.flex.__nowrap {
  flex-wrap: nowrap;
}
.flex.__wrap {
  flex-wrap: wrap !important;
}
.flex > .form-row {
  width: 45%;
  align-self: baseline;
}

.no-border {
  border: 0;
}

.mx-auto {
  margin-left: auto !important;
  margin-right: auto !important;
}

.ml-auto {
  margin-left: auto !important;
  margin-right: 0;
}

.mr-auto {
  margin-right: auto !important;
  margin-left: 0;
}

.col-10 {
  width: 10%;
}

.col-20 {
  width: 20%;
}

.col-25 {
  width: 25%;
}

.col-30 {
  width: 30%;
}

.col-40 {
  width: 40%;
}

.col-45 {
  width: 45%;
}

.col-50 {
  width: 50%;
}

.col-60 {
  width: 60%;
}

.col-70 {
  width: 70%;
}

.col-80 {
  width: 80%;
}

.col-90 {
  width: 90%;
}

.col-100 {
  width: 100%;
}

.grid {
  display: grid;
}

.grid-col-1 {
  grid-template-columns: repeat(1, 1fr);
}

.grid-col-2 {
  grid-template-columns: repeat(2, 1fr);
}

.grid-col-3 {
  grid-template-columns: repeat(3, 1fr);
}

.grid-col-4 {
  grid-template-columns: repeat(4, 1fr);
}

.grid-col-5 {
  grid-template-columns: repeat(5, 1fr);
}

.grid-col-6 {
  grid-template-columns: repeat(6, 1fr);
}

@media only screen and (max-width: 1200px) {
  .grid-tab-2 {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media only screen and (max-width: 600px) {
  .grid-tab-2 {
    grid-template-columns: repeat(1, 1fr);
  }
}

@media only screen and (max-width: 1200px) {
  .grid-tab-1 {
    grid-template-columns: repeat(1, 1fr) !important;
  }
}

.hidden {
  display: none;
}

.brand_img {
  height: auto;
  display: block;
  overflow: hidden;
}
.brand_img img {
  height: 100%;
  width: 100%;
}

.accordion-item {
  border: none;
}

input,
select,
textarea {
  font-size: 1.4rem;
  transition: 0.3s linear !important;
  border-radius: var(--input-radius);
  background: var(--background-surface);
  border: 1px solid var(--border-dark-neutral);
  position: relative;
  width: 100%;
}
input:hover,
select:hover,
textarea:hover {
  border-color: var(--border-primary) !important;
}
input:focus,
select:focus,
textarea:focus {
  background: var(--background-surface);
  border-color: var(--border-primary) !important;
}

textarea {
  padding: 6px;
}

input,
select,
.form-input,
.form-select {
  height: 36px !important;
  line-height: 36px;
  border-radius: var(--input-radius);
  padding: 0 8px;
  font-size: 1.4rem;
  color: var(--content-primary);
  display: inline-flex;
  align-items: center;
}
input.__sm,
select.__sm,
.form-input.__sm,
.form-select.__sm {
  height: 32px !important;
  line-height: 32px;
}
input:focus,
select:focus,
.form-input:focus,
.form-select:focus {
  box-shadow: none !important;
}

.form-control {
  font-size: 1.4rem !important;
}

label,
.form-label {
  margin-bottom: 4px;
  font-size: 1.4rem;
  line-height: 1.43;
  color: var(--content-secondary);
  display: flex;
  align-items: baseline;
  position: relative;
  font-weight: 400;
  width: 100%;
}
label.optional:after,
.form-label.optional:after {
  content: "Optional";
  display: block;
  font-size: 1.3rem;
  margin-left: auto;
  color: var(--interactive-control);
}

.form,
form {
  padding: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  width: 100%;
  max-width: 720px;
}
.form .grid-col-2,
form .grid-col-2 {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-gap: 20px;
}
@media only screen and (max-width: 600px) {
  .form .grid-col-2,
  form .grid-col-2 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.form .grid-col-3,
form .grid-col-3 {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  grid-gap: 10px;
}
@media only screen and (max-width: 600px) {
  .form .grid-col-3,
  form .grid-col-3 {
    grid-template-columns: repeat(1, 1fr);
  }
}
.form .text_small,
form .text_small {
  color: var(--content-secondary);
  margin-top: 4px;
  font-size: 1.2rem;
  line-height: 1.2;
}
.form .button-row,
form .button-row {
  margin-top: 28px;
  margin-bottom: 20px;
  flex-direction: row;
  display: flex;
  justify-content: flex-end;
}
.form .button-row *:not(:last-child),
form .button-row *:not(:last-child) {
  margin-right: 20px;
}

.form-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  margin-right: 0 !important;
  margin-left: 0 !important;
  position: relative;
  width: 100%;
}
.form-row.subheader {
  margin-bottom: 10px;
}
.form-row .value {
  font-size: 3.2rem;
  line-height: 1.22;
  font-weight: 600;
}
.form-row:has(.__error:not(:empty)) input,
.form-row:has(.__error:not(:empty)) select,
.form-row:has(.__error:not(:empty)) .form-input,
.form-row:has(.__error:not(:empty)) .form-select {
  border-color: var(--critical-fill);
}
.form-row .__error:not(:empty) > span {
  font-size: 1.2rem;
  line-height: 1.2;
  font-weight: 500;
  margin-left: 1px;
  margin-top: 2px;
  letter-spacing: 0.01em;
  position: relative;
  color: var(--critical-fill) !important;
}
.form-row .__error:not(:empty) > span:before {
  content: "";
  display: inline-block;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='14' height='14' viewBox='0 0 14 14' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_408_2264)'%3E%3Cpath d='M11.1248 2.87521C10.309 2.0594 9.26958 1.50383 8.13803 1.27875C7.00647 1.05367 5.83358 1.16919 4.76768 1.6107C3.70178 2.05221 2.79074 2.79989 2.14976 3.75917C1.50879 4.71846 1.16667 5.84628 1.16667 7C1.16667 8.15373 1.50879 9.28154 2.14976 10.2408C2.79074 11.2001 3.70178 11.9478 4.76768 12.3893C5.83358 12.8308 7.00647 12.9463 8.13803 12.7212C9.26958 12.4962 10.309 11.9406 11.1248 11.1248C11.6665 10.5831 12.0961 9.94005 12.3893 9.23232C12.6825 8.52459 12.8333 7.76604 12.8333 7C12.8333 6.23396 12.6825 5.47541 12.3893 4.76768C12.0961 4.05995 11.6665 3.41689 11.1248 2.87521V2.87521ZM3.31656 9.85848C2.6294 8.96019 2.29184 7.84292 2.36665 6.71442C2.44145 5.58592 2.92354 4.52298 3.72326 3.72326C4.52299 2.92354 5.58592 2.44145 6.71442 2.36665C7.84292 2.29184 8.96019 2.6294 9.85848 3.31656L3.31656 9.85848ZM4.14152 10.6834L10.6834 4.14152C11.3706 5.03981 11.7082 6.15708 11.6334 7.28558C11.5585 8.41408 11.0765 9.47702 10.2767 10.2767C9.47702 11.0765 8.41408 11.5585 7.28558 11.6334C6.15708 11.7082 5.03981 11.3706 4.14152 10.6834Z' fill='%23E34013'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_408_2264'%3E%3Crect width='14' height='14' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
  height: 14px;
  width: 14px;
  background-size: contain;
  margin-right: 0;
  position: relative;
  top: 3px;
  margin-right: 2px;
}

.form__login {
  max-width: 480px;
  margin: 10px auto;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  flex-wrap: nowrap;
  transition: 0.3s linear !important;
  border-radius: var(--input-radius);
  background: var(--background-surface);
  border: 1px solid var(--border-dark-neutral);
}
.input-group > select {
  width: 80px !important;
  font-size: 1.4rem !important;
  cursor: pointer;
}
.input-group:has(.__prepend) {
  padding-left: 4px;
}
.input-group:has(.__append) {
  padding-right: 4px;
}
.input-group input,
.input-group .form-input {
  border: none;
}
.input-group .__prepend,
.input-group .__append {
  padding: 0 6px;
  height: 28px !important;
  border: none !important;
  line-height: 28px !important;
  display: inline-flex;
  align-items: center;
  text-align: center;
  font-size: 1.4rem;
  font-weight: 500;
  background: var(--background-neutral);
  overflow: hidden;
  border-radius: 6px !important;
  z-index: 10;
  color: var(--content-secondary);
}

.radio-group {
  padding: 0 2px;
  height: auto;
  display: flex;
  flex-direction: column;
}
.radio-group .radio-label {
  display: block;
  position: relative;
  padding-left: 20px;
  margin-right: 32px;
  cursor: pointer;
  user-select: none;
  margin-bottom: 12px;
  width: max-content;
  color: var(--content-primary);
}
.radio-group .radio-label input.radio-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  font-size: 1.8rem;
  height: auto !important;
  line-height: 1 !important;
  top: -2px;
  left: 0;
  margin-right: 10px;
  margin-left: 0;
}
.radio-group .radio-label input.radio-input:checked ~ .radiomark {
  background-color: var(--interactive-control);
  border-color: var(--interactive-control);
}
.radio-group .radio-label input.radio-input:checked ~ .radiomark:after {
  display: block !important;
}
.radio-group .radio-label .radiomark {
  position: absolute;
  top: 2px;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: var(--background-surface);
  border: 1px solid var(--border-dark-neutral);
  border-radius: 50%;
}
.radio-group .radio-label .radiomark:after {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--background-surface);
  content: "";
  position: absolute;
  display: none;
}

.check-group {
  padding: 0 2px;
  height: auto;
  display: flex;
  flex-direction: column;
}
.check-group .check-label {
  display: block;
  position: relative;
  padding-left: 20px;
  margin-right: 32px;
  cursor: pointer;
  user-select: none;
  margin-bottom: 12px;
  width: max-content;
  color: var(--content-primary);
}
.check-group .check-label input.check-input {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  font-size: 1.8rem;
  height: auto !important;
  line-height: 1 !important;
  top: -2px;
  left: 0;
  margin-right: 10px;
  margin-left: 0;
}
.check-group .check-label input.check-input:checked ~ .checkmark {
  background: var(--interactive-control) !important;
  border-color: var(--interactive-control) !important;
}
.check-group .check-label input.check-input:checked ~ .checkmark:after {
  display: block !important;
}
.check-group .check-label .checkmark {
  position: absolute;
  top: 2px;
  left: 0;
  height: 16px;
  width: 16px;
  background-color: var(--background-surface);
  border: 1px solid var(--content-primary);
  border-radius: 3px;
}
.check-group .check-label .checkmark:after {
  top: 0;
  left: 0;
  width: 14px;
  height: 14px;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.6949 3.7051C11.8262 3.83637 11.8999 4.01439 11.8999 4.2C11.8999 4.38562 11.8262 4.56363 11.6949 4.6949L6.09494 10.2949C5.96367 10.4261 5.78566 10.4999 5.60004 10.4999C5.41443 10.4999 5.23641 10.4261 5.10514 10.2949L2.30514 7.4949C2.17763 7.36288 2.10707 7.18606 2.10867 7.00252C2.11026 6.81898 2.18388 6.64342 2.31367 6.51363C2.44345 6.38384 2.61902 6.31023 2.80256 6.30863C2.9861 6.30704 3.16292 6.37759 3.29494 6.5051L5.60004 8.8102L10.7051 3.7051C10.8364 3.57387 11.0144 3.50015 11.2 3.50015C11.3857 3.50015 11.5637 3.57387 11.6949 3.7051Z' fill='white'/%3E%3C/svg%3E");
  content: "";
  position: absolute;
  display: none;
}

.detail-row {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
  position: relative;
  width: 100%;
}
.detail-row .label {
  margin-bottom: 2px;
  font-size: 1.3rem;
  line-height: 1.43;
  color: var(--content-secondary);
  display: flex;
  align-items: baseline;
  position: relative;
  font-weight: 500;
  width: 100%;
}
.detail-row .value {
  font-size: 1.5rem;
  line-height: 1.5;
  font-weight: 500;
}

.lz {
  font-size: 1.4rem;
  line-height: 1;
  font-weight: 500;
  display: inline-block;
  padding: 5px 8px;
  border-radius: 4px;
  align-self: flex-start;
  text-transform: capitalize;
}
.lz.__blue {
  background: var(--info-surface);
  color: var(--info-text);
}
.lz.__red {
  background: var(--critical-surface);
  color: var(--critical-text);
}
.lz.__default {
  background: var(--background-dark-neutral);
  color: var(--content-primary);
}
.lz.__green {
  background: var(--success-surface);
  color: var(--success-text);
}
.lz.__yellow {
  background: var(--warning-surface);
  color: var(--warning-text);
}
.lz.__blue-bold {
  background: var(--info-fill);
  color: var(--content-inverse);
}
.lz.__red-bold {
  background: var(--critical-fill);
  color: var(--content-inverse);
}
.lz.__default-bold {
  background: var(--background-inverse);
  color: var(--content-inverse);
}
.lz.__green-bold {
  background: var(--success-fill);
  color: var(--content-inverse);
}
.lz.__yellow-bold {
  background: var(--warning-fill);
  color: var(--content-inverse);
}

.detail-wrapper {
  display: grid;
  grid-gap: 20px 10px;
}
.detail-wrapper.grid-2 {
  grid-template-columns: repeat(2, 1fr);
}
.detail-wrapper.grid-3 {
  grid-template-columns: repeat(3, 1fr);
}
.detail-wrapper.grid-4 {
  grid-template-columns: repeat(4, 1fr);
}
@media only screen and (max-width: 600px) {
  .detail-wrapper.grid-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}
.detail-wrapper .detail-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}
.detail-wrapper .detail-item .label {
  margin-bottom: 2px;
  font-size: 1.3rem;
  line-height: 1.43;
  color: var(--content-secondary);
  position: relative;
  width: 100%;
}
.detail-wrapper .detail-item .value {
  font-size: 1.4rem;
  line-height: 1.43;
}
.detail-wrapper .detail-item .value.__verified:after {
  content: "";
  display: inline-block;
  height: 14px;
  width: 14px;
  position: relative;
  top: 2px;
  background-position: center;
  background-repeat: no-repeat;
  margin-left: 4px;
  background-size: contain;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10 18C12.1217 18 14.1566 17.1571 15.6569 15.6569C17.1571 14.1566 18 12.1217 18 10C18 7.87827 17.1571 5.84344 15.6569 4.34315C14.1566 2.84285 12.1217 2 10 2C7.87827 2 5.84344 2.84285 4.34315 4.34315C2.84285 5.84344 2 7.87827 2 10C2 12.1217 2.84285 14.1566 4.34315 15.6569C5.84344 17.1571 7.87827 18 10 18ZM13.707 8.707C13.8892 8.5184 13.99 8.2658 13.9877 8.0036C13.9854 7.7414 13.8802 7.49059 13.6948 7.30518C13.5094 7.11977 13.2586 7.0146 12.9964 7.01233C12.7342 7.01005 12.4816 7.11084 12.293 7.293L9 10.586L7.707 9.293C7.5184 9.11084 7.2658 9.01005 7.0036 9.01233C6.7414 9.0146 6.49059 9.11977 6.30518 9.30518C6.11977 9.49059 6.0146 9.7414 6.01233 10.0036C6.01005 10.2658 6.11084 10.5184 6.293 10.707L8.293 12.707C8.48053 12.8945 8.73484 12.9998 9 12.9998C9.26516 12.9998 9.51947 12.8945 9.707 12.707L13.707 8.707Z' fill='%2329C76F'/%3E%3C/svg%3E");
}

/*/EXTRAS
.dropdown-toggle {
  display: flex;
  &:after {
    content: "";
    height: 20px;
    width: 20px;
    border: none !important;
    display: inline-block;
    margin-left: 4px;
    background-position: center;
    background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M5.29303 7.29301C5.48056 7.10553 5.73487 7.00022 6.00003 7.00022C6.26519 7.00022 6.5195 7.10553 6.70703 7.29301L10 10.586L13.293 7.29301C13.3853 7.19749 13.4956 7.12131 13.6176 7.0689C13.7396 7.01649 13.8709 6.98891 14.0036 6.98775C14.1364 6.9866 14.2681 7.0119 14.391 7.06218C14.5139 7.11246 14.6255 7.18672 14.7194 7.28061C14.8133 7.3745 14.8876 7.48615 14.9379 7.60905C14.9881 7.73195 15.0134 7.86363 15.0123 7.99641C15.0111 8.12919 14.9835 8.26041 14.9311 8.38241C14.8787 8.50441 14.8025 8.61476 14.707 8.707L10.707 12.707C10.5195 12.8945 10.2652 12.9998 10 12.9998C9.73487 12.9998 9.48056 12.8945 9.29303 12.707L5.29303 8.707C5.10556 8.51948 5.00024 8.26517 5.00024 8C5.00024 7.73484 5.10556 7.48053 5.29303 7.29301Z' fill='%231C1C1C'/%3E%3C/svg%3E");
  }
}

*/
.accordion-button:after {
  width: 18px;
  height: 18px;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M14.7069 12.7068C14.5194 12.8943 14.2651 12.9996 13.9999 12.9996C13.7348 12.9996 13.4804 12.8943 13.2929 12.7068L9.99992 9.41379L6.70692 12.7068C6.51832 12.8889 6.26571 12.9897 6.00352 12.9875C5.74132 12.9852 5.49051 12.88 5.3051 12.6946C5.11969 12.5092 5.01452 12.2584 5.01224 11.9962C5.00997 11.734 5.11076 11.4814 5.29292 11.2928L9.29292 7.29279C9.48045 7.10532 9.73475 7 9.99992 7C10.2651 7 10.5194 7.10532 10.7069 7.29279L14.7069 11.2928C14.8944 11.4803 14.9997 11.7346 14.9997 11.9998C14.9997 12.265 14.8944 12.5193 14.7069 12.7068V12.7068Z' fill='%23525252'/%3E%3C/svg%3E");
  background-size: cover;
  background-repeat: no-repeat;
}
.accordion-button:not(.collapsed)::after {
  background-repeat: no-repeat;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M14.7069 12.7068C14.5194 12.8943 14.2651 12.9996 13.9999 12.9996C13.7348 12.9996 13.4804 12.8943 13.2929 12.7068L9.99992 9.41379L6.70692 12.7068C6.51832 12.8889 6.26571 12.9897 6.00352 12.9875C5.74132 12.9852 5.49051 12.88 5.3051 12.6946C5.11969 12.5092 5.01452 12.2584 5.01224 11.9962C5.00997 11.734 5.11076 11.4814 5.29292 11.2928L9.29292 7.29279C9.48045 7.10532 9.73475 7 9.99992 7C10.2651 7 10.5194 7.10532 10.7069 7.29279L14.7069 11.2928C14.8944 11.4803 14.9997 11.7346 14.9997 11.9998C14.9997 12.265 14.8944 12.5193 14.7069 12.7068V12.7068Z' fill='%23525252'/%3E%3C/svg%3E");
  transform: rotate(180deg);
}

.modal-title {
  color: var(--content-primary);
}

.modal-dialog {
  top: 10%;
}
.modal-dialog.width-md {
  max-width: 480px;
}
.modal-dialog.width-lg {
  max-width: 600px;
}

.modal-content {
  border-radius: 8px;
  padding: 16px 20px;
}
.modal-content .modal-header {
  padding: 0;
  display: grid;
  grid-column-gap: 20px;
  grid-template-columns: 1fr 32px;
  margin-bottom: 12px;
  align-items: flex-start;
  border-bottom: none;
}
.modal-content .modal-header .__title {
  flex: 1 0 auto;
}
.modal-content .modal-header .modal-title {
  color: var(--content-primary);
  font-size: 1.8rem;
  line-height: 1.4;
  font-weight: 500;
}
.modal-content .modal-header .btn-close {
  position: relative;
  top: 8px;
}
.modal-content .modal-desc {
  font-size: 1.4rem;
  line-height: 1.43;
  color: var(--content-primary);
  margin-bottom: 0;
  margin-top: 12px;
}
.modal-content .modal-body {
  padding: 8px 0;
}
.modal-content .modal-footer {
  border-top: none;
}
.modal-content .modal-footer.w-button * {
  margin-left: 16px;
}
.modal-content .modal-body {
  padding-top: 0;
}
.modal-content .modal-body form {
  padding: 0;
  margin-bottom: 0;
}

.__blue {
  color: var(--info-text) !important;
}

.__red {
  color: var(--critical-text) !important;
}

.__default {
  color: var(--content-primary) !important;
}

.__green {
  color: var(--success-text) !important;
}

.__yellow {
  color: var(--warning-text) !important;
}

.alert {
  padding: 10px 12px 10px 32px;
  border: 1px solid transparent;
  box-sizing: border-box;
  border-radius: 4px;
  width: 100%;
  margin: 12px auto;
  position: relative;
}
.alert:before {
  content: "";
  display: block;
  height: 18px;
  width: 18px;
  background-size: cover;
  position: absolute;
  left: 8px;
}
.alert .al__link {
  text-decoration: none;
}
.alert.fullWidth {
  width: 100%;
}
.alert .al-flex {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}
.alert .al-content {
  display: flex;
  flex-direction: column;
  max-width: 95%;
}
.alert .al-content .al__title {
  font-weight: 500;
  text-align: left;
}
.alert .al-content .al__desc {
  margin-top: 6px;
  max-width: 720px;
  text-align: left;
}
.alert .al-content .al__list {
  margin-top: 6px;
}
.alert .al-content .al__list .list-item {
  position: relative;
  padding-left: 16px;
  text-align: left;
}
.alert .al-content .al__list .list-item:not(:first-of-type) {
  margin-top: 4px;
}
.alert .al-content .al__list .list-item::before {
  content: "";
  display: inline-block;
  height: 5px;
  width: 5px;
  border-radius: 5px;
  position: absolute;
  left: 6px;
  top: 7px;
}
.alert .al-content .al__actions {
  margin-top: 12px;
  display: flex;
}
.alert .al-content .al__actions a:not(:first-of-type) {
  margin-left: 32px;
  text-align: left;
}
.alert.hasClose .al__link.close {
  height: 16px;
  width: 16px;
  position: relative;
}
.alert.hasClose .al__link.close::before {
  content: "";
  display: block;
  height: 2px;
  width: 16px;
  border-radius: 4px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(45deg);
}
.alert.hasClose .al__link.close::after {
  content: "";
  display: block;
  height: 2px;
  width: 16px;
  border-radius: 4px;
  position: absolute;
  top: 50%;
  transform: translateY(-50%) rotate(-45deg);
}
.alert.hasFlex .al-content {
  padding-right: 20px;
  flex: 1 1 auto;
}
.alert.critical {
  background: var(--critical-surface);
  color: var(--critical-text);
}
.alert.critical::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg clip-path='url(%23clip0_458_35)'%3E%3Cpath d='M14.3033 3.6967C13.2544 2.6478 11.918 1.9335 10.4632 1.64411C9.00832 1.35472 7.50032 1.50324 6.12988 2.0709C4.75943 2.63856 3.58809 3.59985 2.76398 4.83322C1.93987 6.06659 1.5 7.51664 1.5 9C1.5 10.4834 1.93987 11.9334 2.76398 13.1668C3.58809 14.4001 4.75943 15.3614 6.12988 15.9291C7.50032 16.4968 9.00832 16.6453 10.4632 16.3559C11.918 16.0665 13.2544 15.3522 14.3033 14.3033C14.9997 13.6069 15.5522 12.7801 15.9291 11.8701C16.306 10.9602 16.5 9.98491 16.5 9C16.5 8.01508 16.306 7.03981 15.9291 6.12987C15.5522 5.21993 14.9997 4.39314 14.3033 3.6967ZM4.26415 12.6752C3.38066 11.5202 2.94665 10.0838 3.04283 8.63282C3.13901 7.18189 3.75884 5.81526 4.78705 4.78705C5.81527 3.75883 7.1819 3.13901 8.63283 3.04283C10.0838 2.94665 11.5202 3.38066 12.6752 4.26415L4.26415 12.6752ZM5.32482 13.7358L13.7359 5.32481C14.6193 6.47975 15.0534 7.91624 14.9572 9.36717C14.861 10.8181 14.2412 12.1847 13.213 13.2129C12.1847 14.2412 10.8181 14.861 9.36718 14.9572C7.91624 15.0533 6.47976 14.6193 5.32482 13.7358Z' fill='%23792215'/%3E%3C/g%3E%3Cdefs%3E%3CclipPath id='clip0_458_35'%3E%3Crect width='18' height='18' fill='white'/%3E%3C/clipPath%3E%3C/defs%3E%3C/svg%3E");
}
.alert.critical .list-item:before {
  background: var(--critical-text);
}
.alert.critical .al__link {
  color: var(--critical-text);
}
.alert.critical.hasClose .al__link.close::before, .alert.critical.hasClose .al__link.close::after {
  background: var(--critical-text);
}
.alert.success {
  background: var(--success-surface);
  color: var(--success-text);
}
.alert.success::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11.04 6.5925L7.8225 9.8175L6.585 8.58C6.51777 8.50149 6.43503 8.43772 6.34198 8.39271C6.24893 8.34769 6.14758 8.32239 6.04429 8.3184C5.941 8.31441 5.838 8.33182 5.74176 8.36952C5.64552 8.40723 5.55811 8.46442 5.48502 8.53751C5.41192 8.6106 5.35473 8.69801 5.31703 8.79426C5.27932 8.8905 5.26191 8.9935 5.2659 9.09679C5.26989 9.20008 5.29519 9.30143 5.34021 9.39448C5.38523 9.48753 5.44899 9.57026 5.5275 9.6375L7.29 11.4075C7.36008 11.477 7.4432 11.532 7.53457 11.5693C7.62595 11.6067 7.7238 11.6256 7.8225 11.625C8.01926 11.6242 8.20781 11.5461 8.3475 11.4075L12.0975 7.6575C12.1678 7.58778 12.2236 7.50483 12.2617 7.41343C12.2997 7.32204 12.3194 7.22401 12.3194 7.125C12.3194 7.02599 12.2997 6.92796 12.2617 6.83657C12.2236 6.74517 12.1678 6.66222 12.0975 6.5925C11.957 6.45281 11.7669 6.37441 11.5688 6.37441C11.3706 6.37441 11.1805 6.45281 11.04 6.5925ZM9 1.5C7.51664 1.5 6.0666 1.93987 4.83323 2.76398C3.59986 3.58809 2.63856 4.75943 2.07091 6.12987C1.50325 7.50032 1.35472 9.00832 1.64411 10.4632C1.9335 11.918 2.64781 13.2544 3.6967 14.3033C4.7456 15.3522 6.08197 16.0665 7.53683 16.3559C8.99168 16.6453 10.4997 16.4968 11.8701 15.9291C13.2406 15.3614 14.4119 14.4001 15.236 13.1668C16.0601 11.9334 16.5 10.4834 16.5 9C16.5 8.01509 16.306 7.03982 15.9291 6.12987C15.5522 5.21993 14.9997 4.39314 14.3033 3.6967C13.6069 3.00026 12.7801 2.44781 11.8701 2.0709C10.9602 1.69399 9.98492 1.5 9 1.5ZM9 15C7.81332 15 6.65328 14.6481 5.66658 13.9888C4.67989 13.3295 3.91085 12.3925 3.45673 11.2961C3.0026 10.1997 2.88378 8.99334 3.11529 7.82946C3.3468 6.66557 3.91825 5.59647 4.75736 4.75736C5.59648 3.91824 6.66558 3.3468 7.82946 3.11529C8.99335 2.88378 10.1997 3.0026 11.2961 3.45672C12.3925 3.91085 13.3295 4.67988 13.9888 5.66658C14.6481 6.65327 15 7.81331 15 9C15 10.5913 14.3679 12.1174 13.2426 13.2426C12.1174 14.3679 10.5913 15 9 15Z' fill='%230A5D2E'/%3E%3C/svg%3E");
}
.alert.success .list-item:before {
  background: var(--success-text);
}
.alert.success .al__link {
  color: var(--success-text);
}
.alert.success.hasClose .al__link.close::before, .alert.success.hasClose .al__link.close::after {
  background: var(--success-text);
}
.alert.warning {
  background: var(--warning-surface);
  color: var(--warning-text);
}
.alert.warning::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 5.25C8.80109 5.25 8.61033 5.32902 8.46967 5.46967C8.32902 5.61032 8.25 5.80109 8.25 6V9C8.25 9.19891 8.32902 9.38968 8.46967 9.53033C8.61033 9.67098 8.80109 9.75 9 9.75C9.19892 9.75 9.38968 9.67098 9.53033 9.53033C9.67099 9.38968 9.75 9.19891 9.75 9V6C9.75 5.80109 9.67099 5.61032 9.53033 5.46967C9.38968 5.32902 9.19892 5.25 9 5.25ZM9.69 11.715C9.67359 11.6672 9.65089 11.6218 9.6225 11.58L9.5325 11.4675C9.42704 11.3634 9.29311 11.2929 9.14761 11.2649C9.00212 11.2369 8.85159 11.2526 8.715 11.31C8.62411 11.348 8.5404 11.4013 8.4675 11.4675C8.39799 11.5376 8.343 11.6207 8.30568 11.7121C8.26835 11.8034 8.24943 11.9013 8.25 12C8.25119 12.098 8.27157 12.1948 8.31 12.285C8.34369 12.3781 8.39743 12.4626 8.46742 12.5326C8.53741 12.6026 8.62193 12.6563 8.715 12.69C8.80478 12.7297 8.90185 12.7502 9 12.7502C9.09816 12.7502 9.19523 12.7297 9.285 12.69C9.37808 12.6563 9.4626 12.6026 9.53259 12.5326C9.60258 12.4626 9.65632 12.3781 9.69 12.285C9.72843 12.1948 9.74882 12.098 9.75 12C9.75368 11.9501 9.75368 11.8999 9.75 11.85C9.73709 11.8022 9.71685 11.7566 9.69 11.715ZM9 1.5C7.51664 1.5 6.0666 1.93987 4.83323 2.76398C3.59986 3.58809 2.63856 4.75943 2.07091 6.12987C1.50325 7.50032 1.35472 9.00832 1.64411 10.4632C1.9335 11.918 2.64781 13.2544 3.6967 14.3033C4.7456 15.3522 6.08197 16.0665 7.53683 16.3559C8.99168 16.6453 10.4997 16.4968 11.8701 15.9291C13.2406 15.3614 14.4119 14.4001 15.236 13.1668C16.0601 11.9334 16.5 10.4834 16.5 9C16.5 8.01509 16.306 7.03982 15.9291 6.12987C15.5522 5.21993 14.9997 4.39314 14.3033 3.6967C13.6069 3.00026 12.7801 2.44781 11.8701 2.0709C10.9602 1.69399 9.98492 1.5 9 1.5ZM9 15C7.81332 15 6.65328 14.6481 5.66658 13.9888C4.67989 13.3295 3.91085 12.3925 3.45673 11.2961C3.0026 10.1997 2.88378 8.99334 3.11529 7.82946C3.3468 6.66557 3.91825 5.59647 4.75736 4.75736C5.59648 3.91824 6.66558 3.3468 7.82946 3.11529C8.99335 2.88378 10.1997 3.0026 11.2961 3.45672C12.3925 3.91085 13.3295 4.67988 13.9888 5.66658C14.6481 6.65327 15 7.81331 15 9C15 10.5913 14.3679 12.1174 13.2426 13.2426C12.1174 14.3679 10.5913 15 9 15Z' fill='%23734510'/%3E%3C/svg%3E");
}
.alert.warning .list-item:before {
  background: var(--warning-text);
}
.alert.warning .al__link {
  color: var(--warning-text);
}
.alert.warning.hasClose .al__link.close::before, .alert.warning.hasClose .al__link.close::after {
  background: var(--warning-text);
}
.alert.info {
  background: var(--info-surface);
  color: var(--info-text);
}
.alert.info::before {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9 8.25C8.80109 8.25 8.61033 8.32902 8.46967 8.46967C8.32902 8.61032 8.25 8.80109 8.25 9V12C8.25 12.1989 8.32902 12.3897 8.46967 12.5303C8.61033 12.671 8.80109 12.75 9 12.75C9.19892 12.75 9.38968 12.671 9.53033 12.5303C9.67099 12.3897 9.75 12.1989 9.75 12V9C9.75 8.80109 9.67099 8.61032 9.53033 8.46967C9.38968 8.32902 9.19892 8.25 9 8.25ZM9.285 5.31C9.10241 5.23499 8.8976 5.23499 8.715 5.31C8.62294 5.3457 8.53883 5.39922 8.4675 5.4675C8.40126 5.5404 8.34798 5.62411 8.31 5.715C8.26802 5.80401 8.24747 5.90162 8.25 6C8.24943 6.0987 8.26835 6.19655 8.30568 6.28793C8.343 6.37931 8.39799 6.46242 8.4675 6.5325C8.5404 6.59875 8.62411 6.65202 8.715 6.69C8.82863 6.73668 8.95198 6.75474 9.07421 6.74258C9.19645 6.73043 9.31383 6.68844 9.41604 6.6203C9.51825 6.55216 9.60215 6.45996 9.66039 6.3518C9.71862 6.24364 9.74939 6.12284 9.75 6C9.74724 5.80142 9.66955 5.61123 9.5325 5.4675C9.46118 5.39922 9.37707 5.3457 9.285 5.31ZM9 1.5C7.51664 1.5 6.0666 1.93987 4.83323 2.76398C3.59986 3.58809 2.63856 4.75943 2.07091 6.12987C1.50325 7.50032 1.35472 9.00832 1.64411 10.4632C1.9335 11.918 2.64781 13.2544 3.6967 14.3033C4.7456 15.3522 6.08197 16.0665 7.53683 16.3559C8.99168 16.6453 10.4997 16.4968 11.8701 15.9291C13.2406 15.3614 14.4119 14.4001 15.236 13.1668C16.0601 11.9334 16.5 10.4834 16.5 9C16.5 8.01509 16.306 7.03982 15.9291 6.12987C15.5522 5.21993 14.9997 4.39314 14.3033 3.6967C13.6069 3.00026 12.7801 2.44781 11.8701 2.0709C10.9602 1.69399 9.98492 1.5 9 1.5ZM9 15C7.81332 15 6.65328 14.6481 5.66658 13.9888C4.67989 13.3295 3.91085 12.3925 3.45673 11.2961C3.0026 10.1997 2.88378 8.99334 3.11529 7.82946C3.3468 6.66557 3.91825 5.59647 4.75736 4.75736C5.59648 3.91824 6.66558 3.3468 7.82946 3.11529C8.99335 2.88378 10.1997 3.0026 11.2961 3.45672C12.3925 3.91085 13.3295 4.67988 13.9888 5.66658C14.6481 6.65327 15 7.81331 15 9C15 10.5913 14.3679 12.1174 13.2426 13.2426C12.1174 14.3679 10.5913 15 9 15Z' fill='%230852A0'/%3E%3C/svg%3E");
}
.alert.info .list-item:before {
  background: var(--info-text);
}
.alert.info .al__link {
  color: var(--info-text);
}
.alert.info.hasClose .al__link.close::before, .alert.info.hasClose .al__link.close::after {
  background: var(--info-text);
}

/*DEFAULT ICON STYLING*/
.svg {
  display: inline-block;
  background-size: cover;
  width: 24px;
  height: 24px;
}

.icon {
  width: 24px;
  height: 24px;
}

.svg-docx {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg filter='url(%23filter0_d_2371_7905)'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M4 4.4C4 4.0287 4.18437 3.6726 4.51256 3.41005C4.84075 3.1475 5.28587 3 5.75 3H30.25C30.7141 3 31.1592 3.1475 31.4874 3.41005C31.8156 3.6726 32 4.0287 32 4.4V29.6C32 29.9713 31.8156 30.3274 31.4874 30.59C31.1592 30.8525 30.7141 31 30.25 31H5.75C5.28587 31 4.84075 30.8525 4.51256 30.59C4.18437 30.3274 4 29.9713 4 29.6V4.4Z' fill='%23F7F9FA'/%3E%3C/g%3E%3Cpath d='M10.125 10.25L19.125 9.125V24.875L10.125 23.75V10.25Z' fill='%230093DD'/%3E%3Cpath d='M18 10.25H24.75C25.0484 10.25 25.3345 10.3685 25.5455 10.5795C25.7565 10.7905 25.875 11.0766 25.875 11.375V21.5C25.875 21.7984 25.7565 22.0845 25.5455 22.2955C25.3345 22.5065 25.0484 22.625 24.75 22.625H18V10.25Z' fill='%230093DD'/%3E%3Cpath d='M19.125 11.375H24.75V21.5H19.125V11.375Z' fill='white'/%3E%3Cpath d='M18 20.375H23.625V19.25H18V20.375ZM18 13.625H23.625V12.5H18V13.625ZM18 15.875H23.625V14.75H18V15.875ZM18 18.125H23.625V17H18V18.125Z' fill='%230093DD'/%3E%3Cpath d='M11.25 13.625L12.375 19.25H13.5L14.625 15.3125L15.75 19.25H16.875L18 13.625H16.875L16.3125 17.5625L15.1875 13.625H14.0625L12.9375 17.5625L12.375 13.625H11.25Z' fill='white'/%3E%3Cdefs%3E%3Cfilter id='filter0_d_2371_7905' x='0' y='0' width='36' height='36' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeColorMatrix in='SourceAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3E%3CfeOffset dy='1'/%3E%3CfeGaussianBlur stdDeviation='2'/%3E%3CfeColorMatrix type='matrix' values='0 0 0 0 0.858859 0 0 0 0 0.871766 0 0 0 0 0.884673 0 0 0 1 0'/%3E%3CfeBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_2371_7905'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='effect1_dropShadow_2371_7905' result='shape'/%3E%3C/filter%3E%3C/defs%3E%3C/svg%3E");
}

.svg-generic {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg filter='url(%23filter0_d_2375_7921)'%3E%3Cpath d='M30.25 3H5.75C4.7835 3 4 3.6268 4 4.4V29.6C4 30.3732 4.7835 31 5.75 31H30.25C31.2165 31 32 30.3732 32 29.6V4.4C32 3.6268 31.2165 3 30.25 3Z' fill='%23F7F9FA'/%3E%3C/g%3E%3Cpath d='M22.2263 22.0811H13.7696C13.436 22.0811 13.1655 22.3515 13.1655 22.6851C13.1655 23.0187 13.436 23.2892 13.7696 23.2892H22.2263C22.5599 23.2892 22.8304 23.0187 22.8304 22.6851C22.8304 22.3515 22.5599 22.0811 22.2263 22.0811Z' fill='%23D3D3D4'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M14.374 17.2486H21.6226V13.6243C21.6226 12.6631 21.2408 11.7412 20.5611 11.0615C19.8814 10.3818 18.9596 10 17.9983 10C17.0371 10 16.1152 10.3818 15.4356 11.0615C14.7559 11.7412 14.374 12.6631 14.374 13.6243V17.2486Z' stroke='%23656769'/%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M11.5231 15.267C12.4304 14.3598 14.2135 13.624 15.4796 13.624H20.5247C21.798 13.624 23.5643 14.3573 24.474 15.267L25.5963 16.3894C26.0711 16.8642 25.9261 17.3244 25.2616 17.4187L20.2867 18.129C19.023 18.3103 16.962 18.3078 15.7104 18.129L10.7354 17.4187C10.0758 17.3244 9.92961 16.8605 10.4008 16.3894L11.5243 15.2658L11.5231 15.267Z' fill='%23656769'/%3E%3Cdefs%3E%3Cfilter id='filter0_d_2375_7921' x='0' y='0' width='36' height='36' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeColorMatrix in='SourceAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3E%3CfeOffset dy='1'/%3E%3CfeGaussianBlur stdDeviation='2'/%3E%3CfeColorMatrix type='matrix' values='0 0 0 0 0.858859 0 0 0 0 0.871766 0 0 0 0 0.884673 0 0 0 1 0'/%3E%3CfeBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_2375_7921'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='effect1_dropShadow_2375_7921' result='shape'/%3E%3C/filter%3E%3C/defs%3E%3C/svg%3E");
}

.svg-pdf {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg filter='url(%23filter0_d_2371_7733)'%3E%3Cpath d='M30.25 3H5.75C4.7835 3 4 3.6268 4 4.4V29.6C4 30.3732 4.7835 31 5.75 31H30.25C31.2165 31 32 30.3732 32 29.6V4.4C32 3.6268 31.2165 3 30.25 3Z' fill='%23F7F9FA'/%3E%3C/g%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.2402 18.0949H11.5016V20.1506H10.125V13.8506H12.3578C13.7511 13.8506 14.6377 14.4435 14.6377 15.9012C14.6377 17.4329 13.6756 18.0949 12.2402 18.0949ZM11.5016 17.0593H12.3326C13.0461 17.0593 13.4574 16.6341 13.4574 15.9012C13.4574 15.1271 13.0461 14.9881 12.3494 14.9881H11.5016V17.0593ZM15.6429 20.1506V13.8506H17.8347C19.7318 13.8506 20.5376 15.1435 20.5376 16.9872C20.5376 18.8309 19.7485 20.1506 17.8683 20.1506H15.644H15.6429ZM16.9481 18.9863H17.8085C18.9165 18.9863 19.1452 18.1453 19.1452 16.9872C19.1452 15.8291 18.8661 14.9881 17.8085 14.9881H16.9062L16.9481 18.9863ZM23.2635 17.7974V20.1506H21.9498V13.8506H25.875V14.9881H23.2624V16.7844H25.7239V17.7974H23.2635Z' fill='%23F75D53'/%3E%3Cdefs%3E%3Cfilter id='filter0_d_2371_7733' x='0' y='0' width='36' height='36' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeColorMatrix in='SourceAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3E%3CfeOffset dy='1'/%3E%3CfeGaussianBlur stdDeviation='2'/%3E%3CfeColorMatrix type='matrix' values='0 0 0 0 0.858859 0 0 0 0 0.871766 0 0 0 0 0.884673 0 0 0 1 0'/%3E%3CfeBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_2371_7733'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='effect1_dropShadow_2371_7733' result='shape'/%3E%3C/filter%3E%3C/defs%3E%3C/svg%3E");
}

.svg-xls {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='36' height='36' viewBox='0 0 36 36' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cg filter='url(%23filter0_d_2375_7913)'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M4 4.4C4 4.0287 4.18437 3.6726 4.51256 3.41005C4.84075 3.1475 5.28587 3 5.75 3H30.25C30.7141 3 31.1592 3.1475 31.4874 3.41005C31.8156 3.6726 32 4.0287 32 4.4V29.6C32 29.9713 31.8156 30.3274 31.4874 30.59C31.1592 30.8525 30.7141 31 30.25 31H5.75C5.28587 31 4.84075 30.8525 4.51256 30.59C4.18437 30.3274 4 29.9713 4 29.6V4.4Z' fill='%23F7F9FA'/%3E%3C/g%3E%3Cpath d='M10.125 10.25L19.125 9.125V24.875L10.125 23.75V10.25Z' fill='%2329C76F'/%3E%3Cpath d='M18 10.25H24.75C25.0484 10.25 25.3345 10.3685 25.5455 10.5795C25.7565 10.7905 25.875 11.0766 25.875 11.375V21.5C25.875 21.7984 25.7565 22.0845 25.5455 22.2955C25.3345 22.5065 25.0484 22.625 24.75 22.625H18V10.25Z' fill='%2329C76F'/%3E%3Cpath d='M19.125 11.375H24.75V21.5H19.125V11.375Z' fill='white'/%3E%3Cpath d='M19.125 18.125H20.25V17H19.125V18.125ZM19.125 20.375H20.25V19.25H19.125V20.375ZM19.125 15.875H20.25V14.75H19.125V15.875ZM18 13.625H20.25V12.5H18V13.625ZM21.375 18.125H23.625V17H21.375V18.125ZM21.375 20.375H23.625V19.25H21.375V20.375ZM21.375 15.875H23.625V14.75H21.375V15.875ZM21.375 13.625H23.625V12.5H21.375V13.625Z' fill='%2329C76F'/%3E%3Cpath d='M13.8746 13.625H12.375L13.8746 17L12.375 20.375H13.8746L14.625 18.125L15.3754 20.375H16.875L15.3754 17L16.875 13.625H15.3754L14.625 15.875L13.8746 13.625Z' fill='white'/%3E%3Cdefs%3E%3Cfilter id='filter0_d_2375_7913' x='0' y='0' width='36' height='36' filterUnits='userSpaceOnUse' color-interpolation-filters='sRGB'%3E%3CfeFlood flood-opacity='0' result='BackgroundImageFix'/%3E%3CfeColorMatrix in='SourceAlpha' type='matrix' values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0' result='hardAlpha'/%3E%3CfeOffset dy='1'/%3E%3CfeGaussianBlur stdDeviation='2'/%3E%3CfeColorMatrix type='matrix' values='0 0 0 0 0.858859 0 0 0 0 0.871766 0 0 0 0 0.884673 0 0 0 1 0'/%3E%3CfeBlend mode='normal' in2='BackgroundImageFix' result='effect1_dropShadow_2375_7913'/%3E%3CfeBlend mode='normal' in='SourceGraphic' in2='effect1_dropShadow_2375_7913' result='shape'/%3E%3C/filter%3E%3C/defs%3E%3C/svg%3E");
}

.svg-chevron {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M7.54289 3.79289C7.93342 3.40237 8.56658 3.40237 8.95711 3.79289L16.4571 11.2929C16.8476 11.6834 16.8476 12.3166 16.4571 12.7071L8.95711 20.2071C8.56658 20.5976 7.93342 20.5976 7.54289 20.2071C7.15237 19.8166 7.15237 19.1834 7.54289 18.7929L14.3358 12L7.54289 5.20711C7.15237 4.81658 7.15237 4.18342 7.54289 3.79289Z' fill='black'/%3E%3C/svg%3E");
}

.svg-double-chevron {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M4.54289 3.79289C4.93342 3.40237 5.56658 3.40237 5.95711 3.79289L13.4571 11.2929C13.8476 11.6834 13.8476 12.3166 13.4571 12.7071L5.95711 20.2071C5.56658 20.5976 4.93342 20.5976 4.54289 20.2071C4.15237 19.8166 4.15237 19.1834 4.54289 18.7929L11.3358 12L4.54289 5.20711C4.15237 4.81658 4.15237 4.18342 4.54289 3.79289ZM10.5429 3.79289C10.9334 3.40237 11.5666 3.40237 11.9571 3.79289L19.4571 11.2929C19.8476 11.6834 19.8476 12.3166 19.4571 12.7071L11.9571 20.2071C11.5666 20.5976 10.9334 20.5976 10.5429 20.2071C10.1524 19.8166 10.1524 19.1834 10.5429 18.7929L17.3358 12L10.5429 5.20711C10.1524 4.81658 10.1524 4.18342 10.5429 3.79289Z' fill='black'/%3E%3C/svg%3E");
}

.svg-add {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M10 3.33341C8.23191 3.33341 6.53622 4.03579 5.28597 5.28604C4.03573 6.53628 3.33335 8.23197 3.33335 10.0001C3.33335 10.8756 3.50579 11.7425 3.84082 12.5513C4.17585 13.3601 4.66692 14.0951 5.28597 14.7141C5.90503 15.3332 6.63996 15.8242 7.4488 16.1593C8.25763 16.4943 9.12454 16.6667 10 16.6667C10.8755 16.6667 11.7424 16.4943 12.5512 16.1593C13.3601 15.8242 14.095 15.3332 14.7141 14.7141C15.3331 14.0951 15.8242 13.3601 16.1592 12.5513C16.4942 11.7425 16.6667 10.8756 16.6667 10.0001C16.6667 8.23197 15.9643 6.53628 14.7141 5.28604C13.4638 4.03579 11.7681 3.33341 10 3.33341ZM4.10746 4.10752C5.67027 2.54472 7.78988 1.66675 10 1.66675C12.2102 1.66675 14.3298 2.54472 15.8926 4.10752C17.4554 5.67033 18.3334 7.78994 18.3334 10.0001C18.3334 11.0944 18.1178 12.1781 17.699 13.1891C17.2802 14.2002 16.6664 15.1188 15.8926 15.8926C15.1188 16.6665 14.2001 17.2803 13.189 17.6991C12.178 18.1179 11.0944 18.3334 10 18.3334C8.90567 18.3334 7.82204 18.1179 6.81099 17.6991C5.79994 17.2803 4.88128 16.6665 4.10746 15.8926C3.33364 15.1188 2.71981 14.2002 2.30102 13.1891C1.88223 12.1781 1.66669 11.0944 1.66669 10.0001C1.66669 7.78994 2.54466 5.67033 4.10746 4.10752ZM10 6.66675C10.4603 6.66675 10.8334 7.03984 10.8334 7.50008V9.16675H12.5C12.9603 9.16675 13.3334 9.53984 13.3334 10.0001C13.3334 10.4603 12.9603 10.8334 12.5 10.8334H10.8334V12.5001C10.8334 12.9603 10.4603 13.3334 10 13.3334C9.53978 13.3334 9.16669 12.9603 9.16669 12.5001V10.8334H7.50002C7.03978 10.8334 6.66669 10.4603 6.66669 10.0001C6.66669 9.53984 7.03978 9.16675 7.50002 9.16675H9.16669V7.50008C9.16669 7.03984 9.53978 6.66675 10 6.66675Z' fill='%2307121D'/%3E%3C/svg%3E");
}

.svg-trash-gray {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='18' height='18' viewBox='0 0 18 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M7.5 13.5C7.69891 13.5 7.88968 13.421 8.03033 13.2803C8.17098 13.1397 8.25 12.9489 8.25 12.75V8.25C8.25 8.05109 8.17098 7.86032 8.03033 7.71967C7.88968 7.57902 7.69891 7.5 7.5 7.5C7.30109 7.5 7.11032 7.57902 6.96967 7.71967C6.82902 7.86032 6.75 8.05109 6.75 8.25V12.75C6.75 12.9489 6.82902 13.1397 6.96967 13.2803C7.11032 13.421 7.30109 13.5 7.5 13.5ZM15 4.5H12V3.75C12 3.15326 11.7629 2.58097 11.341 2.15901C10.919 1.73705 10.3467 1.5 9.75 1.5H8.25C7.65326 1.5 7.08097 1.73705 6.65901 2.15901C6.23705 2.58097 6 3.15326 6 3.75V4.5H3C2.80109 4.5 2.61032 4.57902 2.46967 4.71967C2.32902 4.86032 2.25 5.05109 2.25 5.25C2.25 5.44891 2.32902 5.63968 2.46967 5.78033C2.61032 5.92098 2.80109 6 3 6H3.75V14.25C3.75 14.8467 3.98705 15.419 4.40901 15.841C4.83097 16.2629 5.40326 16.5 6 16.5H12C12.5967 16.5 13.169 16.2629 13.591 15.841C14.0129 15.419 14.25 14.8467 14.25 14.25V6H15C15.1989 6 15.3897 5.92098 15.5303 5.78033C15.671 5.63968 15.75 5.44891 15.75 5.25C15.75 5.05109 15.671 4.86032 15.5303 4.71967C15.3897 4.57902 15.1989 4.5 15 4.5ZM7.5 3.75C7.5 3.55109 7.57902 3.36032 7.71967 3.21967C7.86032 3.07902 8.05109 3 8.25 3H9.75C9.94891 3 10.1397 3.07902 10.2803 3.21967C10.421 3.36032 10.5 3.55109 10.5 3.75V4.5H7.5V3.75ZM12.75 14.25C12.75 14.4489 12.671 14.6397 12.5303 14.7803C12.3897 14.921 12.1989 15 12 15H6C5.80109 15 5.61032 14.921 5.46967 14.7803C5.32902 14.6397 5.25 14.4489 5.25 14.25V6H12.75V14.25ZM10.5 13.5C10.6989 13.5 10.8897 13.421 11.0303 13.2803C11.171 13.1397 11.25 12.9489 11.25 12.75V8.25C11.25 8.05109 11.171 7.86032 11.0303 7.71967C10.8897 7.57902 10.6989 7.5 10.5 7.5C10.3011 7.5 10.1103 7.57902 9.96967 7.71967C9.82902 7.86032 9.75 8.05109 9.75 8.25V12.75C9.75 12.9489 9.82902 13.1397 9.96967 13.2803C10.1103 13.421 10.3011 13.5 10.5 13.5Z' fill='%23334E68'/%3E%3C/svg%3E");
}

.svg-search {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M21.7099 20.2899L17.9999 16.6099C19.44 14.8143 20.1374 12.5352 19.9487 10.2412C19.76 7.94721 18.6996 5.81269 16.9854 4.27655C15.2713 2.74041 13.0337 1.91941 10.7328 1.98237C8.43194 2.04534 6.24263 2.98747 4.61505 4.61505C2.98747 6.24263 2.04534 8.43194 1.98237 10.7328C1.91941 13.0337 2.74041 15.2713 4.27655 16.9854C5.81269 18.6996 7.94721 19.76 10.2412 19.9487C12.5352 20.1374 14.8143 19.44 16.6099 17.9999L20.2899 21.6799C20.3829 21.7736 20.4935 21.848 20.6153 21.8988C20.7372 21.9496 20.8679 21.9757 20.9999 21.9757C21.1319 21.9757 21.2626 21.9496 21.3845 21.8988C21.5063 21.848 21.6169 21.7736 21.7099 21.6799C21.8901 21.4934 21.9909 21.2442 21.9909 20.9849C21.9909 20.7256 21.8901 20.4764 21.7099 20.2899V20.2899ZM10.9999 17.9999C9.61544 17.9999 8.26206 17.5894 7.11091 16.8202C5.95977 16.051 5.06256 14.9578 4.53275 13.6787C4.00293 12.3996 3.86431 10.9921 4.13441 9.63427C4.4045 8.27641 5.07119 7.02912 6.05016 6.05016C7.02912 5.07119 8.27641 4.4045 9.63427 4.13441C10.9921 3.86431 12.3996 4.00293 13.6787 4.53275C14.9578 5.06256 16.051 5.95977 16.8202 7.11091C17.5894 8.26206 17.9999 9.61544 17.9999 10.9999C17.9999 12.8564 17.2624 14.6369 15.9497 15.9497C14.6369 17.2624 12.8564 17.9999 10.9999 17.9999V17.9999Z' fill='%231F2937'/%3E%3C/svg%3E");
}

.svg-clear {
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M13.4099 11.9999L17.7099 7.70994C17.8982 7.52164 18.004 7.26624 18.004 6.99994C18.004 6.73364 17.8982 6.47825 17.7099 6.28994C17.5216 6.10164 17.2662 5.99585 16.9999 5.99585C16.7336 5.99585 16.4782 6.10164 16.2899 6.28994L11.9999 10.5899L7.70994 6.28994C7.52164 6.10164 7.26624 5.99585 6.99994 5.99585C6.73364 5.99585 6.47824 6.10164 6.28994 6.28994C6.10164 6.47825 5.99585 6.73364 5.99585 6.99994C5.99585 7.26624 6.10164 7.52164 6.28994 7.70994L10.5899 11.9999L6.28994 16.2899C6.19621 16.3829 6.12182 16.4935 6.07105 16.6154C6.02028 16.7372 5.99414 16.8679 5.99414 16.9999C5.99414 17.132 6.02028 17.2627 6.07105 17.3845C6.12182 17.5064 6.19621 17.617 6.28994 17.7099C6.3829 17.8037 6.4935 17.8781 6.61536 17.9288C6.73722 17.9796 6.86793 18.0057 6.99994 18.0057C7.13195 18.0057 7.26266 17.9796 7.38452 17.9288C7.50638 17.8781 7.61698 17.8037 7.70994 17.7099L11.9999 13.4099L16.2899 17.7099C16.3829 17.8037 16.4935 17.8781 16.6154 17.9288C16.7372 17.9796 16.8679 18.0057 16.9999 18.0057C17.132 18.0057 17.2627 17.9796 17.3845 17.9288C17.5064 17.8781 17.617 17.8037 17.7099 17.7099C17.8037 17.617 17.8781 17.5064 17.9288 17.3845C17.9796 17.2627 18.0057 17.132 18.0057 16.9999C18.0057 16.8679 17.9796 16.7372 17.9288 16.6154C17.8781 16.4935 17.8037 16.3829 17.7099 16.2899L13.4099 11.9999Z' fill='%23792215'/%3E%3C/svg%3E");
}

@keyframes spinner {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
.table {
  margin: 10px auto 12px;
  clear: both;
  border-collapse: collapse !important;
  width: 100% !important;
  position: relative;
}
.table .icon-wrapper {
  display: inline-flex;
  align-items: center;
}
.table .icon-wrapper .btn--icon,
.table .icon-wrapper a {
  margin-right: 12px;
}
.table .right {
  text-align: right !important;
}

.overflow-wrap {
  display: block;
  overflow-x: auto;
  width: 100%;
}
.overflow-wrap::-webkit-scrollbar {
  height: 4px;
}
.overflow-wrap::-webkit-scrollbar-track {
  box-shadow: inset 0 0 6px rgba(var(--content-tertiary), 0.3);
}
.overflow-wrap::-webkit-scrollbar-thumb {
  background-color: var(--content-tertiary);
}

.table,
.table th,
.table td {
  box-sizing: content-box;
}

th,
td,
tr {
  border: none !important;
}

table tr {
  cursor: pointer;
}

table.table thead th {
  font-weight: 500;
  color: var(--content-secondary);
  padding: 10px 8px;
  background: var(--background-neutral);
  vertical-align: middle;
  font-weight: 500;
  font-size: 1.3rem;
}
table.table tbody td {
  overflow: hidden;
  text-overflow: ellipsis;
  table-layout: fixed;
  padding: 10px 8px;
  border-bottom: 1px solid var(--border-light-neutral) !important;
  vertical-align: middle;
}

.table-footer {
  margin-top: 8px;
  padding: 0 8px;
  align-items: center;
}
.table-footer .records,
.table-footer .vuetable-pagination-info {
  color: var(--content-secondary);
}
.table-footer .pagination,
.table-footer .vuetable-pagination {
  display: flex;
  align-items: center;
}
.table-footer .pagination .counter,
.table-footer .vuetable-pagination .counter {
  margin: 0;
}
.table-footer .pagination a,
.table-footer .vuetable-pagination a {
  color: var(--content-link);
  width: 32px;
  height: 32px;
  line-height: 32px;
  text-align: center;
  font-size: 1.3rem;
  background: none;
  border-radius: 50%;
  margin-left: 8px;
  cursor: pointer;
  text-decoration: none;
  padding: 0;
}
.table-footer .pagination a.active, .table-footer .pagination a.btn-primary,
.table-footer .vuetable-pagination a.active,
.table-footer .vuetable-pagination a.btn-primary {
  background: var(--interactive-primary) !important;
  color: var(--content-inverse) !important;
  font-weight: 500 !important;
}
.table-footer .visited {
  color: var(--content-secondary);
}

.search-wrapper > button {
  margin-right: 10px;
}
.search-wrapper .__filter {
  flex: 1;
  margin-right: 12%;
  margin-right: 64px;
}
.search-wrapper .__filter .accordion-header {
  font-weight: 500;
  background-color: var(--background-neutral);
}
.search-wrapper .__filter .accordion-header > svg {
  height: 16px;
  width: 16px;
}
.search-wrapper .__filter .filter-values {
  color: var(--content-secondary);
  font-weight: 400;
}
.search-wrapper .__filter .filter-items {
  margin-top: 8px;
  padding-left: 0;
  max-width: 100%;
}
.search-wrapper .__filter .filter-items .filter-inputs {
  display: grid;
  grid-gap: 8px;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}
.search-wrapper .__filter .filter-items .form-row {
  width: 100%;
  margin-bottom: 0;
}
.search-wrapper .__filter .filter-items .form-row.__footer {
  width: 100%;
  margin-top: 16px;
  margin-bottom: 0;
}
.search-wrapper .__filter .filter-items .form-row .btn {
  margin-right: 20px;
}
.search-wrapper .__filter .filter-items .form-label {
  font-size: 1.3rem;
}
.search-wrapper .__filter .filter-items input,
.search-wrapper .__filter .filter-items select {
  width: 100%;
  padding-left: 8px;
  margin-right: 8px;
  background: var(--background-surface);
  border-color: var(--border-dark-neutral);
}

.__search {
  position: relative;
}
.__search .search-input {
  width: 240px;
  padding-left: 32px;
}
.__search:before {
  content: "";
  height: 36px;
  width: 36px;
  background-position: center;
  position: absolute;
  z-index: 10;
  top: 0;
  left: 0;
  background-repeat: no-repeat;
  display: block;
  background-image: url("data:image/svg+xml;charset=utf8,%3Csvg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M2.83317 7.33334C2.83317 4.84806 4.84789 2.83334 7.33317 2.83334C9.81845 2.83334 11.8332 4.84806 11.8332 7.33334C11.8332 8.54143 11.3571 9.63833 10.5824 10.4467C10.5575 10.4664 10.5335 10.4878 10.5105 10.5107C10.4876 10.5337 10.4662 10.5577 10.4465 10.5826C9.63812 11.3573 8.54124 11.8333 7.33317 11.8333C4.84789 11.8333 2.83317 9.81862 2.83317 7.33334ZM11.0646 12.2433C10.0286 13.0319 8.73557 13.5 7.33317 13.5C3.92741 13.5 1.1665 10.7391 1.1665 7.33334C1.1665 3.92758 3.92741 1.16667 7.33317 1.16667C10.7389 1.16667 13.4998 3.92758 13.4998 7.33334C13.4998 8.73576 13.0317 10.0288 12.2431 11.0648L14.589 13.4107C14.9145 13.7362 14.9145 14.2638 14.589 14.5893C14.2636 14.9147 13.736 14.9147 13.4105 14.5893L11.0646 12.2433Z' fill='%238D8D8D'/%3E%3C/svg%3E");
}

.title-wrapper {
  margin-bottom: 16px;
  padding: 0 16px;
}

.search-wrapper .search-input + button {
  height: 36px;
}

.daterangepicker select.monthselect,
.daterangepicker select.yearselect {
  height: 32px !important;
  line-height: 32px !important;
}

.single-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
}

.login-wrapper {
  max-width: 600px;
  margin-top: 5%;
}
.login-wrapper .brand-img {
  width: 150px;
  height: auto;
  overflow: hidden;
  margin: 0 auto 80px;
  display: block;
}
.login-wrapper .form-footer {
  margin-top: 20px;
}
.login-wrapper .form-footer p {
  color: var(--content-secondary);
}

.dashboard {
  flex: 1;
  display: flex;
  flex-direction: inherit;
  position: relative;
  --header-height: 60px;
}

.d-main {
  background: var(--background-screen);
  height: 100%;
  width: 100%;
  flex: 1;
  position: relative;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease-in-out;
}
.d-main .d-header {
  padding: 0 50px;
  background-color: var(--background-inverse);
  height: var(--header-height);
  position: fixed;
  z-index: 999;
  right: 0;
  left: 0;
  width: 100%;
  transition: all 0.2s ease-in-out;
}
.d-main .d-header .nav-dropdown .dropdown-toggle {
  padding: 0 2px;
  font-weight: 400;
  cursor: pointer;
  color: var(--content-inverse);
}
.d-main .d-header .nav-dropdown .dropdown-menu {
  top: 4px !important;
  width: 200px;
  padding: 4px;
  z-index: 20;
  border: 1px solid var(--border-light-neutral);
  background: var(--background-surface);
  box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
  -webkit-box-shadow: 0px 4px 12px 0px rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  overflow: hidden;
}
.d-main .d-header .nav-dropdown .task-link {
  display: block;
  height: 32px;
  line-height: 32px;
  padding: 0 8px;
  cursor: pointer;
  border: none;
  color: var(--content-primary);
  text-decoration: none;
}
.d-main .d-header .nav-dropdown .task-link:hover {
  color: var(--interactive-primary);
  background: var(--background-neutral);
}
.d-main .d-header .svg_nav__trigger {
  display: none;
}
.d-main .d-header .d-header__logo {
  margin-right: 40px;
}
.d-main .d-header .d-header__logo a {
  width: 90px;
  height: auto;
}
.d-main .d-header .d-header__logo a img {
  height: 100%;
  width: 100%;
  object-fit: contain;
}
.d-main .d-header .d-menu__link {
  color: var(--content-tertiary);
  text-decoration: none;
  margin: 0 5px;
  height: 32px;
  line-height: 32px;
  padding: 0 16px;
  font-weight: 400;
  border-radius: 60px;
  background-color: transparent;
}
.d-main .d-header .d-menu__link.active, .d-main .d-header .d-menu__link:hover {
  color: var(--content-inverse);
  background-color: var(--neutral-800);
}

.pg-container {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}
.pg-container .pg-layout {
  padding: var(--header-height) 0 24px;
  background-color: var(--background-screen);
  width: 100%;
  margin: 0 auto;
  flex: 1;
  height: 100%;
  position: relative;
}
.pg-container .pg-layout:has(.layout-nav) .layout-main {
  width: calc(100% - 150px);
  margin-left: 150px;
  height: 3000px;
}
.pg-container .pg-layout .layout-nav {
  width: 150px;
  padding-top: 20px;
  height: 100%;
  position: fixed;
  z-index: 1001;
  border-right: 1px solid var(--border-light-neutral);
  background-color: var(--neutral-50);
}
.pg-container .pg-layout .layout-nav .menu-group {
  display: flex;
  flex-direction: column;
  margin-bottom: 20px;
}
.pg-container .pg-layout .layout-nav .menu-group .menu-item {
  padding: 0 10px;
  text-decoration: none;
  color: var(--content-primary);
  font-weight: 400;
  position: relative;
}
.pg-container .pg-layout .layout-nav .menu-group .menu-item.active {
  color: var(--interactive-primary);
  border-left: 2px solid var(--border-primary);
  font-weight: 500;
}
.pg-container .pg-layout .layout-nav .menu-group .menu-item.active:after {
  content: "";
  display: block;
  height: 100%;
  width: 2px;
  background: var(--interactive-primary);
  position: absolute;
  border-radius: 10px;
  left: -2px;
  top: 0;
}
.pg-container .pg-layout .layout-main {
  max-width: 1360px;
  margin: 0 auto;
  padding: 24px 20px;
}
@media only screen and (min-width: 1560px) {
  .pg-container .pg-layout .layout-main {
    max-width: 92%;
  }
}
@media only screen and (max-width: 1366px) {
  .pg-container .pg-layout .layout-main {
    max-width: calc(100% - 40px);
  }
}
.pg-container .title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}
.pg-container .title-row .title {
  font-size: 2rem;
  line-height: 1.4;
  font-weight: 500;
  margin-bottom: 0;
}
.pg-container .pg-row {
  display: grid;
  grid-gap: 20px;
  margin-bottom: 20px;
  padding: 0 20px;
}

.card {
  padding: 16px 20px;
  border-radius: var(--card-radius);
  border: 1px solid var(--border-light-neutral);
  background: var(--background-screen);
  display: flex;
  flex-direction: column;
  position: relative;
  box-shadow: rgba(64, 68, 82, 0.04) 0px 2px 5px 0px;
}
.card > .dropdown {
  position: absolute;
  right: 16px;
  top: 16px;
  margin-left: auto;
}
.card > .dropdown .dropdown-toggle {
  color: var(--content-secondary);
  font-size: 1.25rem;
  line-height: 1.38;
}
.card > .dropdown .dropdown-toggle::after {
  vertical-align: middle;
}
.card > .dropdown .dropdown-menu {
  background: var(--background-surface);
  box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.12);
  -webkit-box-shadow: 0 3px 12px 2px rgba(0, 0, 0, 0.12);
  border: 0;
  border-radius: 4px;
  padding: 2px;
}
.card.no-border {
  border: none;
}
.card .card-title {
  font-size: 1.4rem;
  font-weight: 500;
  margin-bottom: 8px;
  color: var(--content-secondary);
}
.card .card-value {
  font-size: 3.2rem;
  line-height: 1.25;
}
.card .card-label {
  margin-top: 12px;
  font-size: 1.3rem;
  line-height: 1.38;
  font-weight: 500;
  color: var(--content-tertiary);
}

.pg-title {
  margin-bottom: 10px;
  margin-top: 16px;
  padding: 0 20px;
}
.pg-title .title {
  font-size: 1.8rem;
  line-height: 1.4;
  font-weight: 500;
  margin-bottom: 0;
  color: var(--content-primary);
}
.pg-title .breadcrumbs {
  padding-left: 0;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
}
.pg-title .breadcrumbs .__item:not(:first-of-type) {
  margin-left: 2px;
}
.pg-title .breadcrumbs .__item:not(:first-of-type):before {
  content: "/";
  display: inline-block;
  color: var(--content-tertiary);
}
.pg-title .breadcrumbs .__item:not(:first-of-type):last-of-type a {
  color: var(--content-secondary);
}
.pg-title .breadcrumbs .__item.prev-link {
  height: 28px;
  width: 28px;
  line-height: 28px;
  border-radius: 4px;
  padding: 0 8px;
  background-color: var(--background-neutral);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
}
.pg-title .breadcrumbs .__item.prev-link a {
  color: var(--content-primary);
}
.pg-title .breadcrumbs .__item.prev-link a svg {
  height: 16px;
  width: 16px;
}
.pg-title .breadcrumbs .__item a {
  text-decoration: none;
  color: var(--content-tertiary);
  font-size: 1.3rem;
  line-height: 1.38;
}
.pg-title .breadcrumbs .__item a:hover {
  color: var(--content-secondary);
}

.pg-actions {
  display: flex;
  margin-bottom: 24px;
  padding: 0 20px;
}
.pg-actions .actions-item *:not(:last-child) {
  margin-right: 10px;
}

.breadcrumbs {
  padding-left: 0;
  display: flex;
  align-items: center;
  margin-bottom: 0;
}
.breadcrumbs .__item {
  align-items: center;
}
.breadcrumbs .__item a {
  color: var(--content-secondary);
  font-size: 1.3rem;
  line-height: 1;
  text-decoration: none;
  font-weight: 400;
}
.breadcrumbs .__item:not(:first-of-type) {
  display: flex;
}
.breadcrumbs .__item:not(:first-of-type):before {
  content: "";
  margin-right: 4px;
  margin-left: 4px;
  display: inline-block;
  position: relative;
  background-size: contain;
}
.breadcrumbs .__item:has(svg) a {
  border-radius: 10px;
  height: 32px;
  width: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--content-secondary);
  border: 1px solid var(--border-dark-neutral) !important;
}
.breadcrumbs .__item:has(svg) svg {
  height: 18px;
  width: 18px;
}

.pagination-item {
  display: flex;
  align-items: center;
  border-radius: 6px;
  padding: 0;
  height: 32px;
  overflow: hidden;
  border: 1px solid var(--border-light-neutral);
  font-size: 1.3rem;
}
.pagination-item a {
  text-decoration: none;
}
.pagination-item a span.svg {
  height: 16px;
  width: 16px;
}
.pagination-item a span.svg.rotate-180 {
  transform: rotate(180deg);
}
.pagination-item input {
  max-width: 50px;
  border-radius: 0;
  height: 32px !important;
  line-height: 32px !important;
  text-align: center;
  border-color: var(--border-light-neutral);
}
.pagination-item .label {
  color: var(--content-secondary);
}
.pagination-item > *:not(input) {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 8px;
  background-color: var(--background-neutral);
  border-right: 1px solid var(--border-light-neutral);
}
.pagination-item > *:not(input):last-child, .pagination-item > *:not(input):nth-child(3) {
  border-right: none;
}

.records-item {
  display: flex;
  align-items: center;
  gap: 8px;
}
.records-item select {
  height: 32px !important;
  line-height: 32px !important;
  width: max-content;
}

.btn__filter {
  font-weight: 500;
  background-color: var(--background-neutral);
}

.filter-controls .modal-content {
  padding: 0;
}
.filter-controls .modal-body {
  padding-bottom: 0;
}
.filter-controls .modal-header {
  padding: 10px 16px 0;
  align-items: center;
}
.filter-controls .modal-header .btn-close {
  top: 0;
}
.filter-controls .filter-items .filter-inputs {
  padding: 0 16px 0;
}
.filter-controls .filter-items .form-row {
  display: grid;
  grid-column-gap: 8px;
  grid-template-columns: 1fr 1fr 1fr 32px;
}
.filter-controls .filter-items .form-row.grid-5 {
  grid-template-columns: 80px 1fr 1fr 1fr 32px;
}
.filter-controls .filter-items .form-row .btn.btn--icon {
  height: 32px;
  width: 32px;
  border: none !important;
}
.filter-controls .filter-items .form-row .btn.btn--icon span.svg {
  height: 16px;
  width: 16px;
}
.filter-controls .filter-items .form-row.__footer {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 16px;
  margin-bottom: 0;
  border-top: 1px solid var(--border-light-neutral);
  padding: 16px;
}
.filter-controls .add-row .btn span.svg {
  height: 16px;
  width: 16px;
}

.search-wrapper:has(.btn__filter) {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.search-wrapper:has(.btn__filter) .__filter {
  margin-right: 24px;
}

.search-grid {
  display: grid;
  grid-template-columns: auto auto;
  grid-column-gap: 10px;
}
.search-grid .search-item {
  position: relative;
}
.search-grid .search-item .form-input {
  width: 250px;
}
.search-grid .search-item .search-buttons {
  position: absolute;
  top: 4px;
  right: 4px;
  display: grid;
  grid-template-columns: auto auto;
  grid-column-gap: 4px;
}
.search-grid .search-item .btn--icon {
  height: 28px;
  width: 28px;
  border: none;
}
.search-grid .search-item .btn--icon span.svg {
  height: 16px;
  width: 16px;
}

.loan-grid {
  grid-template-columns: 360px 1fr;
}

.info-item {
  margin-bottom: 20px;
}
.info-item .__header {
  margin-bottom: 12px;
}
.info-item .title {
  display: flex;
  flex-direction: column;
}
.info-item .title .__title {
  color: var(--content--primary);
  font-weight: 400;
  font-size: 1.6rem;
  line-height: 1.5;
  margin-bottom: 0;
}
.info-item .title .__amount {
  font-size: 2.8rem;
  line-height: 1.29;
  font-weight: 500;
  margin-bottom: 4px;
}
.info-item .title .status {
  display: flex;
}
.info-item .title .status * {
  margin-right: 6px;
}
.info-item .payment-info {
  padding: 10px;
  padding-right: 16px;
  border-radius: 8px;
  background-color: var(--success-surface);
  height: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
.info-item .payment-info > div {
  display: flex;
  flex-direction: column;
}
.info-item .payment-info .col-left .__title {
  color: var(--success-text);
  font-size: 1.2rem;
  font-weight: 600;
  line-height: 1.33;
  letter-spacing: 0.24px;
  text-transform: uppercase;
  margin-bottom: 4px;
}
.info-item .payment-info .col-left .__value {
  font-size: 1.8rem;
  font-style: normal;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.18px;
}
.info-item .payment-info .col-left .__label {
  color: var(--content-secondary);
  font-size: 13px;
  font-style: normal;
  line-height: 1.38;
  letter-spacing: 0.26px;
}
.info-item .payment-info .col-right {
  align-items: center;
}
.info-item .payment-info .col-right .__month {
  color: var(--content-secondary);
  font-size: 1.3rem;
  font-weight: 700;
  line-height: 1.38;
  letter-spacing: 0.52px;
  text-transform: uppercase;
}
.info-item .payment-info .col-right .__date {
  font-size: 1.8rem;
  font-weight: 500;
  line-height: 1.4;
  letter-spacing: 0.2px;
}

.activity-item .accordion-body {
  padding-left: 0;
}
.activity-item .accordion-button {
  border-bottom: none;
  color: var(--content--primary);
  font-weight: 500;
  font-size: 1.6rem;
  line-height: 1.5;
  padding: 2px;
}
.activity-item .accordion-button:focus {
  border-color: none;
  box-shadow: none;
}
.activity-item .accordion-button:not(.collapsed) {
  background: none;
  box-shadow: none;
}
.activity-item .__search::before {
  height: 32px;
  width: 32px;
}
.activity-item .search-input {
  background-color: var(--background-neutral);
  border-color: transparent;
}
.activity-item .timeline {
  margin-top: 20px;
  padding-left: 0;
  margin-bottom: 0;
}
.activity-item .timeline-item {
  margin-bottom: 24px;
  position: relative;
  display: flex;
}
.activity-item .timeline-item:last-of-type {
  margin-bottom: 0;
}
.activity-item .timeline-item:not(:last-of-type):before {
  display: block;
  content: "";
  border: 1px dashed var(--border-dark-neutral);
  box-sizing: border-box;
  width: 1px;
  height: 110%;
  position: absolute;
  left: 15px;
  top: 20px;
}
.activity-item .timeline-item svg {
  margin-right: 16px;
  height: 20px;
  width: 20px;
  z-index: 20;
  position: relative;
  right: -6px;
  stroke: var(--success-fill);
}
.activity-item .timeline-item .wrapper {
  display: flex;
  flex-direction: column;
}
.activity-item .timeline-item .wrapper .__date,
.activity-item .timeline-item .wrapper .__desc {
  color: var(--content-secondary);
  font-size: 1.3rem;
  line-height: 1.38;
  margin-bottom: 2px;
}
.activity-item .timeline-item .wrapper .__title {
  font-size: 1.4rem;
  line-height: 1.43;
  margin-bottom: 2px;
}
.activity-item .timeline-item .wrapper .__desc {
  margin-bottom: 0;
}

.loan-details .accordion-button {
  font-size: 1.3rem;
  font-style: normal;
  font-weight: 500;
  line-height: 1.38;
  letter-spacing: 0.26px;
  text-transform: uppercase;
  color: var(--content-secondary);
  border-bottom: 1px solid var(--content-tertiary);
  /*&.collapsed:not(:first-of-type) {
      border-bottom: 1px solid green;
  }*/
}
.loan-details .accordion-button:not(.collapsed) {
  color: var(--interactive-primary);
  border-color: var(--interactive-primary);
  background-color: transparent;
  box-shadow: none;
}
.loan-details .accordion-button:not(.collapsed) svg {
  stroke: var(--interactive-primary);
}
.loan-details .accordion-button svg {
  width: 18px;
  height: 18px;
  stroke: var(--content-secondary);
  margin-right: 8px;
  position: relative;
  top: -1px;
}
.loan-details .accordion-body {
  padding-bottom: 32px;
  padding-top: 12px;
}

.app_details .nav-tabs {
  border-bottom: 2px solid var(--content-tertiary);
}
.app_details .nav-item {
  padding: 0;
}
.app_details .nav-item:not(:first-of-type) {
  margin-left: 20px;
}
.app_details .nav-item .nav-link {
  padding: 8px 0;
  border: none;
  color: var(--content-secondary);
  box-sizing: border-box;
  position: relative;
  top: 1px;
}
.app_details .nav-item .nav-link.active {
  color: var(--interactive-primary);
  font-weight: 500;
  border-bottom: 2px solid var(--interactive-primary);
}
.app_details .tab-pane {
  padding: 16px 12px;
}
.app_details .tab-pane:not(.show) {
  opacity: 1;
}

.doc-wrapper {
  flex-wrap: wrap;
  padding-top: 16px;
}
.doc-wrapper .doc-item {
  background: var(--background-neutral);
  width: 140px;
  height: 140px;
  border-radius: 8px;
  padding: 0 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  text-align: center;
  margin-bottom: 16px;
}
.doc-wrapper .doc-item span.svg {
  height: 32px;
  width: 32px;
  margin-bottom: 4px;
}
.doc-wrapper .doc-item .file-type {
  font-weight: 500;
  font-size: 1.2rem;
  line-height: 1.38;
  color: var(--content-secondary);
  margin-bottom: 4px;
}
.doc-wrapper .doc-item .__title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  font-size: 1.3rem;
  line-height: 1.3;
}
.doc-wrapper .doc-item a {
  font-weight: 500;
  font-size: 1.3rem;
  line-height: 1.3;
  margin-top: 8px;
  color: var(--interactive-control);
}
.doc-wrapper .doc-item.__upload {
  background: var(--background-neutral);
  border: 1px solid var(--border-primary);
  color: var(--interactive-control);
  font-weight: 500;
  padding: 0 8px;
  position: relative;
}
.doc-wrapper .doc-item.__upload .upload-item {
  background-color: var(--background-surface);
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 60px;
}
.doc-wrapper .doc-item.__upload .upload-item svg {
  height: 20px;
  width: 20px;
  stroke: var(--interactive-primary);
}
.doc-wrapper .doc-item.__upload .up_desc {
  font-size: 1.3rem;
  line-height: 1.38;
  margin-top: 6px;
}
.doc-wrapper .doc-item.__upload .input-upload {
  position: absolute;
  opacity: 0;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 100% !important;
  cursor: pointer;
}

.activity-search .__search .search-input {
  width: 100%;
}

.pgnContainer {
  margin-top: 20px;
  padding: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.pgnContainer .pgnLeft {
  display: flex;
  align-items: center;
}
.pgnContainer .pgnLeft .label {
  font-size: 1.3rem;
  line-height: 1.33;
}
.pgnContainer .pgnLeft .form--select {
  height: 28px !important;
  line-height: 28px !important;
  width: 50px;
  padding: 0 0 0 6px;
  margin: 0 6px;
}
.pgnContainer .pgnControls {
  display: flex;
  align-items: center;
}
.pgnContainer .pgnControls .form--input {
  width: 50px;
  padding: 0 4px;
  height: 28px !important;
  line-height: 28px !important;
  text-align: center;
}
.pgnContainer .pgnControls a {
  margin: 0 4px;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s linear;
}
.pgnContainer .pgnControls a:hover {
  background: var(--background-neutral);
}
.pgnContainer .pgnControls a.prev svg {
  transform: rotate(180deg);
}
.pgnContainer .pgnControls a.noAdjacent svg {
  fill: var(--content-tertiary);
}
.pgnContainer .pgnControls a svg {
  fill: var(--content-secondary);
  height: 24px;
  width: 24px;
}

/*# sourceMappingURL=main.css.map */

# LendQube Deployment Documentation

## Current Production Environment

### Server Details
- **OS**: Linux (CentOS/RHEL based)
- **User**: silicon
- **Web Server**: Apache with SSL
- **Domain**: badmin.siliconcreditmanagement.com

### Current Deployment Structure
```
/var/www/html/
├── admin/                    # Web.Admin application
├── admin_backup_20250626_181623/
├── background/               # Background service  
├── background.backup/
└── index.htm
```

### Services Running
1. **lendqube-admin.service**
   - Path: `/var/www/html/admin/Web.Admin.dll`
   - Port: 5000
   - User: silicon

2. **lendqube-background.service**
   - Path: `/var/www/html/background/Web.BackgroundService.dll`
   - Port: 5001
   - User: silicon

### Database Configuration
- Host: ***********
- Port: 5432
- Database: lqdev (development)
- User: silicon

## Azure DevOps Pipeline Setup

### Prerequisites

#### 1. SSH Service Connection
1. Go to Project Settings > Service connections
2. Click "New service connection" > SSH
3. Configure:
   - Connection Name: `LendQube-Production-Server`
   - Host name: Your server IP (requires VPN)
   - Port: 22
   - Username: silicon
   - Authentication: SSH Key
   - SSH Key: Use generated key from `/home/<USER>/.ssh/azure_devops_deploy`

#### 2. Environment Configuration
1. Go to Pipelines > Environments
2. Create new environment called "Production"
3. Add approvals if needed

### Pipeline Files Created
1. `Devops/dev-deploy-azure-pipelines.yml` - Main CI/CD pipeline (manual trigger)
2. `Devops/azure-pipelines.yml` - Original automated pipeline
3. `Devops/deploy-dev.yml` - Development pipeline
4. `Devops/test-deployment.yml` - Simple test pipeline

## Key Findings & Notes

### Architecture
- **Web.Api is integrated** - API DLLs found in admin folder, not separate deployment
- **No staging environment** - Deployments go directly to production
- **Backup strategy exists** - Previous deployments saved with timestamps

### Security & Access
- **VPN required** for server access
- **SSH setup completed** - Keys generated and configured
- **Apache reverse proxy** correctly configured

### Configuration Management
- **Missing appsettings.Production.json** in some deployment folders
- **Antiforgery settings** may differ between development and production
- **Environment variables** need verification for production database

## Deployment Process

### Current Manual Process
1. Build application locally
2. Copy files to server via SSH/SCP
3. Stop services
4. Replace files
5. Start services
6. Verify deployment

### Automated Pipeline Process
1. Code pushed to branch
2. Azure DevOps builds application
3. Packages artifacts
4. Deploys to production server via SSH
5. Manages service restart
6. Creates backup of previous version

## Rollback Strategy

If deployment fails:
```bash
# Stop service
sudo systemctl stop lendqube-admin

# Restore backup
sudo rm -rf /var/www/html/admin
sudo mv /var/www/html/admin_backup_[timestamp] /var/www/html/admin

# Start service
sudo systemctl start lendqube-admin
```

## Next Steps & Recommendations

1. **Verify production database connection string**
2. **Set up staging environment** for safer deployments
3. **Complete Azure DevOps pipeline configuration**
4. **Test deployment process** in non-production environment first
5. **Document production appsettings.Production.json** location and contents
6. **Implement proper secret management** for production credentials

## Important Security Notes
- Production database details not yet confirmed
- VPN access required for all server operations
- SSH keys properly configured for automated deployment
- Production application currently running correctly
- Consider implementing staging environment before production deployments
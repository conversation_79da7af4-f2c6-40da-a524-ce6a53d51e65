﻿using System.Collections.Concurrent;
using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Distributed;

namespace LendQube.Infrastructure.Core.Database.Repository;

public interface IUnitofWork
{
    AppDbContext Context { get; }
    IRelationalDbRepository Db { get; }
    void Save();
    Task SaveAsync(CancellationToken ct);
    Task SaveAsyncWithTracking(CancellationToken ct);
}

public sealed class UnitofWork : IUnitofWork
{
    public AppDbContext Context { get; private set; }
    public IRelationalDbRepository Db { get; init; }
    public bool IsPooled { get; init; } = false;

    public UnitofWork(AppDbContext context, IHttpContextAccessor contextAccessor, IDistributedCache cache, ILogManager<IRelationalDbRepository> logger)
    {
        Context = context;
        Db = new RelationalDbRepository(Context, new CurrentUserInfoProvider(Context, contextAccessor, cache), logger);
    }

    public void Save()
    {
        if (!IsPooled)
        {
            if (!Context.ChangeTracker.HasChanges())
                return;

            Db.StampChangeTracker(default);
        }

        _ = Context.SaveChanges();
        Context.ChangeTracker.Clear();
    }

    public async Task SaveAsync(CancellationToken ct)
    {
        if (!IsPooled)
        {
            if (!Context.ChangeTracker.HasChanges())
                return;
            Db.StampChangeTracker(ct);
        }


        _ = await Context.SaveChangesAsync(ct);
        Context.ChangeTracker.Clear();
    }

    public async Task SaveAsyncWithTracking(CancellationToken ct)
    {
        if (!Context.ChangeTracker.HasChanges())
            return;

        Db.StampChangeTracker(ct);
        _ = await Context.SaveChangesAsync(ct);
    }
}

public sealed class PooledDbContext(DbContextOptions<AppDbContext> options, IDistributedCache cache, ILogManager<IRelationalDbRepository> logger)
{
    private readonly ConcurrentQueue<AppDbContext> pool = new();

    private readonly int maxSize = 10;
    private int count;

    private AppDbContext Rent()
    {
        if (pool.TryDequeue(out var context))
        {
            Interlocked.Decrement(ref count);
        }
        else
        {
            context = new AppDbContext(options);
        }

        return context;
    }

    public void Return(IUnitofWork uow)
    {
        if (uow == null || uow.Context == null)
            return;

        if (Interlocked.Increment(ref count) <= maxSize)
        {
            uow.Context.ChangeTracker.Clear();
            pool.Enqueue(uow.Context);
        }
        else
        {
            Interlocked.Decrement(ref count);
            uow.Context.Dispose();
        }
    }

    public IUnitofWork RentUow()
    {
        var context = Rent();
        return new UnitofWork(context, null, cache, logger) { IsPooled = true };
    }
}
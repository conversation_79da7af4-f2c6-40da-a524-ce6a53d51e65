﻿namespace LendQube.Infrastructure.Core.Database.DataPager;

public abstract class AbstractTypedBasePageList : IAbstractPagedList
{
    protected AbstractTypedBasePageList()
    {

    }
    public AbstractTypedBasePageList(IAbstractPagedList vm)
    {
        Total = vm.Total;
        ResultSetTotal = vm.ResultSetTotal;
        CurrentPage = vm.CurrentPage;
        PageSize = vm.PageSize;
        From = vm.From;
        To = vm.To;
        LastPage = vm.LastPage;
        HasPreviousPage = vm.HasPreviousPage;
        HasNextPage = vm.HasNextPage;
        NextPageNumber = vm.NextPageNumber;
        PreviousPageNumber = vm.PreviousPageNumber;
        OrderByColumn = vm.OrderByColumn;
        OrderByDirection = vm.OrderByDirection;
    }
}

public class TypedBasePageList<T> : AbstractTypedBasePageList
{
    public TypedBasePageList()
    {

    }
    public TypedBasePageList(IAbstractPagedList vm) : base(vm)
    {
        
    }

    public TypedBasePageList(AbstractPagedList<T> vm) : base(vm)
    {
        Data = vm.UnboxedData;
    }

    public List<T> Data { get; set; }

    public TypedBasePageList<TVM> ToType<TVM>(Func<T, TVM> mapping) =>
        new(this)
        {
            Data = [.. Data.Select(mapping)],
        };
}


public class TypedBasePageList<T, TVM> : AbstractTypedBasePageList
{
    public TypedBasePageList()
    {

    }

    public TypedBasePageList(AbstractPagedList<T, TVM> vm) : base(vm)
    {
        Data = vm.UnboxedData;
    }

    public List<TVM> Data { get; set; }
}



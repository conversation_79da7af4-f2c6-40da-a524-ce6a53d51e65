﻿using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.EntityFrameworkCore.Query;
using System.Linq.Expressions;

namespace LendQube.Infrastructure.Core.Database.GenericCrud;

public class GenericEntityCrudService<T>(IUnitofWork uow, ILogManager<T> logger) where T : class, IBaseEntityForRelationalDb
{
    public IUnitofWork Uow => uow;
    public IRelationalDbRepository Db => Uow.Db;

    public async Task<bool> Delete(Expression<Func<T, bool>> filter, CancellationToken ct)
    {
        try
        {
            var rowsDeleted = await uow.Db.DeleteAndSaveWithFilterAsync(filter, ct);
            return rowsDeleted > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Delete, ex, "Could not delete all records in filter");

            return false;
        }
    }

    public async Task<bool> Delete(T data, CancellationToken ct)
    {
        try
        {
            uow.Db.Delete(data);
            await uow.SaveAsync(ct);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Delete, ex, "Could not delete record");

            return false;
        }
    }

    public ValueTask<T> Get(object Id, CancellationToken ct) => uow.Db.ByIdAsync<T>(Id, ct);

    public Task<T> Get(ISpecification<T> spec, CancellationToken ct) => uow.Db.OneWithSpecAsync(spec, ct: ct);

    public Task<List<T>> GetAll(ISpecification<T> spec, CancellationToken ct) => uow.Db.ManyWithSpecAsync(spec, ct);

    public ValueTask<TypedBasePageList<T>> GetTypeBasedPagedData(ISpecification<T> spec, DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        spec.DoAllFilter(filterAndPage);
        return new PagedList<T>(uow.Db.NotTrackedWithSpec(spec), filterAndPage, ct).ToType(ct);
    }

    public ColumnList GetTableDefinition(TableSettings<T> settings = null) => GenericColumnAndFilterService<T, T>.GetTableDefinition(settings ?? new());

    public async Task<bool> New(T data, CancellationToken ct)
    {
        try
        {
            uow.Db.Insert(data);
            await uow.SaveAsync(ct);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Add, ex, "Could not insert data");
            return false;
        }
    }

    public async Task<bool> Update(T data, CancellationToken ct)
    {
        try
        {
            uow.Db.Update(data);
            await uow.SaveAsync(ct);
            return true;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Update, ex, "Could not update data");
            return false;
        }
    }
    public async Task<bool> UpdateWithFilter(Expression<Func<T, bool>> filter, Expression<Func<SetPropertyCalls<T>, SetPropertyCalls<T>>> updates, CancellationToken ct)
    {
        try
        {
            var result = await uow.Db.UpdateAndSaveWithFilterAsync(filter, updates, ct);
            return result > 0;
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Update, ex, "Could not update data");
            return false;
        }
    }
}
﻿@page "/account/login/recoverycode"

@using System.ComponentModel.DataAnnotations
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Components.Helpers

@inject IdentityRedirectManager redirectManager
@inject AdminAuthService authService

@inherits SingleFormComponentBase<LoginWithRecoveryCodeVM>

<PageTitle>Log In With Recovery Code</PageTitle>


<div id="wrapper">
    <div id="footer-control" class="index-wrapper">
        <div class="login-wrapper mx-auto">
            <a href="#" class="brand-img mx-auto">
                <img src="@Assets["images/logo/logo.svg"]" alt="Silicon">
            </a>
            <div class="form-wrapper">
                <EditForm class="form form__login" method="post" Model="Input" OnValidSubmit="OnValidSubmitAsync" FormName="login">
                    <div class="title-wrapper flex __justify-between __align-center" style="padding: 0">
                        <span class="text_xl_medium">Enter Your Recovery Code</span>
                    </div>
                    <StatusMessage Message="@Message" />
                    <DataAnnotationsValidator />

                    <div class="form-row">
                        <label for="two-factor-code" class="form-label">Recovery Code</label>
                        <InputText id="two-factor-code" @bind-Value="Input.RecoveryCode" class="form-input" autocomplete="off" />
                        <ValidationMessage For="() => Input.RecoveryCode" class="text-danger" />
                    </div>

                    <div class="button-row">
                        <button id="login" type="submit" class="btn btn--primary __full">Continue</button>
                    </div>
                </EditForm>
            </div>
        </div>
    </div>
</div>


@code {

    [SupplyParameterFromForm]
    private LoginWithRecoveryCodeVM Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string ReturnUrl { get; set; }

    [SupplyParameterFromQuery]
    private string Key { get; set; }

    [CascadingParameter]
    private HttpContext HttpContext { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (!redirectManager.IsValidMFARouteKey(Key, HttpContext))
        {
            await authService.Logout();
            redirectManager.RedirectTo("account/login");
        }
    }

    private async Task OnValidSubmitAsync()
    {
        if (!redirectManager.IsValidMFARouteKey(Key, HttpContext))
        {
            await authService.Logout();
            redirectManager.RedirectTo("account/login");
            return;
        }

        var state = await authService.LoginWithRecoveryCode(Input, HttpContext.GetIpAddress());

        if (state == UserLoginState.RedirectToLogin)
        {
            redirectManager.RedirectTo("account/login");
        }
        else if (state == UserLoginState.ChangePasswordRequired)
        {
            redirectManager.RedirectTo("account/manage/changepassword");
        }
        else if (state == UserLoginState.Succeeded)
        {
            redirectManager.RemoveMFARouteKey(HttpContext);
            redirectManager.RedirectTo(ReturnUrl);
        }
        else if (state == UserLoginState.LockedOut)
        {
            redirectManager.RedirectTo("account/lockout");
        }
        else
        {
            Message.Error("Code is invalid");
        }
    }
}

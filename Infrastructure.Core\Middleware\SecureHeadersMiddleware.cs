﻿using Microsoft.AspNetCore.Http;

namespace LendQube.Infrastructure.Core.Middleware;

internal sealed class SecureHeadersMiddleware(RequestDelegate next)
{
    public async Task InvokeAsync(HttpContext context)
    {
        context.Response.Headers.XFrameOptions = "DENY";
        context.Response.Headers.XContentTypeOptions = "nosniff";
        context.Response.Headers.XXSSProtection = "1; mode=block";
        context.Response.Headers["Referrer-Policy"] = "no-referrer";
        //context.Response.Headers.Add("Content-Security-Policy", "default-src 'self';");
        await next(context);
    }
}


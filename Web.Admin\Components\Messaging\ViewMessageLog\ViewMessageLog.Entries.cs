﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageLog;

public partial class ViewMessageLog
{
    private DataTable<MessageLogEntryVM> entryTable;
    private ColumnList GroupTableDefinition { get; set; }
    private MessageLogEntryVM SelectedEntry { get; set; } = new();

    private void SetupEntries()
    {
        GroupTableDefinition = Service.GetTableDefinition<MessageLogEntry, MessageLogEntryVM>();
        GroupTableDefinition.RowActionButtons.Add(new RowActionButton("Recipients", Action: async (object row) =>
        {
            modalMessage.Close();
            entryTable.Loading = true;
            var item = (row as MessageLogEntryVM);

            SelectedEntry = SelectedEntry?.Id == item.Id ? SelectedEntry : await uow.Db.OneSelectAsync(Query<MessageLogEntry, MessageLogEntryVM>.Select(MessageLogEntryVM.ViewMapping).Where(x => x.Id == item.Id), Cancel);

            await JSRuntime.OpenModal(ViewRecipientsModal, Cancel);
            await recipientsTable.LoadElement();
            entryTable.Loading = false;
            StateHasChanged();

        }, ShowCondition: (object row) => (row as MessageLogEntryVM).TotalRecipients > 0));
    }

    private ValueTask<TypedBasePageList<MessageLogEntryVM>> LoadEntries(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<MessageLogEntry>
        {
            PrimaryCriteria = x => x.MessageLogId == Data.Id
        };

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
            spec.PrimaryCriteria = spec.PrimaryCriteria.CombineWithAndAlso(x => EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
            || EF.Functions.ILike(x.Subject, filterAndPage.TextFilter));
        }

        return Service.GetTypeBasedPagedData(spec, filterAndPage, MessageLogEntryVM.Mapping, ct);
    }
}


# LendQube Visual System Tree

## 🏢 Complete System Overview

```
                            🏢 LENDQUBE LENDING PLATFORM
                                        │
                    ┌───────────────────┼───────────────────┐
                    │                   │                   │
            🖥️ FRONT DESK         🔧 ENGINE ROOM      📋 FILING ROOM
           (What People See)    (How Things Work)   (Where Data Lives)
                    │                   │                   │
        ┌───────────┼───────────┐      │           ┌───────┼───────┐
        │           │           │      │           │               │
   👨‍💼 Staff    👤 Customer  🤖 Robots │      📄 Basic Forms  📁 Customer Files
    Portal      Portal     (Auto)      │                           │
        │           │           │      │                           │
   Web.Admin    Web.Api   Web.Background│                    Entities.Collection
                                        │                           │
                                        │                    ┌─────┼─────┐
                            ┌───────────┼───────────┐        │     │     │
                            │           │           │        │     │     │
                       🔧 Basic    💰 Loan     🤝 Partners   │     │     │
                        Tools     Business   (External)     │     │     │
                            │           │           │        │     │     │
                   Infrastructure Infrastructure Infrastructure │     │     │
                       .Core      .Collection  .ExternalApi   │     │     │
                                                              │     │     │
                                                         Customers Payments Schedules
```

---

## 🌊 Data Flow Visualization

```
                    📱 CUSTOMER USES APP
                            │
                            ▼
                    🌐 Web.Api (Customer Portal)
                            │
                            ▼
                    🔧 Infrastructure.Collection
                            │
                            ▼
                    📋 Entities.Collection (Customer Data)
                            │
                            ▼
                    💾 PostgreSQL Database


                    👨‍💼 STAFF USES SYSTEM
                            │
                            ▼
                    🖥️ Web.Admin (Staff Portal)
                            │
                            ▼
                    🔧 Infrastructure.Core + Infrastructure.Collection
                            │
                            ▼
                    📋 Entities.Core + Entities.Collection
                            │
                            ▼
                    💾 PostgreSQL Database


                    🤖 AUTOMATIC PROCESSES
                            │
                            ▼
                    ⚙️ Web.BackgroundService
                            │
                            ▼
                    🔧 Infrastructure.Core (Messaging, Tasks)
                            │
                            ▼
                    🤝 Infrastructure.ExternalApi (Send SMS, Process Payments)
                            │
                            ▼
                    📋 Update Customer Records
```

---

## 🗂️ Detailed File Structure Tree

```
📁 LendQube/
│
├── 🌐 FRONT DESK (User Interfaces)
│   │
│   ├── 👨‍💼 Web.Admin/ (Staff Dashboard)
│   │   ├── 🚀 Program.cs ........................... "Opens the office, turns on all systems"
│   │   ├── 🎛️ Controllers/ ........................ "Handles staff button clicks and actions"
│   │   ├── 🖼️ Components/ ......................... "Screen layouts and forms staff see"
│   │   ├── 📊 Migrations/ ......................... "Database update instructions"
│   │   └── ⚙️ AppSettings/ ........................ "Configuration files"
│   │
│   ├── 👤 Web.Api/ (Customer Portal Backend)
│   │   ├── 🚀 Program.cs ........................... "Powers customer mobile apps/websites"
│   │   └── 🎛️ Controllers/ ........................ "Handles customer requests (check balance, pay)"
│   │
│   └── 🤖 Web.BackgroundService/ (Automated Workers)
│       ├── 🚀 Program.cs ........................... "Starts the robots that work 24/7"
│       └── 🔧 Components/ .......................... "Background task definitions"
│
├── 🔧 ENGINE ROOM (Business Logic)
│   │
│   ├── 🛠️ Infrastructure.Core/ (Basic Office Equipment)
│   │   ├── 👥 AdminUserManagement/
│   │   │   └── 👤 ApplicationUser.cs .............. "Employee/Customer ID card template"
│   │   ├── 🔐 Authentication/ ..................... "Security guard system"
│   │   ├── 📨 Messaging/
│   │   │   ├── 📮 MessageRouter.cs ................ "Decides: email, SMS, or both?"
│   │   │   ├── ✍️ MessageComposer.cs .............. "Writes emails and texts from templates"
│   │   │   ├── ⏰ MessageScheduler.cs ............. "Schedules when to send messages"
│   │   │   └── 📡 Providers/ ...................... "Email and SMS sending tools"
│   │   ├── ⚙️ BackgroundTasks/ .................... "Automated job definitions"
│   │   ├── 💾 Database/ ........................... "Filing system rules and connections"
│   │   └── 🔌 DependencyInjection/ ................ "Connects all the office equipment"
│   │
│   ├── 💰 Infrastructure.Collection/ (Loan Department)
│   │   ├── 💳 Payments/
│   │   │   ├��─ 🏭 IPaymentProvider.cs ............. "Blueprint for payment processors"
│   │   │   ├── 🏗️ PaymentFactory.cs ............... "Chooses which payment company to use"
│   │   │   ├── 🔧 TransactionHelper.cs ............ "Payment processing utilities"
│   │   │   └── 🏢 Providers/ ...................... "Specific payment company integrations"
│   │   ├── 📅 Schedules/ .......................... "Payment due date management"
│   │   ├── 📊 Analytics/ .......................... "Business reports and statistics"
│   │   ├── 🎛️ ApiControllers/ ..................... "Loan-specific customer portal features"
│   │   ├── 📨 Messaging/ .......................... "Loan-specific message templates"
│   │   └── 🔄 Workflow/Debt/ ...................... "Collection process automation"
│   │
│   └── 🤝 Infrastructure.ExternalApi/ (Partner Connections)
│       ├── 💳 Acquired/ ........................... "Credit card processing partner"
│       ├── ⚙️ AppSettings/
│       │   └── 🔑 externalapisettings.json ....... "Partner company login credentials"
│       └── 🔌 Base/ ............................... "Common tools for partner integrations"
│
└── 📋 FILING ROOM (Data Definitions)
    │
    ├── 📄 Entities.Core/ (Basic Business Forms)
    │   ├── 👤 BaseUser/ ........................... "Basic employee/customer info template"
    │   ├── 📨 Messaging/ .......................... "Message and communication templates"
    │   ├── 📊 Logs/ ............................... "Activity tracking templates"
    │   └── ⚙️ BackgroundTasks/ .................... "Automated job templates"
    │
    └── 📁 Entities.Collection/ (Customer Files)
        ├── 👥 Customers/
        │   ├── 📋 CustomerProfile.cs .............. "Main customer information file"
        │   ├── 🏠 CustomerAddress.cs .............. "Customer address book"
        │   ├── 📞 CustomerContactDetail.cs ........ "Phone numbers and emails"
        │   ├── 📅 CustomerSchedule.cs ............. "Payment schedule and due dates"
        │   ├── 💰 CustomerTransaction.cs .......... "Payment history record"
        │   ├── 📝 CustomerNote.cs ................. "Staff notes and comments"
        │   ├── 💳 CustomerPaymentMethod.cs ........ "Saved credit cards and bank accounts"
        │   ├── 🏷️ CustomerFlag.cs ................. "Special customer markers (VIP, problem, etc.)"
        │   └── ⏸️ CustomerHold.cs ................. "Account restrictions and holds"
        ├── 💰 Collections/ ........................ "Loan and debt information"
        └── 📊 Analytics/ .......................... "Business intelligence data"
```

---

## 🔄 Workflows & Analytics Integration

### **Collection Workflow Process**
```
Customer misses payment
        ↓
🤖 Background service detects overdue
        ↓
📊 System categorizes customer (DebtSegment)
        ↓
👥 WorkflowAssignmentService finds available agent
        ↓
📋 AgentWorkflowTask created
        ↓
👤 Agent contacts customer
        ↓
✅ Agent records outcome
        ↓
📈 Analytics update automatically
```

### **Dashboard Data Flow**
```
Business Activity (payments, calls, etc.)
        ↓
📊 ManageAnalyticsService captures data
        ↓
💾 DashboardAnalytics entity updated
        ↓
🖥️ Dashboard refreshes automatically
        ↓
👥 Users see real-time metrics
```

### **Agent Management Flow**
```
👤 Agent logs in
        ↓
✅ Sets status to "Available"
        ↓
��� WorkflowAssignmentBackgroundService assigns customers
        ↓
📋 Agent sees tasks in queue
        ↓
📞 Agent contacts customers
        ↓
📊 Performance metrics update
```

---

## 🔄 How Components Connect (Workflow View)

### 1. 👤 Customer Checks Balance
```
Customer opens app
        ↓
📱 Web.Api receives request
        ↓
🔧 Infrastructure.Collection finds customer
        ↓
📋 Entities.Collection.CustomerProfile loads data
        ↓
💾 PostgreSQL returns balance
        ↓
📱 App shows balance to customer
```

### 2. 💳 Processing a Payment
```
🤖 Background service runs (daily at 9 AM)
        ↓
🔧 Infrastructure.Collection finds due payments
        ↓
💳 PaymentFactory chooses payment processor
        ↓
🤝 Infrastructure.ExternalApi.Acquired charges card
        ↓
📋 CustomerTransaction record created
        ↓
📨 MessageRouter sends confirmation email/SMS
        ↓
💾 Database updated with payment result
```

### 3. 👨‍💼 Staff Adds New Customer
```
Staff enters customer info in Web.Admin
        ↓
🎛️ Controllers validate information
        ↓
📋 CustomerProfile entity created
        ↓
👤 ApplicationUser account auto-created
        ↓
📨 Welcome message queued
        ↓
🤖 Background service sends welcome email
        ↓
💾 All data saved to database
```

### 4. 📨 Sending Messages
```
System needs to send reminder
        ↓
📨 MessageComposer creates message from template
        ↓
📮 MessageRouter decides: email, SMS, or both?
        ↓
📡 Providers send through email/SMS services
        ↓
📊 Delivery status logged
        ↓
🔄 If failed, try backup channel
```

---

## 🎯 Key Connection Points

### 🔌 How Everything Connects
```
                    🚀 Program.cs Files
                           │
                    (Start everything up)
                           │
                           ▼
                🔌 DependencyInjection/
                           │
                (Connect all the pieces)
                           │
                           ▼
            ┌──────────────┼──────────────┐
            │              │              │
    🎛️ Controllers    📨 Messaging    💳 Payments
            │              │              │
    (Handle requests) (Send messages) (Process money)
            │              │              │
            └──────────────┼──────────────┘
                           │
                           ▼
                    📋 Entities/
                           │
                (Define data structure)
                           │
                           ▼
                    💾 Database
                           │
                (Store everything)
```

---

## 🛠️ Developer Quick Reference

### 🎯 "I want to..." → "Go to this file"

| **Task** | **Start Here** | **Then Check** |
|----------|----------------|----------------|
| 👤 Add customer feature | `Entities.Collection/Customers/` | `Infrastructure.Collection/` |
| 💳 Modify payments | `Infrastructure.Collection/Payments/IPaymentProvider.cs` | `Infrastructure.ExternalApi/` |
| 📨 Change messaging | `Infrastructure.Core/Messaging/MessageRouter.cs` | `Infrastructure.Collection/Messaging/` |
| 🤖 Add background task | `Infrastructure.Core/BackgroundTasks/` | `Web.BackgroundService/Program.cs` |
| 🔐 User management | `Infrastructure.Core/AdminUserManagement/` | `Infrastructure.Core/Authentication/` |
| 💾 Database changes | `Entities.Core/` or `Entities.Collection/` | `Web.Admin/Migrations/` |
| 🌐 Add API endpoint | `Web.Api/Controllers/` | `Infrastructure.Collection/ApiControllers/` |
| ���️ Admin interface | `Web.Admin/Components/` | `Web.Admin/Controllers/` |

---

## 🧩 System Dependencies (What Needs What)

```
Web.Admin
    ├── depends on → Infrastructure.Core
    ├── depends on → Infrastructure.Collection
    └── uses → Entities.Core + Entities.Collection

Web.Api
    ├── depends on → Infrastructure.Core
    ├── depends on → Infrastructure.Collection
    └── uses → Entities.Collection

Web.BackgroundService
    ├─��� depends on → Infrastructure.Core
    ├── depends on → Infrastructure.Collection
    └── uses → Entities.Core

Infrastructure.Collection
    ├── depends on → Infrastructure.Core
    ├── depends on → Infrastructure.ExternalApi
    └── uses → Entities.Collection

Infrastructure.Core
    └── uses → Entities.Core

Infrastructure.ExternalApi
    └── uses → Infrastructure.Core (base classes)

Entities.Collection
    └── extends → Entities.Core

Entities.Core
    └── (no dependencies - foundation layer)
```

---

## 🎭 Real-World Business Analogy

```
🏢 LendQube = Complete Lending Business

🖥️ Web.Admin = Manager's Office Computer
   "Where staff log in to see everything and manage customers"

📱 Web.Api = Customer Service Phone Line
   "How customers check balances and make payments"

🤖 Web.BackgroundService = Night Security Guard
   "Works 24/7 doing automatic tasks like processing payments"

🔧 Infrastructure.Core = Office Building Infrastructure
   "Electricity, phones, filing systems - basic stuff everyone needs"

💰 Infrastructure.Collection = Loan Department
   "Specialists who handle everything about loans and collections"

🤝 Infrastructure.ExternalApi = Business Partners
   "Credit card companies, SMS providers, other services we work with"

📋 Entities = Filing Cabinet Organization System
   "Rules for how customer files and business forms are organized"

💾 Database = The Actual Filing Cabinets
   "Where all the customer information and business data is stored"
```

This visual tree shows you exactly how everything connects and where to find what you need!
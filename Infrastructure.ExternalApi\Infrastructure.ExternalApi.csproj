﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
		<RootNamespace>LendQube.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>
	<ItemGroup>
	  <ProjectReference Include="..\Infrastructure.Core\Infrastructure.Core.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <Content Update="AppSettings\externalapisettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="AppSettings\externalapisettings.Development.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	  <Content Update="AppSettings\externalapisettings.Production.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </Content>
	</ItemGroup>
</Project>

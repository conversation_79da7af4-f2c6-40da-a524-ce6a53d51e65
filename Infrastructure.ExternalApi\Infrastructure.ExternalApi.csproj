﻿<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>disable</Nullable>
		<RootNamespace>LendQube.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>
	<ItemGroup>
	  <ProjectReference Include="..\Infrastructure.Core\Infrastructure.Core.csproj" />
	</ItemGroup>
	<ItemGroup>
	  <None Update="AppSettings\externalapisettings.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	  <None Update="AppSettings\externalapisettings.Production.json">
	    <CopyToOutputDirectory>Always</CopyToOutputDirectory>
	  </None>
	</ItemGroup>
</Project>

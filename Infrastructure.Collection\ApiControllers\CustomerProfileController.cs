﻿using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.GenericSpecification;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Helpers.ApiControllers;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using NodaTime;

namespace LendQube.Infrastructure.Collection.ApiControllers;

[Route(ApiConstants.URL)]
public sealed class CustomerProfileController(GenericVMSpecificationService<CustomerInbox, CustomerInboxVM> service, IClock clock) : ApiAuthControllerBase
{

    [HttpGet(nameof(GetProfile))]
    public async Task<CustomerProfileVM> GetProfile(CancellationToken ct) => 
        await service.CrudService.Db.OneSelectAsync(Query<CustomerProfile, CustomerProfileVM>.Where(x => x.Id == User.Identity.Name).Select(CustomerProfileVM.Mapping), ct);

    [HttpGet(nameof(NotificationAvailable))]
    public async Task<ActionResult<bool>> NotificationAvailable(CancellationToken ct) => await service.CrudService.Db.ExistsAsync<CustomerInbox>(x => x.UserId == User.Identity.Name && !x.ReadOn.HasValue, ct);

    [HttpPost(nameof(MarkNotificationAsSeen))]
    public async Task<IResult> MarkNotificationAsSeen([FromBody] List<string> ids, CancellationToken ct)
    {
        await service.CrudService.UpdateWithFilter(x => x.UserId == User.Identity.Name && !x.ReadOn.HasValue && ids.Contains(x.Id), x => x.SetProperty(y => y.ReadOn, clock.GetCurrentInstant()), ct);
        return TypedResults.Ok();
    }

    [HttpPost(nameof(GetNotifications))]
    public async Task<TypedBasePageList<CustomerInboxVM>> GetNotifications(DataFilterAndPage vm, CancellationToken ct)
    {
        vm.OrderByColumn = nameof(CustomerInbox.CreatedDate);
        service.PrimaryCriteria = x => x.UserId == User.Identity.Name;
        return await service.CrudService.GetTypeBasedPagedData(service, vm, CustomerInboxVM.Mapping, ct);
    }
}


public class CustomerProfileVM
{
    public static readonly Expression<Func<CustomerProfile, CustomerProfileVM>> Mapping = data => new CustomerProfileVM
    {
        AccountId = data.AccountId,
        FirstName = data.FirstName,
        LastName = data.LastName,
        MiddleName = data.MiddleName,
        Email = data.Email,
        MobileNumber = data.MobileNumber.Number,
        PhoneCode = data.MobileNumber.Code,
        CountryCode = data.CountryCode,
        Currency = data.CurrencySymbol,
        CanReschedule = data.CanReschedule,
        HasDateOfBirth = data.DateOfBirth.HasValue,
        NextRescheduleDate = data.NextRescheduleDate,
        Balance = data.BalanceRemaining,
        BalancePaid = data.BalancePaid,
        Frequency = data.PaymentFrequency,
        DisposableIncome = data.IncomeAndExpenditure == null ? 0 : data.IncomeAndExpenditure.NetDisposableIncome,
        TotalPaymentsMade = data.Schedules.Count(x => x.PaymentStatus == SchedulePaymentStatus.Paid),
        Schedule = data.Schedules.Select(d => new CustomerScheduleApiVM()
        {
            Period = d.Period,
            DueDate = d.DueDate,
            Amount = d.Amount,
            AmountPaid = d.AmountPaid,
            CPADate = d.CPADate,
            PeriodStatus = d.PeriodStatus,
            PaymentStatus = d.PaymentStatus,
            Balance = d.Balance,
        })
    };

    public string AccountId { get; set; }
    public string FirstName { get; set; }
    public string MiddleName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string MobileNumber { get; set; }
    public string CountryCode { get; set; }
    public string Currency { get; set; }
    public string PhoneCode { get; set; }
    public decimal Balance { get; set; }
    public decimal BalancePaid { get; set; }
    public decimal DisposableIncome { get; set; }
    public bool CanReschedule { get; set; }
    public bool HasDateOfBirth { get; set; }
    public Instant? NextRescheduleDate { get; set; }
    public int TotalPaymentsMade { get; set; }
    public SchedulePaymentFrequency? Frequency { get; set; }
    public IEnumerable<CustomerScheduleApiVM> Schedule { get; set; }
}

public class CustomerScheduleApiVM
{
    public int Period { get; set; }
    public DateOnly DueDate { get; set; }
    public decimal Amount { get; set; }
    public decimal AmountPaid { get; set; }
    public DateOnly CPADate { get; set; }
    public SchedulePeriodStatus PeriodStatus { get; set; }
    public SchedulePaymentStatus PaymentStatus { get; set; }
    public decimal Balance { get; set; }
}

public class CustomerInboxVM
{
    public static readonly Expression<Func<CustomerInbox, CustomerInboxVM>> Mapping = data => new CustomerInboxVM
    {
        Id = data.Id,
        Subject = data.Subject,
        Type = data.Type,
        Template = data.Template,
        Read = data.ReadOn.HasValue,
        CreatedDate = data.CreatedDate,
    };

    public string Id { get; set; }
    public string Subject { get; set; }
    public MessageConfigTemplateType Type { get; set; }
    public string Template { get; set; }
    public bool Read { get; set; }
    public Instant? CreatedDate { get; set; }
}
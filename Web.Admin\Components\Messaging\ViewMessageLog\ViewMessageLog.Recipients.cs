﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.ViewModels.Messaging;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Web.Admin.Components.Messaging.ViewMessageLog;

public partial class ViewMessageLog
{
    private DataTable<ViewMessageRecipientVM> recipientsTable;
    private ColumnList RecipientTableDefinition { get; set; }

    private void SetupRecipients()
    {
        RecipientTableDefinition = Service.GetTableDefinition<MessageRecipient, ViewMessageRecipientVM>(new() { HasDateColumns = false, TurnOffFilter = true, TurnOffSorting = true });
        RecipientTableDefinition.RowActionButtons.Add(new RowActionButton("Preview", Action: async (object row) =>
        {
            modalMessage.Close();
            recipientsTable.Loading = true;

            await PreviewMessage(row as ViewMessageRecipientVM);

            recipientsTable.Loading = false;
            StateHasChanged();

        }, ShowCondition: (object row) => (row as ViewMessageRecipientVM).IsUser));
    }

    private async ValueTask<TypedBasePageList<ViewMessageRecipientVM>> LoadRecipients(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        var spec = new BaseSpecification<MessageRecipientVM>();

        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            var filter = $"%{filterAndPage.TextFilter.Trim()}%";
            spec.PrimaryCriteria = x => EF.Functions.ILike(x.UserData.FullName, filter)
            || EF.Functions.ILike(x.UserData.Email, filter) || EF.Functions.ILike(x.UserData.PhoneNumber, filter) || EF.Functions.ILike(x.GroupData.Name, filter);
        }

        filterAndPage.OrderByColumn = null;
        var mapping = MessageRecipientVM.SelectorMapping(uow.Db);
        var query = uow.Db.Queryable<MessageLogEntry>().Where(x => x.Id == SelectedEntry.Id).SelectMany(x => x.Recipients).OrderBy(x => x.UserId).ThenBy(x => x.MessagingGroupId).AsNoTracking();

        var result = await Service.GetTypeBasedPagedData(query, spec, filterAndPage, mapping, ct);

        return result.ToType(ViewMessageRecipientVM.Mapping);
    }
}


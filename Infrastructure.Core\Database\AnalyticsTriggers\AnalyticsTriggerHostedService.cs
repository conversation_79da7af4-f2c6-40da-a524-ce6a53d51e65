﻿using LendQube.Infrastructure.Core.DependencyInjection;
using LendQube.Infrastructure.Core.Telemetry;
using Microsoft.Extensions.Hosting;
using Npgsql;

namespace LendQube.Infrastructure.Core.Database.AnalyticsTriggers;

public class AnalyticsTriggerHostedService(NpgsqlDataSource dataSource, ILogManager<AnalyticsTriggerHostedService> logger) : BackgroundService
{
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        if (AdminDI.AnalyticsTriggers.Count == 0)
            return;

        await using var connection = await dataSource.OpenConnectionAsync(stoppingToken);
        using var transaction = await connection.BeginTransactionAsync(System.Data.IsolationLevel.Serializable, stoppingToken);
        try
        {
            var createScripts = AnalyticsTriggerScript.CreateTriggers();

            await using var createTriggerCmd = new NpgsqlCommand(createScripts, connection, transaction);
            await createTriggerCmd.ExecuteNonQueryAsync(stoppingToken);

            await transaction.CommitAsync(stoppingToken);
        }
        catch (Exception e)
        {
            logger.LogError(EventSource.Infrastructure, EventAction.Triggers, e, "Unable to create triggers");
            await transaction.RollbackAsync(stoppingToken);
        }
        finally
        {
            await connection.CloseAsync();
        }
    }
}

﻿using System.ComponentModel;
using System.Linq.Expressions;
using System.Runtime.CompilerServices;
using System.Text.Json;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.DataPager.Filters;
using LendQube.Infrastructure.Core.Extensions;
using Microsoft.EntityFrameworkCore.Query;

namespace LendQube.Infrastructure.Core.Database.Specification;

public class BaseSpecification<T>([CallerFilePath] string filePath = null, [CallerLineNumber] int lineNumber = 0) : ISpecification<T>
{
    public string CallerFilePath { get; set; } = filePath;
    public int CallerLineNumber { get; set; } = lineNumber;
    public Expression<Func<T, bool>> PrimaryCriteria { get; set; }
    protected Expression<Func<T, bool>> FilterCriteria { get; set; }
    protected Expression<Func<T, bool>> ComplexFilterCriteria { get; set; }
    public Func<IQueryable<T>, IIncludableQueryable<T, object>> Includes { get; set; }
    public Func<IQueryable<T>, IOrderedQueryable<T>> OrderBy { get; set; }

    public void TagOrigination([CallerFilePath] string filePath = null, [CallerLineNumber] int lineNumber = 0)
    {
        CallerFilePath = filePath;
        CallerLineNumber = lineNumber;
    }

    public void DoAllFilter(DataFilterAndPage filterAndPage)
    {
        DoJsonFilter(filterAndPage.AllFilter);
        DoComplexFilter(filterAndPage);
    }

    public Expression<Func<TVM, bool>> GetAllFilter<TVM>(DataFilterAndPage filterAndPage)
    {
        Expression<Func<TVM, bool>> filterCriteria = DoJsonFilter<TVM>(filterAndPage.AllFilter);
        var complexFilter = DoComplexFilter<TVM>(filterAndPage);

        if (filterCriteria == null)
            filterCriteria = complexFilter;
        else
            filterCriteria = filterCriteria.CombineWithAndAlso(complexFilter);

        return filterCriteria;
    }

    protected virtual void DoJsonFilter(string allFilter)
    {
        if (!string.IsNullOrEmpty(allFilter))
        {
            var filter = new Dictionary<string, string>(JsonSerializer.Deserialize<Dictionary<string, string>>(allFilter), StringComparer.OrdinalIgnoreCase);

            var fieldsWithValue = TypeDescriptor.GetProperties(typeof(T)).Cast<PropertyDescriptor>()
            .Select(p => new { p.Name, p.PropertyType }).Where(x => filter.ContainsKey(x.Name)).ToList();
            object filterValue = null;
            foreach (var item in fieldsWithValue)
            {
                if (filter.TryGetValue(item.Name, out string value) && !string.IsNullOrEmpty(value))
                {
                    Expression<Func<T, bool>> expression = null;
                    if (item.PropertyType.IsBooleanType())
                    {
                        filterValue = Convert.ToBoolean(Enum.Parse(typeof(ColumnFilterBoolOptions), value));
                        expression = ExpressionsExtension.Equal<T>(item.PropertyType, filterValue, item.Name);
                    }
                    else if ((item.PropertyType.IsEnum && Enum.TryParse(item.PropertyType, value, out filterValue)) ||
                        (item.PropertyType.IsNullableEnum() && Enum.TryParse(Nullable.GetUnderlyingType(item.PropertyType), value, out filterValue)))
                    {
                        expression = ExpressionsExtension.Equal<T>(item.PropertyType, filterValue, item.Name);
                    }
                    else if (item.PropertyType.IsDate())
                    {
                        expression = ExpressionsExtension.DateTimeEqual<T>(item.PropertyType, value, item.Name);
                    }
                    else if (item.PropertyType != typeof(string))
                    {
                        var converter = TypeDescriptor.GetConverter(item.PropertyType);
                        if (converter.IsValid(value))
                        {
                            filterValue = converter.ConvertFrom(value);
                            expression = ExpressionsExtension.Equal<T>(item.PropertyType, filterValue, item.Name);
                        }
                    }
                    else
                    {
                        expression = ExpressionsExtension.Like<T>(value, item.Name);
                    }

                    if (expression == null)
                        continue;

                    if (FilterCriteria == null)
                        FilterCriteria = expression;
                    else
                        FilterCriteria = FilterCriteria.CombineWithAndAlso(expression);
                }
            }
        }

        ApplyCriteria(FilterCriteria);
    }

    protected virtual Expression<Func<TVM, bool>> DoJsonFilter<TVM>(string allFilter)
    {
        Expression<Func<TVM, bool>> filterCriteria = null;
        if (!string.IsNullOrEmpty(allFilter))
        {
            var filter = new Dictionary<string, string>(JsonSerializer.Deserialize<Dictionary<string, string>>(allFilter), StringComparer.OrdinalIgnoreCase);

            var fieldsWithValue = TypeDescriptor.GetProperties(typeof(TVM)).Cast<PropertyDescriptor>()
            .Select(p => new { p.Name, p.PropertyType }).Where(x => filter.ContainsKey(x.Name)).ToList();
            object filterValue = null;
            foreach (var item in fieldsWithValue)
            {
                if (filter.TryGetValue(item.Name, out string value) && !string.IsNullOrEmpty(value))
                {
                    Expression<Func<TVM, bool>> expression = null;
                    if (item.PropertyType.IsBooleanType())
                    {
                        filterValue = Convert.ToBoolean(Enum.Parse(typeof(ColumnFilterBoolOptions), value));
                        expression = ExpressionsExtension.Equal<TVM>(item.PropertyType, filterValue, item.Name);
                    }
                    else if ((item.PropertyType.IsEnum && Enum.TryParse(item.PropertyType, value, out filterValue)) ||
                        (item.PropertyType.IsNullableEnum() && Enum.TryParse(Nullable.GetUnderlyingType(item.PropertyType), value, out filterValue)))
                    {
                        expression = ExpressionsExtension.Equal<TVM>(item.PropertyType, filterValue, item.Name);
                    }
                    else if (item.PropertyType.IsDate())
                    {
                        expression = ExpressionsExtension.DateTimeEqual<TVM>(item.PropertyType, value, item.Name);
                    }
                    else if (item.PropertyType != typeof(string))
                    {
                        var converter = TypeDescriptor.GetConverter(item.PropertyType);
                        if (converter.IsValid(value))
                        {
                            filterValue = converter.ConvertFrom(value);
                            expression = ExpressionsExtension.Equal<TVM>(item.PropertyType, filterValue, item.Name);
                        }
                    }
                    else
                    {
                        expression = ExpressionsExtension.Like<TVM>(value, item.Name);
                    }

                    if (expression == null)
                        continue;

                    if (filterCriteria == null)
                        filterCriteria = expression;
                    else
                        filterCriteria = filterCriteria.CombineWithAndAlso(expression);
                }
            }
        }

        return filterCriteria;
    }

    protected virtual void DoComplexFilter(DataFilterAndPage pagedParam)
    {
        var filters = pagedParam.ComplexFilter;
        if (filters.IsNullOrEmpty())
            return;

        var propertyTypes = TypeDescriptor.GetProperties(typeof(T)).Cast<PropertyDescriptor>()
            .Where(x => filters.Any(y => y.ColumnName == x.Name))
            .Select(p => new { p.Name, p.PropertyType });
        object value = null;
        foreach (var item in filters)
        {
            var rule = ObjectFilterBuilder.Get(item.RuleId);
            var type = propertyTypes.FirstOrDefault(x => x.Name == item.ColumnName);
            if (rule == null || type == null)
                continue;

            rule.EmbeddedQuery = pagedParam.AsyncNotSupportedDataSource || pagedParam.UseEmbeddedQuery; //not async data sources require embedded comparisons e.g. local lists
            if (type.PropertyType.IsBooleanType())
                value = Convert.ToBoolean(Enum.Parse(typeof(ColumnFilterBoolOptions), item.Value.ToString()));
            else
                value = item.Value;

            Expression<Func<T, bool>> expression = rule.GenerateExpression<T>(type.PropertyType, item, value);

            if (expression == null)
                continue;

            if (ComplexFilterCriteria == null)
                ComplexFilterCriteria = expression;
            else
                ComplexFilterCriteria = item.Condition == FilterCondition.Or ? ComplexFilterCriteria.CombineWithOrElse(expression) : ComplexFilterCriteria.CombineWithAndAlso(expression);

        }

        ApplyCriteria(ComplexFilterCriteria);
        ComplexFilterCriteria = null;
    }

    protected virtual Expression<Func<TVM, bool>> DoComplexFilter<TVM>(DataFilterAndPage pagedParam)
    {
        Expression<Func<TVM, bool>> filterCriteria = null;
        var filters = pagedParam.ComplexFilter;
        if (filters.IsNullOrEmpty())
            return filterCriteria;

        var propertyTypes = TypeDescriptor.GetProperties(typeof(TVM)).Cast<PropertyDescriptor>()
            .Where(x => filters.Any(y => y.ColumnName == x.Name))
            .Select(p => new { p.Name, p.PropertyType });
        object value = null;
        foreach (var item in filters)
        {
            var rule = ObjectFilterBuilder.Get(item.RuleId);
            var type = propertyTypes.FirstOrDefault(x => x.Name == item.ColumnName);
            if (rule == null || type == null)
                continue;

            rule.EmbeddedQuery = pagedParam.AsyncNotSupportedDataSource || pagedParam.UseEmbeddedQuery; //not async data sources require embedded comparisons e.g. mongodb or local lists
            if (type.PropertyType.IsBooleanType())
                value = Convert.ToBoolean(Enum.Parse(typeof(ColumnFilterBoolOptions), item.Value.ToString()));
            else
                value = item.Value;

            Expression<Func<TVM, bool>> expression = rule.GenerateExpression<TVM>(type.PropertyType, item, value);

            if (expression == null)
                continue;

            if (filterCriteria == null)
                filterCriteria = expression;
            else
                filterCriteria = item.Condition == FilterCondition.Or ? filterCriteria.CombineWithOrElse(expression) : filterCriteria.CombineWithAndAlso(expression);

        }

        return filterCriteria;
    }

    protected void ApplyCriteria(Expression<Func<T, bool>> criteria)
    {
        if (criteria != null)
        {
            if (PrimaryCriteria != null)
                PrimaryCriteria = PrimaryCriteria.CombineWithAndAlso(criteria);
            else
                PrimaryCriteria = criteria;
        }
    }

}

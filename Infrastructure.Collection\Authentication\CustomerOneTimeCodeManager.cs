﻿using LendQube.Entities.Collection.Customers;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using NodaTime;

namespace LendQube.Infrastructure.Collection.Authentication;

public class CustomerOneTimeCodeManager(IUnitofWork uow, IClock clock, ILogManager<CustomerOneTimeCodeManager> logger)
{
    public async Task<Result<SendCodeResponse>> SendCode(SendCodeRequestVM vm, CancellationToken ct)
    {
        if (string.IsNullOrEmpty(vm.UserId))
            return Result<SendCodeResponse>.Failed("Unable to send code");

        var validCode = await uow.Db.OneAsync(Query<CustomerOneTimeCode>.Where(x => x.ProfileId == vm.UserId && x.CodeType == vm.MessageConfigName && x.IsValid), ct);

        if (validCode != null && validCode.SendCount >= vm.SendLimit && vm.SendLimit > 0)
            return Result<SendCodeResponse>.Successful("Code limit has been reached. Please contact support");

        var result = await DispatchCode(vm, validCode, ct);

        return Result<SendCodeResponse>.Successful("Code sent successfully", data: validCode == null ? result : null);
    }

    public async Task<Result<bool>> ValidateCode(ValidateCodeRequest vm, CancellationToken ct)
    {
        if (string.IsNullOrWhiteSpace(vm.UserId) || string.IsNullOrWhiteSpace(vm.Code))
            return Result<bool>.Failed("Code is invalid");

        var code = await uow.Db.OneAsync(Query<CustomerOneTimeCode>.Where(x => x.ProfileId == vm.UserId && x.CodeType == vm.CodeType && x.Code == vm.Code), ct);

        if (code is null || !code.IsValid)
            return Result<bool>.Failed("Code is invalid");

        if (code.TriesCount >= 3)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerOneTimeCode>(x => x.Id == code.Id, x => x.SetProperty(y => y.IsValid, false), ct);
            return Result<bool>.Failed("Number of tries exceeded, please request new code");
        }

        if (code.ExpireAt < clock.GetCurrentInstant())
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerOneTimeCode>(x => x.Id == code.Id, x => x.SetProperty(y => y.IsValid, false), ct);
            return Result<bool>.Failed("Code expired. Please request new code");
        }

        code.TriesCount++;

        if (code.Code != vm.Code)
        {
            uow.Db.Update(code);
            await uow.SaveAsync(ct);   
            return Result<bool>.Failed("Code is invalid, please request a new code");
        }

        code.IsValid = false;
        uow.Db.Update(code);
        await uow.SaveAsync(ct);

        return Result<bool>.Successful("Successfully verified");
    }

    private async Task<SendCodeResponse> DispatchCode(SendCodeRequestVM vm, CustomerOneTimeCode code, CancellationToken ct)
    {
        var result = Result<long>.Successful(string.Empty, data: code?.MessageLogId ?? 0);
        if(code != null)
        {
            await MessageBuilder.Resend(uow, code.MessageLogId, vm.SendLimit, ct);
        }
        else
        {
            code = CreateCode(vm);

            result = await MessageBuilder.New(vm.OriginatedFrom, vm.UserId)
                .Message(vm.MessageConfigName)
                .WithRecipient(vm.UserId, [new($"{MessageTemplateKeys.Code}", code.Code)])
                .Send(uow, logger, ct);
        }

        if (result.IsSuccessful) //code sent, increase send count
        {
            code.SendCount++;
            code.MessageLogId = result.Data;
            if (code.Id > 0)
                uow.Db.Update(code);
            else
                uow.Db.Insert(code);
            await uow.SaveAsync(ct);

            //invalidate all valid codes
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerOneTimeCode>(x => x.ProfileId == vm.UserId && x.CodeType == vm.MessageConfigName
            && x.Id != code.Id && x.IsValid, x => x.SetProperty(y => y.IsValid, false), ct);
        }

        return new SendCodeResponse(result.IsSuccessful, code.Code);
    }

    private static CustomerOneTimeCode CreateCode(SendCodeRequestVM vm)
    {
        var code = NumberGenerator.GenerateAuthCode(4);
        return new CustomerOneTimeCode()
        {
            ProfileId = vm.UserId,
            Code = code,
            SendLimit = vm.SendLimit,
            CodeType = vm.MessageConfigName,
            ExpireAt = vm.ExpireAt,
            IsValid = true
        };
    }
}

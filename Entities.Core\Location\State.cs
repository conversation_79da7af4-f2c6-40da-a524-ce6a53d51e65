﻿using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Entities.Core.Location;

public class State : BaseEntityWithIdentityId<State>
{
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public long CountryId { get; set; }
    public virtual Country Country { get; set; }
    [Required, MaxLength(EntityConstants.DEFAULT_TYPE_FIELD_LENGTH), ValidString(ValidStringRule.OnlyText), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string StateCode { get; set; }
    [Required, TableDecorator(TableDecoratorType.ShowInDelete), ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string StateName { get; set; }

    public override void Configure(EntityTypeBuilder<State> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => new { x.CountryId, x.StateCode }).IsUnique();
    }
}

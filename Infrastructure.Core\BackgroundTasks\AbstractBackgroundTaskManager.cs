﻿using LendQube.Entities.Core.BackgroundTasks;

namespace LendQube.Infrastructure.Core.BackgroundTasks;

public abstract class AbstractBackgroundTaskManager(BackgroundTaskControlService controlService)
{
    public abstract BackgroundEventSource AllowedSource { get; }

    public abstract IReadOnlyList<BackgroundTask> AllowedTasks { get; }

    public virtual bool IsActive(BackgroundEventSource source) => AllowedSource == source;

    public virtual async Task StartBackgroundTask(BackgroundEventSource source, BackgroundTask key, CancellationToken ct)
    {
        await controlService.CreateOrStart(source, key, ct);
        await StartTasks(key, ct);
    }

    public abstract Task StartTasks(BackgroundTask key, CancellationToken ct);

    public virtual Task StopBackgroundTask(BackgroundTaskEventControl data, CancellationToken ct) => controlService.MarkForStopping(data, ct);
}

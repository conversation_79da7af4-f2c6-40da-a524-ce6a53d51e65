﻿using System.ComponentModel.DataAnnotations.Schema;
using System.Globalization;
using NodaTime;
using OfficeOpenXml.Attributes;

namespace LendQube.Infrastructure.Collection.Reporting.ViewModel;

[EpplusTable(PrintHeaders = true, AutofitColumns = true)]
public class PlacementReportVM
{
    [EpplusIgnore]
    public LocalDate PlacementDate { get; set; }
    [EpplusTableColumn(Order = 0, Header = "Placement Date")]
    public string PlacementDateFormatted => PlacementDate.ToString("dd/MM/yyyy", CultureInfo.InvariantCulture);
    [EpplusTableColumn(Order = 1, Header = "Customers Escalated")]
    public int CustomersEscalated { get; set; }
    [EpplusTableColumn(Order = 2, Header = "Debt Escalated", NumberFormat = "#,##0.00")]
    public decimal DebtEscalated { get; set; }
    [EpplusIgnore]
    public int AccountAgeInDays { get; set; }
    [NotMapped, EpplusTableColumn(Order = 3, Header = "Progress through collections cycle")]
    public int CollectionsCycleProgress => CollectionsCycleCalculator.GetCycle(AccountAgeInDays);
    [EpplusTableColumn(Order = 4, Header = "Active Customers", NumberFormat = "#,##0.00")]
    public decimal ActiveCustomers { get; set; }
    [EpplusTableColumn(Order = 5, Header = "Active Outstanding Balance", NumberFormat = "#,##0.00")]
    public decimal ActiveOutstandingBalance { get; set; }
    [EpplusTableColumn(Order = 6, Header = "Total Payments", NumberFormat = "#,##0.00")]
    public decimal TotalPayments { get; set; }
    [EpplusTableColumn(Order = 7, Header = "Outstanding Balance on Plan", NumberFormat = "#,##0.00")]
    public decimal OutstandingBalanceOnPlan { get; set; }
    [EpplusTableColumn(Order = 8, Header = "% Recovered")]
    public decimal PercentageRecovered => DebtEscalated > 0 && TotalPayments > 0 ? (TotalPayments / DebtEscalated) : 0;

    [EpplusIgnore]
    public decimal AccountsWithSchedule { get; set; }
    [EpplusTableColumn(Order = 9, Header = "% Committed", NumberFormat = "#0.00%")]
    public decimal PercentageCommitted => ActiveCustomers > 0 && AccountsWithSchedule > 0 ? (AccountsWithSchedule / ActiveCustomers) : 0m;

    [EpplusIgnore]
    public decimal AccountsWithLogin { get; set; }
    [EpplusTableColumn(Order = 10, Header = "% Engaged", NumberFormat = "#0.00%")]
    public decimal PercentageEngaged => ActiveCustomers > 0 && AccountsWithLogin > 0 ? (AccountsWithLogin / ActiveCustomers) : 0m;

    [EpplusIgnore]
    public decimal AccountsWithVulnerableHold { get; set; }
    [EpplusTableColumn(Order = 11, Header = "% Customers Likely Vulnerable", NumberFormat = "#0.00%")]
    public decimal PercentageLikelyVulnerable => ActiveCustomers > 0 && AccountsWithVulnerableHold > 0 ? (AccountsWithVulnerableHold / ActiveCustomers) : 0m;


}

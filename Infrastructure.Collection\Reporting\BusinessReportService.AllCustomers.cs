﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Collection.Reporting.ViewModel;
using LendQube.Infrastructure.Core.Extensions;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.Reporting;

partial class BusinessReportService
{
    private void BuildAllCustomersReport(SystemReport data)
    {
        var endDate = data.EndDate ?? clock.GetCurrentInstant();

        var reportQuery = PrepareAllCustomers(endDate);

        queries.Add(BusinessReportTypesEnum.AllCustomers.ToString().SplitOnUpper(), reportQuery);
    }

    private IQueryable<AllCustomersReportVM> PrepareAllCustomers(Instant endDate)
    {
        var zonedEndDate = endDate.InZone(timeZone).Date;
        var query = uow.Db.Queryable<Placement>().Where(x => x.CreatedDate <= endDate).Select(data => new AllCustomersReportVM()
        {
            AccountNumber = data.SourceAccountNumber,
            CompanyName = data.Company,
            PlacementDate = data.CreatedDate.Value.InZone(timeZone).Date,
            DebtStatus = data.Status == PlacementStatus.New ? CustomerDebtStatus.Open :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus >= PlacementStatus.Settled) ||
            data.StatusChanges.Any(x => x.CreatedDate <= endDate && x.ToStatus >= PlacementStatus.Settled)
            ? CustomerDebtStatus.Closed :
            data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus < PlacementStatus.Settled) ||
            data.StatusChanges.Any(x => x.CreatedDate <= endDate && x.ToStatus < PlacementStatus.Settled)
            ? CustomerDebtStatus.Open :
            data.Status == PlacementStatus.Settled ?
            CustomerDebtStatus.Closed :
            CustomerDebtStatus.Open,

            AccountAgeInDays = (zonedEndDate - data.CreatedDate.Value.InZone(timeZone).Date).Days,

            DebtAtEscalation = data.BalanceTotal,
            TotalPaid = data.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Sum(x => x.AmountPaid),
            OutstandingBalance = data.BalanceTotal - data.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Sum(x => x.AmountPaid),

            PlanStatus = !data.Profile.Schedules.Any() ? CustomerPlanStatus.NoPlan.GetDisplayName() :
            data.Status == PlacementStatus.Settled ? CustomerPlanStatus.Finished.GetDisplayName() :
            data.Profile.Schedules.Where(x => (x.CPADate.Year < zonedEndDate.Year || (x.CPADate.Year == zonedEndDate.Year && x.CPADate.Month <= zonedEndDate.Month)) && x.PaymentStatus != SchedulePaymentStatus.NotPaid && x.PeriodStatus == SchedulePeriodStatus.PastDue).Any()
            ? CustomerPlanStatus.Broken.GetDisplayName() : CustomerPlanStatus.Active.GetDisplayName(),

            LastSuccessfulPaymentAt = data.Transactions.Any(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount) ?
            data.Transactions.Where(x => x.CreatedDate <= endDate && x.AmountPaid > 0 && x.PaymentProvider != PaymentProvider.Discount).Select(x => x.CreatedDate.Value.InZone(timeZone).Date).OrderByDescending(x => x).FirstOrDefault() : null,

            NextScheduledPaymentAt = data.Profile.Schedules.Where(x => (x.PaymentStatus & SchedulePaymentStatus.Paid) == 0).OrderBy(x => x.CPADate).Select(x => (LocalDate?)new LocalDate(x.CPADate.Year, x.CPADate.Month, x.CPADate.Day)).FirstOrDefault(),

            PlanSetupAt = data.Profile.Schedules.Any() ? data.Profile.Schedules.Where(x => (x.CPADate.Year < zonedEndDate.Year || (x.CPADate.Year == zonedEndDate.Year && x.CPADate.Month <= zonedEndDate.Month)))
            .Select(x => x.CreatedDate.Value.InZone(timeZone).Date)
            .OrderBy(x => x).FirstOrDefault() : null,

            PlanLength = data.Profile.Schedules.Count(),
            PlanPaymentFrequency = data.Profile.PaymentFrequency.ToString(),
            PlanPaymentAmount = data.Profile.Schedules.Where(x => (x.CPADate.Year < zonedEndDate.Year || (x.CPADate.Year == zonedEndDate.Year && x.CPADate.Month <= zonedEndDate.Month)))
            .OrderByDescending(x => x.Period).Select(x => x.Amount).FirstOrDefault(),

            ClosedAt = data.StatusChangeLogs.Any(x => x.CreatedDate <= endDate && x.NewStatus >= PlacementStatus.Settled) ?
            data.StatusChangeLogs.Where(x => x.CreatedDate <= endDate && x.NewStatus >= PlacementStatus.Settled).Select(x => x.CreatedDate.Value.InZone(timeZone).Date).FirstOrDefault() :
            data.Status == PlacementStatus.Settled ? data.LastModifiedDate.Value.InZone(timeZone).Date : null,

            ClosureCode = data.Status == PlacementStatus.Settled ? data.Discount > 0 ? "SET" : "PIF" : data.StatusChanges.Where(x => x.CreatedDate <= endDate && x.ToStatus >= PlacementStatus.Settled)
            .Select(x => string.IsNullOrEmpty(x.Reason.Code) ? x.Comment : x.Reason.Code).FirstOrDefault()
        });

        return query;
    }

    private static void SaveAllCustomersReport(ExcelWorksheet worksheet, IQueryable<object> reportData)
    {
        var queryData = reportData.Cast<AllCustomersReportVM>();
        worksheet.Cells["A1"].LoadFromCollection(queryData);
    }
}

﻿@using LendQube.Infrastructure.Core.Helpers.Utils
@using LendQube.Web.Admin.Components.Account.Shared
@using Radzen.Blazor
@inherits LayoutComponentBase

<AuthorizeView Policy="@ConfigConstants.MFAPolicy" Context="authContext">
    <Authorized>
        <RadzenComponents @rendermode="InteractiveAuto" />
        <div id="wrapper">
            <div id="footer-control" class="index-wrapper">
                <div class="dashboard">
                    <div class="d-main">
                        <div class="d-header flex __align-center">
                            <div class="d-header__logo">
                                <a href="#">
                                    <img src="images/logo/logo_inverse.svg" alt="Silicon" />
                                </a>
                            </div>
                            <NavMenu />

                        </div>
                        <div class="pg-container">
                            <div class="pg-layout">
                                <SideMenu />
                                <div class="layout-main">
                                    @Body
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </Authorized>
    <NotAuthorized>
        <RedirectToLogin />
    </NotAuthorized>
</AuthorizeView>

<div id="blazor-error-ui">
    An unhandled error has occurred.
    <a href="" class="reload">Reload</a>
    <a class="dismiss">🗙</a>
</div>

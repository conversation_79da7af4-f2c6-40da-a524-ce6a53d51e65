﻿@using LendQube.Infrastructure.Core.Components

<StatusMessage Message="@StatusMessage" />

<div class="form-row">
    <div class="alert warning hasFlex">
        <div class="al-flex">
            <div class="al-content">
                <span class="al__title" role="alert">
                    <p>
                        <strong>Put these codes in a safe place.</strong>
                    </p>
                    <p>
                        If you lose your device and don't have the recovery codes please contact admin
                    </p>
                </span>
            </div>
        </div>
    </div>
</div>
<div class="form-row">
    @foreach (var recoveryCode in RecoveryCodes)
    {
        <div>
            <code class="recovery-code">@recoveryCode</code>
        </div>
    }

</div>

@code {
    [Parameter]
    public string[] RecoveryCodes { get; set; } = [];

    [Parameter]
    public StatusMessageBuilder StatusMessage { get; set; }
}

using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Constants;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Core.Components;
using LendQube.Infrastructure.Core.Components.Table;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using Microsoft.EntityFrameworkCore;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace LendQube.Infrastructure.Collection.Components.CustomerProfiles.ViewProfile;

partial class ViewProfile
{
    private string AddIncomeExpenditureModal => "AddIncomeExpenditureModal";
    private string EditIncomeExpenditureModal => "EditIncomeExpenditureModal";

    private CustomerIncomeAndExpenditure AddIncomeExpenditureModel { get; set; } = new();
    private CustomerIncomeAndExpenditure EditIncomeExpenditureModel { get; set; } = new();

    private ValueTask SubmitNewIncomeExpenditure() => BaseSaveAdd(ManageCustomersNavigation.CustomerProfileViewAddIncomeExpenditurePermission, AddIncomeExpenditureModal, async () =>
    {
        // Check if income and expenditure already exists
        if (Data.IncomeAndExpenditure != null)
        {
            CustomMessage = "Income and Expenditure already exists for this customer. Please edit the existing record.";
            return false;
        }

        AddIncomeExpenditureModel.ProfileId = Data.Id;
        
        // Use data from CustomerProfile
        AddIncomeExpenditureModel.FullName = Data.FullName;
        
        // Set phone number based on selection with validation
        if (SelectedPhoneType == "mobile" && Data.MobileNumber != null)
        {
            var code = EnsurePhoneCodeHasPlus(Data.MobileNumber.Code);
            AddIncomeExpenditureModel.PhoneNumber = new PhoneNumber(code, Data.MobileNumber.Number);
        }
        else if (SelectedPhoneType == "phone" && Data.PhoneNumber != null)
        {
            var code = EnsurePhoneCodeHasPlus(Data.PhoneNumber.Code);
            AddIncomeExpenditureModel.PhoneNumber = new PhoneNumber(code, Data.PhoneNumber.Number);
        }
        else
        {
            // Fallback to any available phone
            var fallbackPhone = Data.MobileNumber ?? Data.PhoneNumber;
            if (fallbackPhone != null)
            {
                var code = EnsurePhoneCodeHasPlus(fallbackPhone.Code);
                AddIncomeExpenditureModel.PhoneNumber = new PhoneNumber(code, fallbackPhone.Number);
            }
        }
        
        // Validate and use selected address
        if (!string.IsNullOrEmpty(SelectedAddressId) && Data.Addresses != null)
        {
            var selectedAddress = Data.Addresses.FirstOrDefault(a => a.Id.ToString() == SelectedAddressId);
            if (selectedAddress != null)
            {
                AddIncomeExpenditureModel.Address = FormatAddress(selectedAddress);
            }
            else
            {
                CustomMessage = "Selected address not found. Please select a valid address.";
                return false;
            }
        }
        else if (!string.IsNullOrEmpty(AddIncomeExpenditureModel.Address))
        {
            // User entered a manual address, keep it
        }
        else
        {
            CustomMessage = "Please select or enter an address.";
            return false;
        }

        uow.Db.Insert(AddIncomeExpenditureModel);
        await uow.SaveAsync(Cancel);

        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Income & Expenditure Added", Activity = $"Income & Expenditure added for {AddIncomeExpenditureModel.FullName}" });
        await uow.SaveAsync(Cancel);

        return true;
    }, async () =>
    {
        AddIncomeExpenditureModel = new();
        SelectedAddressId = null; // Reset selection
        SelectedPhoneType = "mobile"; // Reset to default
        await LoadProfile(); // Reload profile to get updated I&E data
        StateHasChanged();
    });

    private async ValueTask StartEditIncomeExpenditure()
    {
        if (Data.IncomeAndExpenditure == null)
        {
            return;
        }

        EditIncomeExpenditureModel = new CustomerIncomeAndExpenditure
        {
            Id = Data.IncomeAndExpenditure.Id,
            ProfileId = Data.IncomeAndExpenditure.ProfileId,
            FullName = Data.FullName, // Always use current customer data
            Address = Data.IncomeAndExpenditure.Address,
            PhoneNumber = null, // Will be set below
            NetSalary = Data.IncomeAndExpenditure.NetSalary,
            BenefitsIncome = Data.IncomeAndExpenditure.BenefitsIncome,
            OtherIncome = Data.IncomeAndExpenditure.OtherIncome,
            Rent = Data.IncomeAndExpenditure.Rent,
            Utilities = Data.IncomeAndExpenditure.Utilities,
            CouncilTax = Data.IncomeAndExpenditure.CouncilTax,
            Food = Data.IncomeAndExpenditure.Food,
            Transport = Data.IncomeAndExpenditure.Transport,
            Insurance = Data.IncomeAndExpenditure.Insurance,
            Loan = Data.IncomeAndExpenditure.Loan,
            OtherExpenditure = Data.IncomeAndExpenditure.OtherExpenditure
        };
        
        // Set phone number from customer data with validation
        var phoneToUse = Data.MobileNumber ?? Data.PhoneNumber;
        if (phoneToUse != null)
        {
            var code = EnsurePhoneCodeHasPlus(phoneToUse.Code);
            EditIncomeExpenditureModel.PhoneNumber = new PhoneNumber(code, phoneToUse.Number);
        }

        StateHasChanged();
        await Task.Delay(50);
        await JSRuntime.OpenModal(EditIncomeExpenditureModal, Cancel);
    }

    private ValueTask SubmitEditIncomeExpenditure() => BaseSaveEdit(ManageCustomersNavigation.CustomerProfileViewEditIncomeExpenditurePermission, EditIncomeExpenditureModal, async () =>
    {
        // Get the existing entity to update
        var existingEntity = await uow.Db.OneAsync(Query<CustomerIncomeAndExpenditure>.Where(x => x.Id == EditIncomeExpenditureModel.Id).Track(), Cancel);
        
        if (existingEntity == null)
        {
            CustomMessage = "Income and Expenditure record not found.";
            return false;
        }

        // Update properties - DO NOT update FullName and PhoneNumber as they come from CustomerProfile
        existingEntity.FullName = Data.FullName; // Always use current customer data
        
        // Set phone number from customer data with validation
        var phoneToUse = Data.MobileNumber ?? Data.PhoneNumber;
        if (phoneToUse != null)
        {
            var code = EnsurePhoneCodeHasPlus(phoneToUse.Code);
            existingEntity.PhoneNumber = new PhoneNumber(code, phoneToUse.Number);
        }
        else
        {
            existingEntity.PhoneNumber = null;
        }
        
        // Validate address
        if (string.IsNullOrWhiteSpace(EditIncomeExpenditureModel.Address))
        {
            CustomMessage = "Address is required.";
            return false;
        }
        
        existingEntity.Address = EditIncomeExpenditureModel.Address;
        existingEntity.NetSalary = EditIncomeExpenditureModel.NetSalary;
        existingEntity.BenefitsIncome = EditIncomeExpenditureModel.BenefitsIncome;
        existingEntity.OtherIncome = EditIncomeExpenditureModel.OtherIncome;
        existingEntity.Rent = EditIncomeExpenditureModel.Rent;
        existingEntity.Utilities = EditIncomeExpenditureModel.Utilities;
        existingEntity.CouncilTax = EditIncomeExpenditureModel.CouncilTax;
        existingEntity.Food = EditIncomeExpenditureModel.Food;
        existingEntity.Transport = EditIncomeExpenditureModel.Transport;
        existingEntity.Insurance = EditIncomeExpenditureModel.Insurance;
        existingEntity.Loan = EditIncomeExpenditureModel.Loan;
        existingEntity.OtherExpenditure = EditIncomeExpenditureModel.OtherExpenditure;

        await uow.SaveAsync(Cancel);

        uow.Db.Insert(new CustomerActivity { ProfileId = Data.Id, Title = "Income & Expenditure Updated", Activity = $"Income & Expenditure updated for {EditIncomeExpenditureModel.FullName}" });
        await uow.SaveAsync(Cancel);

        return true;
    }, async () =>
    {
        EditIncomeExpenditureModel = new();
        await LoadProfile(); // Reload profile to get updated I&E data
        StateHasChanged();
    });
    
    private string FormatAddress(CustomerAddress address)
    {
        var parts = new List<string>();
        if (!string.IsNullOrWhiteSpace(address.AddressLine1)) parts.Add(address.AddressLine1.Trim());
        if (!string.IsNullOrWhiteSpace(address.AddressLine2)) parts.Add(address.AddressLine2.Trim());
        if (!string.IsNullOrWhiteSpace(address.Locality)) parts.Add(address.Locality.Trim());
        if (!string.IsNullOrWhiteSpace(address.City)) parts.Add(address.City.Trim());
        if (!string.IsNullOrWhiteSpace(address.PostCode)) parts.Add(address.PostCode.Trim());
        if (!string.IsNullOrWhiteSpace(address.Country)) parts.Add(address.Country.Trim());
        return string.Join(", ", parts);
    }
    
    // Helper method to format address for dropdown display
    public string GetAddressDisplayText(CustomerAddress address)
    {
        if (address == null) return string.Empty;
        return FormatAddress(address);
    }
    
    // Helper method to ensure phone code has + prefix
    private string EnsurePhoneCodeHasPlus(string code)
    {
        if (string.IsNullOrEmpty(code))
            return code;
            
        // If code doesn't start with +, add it
        if (!code.StartsWith("+"))
        {
            return "+" + code;
        }
        
        return code;
    }
}
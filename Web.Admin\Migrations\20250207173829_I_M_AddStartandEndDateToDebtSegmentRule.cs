﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class I_M_AddStartandEndDateToDebtSegmentRule : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {

            migrationBuilder.AddColumn<DateOnly>(
                name: "EndDate",
                schema: "collection",
                table: "DebtSegmentRule",
                type: "date",
                nullable: true);

            migrationBuilder.AddColumn<DateOnly>(
                name: "StartDate",
                schema: "collection",
                table: "DebtSegmentRule",
                type: "date",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "EndDate",
                schema: "collection",
                table: "DebtSegmentRule");

            migrationBuilder.DropColumn(
                name: "StartDate",
                schema: "collection",
                table: "DebtSegmentRule");

        }
    }
}

﻿
<button class="btn @Css" type="button" @onclick=ClickButton disabled="@busy">
    @Label
    @if(busy)
    {
        <span> &nbsp;</span>
        <i class="fa fa-spinner fa-spin"></i>
    }
</button>

@code {
    [Parameter]
    public string Css { get; set; } = "btn--primary";

    [Parameter]
    public string Label { get; set; }

    [Parameter]
    public EventCallback<MouseEventArgs> OnClick { get; set; }

    private bool busy = false;

    public async Task ClickButton(MouseEventArgs args)
    {
        if (busy)
            return;
        busy = true;
        StateHasChanged();

        await OnClick.InvokeAsync(args);

        busy = false;
    }
}

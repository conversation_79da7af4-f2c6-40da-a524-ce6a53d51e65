﻿@page "/customers/discountsetup"
@using LendQube.Entities.Collection.Setup
@using LendQube.Entities.Core.BaseUser
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@using LendQube.Infrastructure.Core.Database.Repository
@using Radzen.Blazor

@inherits GenericCrudVMTable<DiscountConfig, DiscountConfigVM>

@attribute [Authorize(Policy = ManageCustomersNavigation.DiscountSetupIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<DiscountConfig> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Role">Role</label>
            <RadzenDropDown @bind-Value=@context.RoleId Data=@roles
                            TextProperty="@nameof(ApplicationRole.Name)" ValueProperty="@nameof(ApplicationRole.Id)"
                            Name="Role" AllowClear=true Placeholder="Select role" class="form-input" />
            <ValidationMessage For="() => context.RoleId" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="PercentageLimit">Limit in %</label>
            <InputNumber @bind-Value="context.PercentageLimit" class="form-input" aria-required="true" placeholder="PercentageLimit" />
            <ValidationMessage For="() => context.PercentageLimit" class="text-danger" />
        </div>
        <div class="form-row">
            <div class="check-group">
                <label class="check-label">
                    Allow users in role override set percentage
                    <InputCheckbox class="check-input" @bind-Value="context.CanOverride" />
                    <span class="checkmark"></span>
                </label>
            </div>
        </div>
    </div>
    ;

    private List<ApplicationRole> roles = [];
    protected override void OnInitialized()
    {
        Title = "Customers";
        SubTitle = "Setup Discount Config";
        FormBaseTitle = "Discount Config";
        CreatePermission = ManageCustomersNavigation.DiscountSetupIndexPermission;
        EditPermission = ManageCustomersNavigation.DiscountSetupEditPermission;
        DeletePermission = ManageCustomersNavigation.DiscountSetupDeletePermission;
        QuerySelector = DiscountConfigVM.Mapping;
        roles = Service.CrudService.Db.Many(Query<ApplicationRole>.All());
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.PercentageLimit.ToString(), filterAndPage.TextFilter);
    }

    protected override ValueTask StartEdit(DiscountConfigVM data, CancellationToken ct) => Edit(() => Task.FromResult(data.Get()), ct);
    protected override ValueTask<bool> SubmitDelete(DiscountConfigVM data, Func<Task> refresh, CancellationToken ct) => SaveDelete(() => Service.CrudService.Delete(x => x.Id == data.Id, ct), refresh);
}
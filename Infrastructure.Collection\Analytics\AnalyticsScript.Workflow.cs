﻿using LendQube.Entities.Collection.Base;
using LendQube.Entities.Collection.Workflows.Debt;
using LendQube.Entities.Core.Base;
using LendQube.Infrastructure.Core.Database.AnalyticsTriggers;
using LendQube.Infrastructure.Core.DependencyInjection;

namespace LendQube.Infrastructure.Collection.Analytics;

static partial class AnalyticsScript
{
    private static void PrepareWorkflowAnalyticsScript()
    {
        var trigger = new AnalyticsTriggerModel
        {
            Schema = CollectionEntityConfig.DefaultSchema,
            Table = nameof(AgentWorkflowTask),
            On = [TriggerChange.Insert, TriggerChange.Update],
            Triggers = []
        };

        PrepareAgentWorkflowDashboardAnalyticsTrigger(trigger);
        PrepareAllWorkflowDashboardAnalyticsTrigger(trigger);

        AdminDI.AnalyticsTriggers.Add(trigger);
    }
}

﻿namespace LendQube.Infrastructure.Core.Navigation;

internal sealed class ProfileNavigation : INavigationDescriptor
{
    public bool IsDisabled { get; set; } = false;
    public const string GroupName = "Profile";
    public void PrepareNavigator()
    {
        var navs = new NavigatorVM
        {
            Name = GroupName,
            Icon = "settings",
            Url = "account/manage/profile",
            Controller = "Account",
            DoNotShowInMainMenu = true,
            SubNavigation =
            [
                new() { Name = "Profile", Icon = "globe", Url = "account/manage/profile", Controller = "Account" },
                new() { Name = "Password", Icon = "globe", Url = "account/manage/changepassword", Controller = "Account" },
                new() { Name = "Transaction Pin", Icon = "globe", Url = "account/manage/changepin", Controller = "Account" },
            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.SetupProfile, GroupName, navs);
    }
}

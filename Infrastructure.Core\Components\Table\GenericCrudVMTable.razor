﻿@using System.Linq.Expressions
@typeparam T where T : class, IBaseEntityForRelationalDb, new()
@typeparam TVM where TVM : class

@inherits TableComponentBase<T>

<PageTitle>@Title</PageTitle>

<div class="pg-row grid grid-col-1 grid-tab-1">
    <div class="card">

        <div class="title-wrapper flex __justify-between __align-center">
            <span class="text_xl_medium">@SubTitle</span>
        </div>
        <StatusMessage @ref="TableMessage" />

        <DataTable T="TVM" TableDefinition="TableDefinition" LoadData="Load" EditRow="StartEdit" DeleteRow="SubmitDelete"  @ref="table" />

    </div>
</div>


@code
{
    [Inject]
    protected GenericVMSpecificationService<T, TVM> Service { get; set; }

    protected Expression<Func<T, TVM>> QuerySelector { get; set; }
    protected DataTable<TVM> table;

    protected override Task OnInitializedAsync()
    {
        ArgumentNullException.ThrowIfNull(QuerySelector);
        return base.OnInitializedAsync();
    }

    protected override ColumnList GetTableDefinition() => Service.CrudService.GetTableDefinition();

    protected override void DefinePrimaryCriteria(DataFilterAndPage filterAndPage) => Service.PrimaryCriteria = null;

    private ValueTask<TypedBasePageList<TVM>> Load(DataFilterAndPage filterAndPage, CancellationToken ct)
    {
        CloseMessage();
        DefinePrimaryCriteria(filterAndPage);
        if (!string.IsNullOrEmpty(filterAndPage.TextFilter))
        {
            GeneralSearchQuery(filterAndPage);
        }
        return Service.CrudService.GetTypeBasedPagedData(Service, filterAndPage, QuerySelector, ct: ct);
    }


    protected virtual ValueTask SubmitAdd() => SaveAdd(Service.CrudService.New, table.Refresh);

    protected virtual ValueTask StartEdit(TVM data, CancellationToken ct) => throw new NotImplementedException($"Provide implementation for method {nameof(StartEdit)}");

    protected virtual ValueTask SubmitEdit() => SaveEdit(Service.CrudService.Update, table.Refresh);

    protected virtual ValueTask<bool> SubmitDelete(TVM data, Func<Task> refresh, CancellationToken ct) => throw new NotImplementedException($"Provide implementation for method {nameof(SubmitDelete)}");

}

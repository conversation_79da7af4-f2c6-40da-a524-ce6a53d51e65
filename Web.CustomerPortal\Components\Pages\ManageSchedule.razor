@page "/manage-schedule"
@using LendQube.Infrastructure.Core.Database.DbContexts
@using LendQube.Entities.Collection.Customers
@using LendQube.Entities.Collection.Placements
@using LendQube.Infrastructure.Collection.Schedules
@using LendQube.Infrastructure.Collection.Payments
@using LendQube.Infrastructure.Collection.ViewModels.PlacementData
@using LendQube.Infrastructure.Core.ViewModels.Base
@using Microsoft.EntityFrameworkCore
@using System.ComponentModel.DataAnnotations
@using LendQube.Entities.Core.Extensions
@using System.Linq
@inject AppDbContext DbContext
@inject ILogger<ManageSchedule> Logger
@inject ManageScheduleService ScheduleService
@inject NavigationManager Navigation
@rendermode InteractiveServer

<PageTitle>Manage Payment Schedule - LendQube Customer Portal</PageTitle>

@if (customer == null)
{
    <div class="loading-container">
        <div class="spinner-border" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p>Loading your schedule information...</p>
    </div>
}
else
{
    <div class="payment-container">
        <div class="payment-header">
            <h1>Manage Payment Schedule</h1>
            <p>Set up or view your payment schedule to manage your account effectively.</p>
        </div>

        @if (!string.IsNullOrEmpty(errorMessage))
        {
            <div class="alert alert-danger">
                @errorMessage
            </div>
        }

        @if (!string.IsNullOrEmpty(successMessage))
        {
            <div class="alert alert-success">
                @successMessage
            </div>
        }

        @if (hasExistingSchedules)
        {
            <div class="schedule-view">
                <h2>Your Payment Schedule</h2>
                <div class="payment-form">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th scope="col" class="text-center">Period</th>
                                    <th scope="col">Due Date</th>
                                    <th scope="col" class="text-end">Amount</th>
                                    <th scope="col" class="text-center">Status</th>
                                    <th scope="col" class="text-end">Balance After</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if (customer?.Schedules != null)
                                {
                                    @foreach (var schedule in customer.Schedules.OrderBy(s => s.Period))
                                    {
                                        <tr class="@(schedule.PaymentStatus == SchedulePaymentStatus.Paid ? "table-success" :
                                                   schedule.PaymentStatus == SchedulePaymentStatus.PartiallyPaid ? "table-warning" : "")">
                                            <td class="text-center fw-bold">@schedule.Period</td>
                                            <td class="fw-medium">@schedule.DueDate.ToString("dd MMM yyyy")</td>
                                            <td class="text-end fw-bold text-primary">£@schedule.Amount.ToString("N2")</td>
                                            <td class="text-center">
                                                <span class="badge @(GetStatusBadgeClass(schedule.PaymentStatus)) px-3 py-2">
                                                    @GetStatusText(schedule.PaymentStatus)
                                                </span>
                                            </td>
                                            <td class="text-end fw-medium">£@schedule.Balance.ToString("N2")</td>
                                        </tr>
                                    }
                                }
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="schedule-summary">
                    <h3>Schedule Summary</h3>
                    <p>Total: @(customer?.Schedules?.Count() ?? 0)</p>
                    <p>Paid: @(customer?.Schedules?.Count(s => s.PaymentStatus == SchedulePaymentStatus.Paid) ?? 0)</p>
                    <p>Pending: @(customer?.Schedules?.Count(s => s.PaymentStatus == SchedulePaymentStatus.NotPaid) ?? 0)</p>
                    <p>Balance: £@(customer?.BalanceRemaining.ToString("N2") ?? "0.00")</p>
                    
                    <div class="action-buttons">
                        <a href="/make-payment?customerId=@CustomerId" class="btn btn-primary make-payment-btn">
                            <span>Make Payment</span>
                        </a>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="create-schedule-section">
                <h2>Create Payment Schedule</h2>
                <p>Set up automated payments to manage your account balance effectively.</p>

                <EditForm Model="@scheduleModel" OnValidSubmit="@CreateSchedule" FormName="CreateScheduleForm">
                    <DataAnnotationsValidator />

                    <div class="form-group">
                        <label for="frequency">Payment Frequency</label>
                        <select @bind="scheduleModel.Frequency" class="form-control" id="frequency" disabled="@isProcessing">
                            <option value="">Select Schedule Frequency</option>
                            @foreach (var freq in EnumExtensions.GetEnumValues<SchedulePaymentFrequency>())
                            {
                                <option value="@freq.Value">@freq.Name</option>
                            }
                        </select>
                        <ValidationMessage For="@(() => scheduleModel.Frequency)" class="text-danger" />
                    </div>

                    <div class="form-group">
                        <label for="startDate">Start Date</label>
                        <input type="date" @bind="scheduleModel.StartDate" class="form-control" id="startDate" disabled="@isProcessing" />
                        <ValidationMessage For="@(() => scheduleModel.StartDate)" class="text-danger" />
                    </div>

                    <div class="form-group">
                        <label for="scheduleAmount">Payment Amount (£)</label>
                        <input type="number" @bind="scheduleModel.Amount" 
                               class="form-control" 
                               id="scheduleAmount" 
                               placeholder="0.00"
                               step="0.01"
                               disabled="@isProcessing" />
                        <ValidationMessage For="@(() => scheduleModel.Amount)" class="text-danger" />
                        <small class="form-text">Maximum: £@(customer?.BalanceRemaining.ToString("N2") ?? "0.00")</small>
                    </div>

                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary create-schedule-btn" disabled="@isProcessing">
                            @if (isProcessing)
                            {
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                <span>Creating Schedule...</span>
                            }
                            else
                            {
                                <span>Create Schedule</span>
                            }
                        </button>
                        
                        <a href="/make-payment?customerId=@CustomerId" class="btn btn-secondary">
                            Make One-Time Payment Instead
                        </a>
                    </div>
                </EditForm>


            </div>
        }

        <div class="back-to-dashboard">
            <a href="/dashboard?customerId=@CustomerId" class="btn btn-outline-secondary">
                ← Back to Dashboard
            </a>
        </div>
    </div>
}

@code {
    [Parameter]
    [SupplyParameterFromQuery]
    public string? CustomerId { get; set; }

    [SupplyParameterFromForm]
    public RequestScheduleVM scheduleModel { get; set; } = new();

    private CustomerProfile? customer;
    private bool isProcessing = false;
    private string errorMessage = string.Empty;
    private string successMessage = string.Empty;
    private bool hasExistingSchedules = false;

    protected override async Task OnInitializedAsync()
    {
        if (string.IsNullOrEmpty(CustomerId))
        {
            errorMessage = "Customer ID is required. Please log in again.";
            return;
        }

        Logger.LogInformation("PHASE 2: Starting database test for Customer: {CustomerId}", CustomerId);

        // Set default start date to next week
        scheduleModel.StartDate = DateTime.Now.AddDays(7).Date;

        // PHASE 2: Test database loading step by step
        await LoadCustomerData();
    }

    private async Task LoadCustomerData()
    {
        try
        {
            Logger.LogInformation("PHASE 3: Loading customer data WITH schedules for ID: {CustomerId}", CustomerId);

            // PHASE 3: Load customer with schedules to check if they exist
            customer = await DbContext.Set<CustomerProfile>()
                .Include(c => c.Schedules)
                .Where(c => c.AccountId == CustomerId)
                .FirstOrDefaultAsync();

            if (customer != null)
            {
                var scheduleCount = customer.Schedules?.Count() ?? 0;
                hasExistingSchedules = scheduleCount > 0;

                Logger.LogInformation("PHASE 3: Customer loaded successfully - ID: {CustomerId}, Balance: {Balance}, Schedules: {ScheduleCount}, HasSchedules: {HasSchedules}",
                    CustomerId, customer.BalanceRemaining, scheduleCount, hasExistingSchedules);

                if (hasExistingSchedules)
                {
                    Logger.LogInformation("PHASE 3: Customer has {ScheduleCount} existing schedules - showing schedule view", scheduleCount);
                }
                else
                {
                    Logger.LogInformation("PHASE 3: Customer has no schedules - showing create form");
                }
            }
            else
            {
                errorMessage = "Customer not found. Please log in again.";
                Logger.LogWarning("PHASE 3: Customer not found for ID: {CustomerId}", CustomerId);
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"PHASE 3: Database error - {ex.Message}";
            Logger.LogError(ex, "PHASE 3: Error loading customer data for ID: {CustomerId}", CustomerId);
        }
    }

    // PHASE 4: Test actual schedule creation with database
    private async Task CreateSchedule()
    {
        try
        {
            isProcessing = true;
            errorMessage = string.Empty;
            successMessage = string.Empty;
            StateHasChanged();

            Logger.LogInformation("PHASE 4: Creating schedule for Customer: {CustomerId}, Amount: {Amount}, Frequency: {Frequency}",
                CustomerId, scheduleModel.Amount, scheduleModel.Frequency);

            // PHASE 4: Call the actual schedule service
            var result = await CallNewScheduleMethod(customer!.Id.ToString(), scheduleModel);

            if (result.IsSuccessful)
            {
                Logger.LogInformation("PHASE 4: Schedule created successfully for Customer: {CustomerId}", CustomerId);

                successMessage = $"Payment schedule created successfully! First payment of £{scheduleModel.Amount:N2} has been initiated.";

                // Extract payment link from the transaction result
                var paymentLink = result.Data?.Txn?.ProviderId;
                if (!string.IsNullOrEmpty(paymentLink))
                {
                    Logger.LogInformation("PHASE 4: Redirecting to payment provider: {PaymentLink}", paymentLink);

                    // Redirect to external payment provider (clean URL, no extra parameters)
                    Navigation.NavigateTo(paymentLink, forceLoad: true);
                }
                else
                {
                    // Reload customer data to show new schedule
                    await LoadCustomerData();
                    StateHasChanged();
                }
            }
            else
            {
                errorMessage = result.Message ?? "Schedule creation failed. Please try again.";
                Logger.LogWarning("PHASE 4: Schedule creation failed for Customer: {CustomerId}. Error: {Error}", CustomerId, result.Message);
            }
        }
        catch (Exception ex)
        {
            errorMessage = $"PHASE 4: Schedule creation failed: {ex.Message}";
            Logger.LogError(ex, "PHASE 4: Error creating schedule for Customer: {CustomerId}", CustomerId);
        }
        finally
        {
            isProcessing = false;
            StateHasChanged();
        }
    }

    private async Task<Result<ScheduleResponseVM>> CallNewScheduleMethod(string customerId, RequestScheduleVM request)
    {
        try
        {
            Logger.LogInformation("PHASE 4: Calling NewSchedule service for customer: {CustomerId}, Amount: {Amount}, Frequency: {Frequency}",
                customerId, request.Amount, request.Frequency);

            // Use reflection to call the internal NewSchedule method (same as web admin)
            var method = typeof(ManageScheduleService).GetMethod("NewSchedule",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method != null)
            {
                var task = (Task<Result<ScheduleResponseVM>>)method.Invoke(ScheduleService, new object[] { customerId, request, CancellationToken.None })!;
                var result = await task;

                Logger.LogInformation("PHASE 4: Schedule service result for customer {CustomerId}: {IsSuccessful}",
                    customerId, result.IsSuccessful);

                return result;
            }

            Logger.LogError("PHASE 4: NewSchedule method not found on ManageScheduleService");
            return Result<ScheduleResponseVM>.Failed("Schedule service not available");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "PHASE 4: Error calling schedule service for customer: {CustomerId}", customerId);
            return Result<ScheduleResponseVM>.Failed($"Schedule creation failed: {ex.Message}");
        }
    }

    private string GetStatusText(SchedulePaymentStatus status)
    {
        return status switch
        {
            SchedulePaymentStatus.Paid => "Paid",
            SchedulePaymentStatus.PartiallyPaid => "Partially Paid",
            SchedulePaymentStatus.NotPaid => "Pending",
            SchedulePaymentStatus.Cancelled => "Cancelled",
            _ => "Unknown"
        };
    }

    private string GetStatusBadgeClass(SchedulePaymentStatus status)
    {
        return status switch
        {
            SchedulePaymentStatus.Paid => "bg-success",
            SchedulePaymentStatus.PartiallyPaid => "bg-warning text-dark",
            SchedulePaymentStatus.NotPaid => "bg-secondary",
            SchedulePaymentStatus.Cancelled => "bg-danger",
            _ => "bg-secondary"
        };
    }
}

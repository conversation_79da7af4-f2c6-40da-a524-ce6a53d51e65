﻿using System.ComponentModel;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;

namespace LendQube.Infrastructure.Collection.ViewModels.PlacementData;

public class PlacementVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<Placement, PlacementVM>> Mapping = data => new PlacementVM
    {
        Id = data.Id,
        ProfileCurrency = data.Profile.CurrencyCode,
        ProfileAccountId = data.Profile.AccountId,
        ProfileId = data.Profile.Id,
        ProfileEmail = data.Profile.Email,
        ProfileFirstName = data.Profile.FirstName,
        ProfileLastName = data.Profile.LastName,
        MobileNumber = data.Profile.MobileNumber.Code + " " + data.Profile.MobileNumber.Number,
        SourceAccountNumber = data.SourceAccountNumber,
        Company = data.Company,
        Status = data.Status,
        AgreementDate = data.AgreementDate,
        DefaultDate = data.DefaultDate,
        BalancePrincipal = data.BalancePrincipal,
        BalanceInterest = data.BalanceInterest,
        BalanceFees = data.BalanceFees,
        BalanceTotal = data.BalanceTotal,
        BalancePaid = data.BalancePaid,
        CreatedDate = data.CreatedDate,
        LastModifiedDate = data.LastModifiedDate,
    };

    [RemoveColumn]
    public string ProfileCurrency { get; set; }
    [RemoveColumn]
    public string ProfileId { get; set; }
    [DisplayName("Account Id"), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ProfileAccountId { get; set; }
    [DisplayName("Email"), TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.HideColumn)]
    public string ProfileEmail { get; set; }
    [DisplayName("First Name"), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ProfileFirstName { get; set; }
    [DisplayName("Last Name"), TableDecorator(TableDecoratorType.ShowInDelete)]
    public string ProfileLastName { get; set; }
    public string MobileNumber { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string SourceAccountNumber { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public string Company { get; set; }
    [TableDecorator(TableDecoratorType.ShowInDelete)]
    public PlacementStatus Status { get; set; }
    public DateOnly? AgreementDate { get; set; }
    public DateOnly? DefaultDate { get; set; }
    public decimal BalancePrincipal { get; set; }
    public decimal BalanceInterest { get; set; }
    public decimal BalanceFees { get; set; }
    public decimal BalanceTotal { get; set; }
    public decimal BalancePaid { get; set; }
    public decimal BalanceRemaining => BalanceTotal - BalancePaid;
}


public class PlacementInProfileVM : IBaseEntityWithNumberId
{
    public static readonly Expression<Func<Placement, PlacementInProfileVM>> Mapping = data => new PlacementInProfileVM
    {
        Id = data.Id,
        ProfileCurrency = data.Profile.CurrencyCode,
        SourceAccountNumber = data.SourceAccountNumber,
        Company = data.Company,
        Status = data.Status,
        AgreementDate = data.AgreementDate,
        DefaultDate = data.DefaultDate,
        BalancePrincipal = data.BalancePrincipal,
        BalanceInterest = data.BalanceInterest,
        BalanceFees = data.BalanceFees,
        BalanceTotal = data.BalanceTotal,
        BalancePaid = data.BalancePaid,
        CreatedDate = data.CreatedDate,
        LastModifiedDate = data.LastModifiedDate,
    };

    [RemoveColumn]
    public string ProfileCurrency { get; set; }
    public string SourceAccountNumber { get; set; }
    public string Company { get; set; }
    public PlacementStatus Status { get; set; }
    public DateOnly? AgreementDate { get; set; }
    public DateOnly? DefaultDate { get; set; }
    public decimal BalancePrincipal { get; set; }
    public decimal BalanceInterest { get; set; }
    public decimal BalanceFees { get; set; }
    public decimal BalanceTotal { get; set; }
    public decimal BalancePaid { get; set; }
    public decimal BalanceRemaining => BalanceTotal - BalancePaid;
}

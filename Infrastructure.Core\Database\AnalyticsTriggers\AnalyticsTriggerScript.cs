﻿using System.Text;
using LendQube.Infrastructure.Core.DependencyInjection;
using LendQube.Infrastructure.Core.Extensions;

namespace LendQube.Infrastructure.Core.Database.AnalyticsTriggers;

internal static class AnalyticsTriggerScript
{
    internal static string CreateTriggers()
    {
        var scripts = new StringBuilder();
        foreach (var item in AdminDI.AnalyticsTriggers)
        {
            scripts.AppendLine(CreateTriggerScript(item));
            scripts.AppendLine(CreateAfterTrigger(item));
        }

        return scripts.ToString();
    }

    private static string CreateTriggerScript(AnalyticsTriggerModel model)
    {
        var modelSripts = GetModelScripts(model);
        return $@"
        CREATE SCHEMA IF NOT EXISTS ""{model.Schema}"";
        CREATE OR REPLACE FUNCTION ""{model.Schema}"".""{model.TriggerFunctionName}""()
        RETURNS TRIGGER AS $$
        BEGIN
            {modelSripts}
          RETURN NEW;
        END;
        $$ LANGUAGE plpgsql;";
    }

    private static string GetModelScripts(AnalyticsTriggerModel model)
    {
        var scripts = new StringBuilder();

        foreach (var item in model.Triggers)
        {
            scripts.Append("BEGIN");
            scripts.Append(CreateConditionScript(item));
            scripts.Append(CreateUpdateScript(item));
            scripts.Append(CreateInsertScript(item));
            scripts.Append(CreateNoUpdateInsertScript(item));
            scripts.Append(CreateCloseConditionScript(item));
            scripts.AppendLine("END;");
        }

        return scripts.ToString();
    }

    private static string CreateConditionScript(AnalyticsTriggerModelScript model)
    {
        if (string.IsNullOrEmpty(model.Condition))
            return string.Empty;
        return
        $@"
        IF {model.Condition} THEN
        ";
    }

    private static string CreateCloseConditionScript(AnalyticsTriggerModelScript model)
    {
        if (string.IsNullOrEmpty(model.Condition))
            return string.Empty;
        return
        $@"
        END IF;
        ";
    }

    private static string CreateUpdateScript(AnalyticsTriggerModelScript model) => model.UpdateScript.IsNullOrEmpty() || string.IsNullOrEmpty(model.UpdateScriptWhere) ? string.Empty :
        $@"
            UPDATE ""{model.TargetSchema}"".""{model.TargetTable}""
            SET 
              {GetSetScript(model)}
            WHERE {model.UpdateScriptWhere};
        ";

    private static string GetSetScript(AnalyticsTriggerModelScript model)
    {
        var scripts = new StringBuilder();
        foreach (var item in model.UpdateScript)
        {
            if (scripts.Length > 0)
                scripts.AppendLine(", ");

            scripts.Append($@" ""{item.LeftExpression}"" = {item.RightExpression}");
        }

        return scripts.ToString();
    }

    private static string CreateInsertScript(AnalyticsTriggerModelScript model) => model.UpdateScript.IsNullOrEmpty() || string.IsNullOrEmpty(model.UpdateScriptWhere) || model.InsertScript == null ? string.Empty :
       $@"IF NOT FOUND THEN
              INSERT INTO ""{model.TargetSchema}"".""{model.TargetTable}"" ({string.Join(", ", model.InsertScript.TargetColumnNames.Select(x => $@"""{x}"""))})
              VALUES ({string.Join(", ", model.InsertScript.SourceValues.Select(x => x.Type == InsertValueType.Column ? $@"NEW.{x.Value}" : x.Value))});
            END IF;
        ";

    private static string CreateNoUpdateInsertScript(AnalyticsTriggerModelScript model) => !model.UpdateScript.IsNullOrEmpty() || model.InsertScript == null ? string.Empty :
       $@"
              INSERT INTO ""{model.TargetSchema}"".""{model.TargetTable}"" ({string.Join(", ", model.InsertScript.TargetColumnNames.Select(x => $@"""{x}"""))})
              VALUES ({string.Join(", ", model.InsertScript.SourceValues.Select(x => x.Type == InsertValueType.Column ? $@"NEW.{x.Value}" : x.Value))});
        ";

    private static string CreateAfterTrigger(AnalyticsTriggerModel model) =>
        $@"
            DROP TRIGGER IF EXISTS ""{model.TriggerName}"" ON ""{model.Schema}"".""{model.Table}""; 
            CREATE TRIGGER ""{model.TriggerName}""
                AFTER {model.NotifyType} 
                ON ""{model.Schema}"".""{model.Table}""
                FOR EACH ROW
            EXECUTE FUNCTION ""{model.Schema}"".""{model.TriggerFunctionName}""();";
}



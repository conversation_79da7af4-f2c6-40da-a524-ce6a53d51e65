﻿@page "/account/manage/changepin"
@using LendQube.Infrastructure.Core.AdminUserManagement
@using LendQube.Infrastructure.Core.AdminUserManagement.ViewModels
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inject AdminUserManagerService service

@inherits SingleFormComponentBase<ChangeTransactionPinVM>

<FormComponent Model="Model" Title="Change Your Transaction Pin" FormName="@FormName" FormMessage="Message" OnValidSubmit="SubmitEdit" >
    <BodyContent>
        <div class="form-row">
            <label class="form-label" for="OldTransactionPin">Old Transaction Pin</label>
            <InputText type="password" @bind-Value="context.OldTransactionPin" class="form-input" aria-required="true" placeholder="Old Transaction Pin" />
            <ValidationMessage For="() => context.OldTransactionPin" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="NewTransactionPin">New Transaction Pin</label>
            <InputText type="password" @bind-Value="context.NewTransactionPin" class="form-input" aria-required="true" placeholder="New Transaction Pin" />
            <ValidationMessage For="() => context.NewTransactionPin" class="text-danger" />
        </div>
    </BodyContent>
</FormComponent>

@code
{
    public Task SubmitEdit() => Submit(() => service.ChangeTransactionPin(UserName, Model));
}


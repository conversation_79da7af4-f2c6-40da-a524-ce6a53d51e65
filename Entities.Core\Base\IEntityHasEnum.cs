﻿using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace LendQube.Entities.Core.Base;

public interface IEntityHasEnum //use to configure named enums for postgre
{
    abstract void RegisterEnumInDataSource(NpgsqlDataSourceBuilder builder, INpgsqlNameTranslator nameTranslator);
    abstract void RegisterEnumInDataSource(NpgsqlDbContextOptionsBuilder builder, INpgsqlNameTranslator nameTranslator);
}

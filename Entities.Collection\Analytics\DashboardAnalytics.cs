﻿using LendQube.Entities.Core.Base;

namespace LendQube.Entities.Collection.Analytics;

public class DashboardAnalytics : BaseEntityWithIdentityId<DashboardAnalytics>
{
    public int TotalCustomers { get; set; }
    public int TotalSignedInCustomers { get; set; }
    public int TotalCustomersWithSchedules { get; set; }
    public int TotalPlacements { get; set; }
    public decimal TotalPlacementValue { get; set; }
    public decimal TotalPlacementValuePaid { get; set; }
    public int TotalClosedPlacements { get; set; }
    public decimal TotalClosedPlacementValue { get; set; }
    public int TotalSettledPlacements { get; set; }
}
﻿using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Reporting;
using LendQube.Infrastructure.Collection.Navigation;
using LendQube.Infrastructure.Collection.Reporting.ViewModel;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Reporting;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.Reporting;

public sealed partial class BusinessReportService(IUnitofWork uow, IClock clock) : IReportDescribesExcelFormat
{
    public string UPLOADKEY => "BusinessReports";

    public static readonly string ReportTypeName = "Portfolio";
    public static readonly HashSet<string> ReportTypes = [.. EnumExtensions.EnumToDescriptiveStringList<BusinessReportTypesEnum>()];
    public static readonly string Permission = ReportingNavigation.RunBusinessReportsIndexPermission;

    private readonly Dictionary<string, IQueryable<object>> queries = [];
    private readonly DateTimeZone timeZone = InstantExtensions.LONDON_TIMEZONE;

    public Dictionary<string, IQueryable<object>> Build(SystemReport data)
    {
        foreach (var report in data.Names)
        {
            var type = report.ToEnum<BusinessReportTypesEnum>();
            switch (type)
            {
                case BusinessReportTypesEnum.PortfolioWeekly:
                    BuildPortfolioWeeklyReport(data);
                    break;
                case BusinessReportTypesEnum.AgedDebt:
                    BuildAgedDebtReport(data);
                    break;
                case BusinessReportTypesEnum.Placement:
                    BuildPlacementReport(data);
                    break;
                case BusinessReportTypesEnum.AllCustomers:
                    BuildAllCustomersReport(data);
                    break;
                case BusinessReportTypesEnum.AllTransactions:
                    BuildAllTransactionsReport(data);
                    break;
                case BusinessReportTypesEnum.AllMessages:
                    BuildAllMessagesReport(data);
                    break;
                default:
                    break;
            }
        }

        return queries;
    }

    public async Task SaveReportAsCustomExcelOrCsv(ExcelPackage package, SystemReport data, Dictionary<string, IQueryable<object>> reportData, CancellationToken ct)
    {
        foreach (var report in reportData)
        {
            var worksheet = package.Workbook.Worksheets.Add(report.Key);
            var type = report.Key.ToEnum<BusinessReportTypesEnum>();
            switch (type)
            {
                case BusinessReportTypesEnum.PortfolioWeekly:
                    await SavePortfolioWeeklyReport(worksheet, report.Value, ct);
                    break;
                case BusinessReportTypesEnum.AgedDebt:
                    SaveAgedDebtReport(worksheet, report.Value);
                    break;
                case BusinessReportTypesEnum.Placement:
                    SavePlacementReport(worksheet, report.Value);
                    break;
                case BusinessReportTypesEnum.AllCustomers:
                    SaveAllCustomersReport(worksheet, report.Value);
                    break;
                case BusinessReportTypesEnum.AllTransactions:
                    SaveAllTransactionsReport(worksheet, report.Value);
                    break;
                case BusinessReportTypesEnum.AllMessages:
                    await SaveAllMessagesReport(worksheet, report.Value, ct);
                    break;
                default:
                    break;
            }
            worksheet.Cells[worksheet.Dimension.Address].AutoFitColumns();
        }
    }

}

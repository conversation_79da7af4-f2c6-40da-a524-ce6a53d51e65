﻿using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.BaseUser;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace LendQube.Entities.Collection.Workflows.Debt;

public class AgentToDebtSegmentMapping : BaseEntityWithIdentityId<AgentToDebtSegmentMapping>
{
    public Guid UserId { get; set; }
    public virtual ApplicationUser User { get; set; }
    public long DebtSegmentId { get; set; }
    public virtual DebtSegment DebtSegment { get; set; }
    public int RecordsPerTime { get; set; }
    public bool Enabled { get; set; }

    public override void Configure(EntityTypeBuilder<AgentToDebtSegmentMapping> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => new { x.UserId, x.DebtSegmentId }).IsUnique();
    }
}

using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using NodaTime;

namespace LendQube.Web.CustomerPortal.ViewModels;

public class CustomerPaymentHistoryVM
{
    public static readonly Expression<Func<Transaction, CustomerPaymentHistoryVM>> Mapping = data => new()
    {
        Id = data.Id,
        ProviderReference = data.ProviderReference ?? string.Empty,
        Purpose = data.Purpose ?? string.Empty,
        CurrencySymbol = data.CurrencySymbol ?? "£",
        Amount = data.Amount,
        TotalAmountPaid = data.TotalAmountPaid,
        Fee = data.Fee,
        Provider = data.Provider,
        Status = data.Status,
        CreatedDate = data.CreatedDate,
        LastModifiedDate = data.LastModifiedDate,
    };

    public string Id { get; set; } = string.Empty;
    public string ProviderReference { get; set; } = string.Empty;
    public string Purpose { get; set; } = string.Empty;
    public string CurrencySymbol { get; set; } = "£";
    public decimal Amount { get; set; }
    public decimal TotalAmountPaid { get; set; }
    public decimal Fee { get; set; }
    public PaymentProvider Provider { get; set; }
    public TransactionStatus Status { get; set; }
    public Instant? CreatedDate { get; set; }
    public Instant? LastModifiedDate { get; set; }

    // Helper properties for display
    public string FormattedAmount => $"{CurrencySymbol}{TotalAmountPaid:N2}";
    public string FormattedDate => CreatedDate?.ToDateTimeUtc().ToString("dd MMM yyyy") ?? "N/A";
    public string FormattedTime => CreatedDate?.ToDateTimeUtc().ToString("HH:mm") ?? "N/A";
    public string StatusText => Status switch
    {
        TransactionStatus.Completed => "Completed",
        TransactionStatus.Successful => "Successful",
        TransactionStatus.Failed => "Failed",
        TransactionStatus.Processing => "Processing",
        TransactionStatus.Validated => "Validated",
        TransactionStatus.Queued => "Queued",
        TransactionStatus.Initiated => "Initiated",
        TransactionStatus.Refunded => "Refunded",
        TransactionStatus.PendingRefund => "Pending Refund",
        _ => Status.ToString()
    };
    
    public string StatusBadgeClass => Status switch
    {
        TransactionStatus.Completed or TransactionStatus.Successful => "bg-success",
        TransactionStatus.Failed => "bg-danger",
        TransactionStatus.Processing or TransactionStatus.Validated or TransactionStatus.Queued => "bg-warning",
        TransactionStatus.Initiated => "bg-info",
        TransactionStatus.Refunded or TransactionStatus.PendingRefund => "bg-secondary",
        _ => "bg-light"
    };
}

﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<RootNamespace>LendQube.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.5" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.5">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Infrastructure.Collection\Infrastructure.Collection.csproj" />
		<ProjectReference Include="..\Infrastructure.Core\Infrastructure.Core.csproj" />
	</ItemGroup>

</Project>

﻿<Project Sdk="Microsoft.NET.Sdk.Web">

	<PropertyGroup>
		<TargetFramework>net9.0</TargetFramework>
		<Nullable>disable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
		<RootNamespace>LendQube.$(MSBuildProjectName.Replace(" ", "_"))</RootNamespace>
	</PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.6" />
		<PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
			<PrivateAssets>all</PrivateAssets>
			<IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
		</PackageReference>
	</ItemGroup>

	<ItemGroup>
		<ProjectReference Include="..\Infrastructure.Collection\Infrastructure.Collection.csproj" />
		<ProjectReference Include="..\Infrastructure.Core\Infrastructure.Core.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Content Include="..\Infrastructure.ExternalApi\AppSettings\externalapisettings.json">
			<Link>AppSettings\externalapisettings.json</Link>
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Infrastructure.ExternalApi\AppSettings\externalapisettings.Development.json">
			<Link>AppSettings\externalapisettings.Development.json</Link>
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
		<Content Include="..\Infrastructure.ExternalApi\AppSettings\externalapisettings.Production.json">
			<Link>AppSettings\externalapisettings.Production.json</Link>
			<CopyToOutputDirectory>Always</CopyToOutputDirectory>
		</Content>
	</ItemGroup>

</Project>

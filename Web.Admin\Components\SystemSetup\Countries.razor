﻿@page "/countries"

@using LendQube.Entities.Core.Location
@using LendQube.Infrastructure.Core.Components.FormsAndModals
@inherits GenericCrudTable<Country>

@attribute [Authorize(Policy = SetupNavigation.CountryIndexPermission)]

@{
    base.BuildRenderTree(__builder);
}

<ModalEditComponent Policy="@CreatePermission" ModalId="@AddModalName" Title=@($"New {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@AddModel" OnValidSubmit="@SubmitAdd"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

<ModalEditComponent Policy="@EditPermission" ModalId="@EditModalName" Title=@($"Modify {FormBaseTitle}") ModalMessage="@ModalMessage" Model="@EditModel" OnValidSubmit="@SubmitEdit"
                    ModalCss="width-md">
    <BodyContent>
        @FormContent(context)
    </BodyContent>
</ModalEditComponent>

@code
{
    private RenderFragment<Country> FormContent => context =>@<div>
        <div class="form-row">
            <label class="form-label" for="Code">Code</label>
            <InputText @bind-Value="context.Code" class="form-input" aria-required="true" placeholder="Code" />
            <ValidationMessage For="() => context.Code" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Name">Name</label>
            <InputText @bind-Value="context.Name" class="form-input" aria-required="true" placeholder="Name" />
            <ValidationMessage For="() => context.Name" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Name">Phone Code</label>
            <InputText @bind-Value="context.PhoneCode" class="form-input" aria-required="true" placeholder="Phone Code" />
            <ValidationMessage For="() => context.PhoneCode" class="text-danger" />
        </div>
        <div class="form-row">
            <label class="form-label" for="Name">Image Url</label>
            <InputText @bind-Value="context.ImageUrl" class="form-input" aria-required="true" placeholder="Image Url" />
            <ValidationMessage For="() => context.ImageUrl" class="text-danger" />
        </div>
    </div>;

    protected override void OnInitialized()
    {
        Title = "Countries";
        SubTitle = "Operative Countries";
        FormBaseTitle = "Country";
        CreatePermission = SetupNavigation.CountryCreatePermission;
        EditPermission = SetupNavigation.CountryEditPermission;
        DeletePermission = SetupNavigation.CountryDeletePermission;
    }

    protected override void GeneralSearchQuery(DataFilterAndPage filterAndPage)
    {
        filterAndPage.TextFilter = $"%{filterAndPage.TextFilter}%";
        Service.PrimaryCriteria = x => EF.Functions.ILike(x.Code, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.Name, filterAndPage.TextFilter)
        || EF.Functions.ILike(x.PhoneCode, filterAndPage.TextFilter);
    }
}

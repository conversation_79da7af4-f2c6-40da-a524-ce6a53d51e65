﻿using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Logs;
using LendQube.Entities.Core.Messaging;
using Microsoft.EntityFrameworkCore.Migrations;
using NodaTime;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace LendQube.Web.Admin.Migrations
{
    /// <inheritdoc />
    public partial class Initial_Create_AppDb : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.EnsureSchema(
                name: "public");

            migrationBuilder.EnsureSchema(
                name: "core");

            migrationBuilder.EnsureSchema(
                name: "collection");

            migrationBuilder.AlterDatabase()
                .Annotation("Npgsql:Enum:collection.CollectionUploadAction", "Analyze,Import,AnalyzeAndImport")
                .Annotation("Npgsql:Enum:collection.CollectionUploadStatus", "Queued,Processing,Analyzed,DoneWithErrors,Imported")
                .Annotation("Npgsql:Enum:collection.CollectionUploadType", "Placement,Transaction,Arrangement,Closure")
                .Annotation("Npgsql:Enum:collection.PaymentMethodStatus", "Active,Suspended,FailedLastCharge,Disabled")
                .Annotation("Npgsql:Enum:collection.PaymentProvider", "Stripe")
                .Annotation("Npgsql:Enum:collection.PaymentType", "SetupCard,Card")
                .Annotation("Npgsql:Enum:collection.SchedulePaymentFrequency", "Weekly,Monthly")
                .Annotation("Npgsql:Enum:collection.TransactionStatus", "Initiated,Validated,Queued,Processing,Successful,Completed,Failed,Refunded,PendingRefund")
                .Annotation("Npgsql:Enum:core.AccessStatus", "Granted,Failed")
                .Annotation("Npgsql:Enum:core.BackgroundControlState", "Running,Idle,Stopping,Stopped")
                .Annotation("Npgsql:Enum:core.BackgroundEventSource", "Queued,Running,Success,Failed")
                .Annotation("Npgsql:Enum:core.CustomerDeviceType", "Web,Android,iOS")
                .Annotation("Npgsql:Enum:core.GrantType", "Password,NewLogin,Login2FA,LoginRecoveryCode,LoginClientId,RefreshToken,Complete2FASetup,AdminReset2FA,ChangePassword,ChangePin,ResetPassword,ResetPasswordConfirm,RequestRegistrationToken,ValidateRegistrationToken,RequestDeviceLoginToken,ValidateDeviceLoginToken")
                .Annotation("Npgsql:Enum:core.MessageStatus", "WaitingForConfiguration,Queued,Processing,Failed,SentPartially,Sent,Delivered,Opened");

            migrationBuilder.CreateSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "collection",
                incrementBy: 100);

            migrationBuilder.CreateSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "core",
                incrementBy: 100);

            migrationBuilder.CreateTable(
                name: "AspNetClaims",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Source = table.Column<string>(type: "character varying(60)", maxLength: 60, nullable: false),
                    Type = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    Value = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetClaims", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoles",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoles", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUsers",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    FirstName = table.Column<string>(type: "text", nullable: true),
                    LastName = table.Column<string>(type: "text", nullable: true),
                    OtherNames = table.Column<string>(type: "text", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true, computedColumnSql: "\"FirstName\"  || ' ' || \"LastName\"", stored: true),
                    LastLoginDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    RegistrationDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    MustChangePassword = table.Column<bool>(type: "boolean", nullable: false),
                    Role = table.Column<string>(type: "character varying(15)", maxLength: 15, nullable: true),
                    WalletPinHash = table.Column<string>(type: "text", nullable: true),
                    PhoneCode = table.Column<string>(type: "text", nullable: true),
                    UserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedUserName = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    NormalizedEmail = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    EmailConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    PasswordHash = table.Column<string>(type: "text", nullable: true),
                    SecurityStamp = table.Column<string>(type: "text", nullable: true),
                    ConcurrencyStamp = table.Column<string>(type: "text", nullable: true),
                    PhoneNumber = table.Column<string>(type: "text", nullable: true),
                    PhoneNumberConfirmed = table.Column<bool>(type: "boolean", nullable: false),
                    TwoFactorEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    LockoutEnd = table.Column<DateTimeOffset>(type: "timestamp with time zone", nullable: true),
                    LockoutEnabled = table.Column<bool>(type: "boolean", nullable: false),
                    AccessFailedCount = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUsers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BackgroundTaskEventControl",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Source = table.Column<int>(type: "integer", nullable: false),
                    Event = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<BackgroundControlState>(type: "core.\"BackgroundControlState\"", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BackgroundTaskEventControl", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "BackgroundTaskEventLog",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    Source = table.Column<int>(type: "integer", nullable: false),
                    Event = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<BackgroundEventStatus>(type: "core.\"BackgroundEventSource\"", nullable: false),
                    Data = table.Column<string>(type: "text", nullable: true),
                    RequeryDelayTime = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TimeStarted = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TimeCompleted = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Tries = table.Column<int>(type: "integer", nullable: false),
                    Uri = table.Column<string>(type: "text", nullable: true),
                    ResponseMessage = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_BackgroundTaskEventLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CollectionTemplate",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    FileUrl = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CollectionTemplate", x => x.Id);
                });


            migrationBuilder.CreateTable(
                name: "Country",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Code = table.Column<string>(type: "character(2)", fixedLength: true, maxLength: 2, nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    ImageUrl = table.Column<string>(type: "text", nullable: false),
                    PhoneCode = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Country", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerDevice",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    DeviceId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Type = table.Column<CustomerDeviceType>(type: "core.\"CustomerDeviceType\"", nullable: false),
                    PushNotificationId = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AppVersion = table.Column<string>(type: "text", nullable: true),
                    AppBuild = table.Column<string>(type: "text", nullable: true),
                    MobileOsVersion = table.Column<string>(type: "text", nullable: true),
                    MobileMake = table.Column<string>(type: "text", nullable: true),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerDevice", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerDeviceVersion",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Type = table.Column<CustomerDeviceType>(type: "core.\"CustomerDeviceType\"", nullable: false),
                    OldVersion = table.Column<string>(type: "text", nullable: false),
                    CurrentVersion = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerDeviceVersion", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerInbox",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: true),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Template = table.Column<string>(type: "text", nullable: true),
                    Read = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerInbox", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerPaymentMethodConfiguration",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    Provider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    Currency = table.Column<string>(type: "text", nullable: true),
                    ProviderId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerPaymentMethodConfiguration", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerProfile",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    AccountId = table.Column<string>(type: "text", nullable: false),
                    FullName = table.Column<string>(type: "text", nullable: true, computedColumnSql: "\"FirstName\"  || ' ' || \"LastName\"", stored: true),
                    FirstName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MiddleName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    LastName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Email = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    DateOfBirth = table.Column<DateOnly>(type: "date", nullable: true),
                    Gender = table.Column<int>(type: "integer", nullable: false),
                    CountryCode = table.Column<string>(type: "text", nullable: true),
                    CurrencySymbol = table.Column<string>(type: "text", nullable: true),
                    CurrencyCode = table.Column<string>(type: "character varying(3)", maxLength: 3, nullable: false),
                    Blacklisted = table.Column<bool>(type: "boolean", nullable: false),
                    LastRescheduleDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    TotalRescheduleCount = table.Column<int>(type: "integer", nullable: false),
                    BalanceTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalancePaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceRemaining = table.Column<decimal>(type: "numeric(18,2)", nullable: false, computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\"", stored: true),
                    PaymentFrequency = table.Column<SchedulePaymentFrequency>(type: "collection.\"SchedulePaymentFrequency\"", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    MobileNumber = table.Column<string>(type: "jsonb", nullable: true),
                    PhoneNumber = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerProfile", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DashboardAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TotalCustomers = table.Column<int>(type: "integer", nullable: false),
                    TotalSignedInCustomers = table.Column<int>(type: "integer", nullable: false),
                    TotalCustomersWithSchedules = table.Column<int>(type: "integer", nullable: false),
                    TotalPlacements = table.Column<int>(type: "integer", nullable: false),
                    TotalPlacementValue = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalPlacementValuePaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalClosedPlacements = table.Column<int>(type: "integer", nullable: false),
                    TotalClosedPlacementValue = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalSettledPlacements = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DashboardTimeAnalytics",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Date = table.Column<DateOnly>(type: "date", nullable: false),
                    TotalCustomersThatCreatedSchedule = table.Column<int>(type: "integer", nullable: false),
                    TotalAmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DashboardTimeAnalytics", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "DataProtectionKeys",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false),
                    FriendlyName = table.Column<string>(type: "text", nullable: true),
                    Xml = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DataProtectionKeys", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessageLog",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    OriginatedFrom = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<MessageStatus>(type: "core.\"MessageStatus\"", nullable: false),
                    AttemptCount = table.Column<int>(type: "integer", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessageProviderConfig",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: true),
                    SupportedCountries = table.Column<List<string>>(type: "text[]", nullable: true),
                    Disabled = table.Column<bool>(type: "boolean", nullable: false),
                    LogActivity = table.Column<bool>(type: "boolean", nullable: false),
                    UnitCost = table.Column<decimal>(type: "numeric(18,8)", nullable: false),
                    TotalCount = table.Column<long>(type: "bigint", nullable: false),
                    FailureCount = table.Column<long>(type: "bigint", nullable: false),
                    TotalFunding = table.Column<decimal>(type: "numeric(18,8)", nullable: false),
                    TotalDebit = table.Column<decimal>(type: "numeric(18,8)", nullable: false),
                    ExpectedTotalDebit = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Balance = table.Column<decimal>(type: "numeric(18,8)", nullable: false, computedColumnSql: "\"TotalFunding\" - \"TotalDebit\"", stored: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageProviderConfig", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessagingGroup",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingGroup", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "MessagingTemplate",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Types = table.Column<int>(type: "integer", nullable: false),
                    Keys = table.Column<List<string>>(type: "text[]", nullable: true),
                    TextTemplate = table.Column<string>(type: "text", nullable: true),
                    HtmlTemplate = table.Column<string>(type: "text", nullable: true),
                    IsContainer = table.Column<bool>(type: "boolean", nullable: false),
                    IsDisabled = table.Column<bool>(type: "boolean", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingTemplate", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictApplications",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApplicationType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ClientId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ClientSecret = table.Column<string>(type: "text", nullable: true),
                    ClientType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    ConsentType = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    DisplayName = table.Column<string>(type: "text", nullable: true),
                    DisplayNames = table.Column<string>(type: "text", nullable: true),
                    JsonWebKeySet = table.Column<string>(type: "text", nullable: true),
                    Permissions = table.Column<string>(type: "text", nullable: true),
                    PostLogoutRedirectUris = table.Column<string>(type: "text", nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    RedirectUris = table.Column<string>(type: "text", nullable: true),
                    Requirements = table.Column<string>(type: "text", nullable: true),
                    Settings = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictApplications", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictScopes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true),
                    Descriptions = table.Column<string>(type: "text", nullable: true),
                    DisplayName = table.Column<string>(type: "text", nullable: true),
                    DisplayNames = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(200)", maxLength: 200, nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    Resources = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictScopes", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TelegramSession",
                schema: "public",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Name = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: false),
                    Data = table.Column<byte[]>(type: "bytea", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TelegramSession", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "UserAccessLog",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    Status = table.Column<AccessStatus>(type: "core.\"AccessStatus\"", nullable: false),
                    GrantType = table.Column<GrantType>(type: "core.\"GrantType\"", nullable: false),
                    Application = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    IpLocation = table.Column<string>(type: "text", nullable: true),
                    LocationLookupData = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_UserAccessLog", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "AspNetRoleClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false),
                    Source = table.Column<string>(type: "text", nullable: true),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetRoleClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetRoleClaims_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "public",
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserClaims",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    ClaimType = table.Column<string>(type: "text", nullable: true),
                    ClaimValue = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserClaims", x => x.Id);
                    table.ForeignKey(
                        name: "FK_AspNetUserClaims_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserLogins",
                columns: table => new
                {
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    ProviderKey = table.Column<string>(type: "text", nullable: false),
                    ProviderDisplayName = table.Column<string>(type: "text", nullable: true),
                    UserId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserLogins", x => new { x.LoginProvider, x.ProviderKey });
                    table.ForeignKey(
                        name: "FK_AspNetUserLogins_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserRoles",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    RoleId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserRoles", x => new { x.UserId, x.RoleId });
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetRoles_RoleId",
                        column: x => x.RoleId,
                        principalSchema: "public",
                        principalTable: "AspNetRoles",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_AspNetUserRoles_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "AspNetUserTokens",
                columns: table => new
                {
                    UserId = table.Column<Guid>(type: "uuid", nullable: false),
                    LoginProvider = table.Column<string>(type: "text", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Value = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_AspNetUserTokens", x => new { x.UserId, x.LoginProvider, x.Name });
                    table.ForeignKey(
                        name: "FK_AspNetUserTokens_AspNetUsers_UserId",
                        column: x => x.UserId,
                        principalSchema: "public",
                        principalTable: "AspNetUsers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Currency",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    CountryId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Code = table.Column<string>(type: "character(3)", fixedLength: true, maxLength: 3, nullable: false),
                    Symbol = table.Column<string>(type: "character(1)", fixedLength: true, maxLength: 1, nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Currency", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Currency_Country_CountryId",
                        column: x => x.CountryId,
                        principalSchema: "core",
                        principalTable: "Country",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "State",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    CountryId = table.Column<long>(type: "bigint", nullable: false),
                    StateCode = table.Column<string>(type: "character varying(32)", maxLength: 32, nullable: false),
                    StateName = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_State", x => x.Id);
                    table.ForeignKey(
                        name: "FK_State_Country_CountryId",
                        column: x => x.CountryId,
                        principalSchema: "core",
                        principalTable: "Country",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerPaymentMethod",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    CustomerPaymentMethodConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    ProviderId = table.Column<string>(type: "text", nullable: true),
                    CanBeReused = table.Column<bool>(type: "boolean", nullable: false),
                    Status = table.Column<PaymentMethodStatus>(type: "collection.\"PaymentMethodStatus\"", nullable: false),
                    ExpiryDate = table.Column<DateOnly>(type: "date", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Data = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerPaymentMethod", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerPaymentMethod_CustomerPaymentMethodConfiguration_Cu~",
                        column: x => x.CustomerPaymentMethodConfigurationId,
                        principalSchema: "collection",
                        principalTable: "CustomerPaymentMethodConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerActivity",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    CustomerProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerActivity_CustomerProfile_CustomerProfileId",
                        column: x => x.CustomerProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerAddress",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    CustomerProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    AddressLine1 = table.Column<string>(type: "text", nullable: true),
                    AddressLine2 = table.Column<string>(type: "text", nullable: true),
                    Locality = table.Column<string>(type: "text", nullable: true),
                    PostCode = table.Column<string>(type: "text", nullable: true),
                    City = table.Column<string>(type: "text", nullable: true),
                    Country = table.Column<string>(type: "text", nullable: true),
                    CountryCode = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerAddress", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerAddress_CustomerProfile_CustomerProfileId",
                        column: x => x.CustomerProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerIncomeAndExpenditure",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    FullName = table.Column<string>(type: "text", nullable: true),
                    Address = table.Column<string>(type: "text", nullable: true),
                    NetSalary = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BenefitsIncome = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    OtherIncome = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Rent = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Utilities = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CouncilTax = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Food = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Transport = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Insurance = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Loan = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    OtherExpenditure = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    PhoneNumber = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerIncomeAndExpenditure", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerIncomeAndExpenditure_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerNote",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Note = table.Column<string>(type: "text", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Files = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerNote", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerNote_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerOneTimeCode",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: true),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    CodeType = table.Column<string>(type: "text", nullable: true),
                    Code = table.Column<string>(type: "text", nullable: true),
                    SendCount = table.Column<int>(type: "integer", nullable: false),
                    SendLimit = table.Column<int>(type: "integer", nullable: false),
                    TriesCount = table.Column<int>(type: "integer", nullable: false),
                    IsValid = table.Column<bool>(type: "boolean", nullable: false),
                    ExpireAt = table.Column<Instant>(type: "timestamp with time zone", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerOneTimeCode", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerOneTimeCode_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "CustomerSchedule",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    Period = table.Column<int>(type: "integer", nullable: false),
                    DueDate = table.Column<DateOnly>(type: "date", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    CPADate = table.Column<DateOnly>(type: "date", nullable: false),
                    PeriodStatus = table.Column<int>(type: "integer", nullable: false),
                    PaymentStatus = table.Column<int>(type: "integer", nullable: false),
                    Balance = table.Column<decimal>(type: "numeric(18,2)", nullable: false, computedColumnSql: "\"Amount\" - \"AmountPaid\"", stored: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerSchedule", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerSchedule_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CustomerTransaction",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    TransactionId = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    PaymentMethod = table.Column<string>(type: "text", nullable: true),
                    PaymentProvider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    AmountTried = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Successful = table.Column<bool>(type: "boolean", nullable: false),
                    SchedulePeriodAffected = table.Column<List<string>>(type: "text[]", nullable: true),
                    PaymentApplied = table.Column<bool>(type: "boolean", nullable: false),
                    PaymentType = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerTransaction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerTransaction_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Placement",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    SourceAccountNumber = table.Column<string>(type: "text", nullable: true),
                    Company = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    AgreementDate = table.Column<DateOnly>(type: "date", nullable: true),
                    DefaultDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LastPaymentDate = table.Column<DateOnly>(type: "date", nullable: true),
                    LastInvoiceDate = table.Column<DateOnly>(type: "date", nullable: true),
                    PlacementCount = table.Column<int>(type: "integer", nullable: false),
                    MonthlyRecurringBills = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalancePrincipal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceInterest = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceFees = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceTotal = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalancePaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    BalanceRemaining = table.Column<decimal>(type: "numeric(18,2)", nullable: false, computedColumnSql: "\"BalanceTotal\" - \"BalancePaid\"", stored: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Placement", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Placement_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Transaction",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: false),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    ProviderReference = table.Column<string>(type: "text", nullable: true),
                    Purpose = table.Column<string>(type: "text", nullable: true),
                    CurrencyCode = table.Column<string>(type: "text", nullable: true),
                    CurrencySymbol = table.Column<string>(type: "text", nullable: true),
                    UnitAmount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Amount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Fee = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalAmountPayable = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    TotalAmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Quantity = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Discount = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    Type = table.Column<PaymentType>(type: "collection.\"PaymentType\"", nullable: false),
                    Provider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    Status = table.Column<TransactionStatus>(type: "collection.\"TransactionStatus\"", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Fields = table.Column<string>(type: "jsonb", nullable: true),
                    UserData = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Transaction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Transaction_CustomerProfile_ProfileId",
                        column: x => x.ProfileId,
                        principalSchema: "collection",
                        principalTable: "CustomerProfile",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageLogActivity",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageLogActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageLogActivity_MessageLog_MessageLogId",
                        column: x => x.MessageLogId,
                        principalSchema: "core",
                        principalTable: "MessageLog",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessagingGroupEntry",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessagingGroupId = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: true),
                    Emails = table.Column<List<string>>(type: "text[]", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    PhoneNumbers = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingGroupEntry", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessagingGroupEntry_MessagingGroup_MessagingGroupId",
                        column: x => x.MessagingGroupId,
                        principalSchema: "core",
                        principalTable: "MessagingGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageConfiguration",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false),
                    Name = table.Column<string>(type: "text", nullable: false),
                    Description = table.Column<string>(type: "text", nullable: false),
                    Subject = table.Column<string>(type: "text", nullable: true),
                    SenderEmail = table.Column<string>(type: "text", nullable: true),
                    SenderName = table.Column<string>(type: "text", nullable: true),
                    Channels = table.Column<int>(type: "integer", nullable: false),
                    Keys = table.Column<List<string>>(type: "text[]", nullable: true),
                    DoNotSendIfExists = table.Column<bool>(type: "boolean", nullable: false),
                    ExistsCheckWindow = table.Column<int>(type: "integer", nullable: false),
                    BodyTemplateId = table.Column<long>(type: "bigint", nullable: true),
                    ContainerTemplateId = table.Column<long>(type: "bigint", nullable: true),
                    TextTemplate = table.Column<string>(type: "text", nullable: true),
                    HtmlTemplate = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageConfiguration", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageConfiguration_MessagingTemplate_BodyTemplateId",
                        column: x => x.BodyTemplateId,
                        principalSchema: "core",
                        principalTable: "MessagingTemplate",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_MessageConfiguration_MessagingTemplate_ContainerTemplateId",
                        column: x => x.ContainerTemplateId,
                        principalSchema: "core",
                        principalTable: "MessagingTemplate",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictAuthorizations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApplicationId = table.Column<Guid>(type: "uuid", nullable: true),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    Scopes = table.Column<string>(type: "text", nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Subject = table.Column<string>(type: "character varying(400)", maxLength: 400, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictAuthorizations", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OpenIddictAuthorizations_OpenIddictApplications_Application~",
                        column: x => x.ApplicationId,
                        principalTable: "OpenIddictApplications",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "PlacementActivity",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementActivity_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementNotes",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    Note = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementNotes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementNotes_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementTag",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    Tag = table.Column<string>(type: "text", nullable: true),
                    Comment = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementTag", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementTag_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "PlacementTransaction",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    ProfileId = table.Column<Guid>(type: "uuid", nullable: false),
                    PlacementId = table.Column<long>(type: "bigint", nullable: false),
                    TransactionId = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    PaymentMethod = table.Column<string>(type: "text", nullable: true),
                    PaymentProvider = table.Column<PaymentProvider>(type: "collection.\"PaymentProvider\"", nullable: false),
                    AmountPaid = table.Column<decimal>(type: "numeric(18,2)", nullable: false),
                    PaymentType = table.Column<string>(type: "text", nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PlacementTransaction", x => x.Id);
                    table.ForeignKey(
                        name: "FK_PlacementTransaction_Placement_PlacementId",
                        column: x => x.PlacementId,
                        principalSchema: "collection",
                        principalTable: "Placement",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TransactionHistory",
                schema: "collection",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    TransactionId = table.Column<string>(type: "character varying(24)", maxLength: 24, nullable: true),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TransactionHistory", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TransactionHistory_Transaction_TransactionId",
                        column: x => x.TransactionId,
                        principalSchema: "collection",
                        principalTable: "Transaction",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageConfigurationMessagingGroup",
                schema: "core",
                columns: table => new
                {
                    MessageConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    MessagingGroupId = table.Column<long>(type: "bigint", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageConfigurationMessagingGroup", x => new { x.MessageConfigurationId, x.MessagingGroupId });
                    table.ForeignKey(
                        name: "FK_MessageConfigurationMessagingGroup_MessageConfiguration_Mes~",
                        column: x => x.MessageConfigurationId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MessageConfigurationMessagingGroup_MessagingGroup_Messaging~",
                        column: x => x.MessagingGroupId,
                        principalSchema: "core",
                        principalTable: "MessagingGroup",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessageLogEntry",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessageLogId = table.Column<long>(type: "bigint", nullable: false),
                    MessageConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    CopiedIn = table.Column<string>(type: "jsonb", nullable: true),
                    Recipients = table.Column<string>(type: "jsonb", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessageLogEntry", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessageLogEntry_MessageConfiguration_MessageConfigurationId",
                        column: x => x.MessageConfigurationId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MessageLogEntry_MessageLog_MessageLogId",
                        column: x => x.MessageLogId,
                        principalSchema: "core",
                        principalTable: "MessageLog",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "MessagingConfigurationActivity",
                schema: "core",
                columns: table => new
                {
                    Id = table.Column<long>(type: "bigint", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityAlwaysColumn),
                    MessageConfigurationId = table.Column<long>(type: "bigint", nullable: false),
                    CreatedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    CreatedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    CreatedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    CreatedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    ModifiedByIp = table.Column<string>(type: "character varying(46)", maxLength: 46, nullable: true),
                    ModifiedByUser = table.Column<string>(type: "character varying(256)", maxLength: 256, nullable: true),
                    ModifiedByUserId = table.Column<Guid>(type: "uuid", nullable: true),
                    LastModifiedDate = table.Column<Instant>(type: "timestamp with time zone", nullable: true),
                    Title = table.Column<string>(type: "text", nullable: true),
                    Activity = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MessagingConfigurationActivity", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MessagingConfigurationActivity_MessageConfiguration_Message~",
                        column: x => x.MessageConfigurationId,
                        principalSchema: "core",
                        principalTable: "MessageConfiguration",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "OpenIddictTokens",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    ApplicationId = table.Column<Guid>(type: "uuid", nullable: true),
                    AuthorizationId = table.Column<Guid>(type: "uuid", nullable: true),
                    ConcurrencyToken = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    CreationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ExpirationDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    Payload = table.Column<string>(type: "text", nullable: true),
                    Properties = table.Column<string>(type: "text", nullable: true),
                    RedemptionDate = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    ReferenceId = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Status = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true),
                    Subject = table.Column<string>(type: "character varying(400)", maxLength: 400, nullable: true),
                    Type = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_OpenIddictTokens", x => x.Id);
                    table.ForeignKey(
                        name: "FK_OpenIddictTokens_OpenIddictApplications_ApplicationId",
                        column: x => x.ApplicationId,
                        principalTable: "OpenIddictApplications",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_OpenIddictTokens_OpenIddictAuthorizations_AuthorizationId",
                        column: x => x.AuthorizationId,
                        principalTable: "OpenIddictAuthorizations",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateIndex(
                name: "IX_AspNetRoleClaims_RoleId",
                table: "AspNetRoleClaims",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "RoleNameIndex",
                schema: "public",
                table: "AspNetRoles",
                column: "NormalizedName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserClaims_UserId",
                table: "AspNetUserClaims",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserLogins_UserId",
                table: "AspNetUserLogins",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUserRoles_RoleId",
                table: "AspNetUserRoles",
                column: "RoleId");

            migrationBuilder.CreateIndex(
                name: "EmailIndex",
                schema: "public",
                table: "AspNetUsers",
                column: "NormalizedEmail");

            migrationBuilder.CreateIndex(
                name: "IX_AspNetUsers_UserName",
                schema: "public",
                table: "AspNetUsers",
                column: "UserName",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "SecurityStamp", "TwoFactorEnabled", "PasswordHash", "WalletPinHash", "MustChangePassword", "Role" });

            migrationBuilder.CreateIndex(
                name: "UserNameIndex",
                schema: "public",
                table: "AspNetUsers",
                column: "NormalizedUserName",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BackgroundTaskEventControl_Source_Event",
                schema: "core",
                table: "BackgroundTaskEventControl",
                columns: new[] { "Source", "Event" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_BackgroundTaskEventLog_UserId",
                schema: "core",
                table: "BackgroundTaskEventLog",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_Country_Code",
                schema: "core",
                table: "Country",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Country_Name",
                schema: "core",
                table: "Country",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Currency_Code",
                schema: "core",
                table: "Currency",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Currency_CountryId",
                schema: "core",
                table: "Currency",
                column: "CountryId");

            migrationBuilder.CreateIndex(
                name: "IX_Currency_Symbol",
                schema: "core",
                table: "Currency",
                column: "Symbol",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerActivity_CustomerProfileId",
                schema: "collection",
                table: "CustomerActivity",
                column: "CustomerProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerActivity_ProfileId",
                schema: "collection",
                table: "CustomerActivity",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerAddress_CustomerProfileId",
                schema: "collection",
                table: "CustomerAddress",
                column: "CustomerProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerDevice_UserId",
                schema: "core",
                table: "CustomerDevice",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerInbox_UserId",
                schema: "core",
                table: "CustomerInbox",
                column: "UserId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerIncomeAndExpenditure_ProfileId",
                schema: "collection",
                table: "CustomerIncomeAndExpenditure",
                column: "ProfileId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerNote_ProfileId",
                schema: "collection",
                table: "CustomerNote",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerOneTimeCode_ProfileId",
                schema: "collection",
                table: "CustomerOneTimeCode",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerPaymentMethod_CustomerPaymentMethodConfigurationId",
                schema: "collection",
                table: "CustomerPaymentMethod",
                column: "CustomerPaymentMethodConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerProfile_AccountId",
                schema: "collection",
                table: "CustomerProfile",
                column: "AccountId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_CustomerSchedule_ProfileId",
                schema: "collection",
                table: "CustomerSchedule",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerTransaction_ProfileId",
                schema: "collection",
                table: "CustomerTransaction",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerTransaction_TransactionId",
                schema: "collection",
                table: "CustomerTransaction",
                column: "TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_DashboardTimeAnalytics_Date",
                schema: "collection",
                table: "DashboardTimeAnalytics",
                column: "Date",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "TotalCustomersThatCreatedSchedule", "TotalAmountPaid" });

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfiguration_BodyTemplateId",
                schema: "core",
                table: "MessageConfiguration",
                column: "BodyTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfiguration_ContainerTemplateId",
                schema: "core",
                table: "MessageConfiguration",
                column: "ContainerTemplateId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfiguration_Name",
                schema: "core",
                table: "MessageConfiguration",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "Channels" });

            migrationBuilder.CreateIndex(
                name: "IX_MessageConfigurationMessagingGroup_MessagingGroupId",
                schema: "core",
                table: "MessageConfigurationMessagingGroup",
                column: "MessagingGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLog_Status",
                schema: "core",
                table: "MessageLog",
                column: "Status");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLogActivity_MessageLogId",
                schema: "core",
                table: "MessageLogActivity",
                column: "MessageLogId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLogEntry_MessageConfigurationId",
                schema: "core",
                table: "MessageLogEntry",
                column: "MessageConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageLogEntry_MessageLogId",
                schema: "core",
                table: "MessageLogEntry",
                column: "MessageLogId");

            migrationBuilder.CreateIndex(
                name: "IX_MessageProviderConfig_Name",
                schema: "core",
                table: "MessageProviderConfig",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MessagingConfigurationActivity_MessageConfigurationId",
                schema: "core",
                table: "MessagingConfigurationActivity",
                column: "MessageConfigurationId");

            migrationBuilder.CreateIndex(
                name: "IX_MessagingGroup_Name",
                schema: "core",
                table: "MessagingGroup",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_MessagingGroupEntry_MessagingGroupId",
                schema: "core",
                table: "MessagingGroupEntry",
                column: "MessagingGroupId");

            migrationBuilder.CreateIndex(
                name: "IX_MessagingTemplate_Name",
                schema: "core",
                table: "MessagingTemplate",
                column: "Name",
                unique: true)
                .Annotation("Npgsql:IndexInclude", new[] { "Types" });

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictApplications_ClientId",
                table: "OpenIddictApplications",
                column: "ClientId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictAuthorizations_ApplicationId_Status_Subject_Type",
                table: "OpenIddictAuthorizations",
                columns: new[] { "ApplicationId", "Status", "Subject", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictScopes_Name",
                table: "OpenIddictScopes",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictTokens_ApplicationId_Status_Subject_Type",
                table: "OpenIddictTokens",
                columns: new[] { "ApplicationId", "Status", "Subject", "Type" });

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictTokens_AuthorizationId",
                table: "OpenIddictTokens",
                column: "AuthorizationId");

            migrationBuilder.CreateIndex(
                name: "IX_OpenIddictTokens_ReferenceId",
                table: "OpenIddictTokens",
                column: "ReferenceId",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Placement_ProfileId",
                schema: "collection",
                table: "Placement",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementActivity_PlacementId",
                schema: "collection",
                table: "PlacementActivity",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementNotes_PlacementId",
                schema: "collection",
                table: "PlacementNotes",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementTag_PlacementId",
                schema: "collection",
                table: "PlacementTag",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementTransaction_PlacementId",
                schema: "collection",
                table: "PlacementTransaction",
                column: "PlacementId");

            migrationBuilder.CreateIndex(
                name: "IX_PlacementTransaction_TransactionId",
                schema: "collection",
                table: "PlacementTransaction",
                column: "TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_State_CountryId_StateCode",
                schema: "core",
                table: "State",
                columns: new[] { "CountryId", "StateCode" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TelegramSession_Name",
                schema: "public",
                table: "TelegramSession",
                column: "Name",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_Transaction_ProfileId",
                schema: "collection",
                table: "Transaction",
                column: "ProfileId");

            migrationBuilder.CreateIndex(
                name: "IX_TransactionHistory_TransactionId",
                schema: "collection",
                table: "TransactionHistory",
                column: "TransactionId");

            migrationBuilder.CreateIndex(
                name: "IX_UserAccessLog_CreatedByUserId",
                schema: "core",
                table: "UserAccessLog",
                column: "CreatedByUserId");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "AspNetClaims",
                schema: "public");

            migrationBuilder.DropTable(
                name: "AspNetRoleClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserClaims");

            migrationBuilder.DropTable(
                name: "AspNetUserLogins");

            migrationBuilder.DropTable(
                name: "AspNetUserRoles");

            migrationBuilder.DropTable(
                name: "AspNetUserTokens");

            migrationBuilder.DropTable(
                name: "BackgroundTaskEventControl",
                schema: "core");

            migrationBuilder.DropTable(
                name: "BackgroundTaskEventLog",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CollectionTemplate",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CollectionUpload",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "Currency",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerActivity",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerAddress",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerDevice",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerDeviceVersion",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerInbox",
                schema: "core");

            migrationBuilder.DropTable(
                name: "CustomerIncomeAndExpenditure",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerNote",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerOneTimeCode",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerPaymentMethod",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerSchedule",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "CustomerTransaction",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DashboardAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DashboardTimeAnalytics",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "DataProtectionKeys");

            migrationBuilder.DropTable(
                name: "MessageConfigurationMessagingGroup",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageLogActivity",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageLogEntry",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageProviderConfig",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessagingConfigurationActivity",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessagingGroupEntry",
                schema: "core");

            migrationBuilder.DropTable(
                name: "OpenIddictScopes");

            migrationBuilder.DropTable(
                name: "OpenIddictTokens");

            migrationBuilder.DropTable(
                name: "PlacementActivity",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementNotes",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementTag",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "PlacementTransaction",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "State",
                schema: "core");

            migrationBuilder.DropTable(
                name: "TelegramSession",
                schema: "public");

            migrationBuilder.DropTable(
                name: "TransactionHistory",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "UserAccessLog",
                schema: "core");

            migrationBuilder.DropTable(
                name: "AspNetRoles",
                schema: "public");

            migrationBuilder.DropTable(
                name: "AspNetUsers",
                schema: "public");

            migrationBuilder.DropTable(
                name: "CustomerPaymentMethodConfiguration",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "MessageLog",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessageConfiguration",
                schema: "core");

            migrationBuilder.DropTable(
                name: "MessagingGroup",
                schema: "core");

            migrationBuilder.DropTable(
                name: "OpenIddictAuthorizations");

            migrationBuilder.DropTable(
                name: "Placement",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "Country",
                schema: "core");

            migrationBuilder.DropTable(
                name: "Transaction",
                schema: "collection");

            migrationBuilder.DropTable(
                name: "MessagingTemplate",
                schema: "core");

            migrationBuilder.DropTable(
                name: "OpenIddictApplications");

            migrationBuilder.DropTable(
                name: "CustomerProfile",
                schema: "collection");

            migrationBuilder.DropSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "collection");

            migrationBuilder.DropSequence(
                name: "EntityFrameworkHiLoSequence",
                schema: "core");
        }
    }
}

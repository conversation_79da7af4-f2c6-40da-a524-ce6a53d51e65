﻿using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Collection.Setup;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NodaTime;

namespace LendQube.Entities.Collection.Customers;

public class CustomerHold : BaseEntityWithIdentityId<CustomerHold>
{
    [DbGuid]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public long? PlacementId { get; set; }
    public virtual Placement? Placement { get; set; }
    public long HoldConfigId { get; set; }
    public virtual HoldConfig Hold { get; set; }
    public string Comment { get; set; }
    public Instant? ExpiresOn { get; set; }
    public bool HoldDefaultsOverriden { get; set; }
    public HoldAction Action { get; set; }
    public bool Disabled { get; set; }

    [NotMapped]
    public List<HoldAction> ActionList { get; set; } = [];

    [NotMapped]
    public string Expiry { get; set; }

    public override void Configure(EntityTypeBuilder<CustomerHold> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.ExpiresOn)
            .IncludeProperties(x => new { x.HoldDefaultsOverriden });
    }
}

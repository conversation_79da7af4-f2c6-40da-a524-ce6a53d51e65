﻿using LendQube.Infrastructure.Core.Database.DbContexts;
using LendQube.Infrastructure.Core.Database.Repository;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.AdminUserManagement;

public static class AdminCompiledQueries
{
    private static readonly Func<AppDbContext, string, string> CompiledUsernameQuery =
        EF.CompileQuery((AppDbContext context, string userName) => context.Users.Where(x => x.UserName == userName).Select(x => x.FullName).AsNoTracking().FirstOrDefault());

    public static string GetUserFullName(this AppDbContext context, string userName) => CompiledUsernameQuery(context, userName);

    private static readonly Func<AppDbContext, bool, string, string, CancellationToken, Task<bool>> CompiledSecurityStampCheck =
        EF.CompileAsyncQuery((AppDbContext context, bool doNotCheckTwoFactor, string securityStamp, string userName, CancellationToken ct) => context.Users.Where(x => x.UserName == userName
        && x.SecurityStamp == securityStamp && (doNotCheckTwoFactor || x.TwoFactorEnabled))
        .AsNoTracking().Any());

    public static Task<bool> CheckSecurityStamp(IUnitofWork uow, bool doNotCheckTwoFactor, string securityStamp, string userName, CancellationToken ct) => CompiledSecurityStampCheck(uow.Context, doNotCheckTwoFactor, securityStamp, userName, ct);
}

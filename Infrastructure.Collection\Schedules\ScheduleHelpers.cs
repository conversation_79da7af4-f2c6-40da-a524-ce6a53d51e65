﻿using LendQube.Entities.Collection.Customers;

namespace LendQube.Infrastructure.Collection.Schedules;

public static class ScheduleHelpers
{
    public static DateOnly GetDate(this SchedulePaymentFrequency frequency, DateTime date, int count)
        => frequency switch
        {
            SchedulePaymentFrequency.Weekly => DateOnly.FromDateTime(date.AddDays(count * 7)),
            _ => DateOnly.FromDateTime(date.AddMonths(count))
        };
    public static DateOnly GetDate(this SchedulePaymentFrequency frequency, DateOnly date, int count)
        => frequency switch
        {
            SchedulePaymentFrequency.Weekly => date.AddDays(count * 7),
            _ => date.AddMonths(count)
        };
}
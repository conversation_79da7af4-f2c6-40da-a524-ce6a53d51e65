﻿@using LendQube.Infrastructure.Core.DateAndTime
@using NodaTime
@using Radzen.Blazor
<RadzenDatePicker style="display: block; width: 100%"
                  @bind-Value="@dateTime"
                  TValue="DateTimeOffset?"
                  Change="UpdateTime"
                  Name="@Name"
                  Placeholder="@Placeholder"
                  DateFormat="dd MMM, yyyy" />

@code {

    [Inject]
    public UserTimeProvider TimeProvider { get; set; }

    [Parameter, EditorRequired]
    public Instant? DateValue { get; set; }

    [Parameter]
    public EventCallback<Instant?> DateValueChanged { get; set; }

    [Parameter]
    public string Name { get; set; }

    [Parameter]
    public string Placeholder { get; set; }

    private DateTimeOffset? dateTime { get; set; }

    protected override void OnInitialized()
    {
        if (DateValue.HasValue)
        {
            dateTime = DateValue.Value.InZone(TimeProvider.LocalTimeZone ?? DateTimeZone.Utc).ToDateTimeOffset();
        }
        base.OnInitialized();
    }

    void UpdateTime(DateTime? dateTime)
    {
        if (dateTime is null) return;
        DateValue = Instant.FromDateTimeOffset(dateTime.Value);
        DateValueChanged.InvokeAsync(DateValue);
    }
}
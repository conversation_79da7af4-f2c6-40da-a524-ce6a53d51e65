﻿using System.Text.Json.Serialization;

namespace LendQube.Infrastructure.ExternalApi.Acquired;

public class AcquiredTransactionResponseVM
{
    [JsonPropertyName("meta")]
    public AcquiredTransactionResponsePaginationVM Meta { get; set; }

    [JsonPropertyName("data")]
    public List<AcquiredCardTransactionResponseVM> Data { get; set; }
}


public class AcquiredTransactionResponsePaginationVM
{
    [JsonPropertyName("count")]
    public int Count { get; set; }

    [JsonPropertyName("offset")]
    public int Offset { get; set; }

    [JsonPropertyName("limit")]
    public int Limit { get; set; }

    [JsonPropertyName("total")]
    public int Total { get; set; }
}

public class AcquiredCardTransactionResponseVM
{
    [JsonPropertyName("transaction_id")]
    public string TransactionId { get; set; }

    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("reason")]
    public string Reason { get; set; }

    [JsonPropertyName("mid")]
    public string Mid { get; set; }

    [JsonPropertyName("payment_method")]
    public string PaymentMethod { get; set; }

    [JsonPropertyName("transaction_type")]
    public string TransactionType { get; set; }

    [JsonPropertyName("transaction")]
    public AcquiredCardResponseTransaction Transaction { get; set; }

    [JsonPropertyName("check")]
    public AcquiredCardResponseCheck Check { get; set; }

    [JsonPropertyName("tds")]
    public AcquiredCardResponseTds Tds { get; set; }

    [JsonPropertyName("issuer_response_code")]
    public string IssuerResponseCode { get; set; }

    [JsonPropertyName("authorisation_code")]
    public string AuthorisationCode { get; set; }

    [JsonPropertyName("acquirer_reference_number")]
    public string AcquirerReferenceNumber { get; set; }

    [JsonPropertyName("scheme_reference_data")]
    public string SchemeReferenceData { get; set; }

    [JsonPropertyName("card_id")]
    public string CardId { get; set; }

    [JsonPropertyName("card")]
    public AcquiredCardTransactionResponseCard Card { get; set; }

    [JsonPropertyName("bin")]
    public AcquiredCardResponseBin Bin { get; set; }

    [JsonPropertyName("customer")]
    public AcquiredCardResponseCustomer Customer { get; set; }
}


public class AcquiredCardResponseBin
{
    [JsonPropertyName("scheme")]
    public string Scheme { get; set; }

    [JsonPropertyName("type")]
    public string Type { get; set; }

    [JsonPropertyName("product")]
    public string Product { get; set; }

    [JsonPropertyName("level")]
    public string Level { get; set; }

    [JsonPropertyName("issuer")]
    public string Issuer { get; set; }

    [JsonPropertyName("issuer_country")]
    public string IssuerCountry { get; set; }

    [JsonPropertyName("issuer_country_code")]
    public string IssuerCountryCode { get; set; }

    [JsonPropertyName("eea")]
    public bool Eea { get; set; }

    [JsonPropertyName("non_reloadable")]
    public bool NonReloadable { get; set; }
}

public class AcquiredCardTransactionResponseCard
{
    [JsonPropertyName("holder_name")]
    public string HolderName { get; set; }

    [JsonPropertyName("scheme")]
    public string Scheme { get; set; }

    [JsonPropertyName("number")]
    public string Number { get; set; }

    [JsonPropertyName("expiry_month")]
    public string ExpiryMonth { get; set; }

    [JsonPropertyName("expiry_year")]
    public string ExpiryYear { get; set; }
}

public class AcquiredCardResponseCheck
{
    [JsonPropertyName("avs_line1")]
    public string AvsLine1 { get; set; }

    [JsonPropertyName("avs_postcode")]
    public string AvsPostcode { get; set; }

    [JsonPropertyName("cvv")]
    public string Cvv { get; set; }
}

public class AcquiredCardResponseCustomer
{
    [JsonPropertyName("company_id")]
    public string CompanyId { get; set; }

    [JsonPropertyName("reference")]
    public string Reference { get; set; }
}

public class AcquiredCardResponseTds
{
    [JsonPropertyName("status")]
    public string Status { get; set; }

    [JsonPropertyName("eci")]
    public string Eci { get; set; }

    [JsonPropertyName("reason")]
    public string Reason { get; set; }
}

public class AcquiredCardResponseTransaction
{
    [JsonPropertyName("order_id")]
    public string OrderId { get; set; }

    [JsonPropertyName("amount")]
    public decimal Amount { get; set; }

    [JsonPropertyName("currency")]
    public string Currency { get; set; }

    [JsonPropertyName("capture")]
    public bool Capture { get; set; }
}


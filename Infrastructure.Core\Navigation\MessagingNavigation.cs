﻿namespace LendQube.Infrastructure.Core.Navigation;

internal sealed class MessagingNavigation : INavigationDescriptor, INavigatorHasPermissions
{
    public bool IsDisabled { get; set; } = false;
    public const string GroupName = "Messaging";
    public void PrepareNavigator()
    {

        var navs = new NavigatorVM
        {
            Name = GroupName,
            Icon = "send",
            Permission = MessageLogsIndexPermission,
            Controller = GroupName,
            Url = "messaging/logs",
            SubNavigation =
            [
                new NavigatorVM { Name = "Logs", Icon = "send", Permission = MessageLogsIndexPermission, Url = "messaging/logs", Controller = GroupName },
                new NavigatorVM { Name = "Scheduling", Icon = "send", Permission = MessageSchedulerIndexPermission, Url = "messaging/scheduler", Controller = GroupName },
                new NavigatorVM { Name = "Configuration", Icon = "message-square", Permission = MessageConfigurationIndexPermission, Url = "messaging/configuration", Controller = GroupName },
                new NavigatorVM { Name = "Templates", Icon = "at-sign", Permission = MessagingTemplateIndexPermission, Url = "messaging/templates", Controller = GroupName },
                new NavigatorVM { Name = "Directory", Icon = "mail", Permission = MessagingGroupIndexPermission, Url = "messaging/directory", Controller = GroupName },
                new NavigatorVM { Name = "Upload Directory", Icon = "mail", Permission = MessagingGroupUploadIndexPermission, Url = "messaging/uploaddirectory", Controller = GroupName },
                new NavigatorVM { Name = "Providers", Icon = "mail", Permission = MessageProviderConfigIndexPermission, Url = "messaging/providers", Controller = GroupName },
            ]
        };

        Navigator.SetupModuleNavigation(NavigationOrder.MessagingIndex, GroupName, navs);
    }

    public void PreparePermissionDescriptions()
    {

        Navigator.PermissionDescription[MessageConfigurationIndexPermission] = $"Can view message configuration";
        Navigator.PermissionDescription[MessageConfigurationCreatePermission] = "Can create message configuration";
        Navigator.PermissionDescription[MessageConfigurationEditPermission] = "Can modify message configuration";
        Navigator.PermissionDescription[MessageConfigurationDeletePermission] = "Can delete message configuration";

        Navigator.PermissionDescription[MessagingTemplateIndexPermission] = "Can view message templates";
        Navigator.PermissionDescription[MessagingTemplateCreatePermission] = "Can create message templates";
        Navigator.PermissionDescription[MessagingTemplateEditPermission] = "Can modify message templates";
        Navigator.PermissionDescription[MessagingTemplateDeletePermission] = "Can delete message templates";

        Navigator.PermissionDescription[MessagingGroupIndexPermission] = "Can view message directory";
        Navigator.PermissionDescription[MessagingGroupCreatePermission] = "Can create message directory";
        Navigator.PermissionDescription[MessagingGroupEditPermission] = "Can modify message directory";
        Navigator.PermissionDescription[MessagingGroupDeletePermission] = "Can delete message directory";

        Navigator.PermissionDescription[MessageProviderConfigIndexPermission] = "Can view message provider config";
        Navigator.PermissionDescription[MessageProviderConfigCreatePermission] = "Can create message provider config";
        Navigator.PermissionDescription[MessageProviderConfigEditPermission] = "Can modify message provider config";
        Navigator.PermissionDescription[MessageProviderConfigDeletePermission] = "Can delete message provider config";


        Navigator.PermissionDescription[MessageLogsIndexPermission] = $"Can view message logs and access {GroupName}";
        Navigator.PermissionDescription[MessageLogsDeletePermission] = "Can create remove message logs";


        Navigator.PermissionDescription[MessageSchedulerIndexPermission] = "Can view scheduled messages";
        Navigator.PermissionDescription[MessageSchedulerCreatePermission] = "Can create scheduled messages";
        Navigator.PermissionDescription[MessageSchedulerEditPermission] = "Can modify scheduled messages";
        Navigator.PermissionDescription[MessageSchedulerDeletePermission] = "Can delete scheduled messages";

        Navigator.PermissionDescription[MessagingGroupUploadIndexPermission] = "Can view message directory uploads";
        Navigator.PermissionDescription[MessagingGroupUploadCreatePermission] = "Can create message directory uploads";
        Navigator.PermissionDescription[MessagingGroupUploadEditPermission] = "Can modify message directory uploads";
        Navigator.PermissionDescription[MessagingGroupUploadImportPermission] = "Can import message directory uploads";
        Navigator.PermissionDescription[MessagingGroupUploadDeletePermission] = "Can delete message directory uploads";
    }

    public const string MessagingTemplateIndexPermission = "Permission.MessagingTemplate.Index";
    public const string MessagingTemplateCreatePermission = "Permission.MessagingTemplate.Create";
    public const string MessagingTemplateEditPermission = "Permission.MessagingTemplate.Edit";
    public const string MessagingTemplateDeletePermission = "Permission.MessagingTemplate.Delete";

    public const string MessageConfigurationIndexPermission = "Permission.MessageConfiguration.Index";
    public const string MessageConfigurationCreatePermission = "Permission.MessageConfiguration.Create";
    public const string MessageConfigurationEditPermission = "Permission.MessageConfiguration.Edit";
    public const string MessageConfigurationDeletePermission = "Permission.MessageConfiguration.Delete";

    public const string MessagingGroupIndexPermission = "Permission.MessagingGroup.Index";
    public const string MessagingGroupCreatePermission = "Permission.MessagingGroup.Create";
    public const string MessagingGroupEditPermission = "Permission.MessagingGroup.Edit";
    public const string MessagingGroupDeletePermission = "Permission.MessagingGroup.Delete";

    public const string MessageProviderConfigIndexPermission = "Permission.MessageProviderConfig.Index";
    public const string MessageProviderConfigCreatePermission = "Permission.MessageProviderConfig.Create";
    public const string MessageProviderConfigEditPermission = "Permission.MessageProviderConfig.Edit";
    public const string MessageProviderConfigDeletePermission = "Permission.MessageProviderConfig.Delete";


    public const string MessageLogsIndexPermission = "Permission.MessageLogs.Index";
    public const string MessageLogsDeletePermission = "Permission.MessageLogs.Delete";

    public const string MessageSchedulerIndexPermission = "Permission.MessageScheduler.Index";
    public const string MessageSchedulerCreatePermission = "Permission.MessageScheduler.Create";
    public const string MessageSchedulerEditPermission = "Permission.MessageScheduler.Edit";
    public const string MessageSchedulerDeletePermission = "Permission.MessageScheduler.Delete";

    public const string MessagingGroupUploadIndexPermission = "Permission.MessagingGroupUpload.Index";
    public const string MessagingGroupUploadCreatePermission = "Permission.MessagingGroupUpload.Create";
    public const string MessagingGroupUploadEditPermission = "Permission.MessagingGroupUpload.Edit";
    public const string MessagingGroupUploadImportPermission = "Permission.MessagingGroupUpload.Import";
    public const string MessagingGroupUploadDeletePermission = "Permission.MessagingGroupUpload.Delete";
}

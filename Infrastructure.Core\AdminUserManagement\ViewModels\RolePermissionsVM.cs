﻿using LendQube.Entities.Core.Attributes;

namespace LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;

public sealed class RolePermissionsVM
{
    [TableDecorator(TableDecoratorType.GroupActionCheckbox)]
    public bool Assigned { get; set; }
    [TableDecorator(TableDecoratorType.HideColumn)]
    public string Source { get; set; }
    public string Permission { get; set; }
    public string Description { get; set; }

    public override bool Equals(object obj) => Permission == (obj as RolePermissionsVM).Permission; //use to determine runtime equality for checkbox history
    public override int GetHashCode() =>  base.GetHashCode();
}

﻿using System.ComponentModel.DataAnnotations.Schema;
using LendQube.Entities.Core.Attributes;

namespace LendQube.Entities.Core.Base;

public abstract class BaseEntityWithHiloId : IBaseEntityWithNumberId, IAddToDbContext
{
    [DatabaseGenerated(DatabaseGeneratedOption.Identity), DbHilo, TableDecorator(TableDecoratorType.ShowInDelete, TableDecoratorType.ShowInInfo)]
    public new long Id { get; set; }
}

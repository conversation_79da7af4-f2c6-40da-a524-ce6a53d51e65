﻿using System.ComponentModel.DataAnnotations;
using LendQube.Entities.Collection.Base;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Constants;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Npgsql;
using Npgsql.EntityFrameworkCore.PostgreSQL.Infrastructure;

namespace LendQube.Entities.Collection.Customers;

public class Transaction : BaseEntityWithObjectId, IEntityHasEnum, IEntityTypeConfiguration<Transaction>
{
    [DbGuid, Required]
    public string ProfileId { get; set; }
    public virtual CustomerProfile Profile { get; set; }
    public string ProviderReference { get; set; }
    public string Purpose { get; set; }
    public string CurrencyCode { get; set; }
    public string CurrencySymbol { get; set; }
    public decimal UnitAmount { get; set; }
    public decimal Amount { get; set; }
    public decimal Fee { get; set; }
    public decimal TotalAmountPayable { get; set; }
    public decimal TotalAmountPaid { get; set; }
    public decimal Quantity { get; set; }
    public decimal Discount { get; set; }
    public PaymentType Type { get; set; }
    public PaymentProvider Provider { get; set; }
    public TransactionStatus Status { get; set; }
    public List<KeyValueHelperVM> Fields { get; set; } = [];
    public List<KeyValueHelperVM> UserData { get; set; } = [];
    public virtual ICollection<TransactionHistory> History { get; set; }

    public void Configure(EntityTypeBuilder<Transaction> builder)
    {
        builder.OwnsMany(x => x.Fields, x => x.ToJson());
        builder.OwnsMany(x => x.UserData, x => x.ToJson());

        builder.HasMany(x => x.History)
            .WithOne(x => x.Transaction)
            .OnDelete(DeleteBehavior.Cascade);
    }

    public void RegisterEnumInContext(ModelBuilder builder, string schema, INpgsqlNameTranslator nameTranslator)
    {
        builder.HasPostgresEnum<PaymentType>(schema, nameof(PaymentType), nameTranslator);
        builder.HasPostgresEnum<PaymentProvider>(schema, nameof(PaymentProvider), nameTranslator);
        builder.HasPostgresEnum<TransactionStatus>(schema, nameof(TransactionStatus), nameTranslator);
    }

    public void RegisterEnumInDataSource(NpgsqlDataSourceBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<PaymentType>($"{CollectionEntityConfig.DefaultSchema}.{nameof(PaymentType)}", nameTranslator);
        builder.MapEnum<PaymentProvider>($"{CollectionEntityConfig.DefaultSchema}.{nameof(PaymentProvider)}", nameTranslator);
        builder.MapEnum<TransactionStatus>($"{CollectionEntityConfig.DefaultSchema}.{nameof(TransactionStatus)}", nameTranslator);
    }

    public void RegisterEnumInDataSource(NpgsqlDbContextOptionsBuilder builder, INpgsqlNameTranslator nameTranslator)
    {
        builder.MapEnum<PaymentType>(nameof(PaymentType), CollectionEntityConfig.DefaultSchema, nameTranslator);
        builder.MapEnum<PaymentProvider>(nameof(PaymentProvider), CollectionEntityConfig.DefaultSchema, nameTranslator);
        builder.MapEnum<TransactionStatus>(nameof(TransactionStatus), CollectionEntityConfig.DefaultSchema, nameTranslator);
    }
}

public class TransactionHistory : BaseActivityTimeline<TransactionHistory>
{
    [StringLength(EntityConstants.DEFAULT_ID_FIELD_LENGTH)]
    public required string TransactionId { get; set; }
    public virtual Transaction Transaction { get; set; }

    public override void Configure(EntityTypeBuilder<TransactionHistory> builder)
    {
        base.Configure(builder);
        builder.HasIndex(x => x.TransactionId);
    }
}

public class TransactionLog
{
    public string RequestPayload { get; set; }
    public string ResponsePayload { get; set; }
}

[Flags]
public enum TransactionStatus
{
    Initiated = 1,
    Validated = 2,
    Queued = 3,
    Processing = 4,
    Successful = 5,
    Completed = 6,
    Failed = 10,
    Refunded = 11,
    PendingRefund = 12
}

public enum PaymentProvider
{
    Stripe,
    Upload,
    Acquired,
    Discount
}

public enum PaymentType
{
    SetupCard,
    Card,
    Bank,
}

﻿using System.Collections.Concurrent;
using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Entities.Core.BaseUser;
using LendQube.Entities.Core.Extensions;
using LendQube.Entities.Core.Location;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Collection.Analytics;
using LendQube.Infrastructure.Collection.Schedules;
using LendQube.Infrastructure.Collection.UploadServices.UploadTypes.ViewModels;
using LendQube.Infrastructure.Collection.ViewModels.Messaging;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using LendQube.Infrastructure.Core.ViewModels.Upload;
using Microsoft.EntityFrameworkCore;
using NodaTime;
using OfficeOpenXml;

namespace LendQube.Infrastructure.Collection.UploadServices.UploadTypes.PlacementUpload;

internal sealed class PlacementUploadService(IUnitofWork uow, IClock clock, ILogManager<PlacementUploadService> logger) : IUploadTypeService
{
    public async Task<Result<MessageBrick>> SaveData(CollectionFileUpload upload, ExcelWorksheet worksheet, MessageBrick message, ConcurrentBag<UploadResult> resultList, CancellationToken ct)
    {
        try
        {
            var countries = await uow.Db.ManySelectAsync(Query<Currency, CountryWithCurrencyVM>.Select(CountryWithCurrencyVM.Mapping), ct);

            //load all data into collections
            var start = worksheet.Dimension.Start;
            var end = worksheet.Dimension.End;
            var loadedData = worksheet.Cells[$"{start.Address}:{end.Address}"].ToCollectionWithMappings
                (
                    row => new UploadData().LoadCustomerData(upload, row, countries).LoadPlacementData(upload, row),
                    options =>
                    {
                        options.SetCustomHeaders(PlacementUploadDataLoader.Headers);
                        options.HeaderRow = null;
                        options.DataStartRow = 1;
                    }
                );

            if (loadedData.Count == 0)
            {
                resultList.Add(new(0, string.Empty, "No records found"));
                return "No records found";
            }


            var parallelData = loadedData.AsParallel();
            //check that data is valid
            if (upload.Action != UploadAction.Import)
            {
                var placementAccountIds = parallelData.Select(x => $"{x.Profile.Email}:{x.Placement.SourceAccountNumber}").ToList();
                var existingPlacements = await uow.Db.ManySelectAsync(Query<Placement, string>.Where(x => placementAccountIds.Contains(x.Profile.Email + ":" + x.SourceAccountNumber)).Select(x => x.SourceAccountNumber), ct);

                Parallel.ForEach(parallelData, new ParallelOptions { MaxDegreeOfParallelism = loadedData.Count, CancellationToken = ct }, (item, state, index) =>
                {
                    item.ValidateData(index + 2, existingPlacements, resultList);
                });
            }

            if (upload.Action == UploadAction.Analyze)
                return message;

            //prepare for insert
            var emails = parallelData.Where(x => !string.IsNullOrEmpty(x.Profile.Email)).Select(x => x.Profile.Email);
            var emailsToServer = emails.ToList();
            var existingCustomerData = await uow.Db.ManySelectAsync(Query<CustomerProfile, CustomerMapping>
                .Where(x => emailsToServer.Contains(x.Email))
                .DistinctBy(x => x.Id).Select(CustomerMapping.Mapping), ct);

            var existingCustomers = existingCustomerData.AsParallel();

            if (!existingCustomers.IsNullOrEmpty())
            {
                var emailsThatDontExist = emails.Where(x => !existingCustomers.Any(y => y.Email == x)).ToList();
                var usersNotToCreate = await uow.Db.ManySelectAsync(Query<ApplicationUser>.Where(x => emailsThatDontExist.Contains(x.Email)).DistinctBy(x => x.Id).Select(x => x.Email), ct);
                if (usersNotToCreate.Count > 0)
                {
                    var errorMessage = $"Admin account exists for {string.Join(", ", usersNotToCreate)}";
                    resultList.Add(new(0, string.Empty, errorMessage));
                    return errorMessage;
                }
            }

            message.Message(MessageConfigNames.NewPlacement.GetDisplayName(), upload.MessageConfigId);

            ConcurrentDictionary<string, List<UploadData>> dataSortedByEmail = [];
            ConcurrentBag<UploadData> unsortableData = [];

            PrepareDataForInsert(upload, message, parallelData, loadedData.Count, existingCustomers, dataSortedByEmail, unsortableData, ct);

            //insert data
            await InsertData(dataSortedByEmail, unsortableData, ct);

            //update statistics
            await UpdateStatistics(parallelData, dataSortedByEmail, unsortableData, ct);

            //set balances based on inserted data
            await SetBalances(existingCustomers, ct);


            //clean up
            loadedData.Clear();
            dataSortedByEmail.Clear();
            unsortableData.Clear();
            existingCustomerData.Clear();

            return message;
        }
        catch (Exception ex)
        {
            resultList.Add(new(0, string.Empty, ex.Message));
            logger.LogError(EventSource.Infrastructure, EventAction.FileProcessing, ex, $"Placement upload failed for upload: {upload.Description}");
        }
        return "Placement upload failed. Consult admin";
    }

    private void PrepareDataForInsert(CollectionFileUpload upload, MessageBrick message, ParallelQuery<UploadData> parallelData, int loadedDataCount, ParallelQuery<CustomerMapping> existingCustomers,
        ConcurrentDictionary<string, List<UploadData>> dataSortedByEmail, ConcurrentBag<UploadData> unsortableData, CancellationToken ct)
    {
        Parallel.ForEach(parallelData, new ParallelOptions { MaxDegreeOfParallelism = loadedDataCount, CancellationToken = ct }, (item) =>
        {
            var existingCustomer = existingCustomers.FirstOrDefault(x => x.Email == item.Profile.Email);
            if (existingCustomer != null)
            {
                item.ProfileId = existingCustomer.Id;
                item.Profile = null;
                if (existingCustomer.PostCodes.Contains(item.Address.PostCode))
                {
                    item.Address = null;
                }
            }
            else
            {
                item.ProfileId = item.Profile.Id;
                item.AppUser = item.Profile.GetUser(StringConstants.TempDomain);

                if (item.Placement.Status != PlacementStatus.Closed)
                {
                    item.Profile.BalanceTotal = item.Placement.BalanceTotal;
                    item.Profile.BalancePaid = item.Placement.BalancePaid;
                }
            }

            item.Placement.ProfileId = item.ProfileId;

            if (item.Placement.Status == PlacementStatus.Closed)
            {
                item.CustomerActivities.Add(new CustomerActivity
                {
                    ProfileId = item.ProfileId,
                    Title = "Closed placement",
                    Activity = $"Placement from {item.Placement.Company} with source account {item.Placement.SourceAccountNumber} uploaded as closed successfully",
                    CreatedByUserId = upload.CreatedByUserId,
                    CreatedByUser = upload.CreatedByUser,
                    CreatedByIp = upload.CreatedByIp,
                    CreatedDate = clock.GetCurrentInstant(),
                });
            }
            else
            {
                item.CustomerActivities.Add(new CustomerActivity
                {
                    ProfileId = item.ProfileId,
                    Title = "New placement",
                    Activity = $"Placement from {item.Placement.Company} with source account {item.Placement.SourceAccountNumber} created successfully",
                    CreatedByUserId = upload.CreatedByUserId,
                    CreatedByUser = upload.CreatedByUser,
                    CreatedByIp = upload.CreatedByIp,
                    CreatedDate = clock.GetCurrentInstant(),
                });


                if (existingCustomer != null)
                {
                    AddPlacementToSchedule(existingCustomer, item.Placement, item, item.SchedulesToCreate);
                }
            }

            item.Placement.Activities.Add(new PlacementActivity
            {
                Title = "New placement",
                Activity = "Placement created successfully",
                CreatedByUserId = upload.CreatedByUserId,
                CreatedByUser = upload.CreatedByUser,
                CreatedByIp = upload.CreatedByIp,
                CreatedDate = clock.GetCurrentInstant(),
            });

            if (item.Address != null)
                item.Address.CustomerProfileId = item.ProfileId;


            if (item.Profile == null || !string.IsNullOrEmpty(item.Profile.Email) || item.Profile.PhoneNumber != null)
            {
                message.WithRecipient(item.ProfileId,
                [
                    new($"{MessageTemplateKeys.CompanyName}", item.Placement.Company),
                    new($"{MessageTemplateKeys.AccountNumber}", item.Placement.SourceAccountNumber),
                    new($"{MessageTemplateKeys.Currency}", item.CurrencySymbol),
                    new($"{MessageTemplateKeys.Amount}", $"{item.Placement.BalanceTotal:n2}")
                ]);
            }

            if (item.Profile != null)
            {
                item.Profile.Activities = [.. item.CustomerActivities];
                item.CustomerActivities = null;
            }

            if (!string.IsNullOrEmpty(item.Profile?.Email))
            {
                if (dataSortedByEmail.TryGetValue(item.Profile.Email, out var record))
                {
                    record.Add(item);
                }
                else
                {
                    dataSortedByEmail[item.Profile.Email] = [item];
                }
            }
            else
            {
                unsortableData.Add(item);
            }
        });
    }

    private async Task InsertData(ConcurrentDictionary<string, List<UploadData>> dataSortedByEmail, ConcurrentBag<UploadData> unsortableData, CancellationToken ct)
    {
        if (!dataSortedByEmail.IsEmpty)
        {
            ConcurrentBag<SortedUploadData> sortedUploadData = [];

            Parallel.ForEach(dataSortedByEmail, new ParallelOptions { MaxDegreeOfParallelism = dataSortedByEmail.Count, CancellationToken = ct }, items =>
            {
                var referenceItem = items.Value.FirstOrDefault();
                var sortedData = new SortedUploadData
                {
                    AppUser = referenceItem.AppUser,
                    Profile = referenceItem.Profile
                };

                var parallelItems = items.Value.AsParallel();
                sortedData.Profile.BalanceTotal = parallelItems.Sum(x => x.Placement.Status != PlacementStatus.Closed ? x.Placement.BalanceTotal : 0);
                sortedData.Profile.BalancePaid = parallelItems.Sum(x => x.Placement.Status != PlacementStatus.Closed ? x.Placement.BalancePaid : 0);
                sortedData.Profile.Activities = parallelItems.SelectMany(x => x.Profile.Activities).ToList();

                sortedData.Profile.Activities.AsParallel().ForAll(y => y.ProfileId = referenceItem.ProfileId);

                sortedData.Address = referenceItem.Address;

                parallelItems.ForAll(x => x.Placement.ProfileId = referenceItem.ProfileId);

                sortedData.Placement = parallelItems.Select(x => x.Placement).ToList();
                sortedUploadData.Add(sortedData);

            });


            uow.Db.InsertBulk(sortedUploadData.Select(x => x.AppUser), ct);
            uow.Db.InsertBulk(sortedUploadData.Select(x => x.Profile), ct);
            uow.Db.InsertBulk(sortedUploadData.Select(x => x.Address), ct);
            await uow.Db.InsertBulkAsync(sortedUploadData.SelectMany(x => x.Placement), ct);
        }

        if (!unsortableData.IsEmpty)
        {
            uow.Db.InsertBulk(unsortableData.Where(x => x.AppUser != null).Select(x => x.AppUser), ct);
            uow.Db.InsertBulk(unsortableData.Where(x => x.Profile != null).Select(x => x.Profile), ct);
            uow.Db.InsertBulk(unsortableData.Where(x => x.Address != null).Select(x => x.Address), ct);
            uow.Db.InsertBulk(unsortableData.Where(x => !x.CustomerActivities.IsNullOrEmpty()).SelectMany(x => x.CustomerActivities), ct);
            uow.Db.InsertBulk(unsortableData.Where(x => !x.SchedulesToCreate.IsNullOrEmpty()).SelectMany(x => x.SchedulesToCreate), ct);
            await uow.Db.InsertBulkAsync(unsortableData.Select(x => x.Placement), ct);
        }

        await uow.SaveAsync(ct);
    }

    private async Task SetBalances(ParallelQuery<CustomerMapping> existingCustomers, CancellationToken ct)
    {
        if (existingCustomers.IsNullOrEmpty())
            return;

        var existingCustomerId = existingCustomers.Select(x => x.Id).ToList();

        var updateData = await uow.Db.ManySelectAsync(Query<CustomerProfile, CustomerProfileBalanceVM>.Where(x => existingCustomerId.Contains(x.Id))
            .Select(CustomerProfileBalanceVM.Mapping)
            .Where(x => x.DbBalanceTotal != x.BalanceTotal || x.DbBalancePaid != x.BalancePaid), ct);

        foreach (var item in updateData)
        {
            _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerProfile>(x => x.Id == item.Id, x =>
                x.SetProperty(y => y.BalanceTotal, item.BalanceTotal)
                .SetProperty(y => y.BalancePaid, item.BalancePaid), ct);
        }

    }

    private async Task UpdateStatistics(ParallelQuery<UploadData> parallelData, ConcurrentDictionary<string, List<UploadData>> dataSortedByEmail, ConcurrentBag<UploadData> unsortableData, CancellationToken ct)
    {
        var totalClosedPlacements = parallelData.Where(x => x.Placement.Status == PlacementStatus.Closed).Count();
        var totalClosedPlacementValue = parallelData.Where(x => x.Placement.Status == PlacementStatus.Closed).Sum(x => x.Placement.BalanceTotal - x.Placement.BalancePaid);

        var totalPlacements = parallelData.Where(x => x.Placement.Status != PlacementStatus.Closed).Count();
        var totalPlacementValue = parallelData.Where(x => x.Placement.Status != PlacementStatus.Closed).Sum(x => x.Placement.BalanceTotal - x.Placement.BalancePaid);

        var totalCustomers = dataSortedByEmail.Keys.Count + unsortableData.Where(x => x.Profile != null).Count();

        _ = await ManageAnalyticsService.UpdateTotalCustomers(uow, totalCustomers, totalPlacements, totalPlacementValue, totalClosedPlacements, totalClosedPlacementValue, ct);
    }

    private static void AddPlacementToSchedule(CustomerMapping profile, Placement placement, UploadData data, List<CustomerSchedule> schedulesToCreate)
    {
        var lastSchedule = profile.Schedules?.MaxBy(x => x.Period);
        if (lastSchedule == null || !profile.PaymentFrequency.HasValue)
            return;

        var amount = profile.Schedules.TakeLast(3).GroupBy(x => x.Amount).OrderByDescending(x => x.Count()).FirstOrDefault().Select(x => x.Amount).FirstOrDefault();
        var noOfPayments = (int)Math.Ceiling(placement.BalanceTotal / lastSchedule.Amount);
        var balanceLeft = placement.BalanceTotal;
        for (int i = 0; i < noOfPayments; i++)
        {
            amount = amount < balanceLeft ? amount : balanceLeft;
            var date = profile.PaymentFrequency.Value.GetDate(lastSchedule.DueDate, i);
            var schedule = new CustomerSchedule
            {
                ProfileId = profile.Id,
                Period = lastSchedule.Period + i,
                Amount = amount,
                DueDate = date,
                CPADate = date,
                PeriodStatus = SchedulePeriodStatus.NotDue,
                PaymentStatus = SchedulePaymentStatus.NotPaid
            };

            schedulesToCreate.Add(schedule);
            balanceLeft -= amount;
        }

        placement.Status = PlacementStatus.Active;

        data.CustomerActivities.Add(new CustomerActivity { ProfileId = profile.Id, Title = "Placement Upload", Activity = $"Account schedule updated to add placement with account no {placement.SourceAccountNumber} from {placement.Company}" });
    }
}
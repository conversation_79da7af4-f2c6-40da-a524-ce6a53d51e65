﻿using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Core.Attributes;
using LendQube.Entities.Core.Constants;
using LendQube.Infrastructure.Collection.Payments;
using LendQube.Infrastructure.Core.Helpers.Utils;

namespace LendQube.Infrastructure.Collection.Schedules;

public class RequestScheduleVM
{
    [Required]
    public SchedulePaymentFrequency Frequency { get; set; }
    [Required]
    public decimal Amount { get; set; }
    [Required]
    public DateTime? StartDate { get; set; }
    public DateOnly? DateOfBirth { get; set; }
}

public class ScheduleResponseVM
{
    public ScheduleResponseVM()
    {

    }

    public ScheduleResponseVM(decimal amount, decimal balanceAfterPayment, string currencySymbol, DateTime startDate, TransactionResultVM txn, string type, DateOnly? endDate = null)
    {
        Amount = amount;
        BalanceAfterPayment = balanceAfterPayment;
        CurrencySymbol = currencySymbol;
        StartDate = startDate;
        EndDate = endDate;
        Txn = txn;
        Type = type;
    }

    public decimal Amount { get; set; }
    public decimal BalanceAfterPayment { get; set; }
    public string CurrencySymbol { get; set; }
    public DateTime? StartDate { get; set; }
    public DateOnly? EndDate { get; set; }
    public TransactionResultVM Txn { get; set; }
    public string Type { get; set; }
}

public class CustomerScheduleVM
{
    public static readonly Expression<Func<CustomerProfile, CustomerScheduleVM>> Mapping = data => new CustomerScheduleVM
    {
        Id = data.Id,
        AccountId = data.AccountId,
        Email = data.Email,
        FirstName = data.FirstName,
        LastName = data.LastName,
        Phone = data.PhoneNumber,
        DateOfBirth = data.DateOfBirth,
        CurrencyCode = data.CurrencyCode,
        CurrencySymbol = data.CurrencySymbol,
        CanReschedule = data.CanReschedule,
        BalanceRemaining = data.BalanceRemaining,
        Schedules = data.Schedules
    };

    public static readonly Expression<Func<CustomerProfile, CustomerScheduleVM>> PaymentMapping = data => new CustomerScheduleVM
    {
        Id = data.Id,
        AccountId = data.AccountId,
        Email = data.Email,
        FirstName = data.FirstName,
        LastName = data.LastName,
        CurrencyCode = data.CurrencyCode,
        CurrencySymbol = data.CurrencySymbol,
        BalanceRemaining = data.BalanceRemaining,
    };

    public static readonly Expression<Func<CustomerProfile, CustomerScheduleVM>> FirstTimeScheduleMapping = data => new CustomerScheduleVM
    {
        Id = data.Id,
        BalanceRemaining = data.BalanceRemaining,
        PaymentFrequency = data.PaymentFrequency,
        Schedules = data.Schedules
    };

    public static readonly Expression<Func<CustomerProfile, CustomerScheduleVM>> RecalculateScheduleMapping = data => new CustomerScheduleVM
    {
        Id = data.Id,
        BalanceRemaining = data.BalanceRemaining,
        PaymentFrequency = data.PaymentFrequency,
        Schedules = data.Schedules.Where(x => x.PaymentStatus != Entities.Collection.Placements.SchedulePaymentStatus.Paid).OrderBy(x => x.Period).ToList()
    };

    public string Id { get; set; }
    public string AccountId { get; set; }
    public string Email { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public PhoneNumber Phone { get; set; }
    public DateOnly? DateOfBirth { get; set; }
    public string CurrencyCode { get; set; }
    public string CurrencySymbol { get; set; }
    public decimal BalanceRemaining { get; set; }
    public bool CanReschedule { get; set; }
    public SchedulePaymentFrequency? PaymentFrequency { get; set; }
    public IEnumerable<CustomerSchedule> Schedules { get; set; }
}

public class CustomerIncomeAndExpenditureVM
{
    public CustomerIncomeAndExpenditure Get(string userId)
    {
        if (string.IsNullOrEmpty(Address) || PhoneNumber == null)
            return null;

        return new()
        {
            ProfileId = userId,
            FullName = FullName,
            Address = Address,
            PhoneNumber = new PhoneNumber(PhoneNumber.Code, PhoneNumber.Number.CleanPhoneNumber(PhoneNumber.Code)),
            NetSalary = NetSalary,
            BenefitsIncome = BenefitsIncome,
            OtherIncome = OtherIncome,
            Rent = Rent,
            Utilities = Utilities,
            CouncilTax = CouncilTax,
            Food = Food,
            Transport = Transport,
            Insurance = Insurance,
            Loan = Loan,
            OtherExpenditure = OtherExpenditure,
        };
    }

    [Required, MaxLength(EntityConstants.DEFAULT_NAME_FIELD_LENGTH), ValidString(ValidStringRule.NoScriptTag)]
    public string FullName { get; set; }
    [ValidString(ValidStringRule.NoScriptTag)]
    public string Address { get; set; }
    public PhoneNumber PhoneNumber { get; set; }
    public decimal NetSalary { get; set; }
    public decimal BenefitsIncome { get; set; }
    public decimal OtherIncome { get; set; }
    public decimal Rent { get; set; }
    public decimal Utilities { get; set; }
    public decimal CouncilTax { get; set; }
    public decimal Food { get; set; }
    public decimal Transport { get; set; }
    public decimal Insurance { get; set; }
    public decimal Loan { get; set; }
    public decimal OtherExpenditure { get; set; }

    [Required]
    public decimal AmountToPay { get; set; }
    [Required]
    public decimal Balance { get; set; }
    [Required]
    public int DurationInMonths { get; set; }
}

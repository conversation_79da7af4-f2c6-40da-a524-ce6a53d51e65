﻿using System.ComponentModel;
using System.Linq.Expressions;
using System.Reflection;
using LendQube.Entities.Core.Base;
using LendQube.Entities.Core.Extensions;
using LendQube.Infrastructure.Core.Database.DataPager;
using LendQube.Infrastructure.Core.Database.DataPager.Filters;
using LendQube.Infrastructure.Core.Database.GenericCrud;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Database.Specification;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Telemetry;
using LendQube.Infrastructure.Core.ViewModels.Base;
using Microsoft.EntityFrameworkCore;

namespace LendQube.Infrastructure.Core.Messaging;

internal sealed class MessagingGroupBuilder(GeneralGenericCrudService crudService, ILogManager<MessagingGroupBuilder> logger)
{
    private readonly IUnitofWork uow = crudService.Uow;

    private readonly IReadOnlyList<string> columnsToExclude = [nameof(IBaseEntityWithNumberId.Id), nameof(IBaseEntityWithSystemStamp.CreatedByIp), nameof(IBaseEntityWithSystemStamp.CreatedByUser),
        nameof(IBaseEntityWithSystemStamp.CreatedByUserId), nameof(IBaseEntityWithSystemStamp.ModifiedByIp), nameof(IBaseEntityWithSystemStamp.ModifiedByUser),
        nameof(IBaseEntityWithSystemStamp.ModifiedByUserId)];

    public Dictionary<string, List<ColumnFilter>> GetAllRelatedTablesAndColumns<T>()
    {
        Dictionary<string, List<ColumnFilter>> properties = [];

        var tableData = uow.Context.Model.FindEntityType(typeof(T));

        properties.Add(tableData.ClrType.Name, []);
        foreach (var item in tableData.GetDeclaredProperties().Where(x => !columnsToExclude.Contains(x.Name)))
        {
            AddFilter(properties[tableData.ClrType.Name], new ColumnPropertyInfo(item.Name, item.Name, item.ClrType, true), null);
        }

        var navigations = tableData.GetDeclaredReferencingForeignKeys().Where(x => !x.IsOwnership && x.PrincipalToDependent != null);

        foreach (var navigation in navigations)
        {
            var navigationProperty = tableData.ClrType.GetProperty(navigation.PrincipalToDependent.Name);
            AddFilter(properties[tableData.ClrType.Name], new ColumnPropertyInfo(navigation.PrincipalToDependent.Name, navigation.PrincipalToDependent.Name, navigationProperty.PropertyType, true), null);
            var navigationTable = uow.Context.Model.FindEntityType(navigation.DeclaringEntityType.ClrType);
            properties.Add(navigation.DeclaringEntityType.ClrType.Name, []);
            foreach (var item in navigationTable.GetDeclaredProperties().Where(x => !columnsToExclude.Contains(x.Name)))
            {
                AddFilter(properties[navigation.DeclaringEntityType.ClrType.Name], new ColumnPropertyInfo(item.Name, item.Name, item.ClrType, true), navigation.PrincipalToDependent.IsCollection ? null : navigation.PrincipalToDependent.Name);
            }
        }

        return properties;
    }

    private static void AddFilter(List<ColumnFilter> columnFilters, ColumnPropertyInfo item, string tableName)
    {
        var filter = new ColumnFilter
        {
            Name = string.IsNullOrEmpty(tableName) ? item.Name : $"{tableName}.{item.Name}",
            DisplayName = item.Name.SplitOnUpper(),
            DataType = item switch
            {
                { PropertyType: var type } when type.IsEnum() => ColumnFilterDataType.EnumList,
                { PropertyType: var type } when type.IsDate() || type.IsInstant() => ColumnFilterDataType.Date,
                { PropertyType: var type } when type.IsNumericType() => ColumnFilterDataType.Number,
                { PropertyType: var type } when type.IsBooleanType() => ColumnFilterDataType.BoolList,
                { PropertyType: var type } when type.IsPhoneNumber() => ColumnFilterDataType.PhoneNumber,
                { PropertyType: var type } when type == typeof(string) => ColumnFilterDataType.Text,
                _ => ColumnFilterDataType.Object
            }
        };

        filter.DataTypeValueList = filter.DataType switch
        {
            ColumnFilterDataType.EnumList => item.PropertyType.IsEnum ? [.. Enum.GetNames(item.PropertyType)] : [.. Enum.GetNames(Nullable.GetUnderlyingType(item.PropertyType))],
            ColumnFilterDataType.BoolList => [.. Enum.GetNames(typeof(ColumnFilterBoolOptions))],
            _ => null
        };

        filter.Rules = ObjectFilterBuilder.GetAll(new(item.PropertyType, item.Name == nameof(IBaseEntityWithNumberId.Id), filter.DataType));

        columnFilters.Add(filter);
    }

    public Result<string> GenerateQuery<T, TVM>(MessagingGroupBuilderVM builder, Expression<Func<T, TVM>> selector) where T : class, IBaseEntityForRelationalDb
    {
        try
        {
            var tableData = uow.Context.Model.FindEntityType(typeof(T));
            var blocks = builder.Blocks.ToDictionary(x => x.Value.TableName, x => x.Value);
            var navigations = tableData.GetDeclaredReferencingForeignKeys().Where(x => !x.IsOwnership && x.PrincipalToDependent != null && blocks.ContainsKey(x.DeclaringEntityType.ClrType.Name));


            Expression<Func<T, bool>> where = blocks.Remove(tableData.ClrType.Name, out var baseBlock) ? GetComplexFilter<T>(baseBlock) : null;

            MethodInfo method = typeof(MessagingGroupBuilder).GetMethod(nameof(GetComplexFilter), BindingFlags.Static | BindingFlags.NonPublic);

            foreach (var block in blocks)
            {
                var navigation = navigations.FirstOrDefault(x => x.DeclaringEntityType.ClrType.Name == block.Key);

                Expression<Func<T, bool>> expression = null;

                if (navigation.PrincipalToDependent.IsCollection)
                {
                    MethodInfo generic = method.MakeGenericMethod(navigation.DeclaringEntityType.ClrType);
                    var innerExpression = generic.Invoke(this, [block.Value]);

                    if (innerExpression == null)
                        continue;

                    expression = ExpressionsExtension.AnyExpression<T>(innerExpression as LambdaExpression, navigation.DeclaringEntityType.ClrType, navigation.PrincipalToDependent.Name);
                }
                else
                    expression = GetComplexFilter<T>(block.Value);

                if (expression == null)
                    continue;

                if (where == null)
                    where = expression;
                else
                    where = block.Value.Condition == FilterCondition.Or ? where.CombineWithOrElse(expression) : where.CombineWithAndAlso(expression);
            }

            var query = uow.Db.Queryable<T>().AsNoTracking().Where(where).Select(selector).ToQueryString();
            return Result<string>.Successful(data: query);

        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.Messaging, EventAction.Update, ex, $"Builder query generation failed");
        }

        return Result<string>.Failed("An error occurred generating query. Please notify admin");
    }

    private static Expression<Func<T, bool>> GetComplexFilter<T>(MessageGroupBuilderBlock block)
    {
        Expression<Func<T, bool>> filterCriteria = null;
        var filters = block.FilterInputs.Values.Where(x => x.IsValid);
        if (filters.IsNullOrEmpty())
            return filterCriteria;

        var propertyTypes = TypeDescriptor.GetProperties(typeof(T)).Cast<PropertyDescriptor>()
            .Where(x => filters.Any(y => y.ColumnName == x.Name || x.Name == y.GetTableName()))
            .Select(p => new { p.Name, p.PropertyType, IsLocal = filters.Any(y => y.ColumnName == p.Name) });

        var internalPropertyTypes = propertyTypes.Any(x => x.IsLocal) ? [] :
            TypeDescriptor.GetProperties(propertyTypes.FirstOrDefault(x => !x.IsLocal).PropertyType).Cast<PropertyDescriptor>()
            .Where(x => filters.Any(y => x.Name == y.GetTableColumnName()))
            .Select(p => new { p.Name, p.PropertyType });

        object value = null;
        foreach (var item in filters)
        {
            var rule = ObjectFilterBuilder.Get(item.RuleId);
            var type = propertyTypes.FirstOrDefault(x => x.Name == item.ColumnName)?.PropertyType ??
                internalPropertyTypes.FirstOrDefault(x => x.Name == item.GetTableColumnName())?.PropertyType;

            if (rule == null || type == null)
                continue;

            rule.EmbeddedQuery = false;
            if (type.IsBooleanType())
                value = Convert.ToBoolean(Enum.Parse(typeof(ColumnFilterBoolOptions), item.Value.ToString()));
            else
                value = item.Value;

            Expression<Func<T, bool>> expression = rule.GenerateExpression<T>(type, item, value);

            if (expression == null)
                continue;

            if (filterCriteria == null)
                filterCriteria = expression;
            else
                filterCriteria = item.Condition == FilterCondition.Or ? filterCriteria.CombineWithOrElse(expression) : filterCriteria.CombineWithAndAlso(expression);

        }

        return filterCriteria;
    }

    public ColumnList GetTableDefinition() => crudService.GetTableDefinition<MessageGroupUserForBuilder>(new() { HasId = false, HasDateColumns = false });

    public ValueTask<TypedBasePageList<MessageGroupUserForBuilder>> GetPreviewData(string query, DataFilterAndPage filter, CancellationToken ct)
    {
        var spec = new BaseSpecification<MessageGroupUserForBuilder>();

        var dbSet = uow.Context.Database.SqlQueryRaw<MessageGroupUserForBuilder>(query);

        return crudService.GetTypeBasedPagedData(dbSet, spec, filter, ct);
    }

    public IQueryable<MessageGroupUserForBuilder> GetPreviewDataQueryable(string query)
    {
        return uow.Context.Database.SqlQueryRaw<MessageGroupUserForBuilder>(query);
    }

}

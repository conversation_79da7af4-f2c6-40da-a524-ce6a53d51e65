﻿using LendQube.Entities.Core.Attributes;
using System.ComponentModel.DataAnnotations;

namespace LendQube.Infrastructure.Core.AdminUserManagement.ViewModels;

public sealed class RoleVM
{
    public Guid? Id { get; set; }
    [Required, ValidString(ValidStringRule.OnlyTextWithSpacing)]
    public string Name { get; set; }

    [Required, ValidString(ValidStringRule.OnlyTextWithSpecialCharactersWithSpacing)] 
    public string Description { get; set; }
}


public sealed class UserRoleVM
{
    [TableDecorator(TableDecoratorType.GroupActionCheckbox)]
    public bool Assigned { get; set; }
    public string Role { get; set; }
    public string Description { get; set; }
    public override bool Equals(object obj) => Role == (obj as UserRoleVM).Role; //use to determine runtime equality for checkbox history
    public override int GetHashCode() => base.GetHashCode();
}
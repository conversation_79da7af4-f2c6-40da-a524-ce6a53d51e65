﻿using Coravel.Invocable;
using LendQube.Entities.Collection.Customers;
using LendQube.Entities.Collection.Placements;
using LendQube.Infrastructure.Collection.Helpers;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Collection.Schedules.BackgroundServices;

internal sealed class DueScheduleMonitorBackgroundService(IUnitofWork uow) : IInvocable, ICancellableInvocable
{
    public CancellationToken CancellationToken { get; set; }

    public async Task Invoke()
    {
        var now = DateOnly.FromDateTime(DateTime.UtcNow);
        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerSchedule>(x => x.CPADate == now && ScheduleHelper.UnsettledStatus.HasFlag(x.PaymentStatus)
            && x.PeriodStatus == SchedulePeriodStatus.NotDue, x => x.SetProperty(y => y.PeriodStatus, SchedulePeriodStatus.Due), CancellationToken);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<CustomerSchedule>(x => x.CPADate < now && ScheduleHelper.UnsettledStatus.HasFlag(x.PaymentStatus)
            && x.PeriodStatus != SchedulePeriodStatus.PastDue, x => x.SetProperty(y => y.PeriodStatus, SchedulePeriodStatus.PastDue), CancellationToken);

        _ = await uow.Db.UpdateAndSaveWithFilterAsync<Placement>(x => x.Status == PlacementStatus.Active && x.Profile.Schedules.Any(y => y.PeriodStatus == SchedulePeriodStatus.PastDue && ScheduleHelper.UnsettledStatus.HasFlag(y.PaymentStatus)),
        x => x.SetProperty(y => y.Status, PlacementStatus.Broken), CancellationToken);
    }
}

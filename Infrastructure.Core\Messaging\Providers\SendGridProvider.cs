﻿using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.Database.Repository;
using LendQube.Infrastructure.Core.Extensions;
using LendQube.Infrastructure.Core.Helpers.Utils;
using LendQube.Infrastructure.Core.Messaging.Configuration;
using LendQube.Infrastructure.Core.Telemetry;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace LendQube.Infrastructure.Core.Messaging.Providers;

internal sealed class SendGridProvider(IUnitofWork uow, DefaultAppConfig config, ISendGridClient sendgridClient, HttpClient httpClient, ILogManager<SendGridProvider> logger) : AbstractMessageProvider(uow), IEmailProvider
{
    public const string Name = "SendGrid";
    protected override MessageChannel SupportedChannel => MessageChannel.Email;
    private readonly int bulkLimit = 1000;

    public override ProviderConfigVM Config { get; } = MessagingCompiledQueries.GetProviderConfig(uow, Name) ?? new ProviderConfigVM { Disabled = true };

    public override async Task<MessageStatus> ProcessMessage(IReadOnlyList<PreparedMessageVM> messages, CancellationToken ct)
    {
        Dictionary<long, MessageStatus> result = [];
        var leadingMessage = messages[0];
        LogActivity(leadingMessage, MessageStatus.Processing, Name);

        var from = new EmailAddress(config.SendGrid.SenderEmail, config.SendGrid.SenderName);

        var parallelCount = messages.Count < 8000 ? messages.Count : 8000;
        await Parallel.ForEachAsync(messages, new ParallelOptions { MaxDegreeOfParallelism = parallelCount, CancellationToken = ct }, async (message, ct) =>
        {
            message.Data = message.Data.Where(x => x.HasEmail).ToList();
            if (message.Data.Count == 0)
                return;

            if (!string.IsNullOrEmpty(message.SenderName))
                from.Name = message.SenderName;
            if (!string.IsNullOrEmpty(message.SenderEmail))
                from.Email = message.SenderEmail;

            if (message.HasSeparateTemplateValues || message.Data.Any(x => !x.Attachments.IsNullOrEmpty()) || message.Data.Any(x => !x.CopiedIn.IsNullOrEmpty()))
            {
                foreach (var (recipient, index) in message.Data.Select((x, i) => (x, i)))
                {
                    result[message.MessageId + index] = await SendSingleMessage(from, message, recipient, ct);
                }
                return;
            }

            if (!message.HasSeparateTemplateValues)
                SubstituteAllTemplateTypeKeys(message);

            var data = message.Data.SelectMany(x => x.Emails.Select(y => new SinglePreparedMessageVM
            {
                UserId = x.UserId,
                Email = y,
                Name = x.Name,
                TemplateValues = x.TemplateValues,
                Subject = x.Subject,
                HtmlTemplate = x.HtmlTemplate,
            })).OrderBy(x => x.Email);

            var total = data.Count();
            var skip = 0;

            SendGridMessage dispatch = null;

            while (total > 0)
            {
                var toSkip = skip * bulkLimit;
                var pagedData = data.Skip(toSkip).Take(bulkLimit);

                var emails = pagedData.Select(m => new EmailAddress(m.Email, m.Name)).ToList();
                if (message.HasSeparateTemplateValues)
                {
                    var substitutions = pagedData.Select(m => m.TemplateValues.ToDictionary(t => $"{{{t.Key.ToLowerInvariant()}}}", t => t.Value)).ToList();
                    var subjects = pagedData.Select(x => x.Subject ?? message.Subject).ToList();
                    dispatch = MailHelper.CreateMultipleEmailsToMultipleRecipients
                        (
                            from,
                            emails,
                            subjects,
                            null,
                            message.HtmlTemplate,
                            substitutions
                        );
                }
                else
                {
                    dispatch = MailHelper.CreateSingleEmailToMultipleRecipients
                        (
                            from,
                            emails,
                            message.Subject,
                            null,
                            message.HtmlTemplate
                        );
                }


                if (!message.CopiedIn.IsNullOrEmpty())
                {
                    dispatch.AddCcs(message.CopiedIn.Where(c => !c.BlindCopy).Select(v => new EmailAddress(v.Email, v.Name)).ToList());
                    dispatch.AddBccs(message.CopiedIn.Where(c => c.BlindCopy).Select(v => new EmailAddress(v.Email, v.Name)).ToList());
                }

                dispatch.CustomArgs = new Dictionary<string, string>
                {
                    { "Subject", message.Subject },
                    { "IsDev", config.DeployState.IsDemo.ToString() }
                };

                try
                {
                    var response = await sendgridClient.SendEmailAsync(dispatch, ct);
                    result[message.MessageId + skip] = (System.Net.HttpStatusCode.Accepted == response?.StatusCode).ToMessageStatus();
                    if (result[message.MessageId + skip] == MessageStatus.Failed)
                    {
                        var error = await response.DeserializeResponseBodyAsync();
                        logger.LogWarning(EventSource.Infrastructure, EventAction.SendMail, $"SendGrid {nameof(ProcessMessage)} failed", data: error);
                    }
                }
                catch (Exception ex)
                {
                    result[message.MessageId + skip] = MessageStatus.Failed;

                    logger.LogError(EventSource.BackgroundTask, EventAction.SendMail, ex, $"Could not ProcessMessage: {message.MessageId} {skip}");
                    LogActivity(leadingMessage, MessageStatus.Failed, Name, "Provider failure");
                }

                skip++;
                total -= bulkLimit;
            }
        });

        var status = result.ToMessageStatus();
        LogActivity(leadingMessage, status, Name);
        await uow.SaveAsync(ct);

        await UpdateProviderWithResult(messages, result, null, null, ct);
        return status;
    }

    private async Task<MessageStatus> SendSingleMessage(EmailAddress from, PreparedMessageVM message, SinglePreparedMessageVM vm, CancellationToken ct)
    {
        SubstituteSingleReceiverHtmlTemplateTypeKeys(message, vm);
        var msg = new SendGridMessage()
        {
            From = from,
            Subject = vm.Subject,
            CustomArgs = new Dictionary<string, string>
                {
                    { "Subject", vm.Subject },
                    { "IsDev", config.DeployState.IsDemo.ToString() }
                }
        };

        msg.AddContent(MimeType.Html, vm.HtmlTemplate);

        foreach (var email in vm.Emails)
        {
            msg.AddTo(new EmailAddress(email, vm.Name));
        }

        if (!vm.CopiedIn.IsNullOrEmpty())
        {
            msg.AddCcs(message.CopiedIn.Where(c => !c.BlindCopy).Select(v => new EmailAddress(v.Email, v.Name)).ToList());
            msg.AddBccs(message.CopiedIn.Where(c => c.BlindCopy).Select(v => new EmailAddress(v.Email, v.Name)).ToList());
        }

        var successful = false;

        try
        {
            if (!vm.Attachments.IsNullOrEmpty())
            {
                foreach (var item in vm.Attachments)
                {
                    var fileType = Path.GetExtension(item.Url);
                    var bytes = await httpClient.ReadPhysicalFileAsByteArray(item.Url, ct);
                    if (bytes != null && bytes.Length > 0)
                    {
                        var file = Convert.ToBase64String(bytes);
                        msg.AddAttachment(item.FileName + fileType, file);
                    }
                }
            }

            var response = await sendgridClient.SendEmailAsync(msg, ct);
            successful = System.Net.HttpStatusCode.Accepted == response?.StatusCode;
            if (!successful)
            {
                var error = await response.DeserializeResponseBodyAsync();
                logger.LogWarning(EventSource.Infrastructure, EventAction.SendMail, $"SendGrid {nameof(SendSingleMessage)} failed", data: error);
            }
        }
        catch (Exception ex)
        {
            logger.LogError(EventSource.BackgroundTask, EventAction.SendMail, ex, $"Could not SendSingleMessage: {message.MessageId}");
        }

        return successful.ToMessageStatus();
    }
}

﻿using LendQube.Infrastructure.Core.Components.Helpers;
using LendQube.Infrastructure.Core.Database.DataPager;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;

namespace LendQube.Infrastructure.Core.Components.Table;

public abstract class AbstractBaseTableComponentBase : AuthComponentBase
{
    [Inject]
    protected IJSRuntime JSRuntime { get; set; }

    protected List<RowActionButton> RowActionButtons { get; init; } = [];
    protected List<TopActionButton> TopActionButtons { get; init; } = [];
    protected StatusMessage TableMessage { get; set; }
    protected StatusMessageBuilder ModalMessage { get; init; } = new();

    protected string CustomMessage { get; set; }
    protected override Task OnInitializedAsync() => base.OnInitializedAsync();

    protected ColumnList GetTableDefinition(ColumnList definition, string editPermission = null, string deletePermission = null)
    {
        ArgumentNullException.ThrowIfNull(definition);

        definition.HasEdit = HasClaim(editPermission);
        definition.HasDelete = HasClaim(deletePermission);
        definition.RowActionButtons = [];
        definition.TopActionButtons = [];
        return definition;
    }

    protected ColumnList GetLocalTableDefinition(ColumnList definition, string editPermission, string deletePermission)
    {
        ArgumentNullException.ThrowIfNull(definition);

        definition.HasEdit = HasClaim(editPermission);
        definition.HasDelete = HasClaim(deletePermission);
        definition.RowActionButtons = RowActionButtons;
        definition.TopActionButtons = TopActionButtons;
        return definition;
    }

    protected void CloseMessage()
    {
        CustomMessage = null;
        TableMessage?.Close();
        ModalMessage?.Close();
        StateHasChanged();
    }
    protected Task CloseMessage(StatusMessageBuilder message)
    {
        message?.Close();
        StateHasChanged();
        return Task.CompletedTask;
    }

    protected void AddTopButton(TopActionButton button) => TopActionButtons.Add(button);

    protected void AddTopButton(string permission, TopActionButton button)
    {
        if (HasClaim(permission))
            TopActionButtons.Add(button);
    }

    protected static void AddTopButton(ColumnList definition, TopActionButton button) => definition.TopActionButtons.Add(button);

    protected void AddTopButton(ColumnList definition, string permission, TopActionButton button)
    {
        if (HasClaim(permission))
            definition.TopActionButtons.Add(button);
    }

    protected void AddRowButton(RowActionButton button) => RowActionButtons.Add(button);

    protected void AddRowButton(string permission, RowActionButton button)
    {
        if (HasClaim(permission))
            RowActionButtons.Add(button);
    }

    protected static void AddRowButton(ColumnList definition, RowActionButton button) => definition.RowActionButtons.Add(button);

    protected void AddRowButton(ColumnList definition, string permission, RowActionButton button)
    {
        if (HasClaim(permission))
            definition.RowActionButtons.Add(button);
    }

    protected async ValueTask BaseSaveAdd(string permission, string modalName, Func<Task<bool>> save, Func<Task> refresh)
	{
		CloseMessage();
		if (!HasClaim(permission))
        {
            TableMessage?.Warning("User not permitted to perform this action");
            await JSRuntime.CloseModal(modalName, Cancel);
            return;
        }

        var result = await save();
        if (result)
        {
            await JSRuntime.CloseModal(modalName, Cancel);
            await refresh();
            TableMessage?.Success($"Data saved successfully. {CustomMessage}");
        }
        else
        {
            ModalMessage?.Error($"Saving data failed. {CustomMessage}");
            StateHasChanged();
        }
    }

    protected async ValueTask BaseSaveAddWithRedirect(string permission, string modalName, Func<Task<bool>> save, Action redirect)
    {
        CloseMessage();

        if (!HasClaim(permission))
        {
            TableMessage?.Warning("User not permitted to perform this action");
            await JSRuntime.CloseModal(modalName, Cancel);
            return;
        }

        var result = await save();
        if (result)
        {
            await JSRuntime.CloseModal(modalName, Cancel);
            redirect();
        }
        else
        {
            ModalMessage?.Error($"Saving data failed. {CustomMessage}");
            StateHasChanged();
        }
    }

    protected async ValueTask BaseEdit(string permission, string modalName, Func<Task> loadData, CancellationToken ct)
    {
        if (!HasClaim(permission))
        {
            TableMessage?.Warning("User not permitted to perform this action");
            return;
        }

		CloseMessage();

		await loadData();
        StateHasChanged();
        if (!ct.IsCancellationRequested)
            await JSRuntime.OpenModal(modalName, ct);
    }

    protected async ValueTask BaseSaveEdit(string permission, string modalName, Func<Task<bool>> save, Func<Task> refresh)
	{
		CloseMessage();

		if (!HasClaim(permission))
        {
            TableMessage?.Warning("User not permitted to perform this action");
            await JSRuntime.CloseModal(modalName, Cancel);
            return;
        }

        var result = await save();
        if (result)
        {
            await JSRuntime.CloseModal(modalName, Cancel);
            await refresh();
            TableMessage?.Success($"Data saved successfully. {CustomMessage}");
        }
        else
        {
            ModalMessage?.Error($"Saving data failed. {CustomMessage}");
            StateHasChanged();
        }
    }

    protected async ValueTask<bool> SaveDelete(string permission, Func<Task<bool>> save, Func<Task> refresh)
	{
		CloseMessage();

		if (!HasClaim(permission))
        {
            TableMessage?.Warning("User not permitted to perform this action");
            return false;
        }

        var result = await save();
        await refresh();
        if (result)
        {
            TableMessage?.Success($"Data deleted successfully. {CustomMessage}");
        }
        else
        {
            TableMessage?.Error($"Deleting data failed. {CustomMessage}");
        }

        return result;
    }
}

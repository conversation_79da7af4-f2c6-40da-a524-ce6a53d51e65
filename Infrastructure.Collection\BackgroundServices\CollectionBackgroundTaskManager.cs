﻿using LendQube.Entities.Collection.Collections;
using LendQube.Entities.Core.BackgroundTasks;
using LendQube.Entities.Core.Uploads;
using LendQube.Infrastructure.Core.AppSettings;
using LendQube.Infrastructure.Core.BackgroundTasks;
using LendQube.Infrastructure.Core.Database.Repository;

namespace LendQube.Infrastructure.Collection.BackgroundServices;

internal sealed class CollectionBackgroundTaskManager(DefaultAppConfig config, BackgroundTaskControlService controlService, IUnitofWork uow) : AbstractBackgroundTaskManager(controlService)
{
    public override BackgroundEventSource AllowedSource => BackgroundEventSource.Collection;
    public override IReadOnlyList<BackgroundTask> AllowedTasks => [BackgroundTask.UploadCollection];

    public override bool IsActive(BackgroundEventSource source) => config.DeployState.EnabledApplications.Collections && base.IsActive(source);

    public override async Task StartTasks(BackgroundTask key, CancellationToken ct)
    {
        switch (key)
        {
            case BackgroundTask.UploadCollection:
                _ = await uow.Db.UpdateAndSaveWithFilterAsync<CollectionFileUpload>(x => x.Status == UploadStatus.Queued,
             x => x.SetProperty(y => y.Status, UploadStatus.Queued), ct);
                break;
            default:
                break;
        }
    }
}

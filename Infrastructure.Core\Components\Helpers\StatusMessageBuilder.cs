﻿namespace LendQube.Infrastructure.Core.Components.Helpers;

internal enum MessageType
{
    Info,
    Success,
    Warning,
    Error
}

public class StatusMessageBuilder
{
    internal MessageType Type { get; set; }
    internal string Message { get; set; }
    internal bool Loading { get; set; }
    internal bool Showing { get; set; }
}

public static class StatusMessageBuilderExtensions
{
    public static void Set(this StatusMessageBuilder builder, bool result, string message, bool loading = false)
    {
        builder.Type = result ? MessageType.Success : MessageType.Error;
        builder.Message = message;
        builder.Loading = loading;
        builder.Showing = true;
    }

    public static void Set(this StatusMessageBuilder builder, bool result, string success, string failure, bool loading = false)
    {
        builder.Type = result ? MessageType.Success : MessageType.Error;
        builder.Message = result ? success : failure;
        builder.Loading = loading;
        builder.Showing = true;
    }

    public static void Success(this StatusMessageBuilder builder, string message, bool loading = false)
    {
        builder.Type = MessageType.Success;
        builder.Message = message;
        builder.Loading = loading;
        builder.Showing = true;
    }

    public static void Info(this StatusMessageBuilder builder, string message, bool loading = false)
    {
        builder.Type = MessageType.Info;
        builder.Message = message;
        builder.Loading = loading;
        builder.Showing = true;
    }

    public static void Warning(this StatusMessageBuilder builder, string message, bool loading = false)
    {
        builder.Type = MessageType.Warning;
        builder.Message = message;
        builder.Loading = loading;
        builder.Showing = true;
    }

    public static void Error(this StatusMessageBuilder builder, string message, bool loading = false)
    {
        builder.Type = MessageType.Error;
        builder.Message = message;
        builder.Loading = loading;
        builder.Showing = true;
    }

    public static void Close(this StatusMessageBuilder builder)
    {
        builder.Showing = false;
    }
}

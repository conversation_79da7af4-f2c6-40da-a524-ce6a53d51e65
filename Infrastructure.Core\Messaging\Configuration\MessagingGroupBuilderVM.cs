﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json;
using System.Text.Json.Serialization;
using LendQube.Entities.Core.Messaging;
using LendQube.Infrastructure.Core.Database.DataPager;

namespace LendQube.Infrastructure.Core.Messaging.Configuration;

public sealed class MessagingGroupBuilderVM
{
    public long Id { get; set; }
    [Required]
    public string Description { get; set; }
    public string Query { get; set; }
    public bool WriteOwnSqlQuery { get; set; }
    public bool CustomQueryValidated { get; set; }
    public string CustomQuery { get; set; }
    public List<string> TableNames { get; set; } = [];
    public Dictionary<Guid, MessageGroupBuilderBlock> Blocks { get; set; } = [];

    public MessagingGroupQuery Get(long messagingGroupId) => new()
    {
        Id = Id,
        MessagingGroupId = messagingGroupId,
        Description = Description,
        Query = Query,
        BuildingBlock = !Blocks.Values.Any(x => x.IsValid) || CustomQueryValidated ? null : JsonSerializer.Serialize(Blocks),
    };
}

public sealed class MessageGroupBuilderBlock
{
    [Required]
    public string TableName { get; set; }
    [JsonIgnore]
    public List<string> TableNames { get; set; } = [];
    [JsonIgnore]
    public List<ColumnFilter> FilterDefinition { get; set; } = [];
    public Dictionary<Guid, ComplexFilter> FilterInputs { get; set; } = [];
    public FilterCondition? Condition { get; set; }
    public bool ShouldHaveCondition { get; set; }
    public bool IsValid => FilterInputs.Values.Any(x => x.IsValid) && (!ShouldHaveCondition || Condition.HasValue);
}

public sealed class MessageGroupUserForBuilder
{
    public string FullName { get; set; }
    public string EmailAddress { get; set; }
    public string AccountId { get; set; }
    public string PhoneNumber { get; set; }
}